import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from datetime import datetime, timedelta
import json

# Import specialized forecasting libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from pmdarima import auto_arima
from sklearn.metrics import mean_absolute_error, mean_squared_error

logger = logging.getLogger(__name__)

class ForecastingService:
    """
    Service for time series forecasting
    """

    def __init__(self, df: Optional[pd.DataFrame] = None):
        """
        Initialize the forecasting service

        Args:
            df: Optional DataFrame to analyze
        """
        self.df = df

    def set_dataframe(self, df: pd.DataFrame) -> None:
        """
        Set the DataFrame to analyze

        Args:
            df: DataFrame to analyze
        """
        self.df = df

    def forecast(
        self,
        target_column: str,
        date_column: str,
        horizon: int = 10,
        frequency: Optional[str] = None,
        method: str = "auto",
        confidence_interval: float = 0.95
    ) -> Dict[str, Any]:
        """
        Generate forecast for a time series

        Args:
            target_column: Column containing the values to forecast
            date_column: Column containing the dates
            horizon: Number of periods to forecast
            frequency: Time series frequency (e.g., 'D', 'M', 'Y')
            method: Forecasting method ('auto', 'arima', 'exponential_smoothing', 'prophet', 'simple')
            confidence_interval: Confidence interval for prediction bounds (0-1)

        Returns:
            Dictionary with forecast results
        """
        if self.df is None:
            raise ValueError("DataFrame not set")

        if target_column not in self.df.columns:
            raise ValueError(f"Column '{target_column}' not found in DataFrame")

        if date_column not in self.df.columns:
            raise ValueError(f"Column '{date_column}' not found in DataFrame")

        # Try to convert target column to numeric if it's not already
        if not pd.api.types.is_numeric_dtype(self.df[target_column]):
            try:
                self.df[target_column] = pd.to_numeric(self.df[target_column], errors='coerce')
            except Exception as e:
                logger.warning(f"Could not convert target column to numeric: {str(e)}")
                raise ValueError(f"Target column '{target_column}' must be numeric")

        # Try to convert date column to datetime if it's not already
        if not pd.api.types.is_datetime64_dtype(self.df[date_column]):
            try:
                self.df[date_column] = pd.to_datetime(self.df[date_column], errors='coerce')
            except Exception as e:
                logger.warning(f"Could not convert date column to datetime: {str(e)}")
                raise ValueError(f"Date column '{date_column}' must be convertible to datetime")

        # Determine the best method if auto is selected
        if method == "auto":
            try:
                method = self._select_best_method(self.df, target_column)
                logger.info(f"Auto-selected forecasting method: {method}")
            except Exception as e:
                logger.warning(f"Error in method selection: {str(e)}. Falling back to simple method.")
                method = "simple"

        # Validate the method
        valid_methods = ["simple", "arima", "exponential_smoothing", "prophet"]
        if method not in valid_methods:
            logger.warning(f"Invalid method '{method}'. Falling back to simple method.")
            method = "simple"

        # Prepare time series data
        try:
            ts_df = self._prepare_time_series(target_column, date_column, frequency)

            # Generate forecast using the selected method
            if method == "simple":
                forecast_result = self._forecast_simple(ts_df, target_column, horizon, confidence_interval)
            elif method == "arima":
                try:
                    forecast_result = self._forecast_arima(ts_df, target_column, horizon, confidence_interval)
                except Exception as e:
                    logger.warning(f"Error in ARIMA forecast: {str(e)}. Falling back to simple method.")
                    forecast_result = self._forecast_simple(ts_df, target_column, horizon, confidence_interval)
                    method = "simple"  # Update method to reflect what was actually used
            elif method == "exponential_smoothing":
                try:
                    forecast_result = self._forecast_exponential_smoothing(ts_df, target_column, horizon, confidence_interval)
                except Exception as e:
                    logger.warning(f"Error in Exponential Smoothing forecast: {str(e)}. Falling back to simple method.")
                    forecast_result = self._forecast_simple(ts_df, target_column, horizon, confidence_interval)
                    method = "simple"  # Update method to reflect what was actually used
            elif method == "prophet":
                try:
                    from prophet import Prophet
                    forecast_result = self._forecast_prophet(ts_df, target_column, horizon, frequency, confidence_interval)
                except Exception as e:
                    logger.warning(f"Error in Prophet forecast: {str(e)}. Falling back to simple method.")
                    forecast_result = self._forecast_simple(ts_df, target_column, horizon, confidence_interval)
                    method = "simple"  # Update method to reflect what was actually used
            else:
                # This should never happen due to validation above, but just in case
                forecast_result = self._forecast_simple(ts_df, target_column, horizon, confidence_interval)
                method = "simple"

            # Add metadata to the forecast result
            forecast_result["target_column"] = target_column
            forecast_result["date_column"] = date_column
            forecast_result["horizon"] = horizon
            forecast_result["frequency"] = frequency
            forecast_result["method"] = method
            forecast_result["confidence_interval"] = confidence_interval

            return forecast_result
        except Exception as e:
            logger.error(f"Error in forecast: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            raise ValueError(f"Error in forecast: {str(e)}")

    def _forecast_simple(
        self,
        ts_df: pd.DataFrame,
        target_column: str,
        horizon: int,
        confidence_interval: float = 0.95
    ) -> Dict[str, Any]:
        """
        Generate a simple forecast using moving average and linear trend

        Args:
            ts_df: Time series DataFrame
            target_column: Column containing the values to forecast
            horizon: Number of periods to forecast
            confidence_interval: Confidence interval for prediction bounds

        Returns:
            Dictionary with forecast results
        """
        try:
            # Extract the time series
            series = ts_df[target_column]

            # Calculate moving average for smoothing
            window = min(len(series), 5)  # Use up to 5 periods for moving average
            if window < 2:
                window = 2  # Minimum window size

            # Calculate moving average
            ma = series.rolling(window=window, min_periods=1).mean()

            # Calculate trend component
            x = np.arange(len(series))
            y = series.values

            # Convert to float arrays to ensure compatible types
            x = x.astype(float)
            y = y.astype(float)

            # Handle NaN values
            mask = ~np.isnan(y)
            if np.sum(mask) < 2:
                # Not enough valid data points for regression
                slope = 0
                intercept = np.nanmean(y) if len(y) > 0 else 0
            else:
                # Perform linear regression
                try:
                    from scipy import stats
                    slope, intercept, _, _, _ = stats.linregress(x[mask], y[mask])
                except:
                    # Fallback if scipy is not available or regression fails
                    slope = 0
                    intercept = np.nanmean(y) if len(y) > 0 else 0

            # Calculate historical trend values
            historical_trend = intercept + slope * x

            # Generate forecast trend
            forecast_x = np.arange(len(series), len(series) + horizon)
            trend_forecast = intercept + slope * forecast_x

            # Extract seasonality if enough data, but limit to smaller datasets
            seasonality_values = None
            if len(series) >= 24 and len(series) <= 1000:  # Need enough data but not too much
                try:
                    from statsmodels.tsa.seasonal import seasonal_decompose

                    # Use a fixed period based on data length to avoid trying multiple periods
                    if len(series) >= 365:
                        # For long series, try weekly seasonality
                        period = 7
                    elif len(series) >= 60:
                        # For medium series, try monthly seasonality
                        period = 30
                    else:
                        # For shorter series, try weekly seasonality
                        period = 7

                    # Only proceed if we have enough data for the chosen period
                    if len(series) >= 2 * period:
                        try:
                            # Use fft=False for faster computation
                            result = seasonal_decompose(series, model='additive', period=period, fft=False)

                            # Check if seasonality is significant
                            strength = np.abs(result.seasonal).max() / (np.abs(result.trend).std() + 1e-10)

                            if strength > 0.1:  # Lower threshold for significance
                                # Extract seasonality pattern
                                seasonal_pattern = result.seasonal[-period:].values

                                # Repeat the pattern for the forecast horizon
                                seasonality_forecast = np.tile(seasonal_pattern, int(np.ceil(horizon / len(seasonal_pattern))))[:horizon]

                                # Combine all seasonality values for visualization
                                seasonality_values = np.concatenate([result.seasonal.values, seasonality_forecast])

                                # Add seasonality to forecast
                                forecast_values = trend_forecast + seasonality_forecast
                            else:
                                # No significant seasonality found
                                forecast_values = trend_forecast
                        except Exception as e:
                            logger.warning(f"Error in seasonal decomposition with period {period}: {str(e)}")
                            forecast_values = trend_forecast
                    else:
                        forecast_values = trend_forecast
                except Exception as e:
                    logger.warning(f"Error extracting seasonality: {str(e)}")
                    forecast_values = trend_forecast
            else:
                # For very large datasets or very small ones, skip seasonality detection
                # and use simpler methods
                if len(series) >= 12:
                    # Use last value as level adjustment
                    level = ma.iloc[-1] if not np.isnan(ma.iloc[-1]) else series.iloc[-1]
                    forecast_values = trend_forecast + (level - (intercept + slope * (len(series) - 1)))
                else:
                    # Simple trend forecast for very short series
                    forecast_values = trend_forecast

            # Ensure non-negative values if the original series is non-negative
            if np.all(series[~np.isnan(series)] >= 0):
                forecast_values = np.maximum(forecast_values, 0)

            # Generate prediction intervals
            std = series.std()
            z_value = 1.96  # For 95% confidence interval
            if confidence_interval != 0.95:
                try:
                    from scipy import stats
                    z_value = stats.norm.ppf(1 - (1 - confidence_interval) / 2)
                except:
                    z_value = 1.96  # Default to 95% if scipy is not available

            margin = z_value * std
            lower_bound = forecast_values - margin
            upper_bound = forecast_values + margin

            # Ensure non-negative lower bounds if the original series is non-negative
            if np.all(series[~np.isnan(series)] >= 0):
                lower_bound = np.maximum(lower_bound, 0)

            # Generate future dates
            future_dates = self._generate_future_dates(ts_df.index, horizon)

            # Prepare historical data for plotting
            historical_dates = ts_df.index.strftime('%Y-%m-%d').tolist()
            historical_values = series.tolist()

            # Prepare forecast data for plotting
            forecast_dates = future_dates.strftime('%Y-%m-%d').tolist()

            # Convert numpy arrays to lists
            forecast_values_list = forecast_values.tolist()
            lower_bound_list = lower_bound.tolist()
            upper_bound_list = upper_bound.tolist()

            # Calculate simple accuracy metrics
            accuracy_metrics = self._calculate_simple_accuracy(series)

            # Prepare result dictionary
            result = {
                "method": "simple",
                "historical_dates": historical_dates,
                "historical_values": historical_values,
                "forecast_dates": forecast_dates,
                "forecast_values": forecast_values_list,
                "lower_bound": lower_bound_list,
                "upper_bound": upper_bound_list,
                "accuracy_metrics": accuracy_metrics
            }

            # Add trend components if available
            if 'historical_trend' in locals() and historical_trend is not None:
                result["historical_trend_values"] = historical_trend.tolist()

            if 'trend_forecast' in locals() and trend_forecast is not None:
                result["trend_values"] = trend_forecast.tolist()

            # Add seasonality component if available
            if 'seasonality_values' in locals() and seasonality_values is not None:
                result["seasonality_values"] = seasonality_values.tolist()

            return result

        except Exception as e:
            logger.error(f"Error in simple forecast: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            raise ValueError(f"Error in simple forecast: {str(e)}")

    def _calculate_simple_accuracy(self, series: pd.Series) -> Dict[str, float]:
        """
        Calculate simple accuracy metrics

        Args:
            series: Time series data

        Returns:
            Dictionary with accuracy metrics
        """
        try:
            # Use a simple approach - compare last value to mean
            if len(series) < 3:
                return {
                    "mae": None,
                    "rmse": None,
                    "mape": None
                }

            # Calculate mean absolute error between each value and the previous value
            diffs = np.abs(series.diff().dropna())
            mae = float(diffs.mean())

            # Calculate RMSE
            rmse = float(np.sqrt((diffs ** 2).mean()))

            # Calculate MAPE
            try:
                # Avoid division by zero
                valid_indices = (series.shift() != 0) & ~series.shift().isna()
                if valid_indices.sum() > 0:
                    mape = float((np.abs(series.diff() / series.shift())[valid_indices]).mean() * 100)
                else:
                    mape = None
            except:
                mape = None

            return {
                "mae": mae,
                "rmse": rmse,
                "mape": mape
            }
        except Exception as e:
            logger.warning(f"Error calculating accuracy metrics: {str(e)}")
            return {
                "mae": None,
                "rmse": None,
                "mape": None
            }

    def _prepare_time_series(
        self,
        target_column: str,
        date_column: str,
        frequency: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Prepare time series data for forecasting

        Args:
            target_column: Column containing the values to forecast
            date_column: Column containing the dates
            frequency: Time series frequency (e.g., 'D', 'M', 'Y')

        Returns:
            DataFrame with prepared time series
        """
        # Ensure date column is datetime type
        try:
            date_series = pd.to_datetime(self.df[date_column])
        except:
            raise ValueError(f"Column '{date_column}' cannot be converted to datetime")

        # Create time series
        ts_df = self.df[[date_column, target_column]].copy()
        ts_df['date'] = date_series
        ts_df = ts_df.sort_values('date')

        # Drop rows with missing values
        ts_df = ts_df.dropna(subset=[target_column])

        if len(ts_df) < 4:
            raise ValueError("Not enough data points for forecasting (minimum 4 required)")

        # Set date as index
        ts_df = ts_df.set_index('date')

        # Resample if frequency is provided
        if frequency is not None:
            ts_df = ts_df.resample(frequency).mean()

        # Fill missing values after resampling
        ts_df = ts_df.fillna(method='ffill')

        return ts_df

    def _select_best_method(self, df: pd.DataFrame, target_column: str) -> str:
        """
        Select the best forecasting method based on data characteristics

        Args:
            df: DataFrame with the data
            target_column: Column containing the values to forecast

        Returns:
            Selected forecasting method
        """
        try:
            # Make sure target column is numeric
            if not pd.api.types.is_numeric_dtype(df[target_column]):
                df[target_column] = pd.to_numeric(df[target_column], errors='coerce')

            # Drop missing values
            series = df[target_column].dropna()

            # Check if we have enough data
            n_samples = len(series)
            if n_samples < 10:
                logger.info("Small dataset detected (< 10 samples). Using simple forecasting method.")
                return "simple"

            # Check for stationarity
            try:
                from statsmodels.tsa.stattools import adfuller
                adf_result = adfuller(series)
                is_stationary = adf_result[1] < 0.05  # p-value < 0.05 indicates stationarity
            except Exception as e:
                logger.warning(f"Error checking stationarity: {str(e)}")
                is_stationary = False

            # Check for seasonality
            has_seasonality = False
            seasonal_strength = 0
            try:
                from statsmodels.tsa.seasonal import seasonal_decompose
                # Try to detect seasonality
                if n_samples >= 24:  # Need enough data for seasonal decomposition
                    # Try different seasonal periods
                    for period in [7, 12, 24, 52]:
                        if n_samples >= 2 * period:  # Need at least 2 full cycles
                            try:
                                result = seasonal_decompose(series, model='additive', period=period)
                                strength = np.abs(result.seasonal).max() / np.abs(result.trend).std()
                                if strength > seasonal_strength:
                                    seasonal_strength = strength
                            except Exception as e:
                                logger.warning(f"Error in seasonal decomposition with period {period}: {str(e)}")

                has_seasonality = seasonal_strength > 0.3
            except Exception as e:
                logger.warning(f"Error checking seasonality: {str(e)}")
                has_seasonality = False

            # Check for trend
            has_trend = False
            try:
                from scipy import stats
                x = np.arange(len(series))
                y = series.values

                # Convert to float arrays to ensure compatible types
                x = x.astype(float)
                y = y.astype(float)

                slope, _, r_value, p_value, _ = stats.linregress(x, y)
                has_trend = p_value < 0.05 and abs(r_value) > 0.5
            except Exception as e:
                logger.warning(f"Error checking trend: {str(e)}")
                has_trend = False

            # Check for outliers
            has_outliers = False
            try:
                # Use IQR method to detect outliers
                q1 = series.quantile(0.25)
                q3 = series.quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                outliers = series[(series < lower_bound) | (series > upper_bound)]
                has_outliers = len(outliers) > 0.05 * len(series)  # More than 5% outliers
            except Exception as e:
                logger.warning(f"Error checking outliers: {str(e)}")
                has_outliers = False

            # Log data characteristics
            logger.info(f"Data characteristics: samples={n_samples}, stationary={is_stationary}, "
                       f"seasonal={has_seasonality} (strength={seasonal_strength:.2f}), "
                       f"trend={has_trend}, outliers={has_outliers}")

            # Select method based on data characteristics
            if has_outliers and n_samples < 50:
                logger.info("Selected simple method due to outliers in small dataset")
                return "simple"  # Simple method is more robust to outliers in small datasets
            elif has_seasonality and n_samples >= 50:
                try:
                    # Check if Prophet is available
                    import prophet
                    logger.info("Selected Prophet method due to seasonality")
                    return "prophet"  # Prophet handles seasonality well
                except ImportError:
                    logger.info("Prophet not available, falling back to exponential smoothing")
                    return "exponential_smoothing"
            elif has_trend and not has_seasonality and n_samples >= 30:
                logger.info("Selected ARIMA method due to trend without seasonality")
                return "arima"  # ARIMA handles trends well
            elif has_seasonality:
                logger.info("Selected exponential smoothing due to seasonality")
                return "exponential_smoothing"  # Exponential smoothing handles seasonality
            elif n_samples >= 100:
                logger.info("Selected ARIMA method due to large dataset")
                return "arima"  # ARIMA works well with large datasets
            else:
                logger.info("Selected simple method as default")
                return "simple"  # Default to simple model for reliability
        except Exception as e:
            logger.error(f"Error in method selection: {str(e)}")
            logger.info("Falling back to simple method due to error")
            return "simple"  # Fallback to simple method in case of errors

    def _forecast_arima(
        self,
        ts_df: pd.DataFrame,
        target_column: str,
        horizon: int,
        confidence_interval: float = 0.95
    ) -> Dict[str, Any]:
        """
        Generate forecast using ARIMA model

        Args:
            ts_df: Time series DataFrame
            target_column: Column containing the values to forecast
            horizon: Number of periods to forecast
            confidence_interval: Confidence interval for prediction bounds

        Returns:
            Dictionary with forecast results
        """
        try:
            # Automatically find the best ARIMA parameters
            model = auto_arima(
                ts_df[target_column],
                seasonal=True,
                m=12,  # Default seasonal period
                suppress_warnings=True,
                error_action='ignore',
                max_p=5,
                max_d=2,
                max_q=5,
                max_P=2,
                max_D=1,
                max_Q=2,
                stepwise=True
            )

            # Get the best parameters
            order = model.order
            seasonal_order = model.seasonal_order

            # Fit ARIMA model
            arima_model = ARIMA(
                ts_df[target_column],
                order=order,
                seasonal_order=seasonal_order
            )
            model_fit = arima_model.fit()

            # Generate forecast
            forecast = model_fit.forecast(steps=horizon)
            forecast_index = self._generate_future_dates(ts_df.index, horizon)

            # Get confidence intervals
            alpha = 1 - confidence_interval
            pred_conf = model_fit.get_forecast(steps=horizon).conf_int(alpha=alpha)
            lower_bound = pred_conf.iloc[:, 0]
            upper_bound = pred_conf.iloc[:, 1]

            # Prepare historical data for plotting
            historical_dates = ts_df.index.strftime('%Y-%m-%d').tolist()
            historical_values = ts_df[target_column].tolist()

            # Prepare forecast data for plotting
            forecast_dates = forecast_index.strftime('%Y-%m-%d').tolist()
            forecast_values = forecast.tolist()
            lower_bound_values = lower_bound.tolist()
            upper_bound_values = upper_bound.tolist()

            # Calculate forecast accuracy metrics using cross-validation
            accuracy_metrics = self._calculate_forecast_accuracy(ts_df, target_column, order, seasonal_order)

            return {
                "method": "ARIMA",
                "model_params": {
                    "order": order,
                    "seasonal_order": seasonal_order
                },
                "historical_dates": historical_dates,
                "historical_values": historical_values,
                "forecast_dates": forecast_dates,
                "forecast_values": forecast_values,
                "lower_bound": lower_bound_values,
                "upper_bound": upper_bound_values,
                "accuracy_metrics": accuracy_metrics
            }

        except Exception as e:
            logger.error(f"Error in ARIMA forecasting: {str(e)}")
            raise ValueError(f"Error in ARIMA forecasting: {str(e)}")

    def _forecast_exponential_smoothing(
        self,
        ts_df: pd.DataFrame,
        target_column: str,
        horizon: int,
        confidence_interval: float = 0.95
    ) -> Dict[str, Any]:
        """
        Generate forecast using Exponential Smoothing

        Args:
            ts_df: Time series DataFrame
            target_column: Column containing the values to forecast
            horizon: Number of periods to forecast
            confidence_interval: Confidence interval for prediction bounds

        Returns:
            Dictionary with forecast results
        """
        try:
            # Determine seasonality
            seasonal_periods = 12  # Default to monthly seasonality

            # Fit Exponential Smoothing model
            model = ExponentialSmoothing(
                ts_df[target_column],
                seasonal='add',
                seasonal_periods=seasonal_periods
            )
            model_fit = model.fit()

            # Generate forecast
            forecast = model_fit.forecast(steps=horizon)
            forecast_index = self._generate_future_dates(ts_df.index, horizon)

            # Prepare historical data for plotting
            historical_dates = ts_df.index.strftime('%Y-%m-%d').tolist()
            historical_values = ts_df[target_column].tolist()

            # Prepare forecast data for plotting
            forecast_dates = forecast_index.strftime('%Y-%m-%d').tolist()
            forecast_values = forecast.tolist()

            # Calculate forecast accuracy metrics using cross-validation
            accuracy_metrics = self._calculate_forecast_accuracy_es(ts_df, target_column, seasonal_periods)

            # Simulate confidence intervals (ExponentialSmoothing doesn't provide them directly)
            std_residuals = np.std(model_fit.resid)
            z_value = 1.96  # Approximately 95% confidence interval
            margin = z_value * std_residuals

            lower_bound = [max(0, val - margin) for val in forecast_values]  # Ensure non-negative values
            upper_bound = [val + margin for val in forecast_values]

            return {
                "method": "Exponential Smoothing",
                "model_params": {
                    "seasonal_periods": seasonal_periods
                },
                "historical_dates": historical_dates,
                "historical_values": historical_values,
                "forecast_dates": forecast_dates,
                "forecast_values": forecast_values,
                "lower_bound": lower_bound,
                "upper_bound": upper_bound,
                "accuracy_metrics": accuracy_metrics
            }

        except Exception as e:
            logger.error(f"Error in Exponential Smoothing forecasting: {str(e)}")
            raise ValueError(f"Error in Exponential Smoothing forecasting: {str(e)}")

    def _forecast_prophet(
        self,
        ts_df: pd.DataFrame,
        target_column: str,
        horizon: int,
        frequency: Optional[str] = None,
        confidence_interval: float = 0.95
    ) -> Dict[str, Any]:
        """
        Generate forecast using Facebook Prophet

        Args:
            ts_df: Time series DataFrame
            target_column: Column containing the values to forecast
            horizon: Number of periods to forecast
            frequency: Time series frequency
            confidence_interval: Confidence interval for prediction bounds

        Returns:
            Dictionary with forecast results
        """
        try:
            from prophet import Prophet

            # Prepare data for Prophet
            prophet_df = pd.DataFrame({
                'ds': ts_df.index,
                'y': ts_df[target_column]
            })

            # Fit Prophet model
            model = Prophet(
                yearly_seasonality=True,
                weekly_seasonality=True,
                daily_seasonality=False,
                interval_width=confidence_interval
            )
            model.fit(prophet_df)

            # Create future dataframe
            if frequency:
                future = model.make_future_dataframe(
                    periods=horizon,
                    freq=frequency
                )
            else:
                future = model.make_future_dataframe(
                    periods=horizon
                )

            # Generate forecast
            forecast = model.predict(future)

            # Prepare historical data for plotting
            historical_dates = prophet_df['ds'].dt.strftime('%Y-%m-%d').tolist()
            historical_values = prophet_df['y'].tolist()

            # Prepare forecast data for plotting
            forecast_dates = forecast['ds'].dt.strftime('%Y-%m-%d').tolist()[-horizon:]
            forecast_values = forecast['yhat'].tolist()[-horizon:]
            lower_bound = forecast['yhat_lower'].tolist()[-horizon:]
            upper_bound = forecast['yhat_upper'].tolist()[-horizon:]

            # Extract components
            components = {
                "trend": forecast['trend'].tolist()[-horizon:],
                "yearly": forecast['yearly'].tolist()[-horizon:] if 'yearly' in forecast.columns else None,
                "weekly": forecast['weekly'].tolist()[-horizon:] if 'weekly' in forecast.columns else None
            }

            # Calculate forecast accuracy metrics
            accuracy_metrics = self._calculate_forecast_accuracy_prophet(prophet_df, model)

            return {
                "method": "Prophet",
                "historical_dates": historical_dates,
                "historical_values": historical_values,
                "forecast_dates": forecast_dates,
                "forecast_values": forecast_values,
                "lower_bound": lower_bound,
                "upper_bound": upper_bound,
                "components": components,
                "accuracy_metrics": accuracy_metrics
            }

        except Exception as e:
            logger.error(f"Error in Prophet forecasting: {str(e)}")
            raise ValueError(f"Error in Prophet forecasting: {str(e)}")

    def _generate_future_dates(
        self,
        last_date_index: pd.DatetimeIndex,
        periods: int
    ) -> pd.DatetimeIndex:
        """
        Generate future dates for forecasting

        Args:
            last_date_index: DatetimeIndex of the last date in the time series
            periods: Number of periods to generate

        Returns:
            DatetimeIndex of future dates
        """
        last_date = last_date_index[-1]

        # Try to infer frequency
        frequency = pd.infer_freq(last_date_index)

        if frequency is None:
            # Default to daily if frequency cannot be inferred
            frequency = 'D'

        # Generate future dates
        future_dates = pd.date_range(
            start=last_date + pd.Timedelta(days=1),
            periods=periods,
            freq=frequency
        )

        return future_dates

    def _calculate_forecast_accuracy(
        self,
        ts_df: pd.DataFrame,
        target_column: str,
        order: Tuple[int, int, int],
        seasonal_order: Tuple[int, int, int, int]
    ) -> Dict[str, float]:
        """
        Calculate forecast accuracy metrics for ARIMA using cross-validation

        Args:
            ts_df: Time series DataFrame
            target_column: Column containing the values to forecast
            order: ARIMA order parameters (p, d, q)
            seasonal_order: Seasonal ARIMA parameters (P, D, Q, s)

        Returns:
            Dictionary with accuracy metrics
        """
        # Use a simple train-test split for efficiency
        train_size = int(len(ts_df) * 0.8)
        train = ts_df.iloc[:train_size]
        test = ts_df.iloc[train_size:]

        if len(test) == 0:
            return {
                "mae": None,
                "rmse": None,
                "mape": None
            }

        # Fit model on training data
        model = ARIMA(
            train[target_column],
            order=order,
            seasonal_order=seasonal_order
        )
        model_fit = model.fit()

        # Forecast for test period
        forecast = model_fit.forecast(steps=len(test))

        # Calculate metrics
        mae = mean_absolute_error(test[target_column], forecast)
        rmse = np.sqrt(mean_squared_error(test[target_column], forecast))

        # Calculate MAPE (Mean Absolute Percentage Error)
        try:
            actual = test[target_column].values
            pred = forecast.values

            # Convert to float arrays to ensure compatible types
            actual = actual.astype(float)
            pred = pred.astype(float)

            # Avoid division by zero
            mask = actual != 0
            if np.any(mask):
                mape = np.mean(np.abs((actual[mask] - pred[mask]) / actual[mask])) * 100
            else:
                mape = None
        except Exception as e:
            logger.warning(f"Error calculating MAPE: {str(e)}")
            mape = None

        return {
            "mae": float(mae),
            "rmse": float(rmse),
            "mape": float(mape) if mape is not None else None
        }

    def _calculate_forecast_accuracy_es(
        self,
        ts_df: pd.DataFrame,
        target_column: str,
        seasonal_periods: int
    ) -> Dict[str, float]:
        """
        Calculate forecast accuracy metrics for Exponential Smoothing using cross-validation

        Args:
            ts_df: Time series DataFrame
            target_column: Column containing the values to forecast
            seasonal_periods: Number of periods in a seasonal cycle

        Returns:
            Dictionary with accuracy metrics
        """
        # Use a simple train-test split for efficiency
        train_size = int(len(ts_df) * 0.8)
        train = ts_df.iloc[:train_size]
        test = ts_df.iloc[train_size:]

        if len(test) == 0:
            return {
                "mae": None,
                "rmse": None,
                "mape": None
            }

        # Fit model on training data
        model = ExponentialSmoothing(
            train[target_column],
            seasonal='add',
            seasonal_periods=seasonal_periods
        )
        model_fit = model.fit()

        # Forecast for test period
        forecast = model_fit.forecast(steps=len(test))

        # Calculate metrics
        mae = mean_absolute_error(test[target_column], forecast)
        rmse = np.sqrt(mean_squared_error(test[target_column], forecast))

        # Calculate MAPE (Mean Absolute Percentage Error)
        try:
            actual = test[target_column].values
            pred = forecast.values

            # Convert to float arrays to ensure compatible types
            actual = actual.astype(float)
            pred = pred.astype(float)

            # Avoid division by zero
            mask = actual != 0
            if np.any(mask):
                mape = np.mean(np.abs((actual[mask] - pred[mask]) / actual[mask])) * 100
            else:
                mape = None
        except Exception as e:
            logger.warning(f"Error calculating MAPE: {str(e)}")
            mape = None

        return {
            "mae": float(mae),
            "rmse": float(rmse),
            "mape": float(mape) if mape is not None else None
        }

    def _calculate_forecast_accuracy_prophet(
        self,
        prophet_df: pd.DataFrame,
        model: Any
    ) -> Dict[str, float]:
        """
        Calculate forecast accuracy metrics for Prophet using cross-validation

        Args:
            prophet_df: DataFrame with ds and y columns
            model: Fitted Prophet model

        Returns:
            Dictionary with accuracy metrics
        """
        try:
            from prophet.diagnostics import cross_validation, performance_metrics

            # Perform cross-validation
            df_cv = cross_validation(
                model=model,
                initial='80%',
                period='10D',
                horizon='30D'
            )

            # Calculate performance metrics
            df_p = performance_metrics(df_cv)

            # Handle potential NaN values
            mae = df_p['mae'].mean()
            rmse = df_p['rmse'].mean()
            mape = df_p['mape'].mean() * 100 if 'mape' in df_p.columns else None

            return {
                "mae": float(mae) if not pd.isna(mae) else None,
                "rmse": float(rmse) if not pd.isna(rmse) else None,
                "mape": float(mape) if mape is not None and not pd.isna(mape) else None
            }
        except Exception as e:
            logger.warning(f"Error in Prophet cross-validation: {str(e)}")

            # Fallback to simple train-test split
            train_size = int(len(prophet_df) * 0.8)
            train = prophet_df.iloc[:train_size]
            test = prophet_df.iloc[train_size:]

            if len(test) == 0:
                return {
                    "mae": None,
                    "rmse": None,
                    "mape": None
                }

            # Fit model on training data
            model = Prophet(
                yearly_seasonality=True,
                weekly_seasonality=True,
                daily_seasonality=False
            )
            model.fit(train)

            # Create future dataframe for test period
            future = model.make_future_dataframe(periods=len(test))

            # Generate forecast
            forecast = model.predict(future)

            # Extract predictions for test period
            predictions = forecast.iloc[-len(test):]['yhat'].values

            # Calculate metrics
            mae = mean_absolute_error(test['y'], predictions)
            rmse = np.sqrt(mean_squared_error(test['y'], predictions))

            # Calculate MAPE (Mean Absolute Percentage Error)
            try:
                actual = test['y'].values
                pred = predictions

                # Convert to float arrays to ensure compatible types
                actual = actual.astype(float)
                pred = pred.astype(float)

                # Avoid division by zero
                mask = actual != 0
                if np.any(mask):
                    mape = np.mean(np.abs((actual[mask] - pred[mask]) / actual[mask])) * 100
                else:
                    mape = None
            except Exception as e:
                logger.warning(f"Error calculating MAPE: {str(e)}")
                mape = None

            return {
                "mae": float(mae),
                "rmse": float(rmse),
                "mape": float(mape) if mape is not None else None
            }
