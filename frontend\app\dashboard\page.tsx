'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Navbar from '@/components/ui/navbar';
import Footer from '@/components/ui/footer';
import DashboardNav from '@/components/dashboard/dashboard-nav';
import FileUploader from '@/components/dashboard/file-uploader';
import DatasetList from '@/components/dashboard/dataset-list';
import SavedQueries from '@/components/dashboard/saved-queries';
import RAGChatInterface from '@/components/chat/rag-chat-interface';
import ConversationHistory from '@/components/chat/conversation-history';
import { createApiInstance } from '@/lib/api';

interface Dataset {
  id: number;
  name: string;
  columns: string[];
  row_count: number;
  created_at: string;
  file_type?: string;
  content_type?: string;
  processing_status?: string;
  word_count?: number;
  character_count?: number;
}

interface Conversation {
  id: string;
  title: string;
  datasetId: number;
  datasetName: string;
  lastMessage: string;
  messageCount: number;
  createdAt: Date;
  updatedAt: Date;
  isImportant: boolean;
  isTemporary?: boolean;
  savedQuery?: any;
}

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [availableDatasets, setAvailableDatasets] = useState<Dataset[]>([]);
  const [showConversationHistory, setShowConversationHistory] = useState(false); // Collapsed by default
  const [showSavedQueries, setShowSavedQueries] = useState(false);
  const [conversationRefreshTrigger, setConversationRefreshTrigger] = useState(0);

  // Use real user ID from session - no demo fallback
  const userId = (session?.user as any)?.id;

  // Redirect to sign-in if not authenticated
  useEffect(() => {
    if (status === 'loading') return; // Still loading
    if (!session) {
      router.push('/auth/signin');
    }
  }, [session, status, router]);

  // Load available datasets
  useEffect(() => {
    loadDatasets();
  }, [refreshTrigger]);

  // Auto-select first dataset when datasets are loaded
  useEffect(() => {
    if (availableDatasets.length > 0 && !selectedDataset) {
      const firstDataset = availableDatasets[0];
      setSelectedDataset(firstDataset);
      // Also trigger the dataset list to select it
      handleSelectDataset(firstDataset.id);
    }
  }, [availableDatasets]);

  const loadDatasets = async () => {
    try {
      // Check if API key exists in session
      const userWithApiKey = session?.user as any;
      if (!userWithApiKey?.apiKey) {
        console.log('API key missing from session. Please sign out and sign back in to refresh your session.');

        // Show a user-friendly message instead of trying to sync
        setAvailableDatasets([]);
        return;
      }

      // Use session-aware API instance
      const sessionApi = createApiInstance(session);
      const response = await sessionApi.get('/api/v1/datasets');
      if (response && response.data) {
        setAvailableDatasets(response.data);
        console.log(`Loaded ${response.data.length} datasets for user`);
      }
    } catch (error: any) {
      console.error('Error loading datasets:', error);
      // If API key error, show helpful message
      if (error.response?.status === 401) {
        console.log('Authentication failed. Please sign out and sign back in.');
      }
    }
  };

  // Show loading state while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!session) {
    return null;
  }

  const handleUploadSuccess = (datasetId: number) => {
    setRefreshTrigger((prev: number) => prev + 1);
    const newDataset = availableDatasets.find(d => d.id === datasetId);
    if (newDataset) {
      setSelectedDataset(newDataset);
    }
  };

  const handleSelectDataset = (datasetId: number) => {
    const dataset = availableDatasets.find(d => d.id === datasetId);
    setSelectedDataset(dataset || null);
  };

  const handleDatasetChange = (dataset: Dataset | null) => {
    setSelectedDataset(dataset);
  };

  const handleSelectConversation = (conversation: Conversation) => {
    // Find and select the dataset for this conversation
    const dataset = availableDatasets.find(d => d.id === conversation.datasetId);
    if (dataset) {
      setSelectedDataset(dataset);
    }
    // Don't close the conversation history when selecting a conversation
    // setShowConversationHistory(false);
  };



  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow py-8">
        <DashboardNav />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Left Panel - Upload & Datasets */}
            <div className="lg:col-span-1 space-y-4">
              <div className="bg-white rounded-lg shadow-md p-4">
                <FileUploader onUploadSuccess={handleUploadSuccess} />
              </div>

              <div className="bg-white rounded-lg shadow-md p-4">
                <DatasetList
                  onSelectDataset={handleSelectDataset}
                  onRefreshNeeded={() => setRefreshTrigger((prev: number) => prev + 1)}
                  refreshTrigger={refreshTrigger}
                  selectedDatasetId={selectedDataset?.id || null}
                />
              </div>
            </div>

            {/* Center Panel - Chat Interface */}
            <div className="lg:col-span-3 space-y-4">
              {/* Chat Interface - Always show, handle empty state inside */}
              <div className="bg-white rounded-lg shadow-md" style={{ height: '600px' }}>
                <RAGChatInterface
                  selectedDataset={selectedDataset}
                  availableDatasets={availableDatasets}
                  onDatasetChange={handleDatasetChange}
                />
              </div>

              {/* Bottom Panel - History and Saved Queries */}
              <div className="bg-white rounded-lg shadow-md p-4">
                <div className="flex space-x-2 mb-4">
                  <button
                    onClick={() => {
                      setShowConversationHistory(!showConversationHistory);
                      setShowSavedQueries(false);
                    }}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium ${
                      showConversationHistory
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    History
                  </button>
                  <button
                    onClick={() => {
                      setShowSavedQueries(!showSavedQueries);
                      setShowConversationHistory(false);
                    }}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium ${
                      showSavedQueries
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Saved Queries
                  </button>
                </div>

                {showConversationHistory && (
                  <ConversationHistory
                    onSelectConversation={handleSelectConversation}
                    currentConversationId=""
                    refreshTrigger={conversationRefreshTrigger}
                  />
                )}

                {showSavedQueries && (
                  <SavedQueries
                    onSelectQuery={(query) => {
                      console.log('Selected saved query:', query);
                      // TODO: Handle query selection
                    }}
                    currentDatasetId={selectedDataset?.id || null}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
