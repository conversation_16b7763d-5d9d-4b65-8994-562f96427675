'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { createApiInstance } from '@/lib/api';
import Navbar from '@/components/ui/navbar';
import Footer from '@/components/ui/footer';
import DashboardNav from '@/components/dashboard/dashboard-nav';
import StatisticalAnalysis from '@/components/analytics/statistical-analysis';
import TimeSeriesAnalysis from '@/components/analytics/time-series-analysis';
import BusinessTemplates from '@/components/analytics/business-templates';

interface Dataset {
  id: number;
  name: string;
  columns: string[];
  row_count: number;
  created_at: string;
}

export default function AnalyticsPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDatasetId, setSelectedDatasetId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<string>('statistical');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user's datasets
  useEffect(() => {
    const fetchDatasets = async () => {
      try {
        if (!session) {
          setLoading(false);
          return;
        }

        // Use session-aware API instance
        const sessionApi = createApiInstance(session);
        const response = await sessionApi.get('/api/v1/datasets');

        if (response && response.data) {
          setDatasets(response.data);

          // Select the first dataset by default if available
          if (response.data.length > 0) {
            setSelectedDatasetId(response.data[0].id);
          }
        }

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching datasets:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching datasets');
        setLoading(false);
      }
    };

    if (status !== 'loading') {
      fetchDatasets();
    }
  }, [session, status]);

  useEffect(() => {
    // Redirect to sign-in if not authenticated
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
    }
  }, [session, status, router]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow py-8">
        <DashboardNav />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-black mb-2">Advanced Analytics & Time Series Forecasting</h1>
          <p className="text-md text-black mb-8">Comprehensive statistical analysis, time series forecasting, and business intelligence templates for data-driven insights</p>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        ) : datasets.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <h2 className="text-xl font-semibold text-black mb-2">No Datasets Found</h2>
            <p className="text-gray-600 mb-4">Upload a dataset to get started with advanced analytics.</p>
            <button
              onClick={() => router.push('/dashboard')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Go to Dashboard
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-8">
            {/* Dataset Selection */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-black mb-4">Select Dataset</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {datasets.map(dataset => (
                  <div
                    key={dataset.id}
                    onClick={() => setSelectedDatasetId(dataset.id)}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedDatasetId === dataset.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    <h3 className="font-medium text-black">{dataset.name}</h3>
                    <p className="text-sm text-gray-500">{dataset.row_count} rows</p>
                    <p className="text-sm text-gray-500">{dataset.columns.length} columns</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Analytics Tabs */}
            {selectedDatasetId && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="border-b border-gray-200 mb-6">
                  <nav className="-mb-px flex flex-wrap gap-8">
                    <button
                      onClick={() => setActiveTab('statistical')}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'statistical'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      📊 Statistical Analysis
                    </button>
                    <button
                      onClick={() => setActiveTab('timeseries')}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'timeseries'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      📈 Time Series Analysis
                    </button>
                    <button
                      onClick={() => setActiveTab('templates')}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'templates'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      📂 Business Templates
                    </button>
                  </nav>
                </div>

                {/* Analytics Content */}
                <div>
                  {activeTab === 'statistical' && (
                    <StatisticalAnalysis datasetId={selectedDatasetId} />
                  )}

                  {activeTab === 'timeseries' && (
                    <TimeSeriesAnalysis datasetId={selectedDatasetId} />
                  )}

                  {activeTab === 'templates' && (
                    <BusinessTemplates datasetId={selectedDatasetId} />
                  )}
                </div>
              </div>
            )}
          </div>
        )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
