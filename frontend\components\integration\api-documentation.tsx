'use client';

import { useState } from 'react';

interface EndpointProps {
  method: string;
  path: string;
  description: string;
  requestBody?: string;
  responseBody: string;
}

const Endpoint = ({ method, path, description, requestBody, responseBody }: EndpointProps) => {
  const [showRequest, setShowRequest] = useState(false);
  const [showResponse, setShowResponse] = useState(false);
  
  const methodColors: Record<string, string> = {
    GET: 'bg-blue-500',
    POST: 'bg-green-500',
    PUT: 'bg-yellow-500',
    DELETE: 'bg-red-500'
  };
  
  return (
    <div className="border border-gray-200 rounded-md mb-6">
      <div className="flex items-center p-4 border-b border-gray-200">
        <span className={`${methodColors[method]} text-white px-3 py-1 rounded-md text-sm font-bold mr-3`}>
          {method}
        </span>
        <code className="text-gray-800 font-mono">{path}</code>
      </div>
      
      <div className="p-4">
        <p className="text-gray-700 mb-4">{description}</p>
        
        {requestBody && (
          <div className="mb-4">
            <button
              onClick={() => setShowRequest(!showRequest)}
              className="flex items-center text-sm text-blue-600 hover:text-blue-800 mb-2"
            >
              <svg
                className={`w-4 h-4 mr-1 transform ${showRequest ? 'rotate-90' : ''} transition-transform`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              Request Body
            </button>
            
            {showRequest && (
              <pre className="bg-gray-800 text-white p-3 rounded-md text-xs overflow-x-auto">
                {requestBody}
              </pre>
            )}
          </div>
        )}
        
        <div>
          <button
            onClick={() => setShowResponse(!showResponse)}
            className="flex items-center text-sm text-blue-600 hover:text-blue-800 mb-2"
          >
            <svg
              className={`w-4 h-4 mr-1 transform ${showResponse ? 'rotate-90' : ''} transition-transform`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            Response Body
          </button>
          
          {showResponse && (
            <pre className="bg-gray-800 text-white p-3 rounded-md text-xs overflow-x-auto">
              {responseBody}
            </pre>
          )}
        </div>
      </div>
    </div>
  );
};

export default function ApiDocumentation() {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-black mb-2">API Documentation</h2>
      <p className="text-gray-600 mb-6">
        Use our REST API to programmatically access your data and perform natural language queries.
      </p>
      
      <div className="mb-8">
        <h3 className="text-lg font-medium text-black mb-4">Authentication</h3>
        <p className="text-gray-700 mb-4">
          All API requests must include your API key in the <code className="bg-gray-100 px-1 py-0.5 rounded">X-API-Key</code> header.
        </p>
        <pre className="bg-gray-800 text-white p-3 rounded-md text-xs overflow-x-auto mb-4">
{`curl -X GET "https://api.askdata.ai/api/v1/datasets" \\
  -H "X-API-Key: your_api_key_here"`}
        </pre>
        <p className="text-gray-700">
          You can create and manage your API keys in the <a href="/dashboard/integration" className="text-blue-600 hover:underline">API Keys</a> section.
        </p>
      </div>
      
      <div className="mb-8">
        <h3 className="text-lg font-medium text-black mb-4">Datasets</h3>
        
        <Endpoint
          method="GET"
          path="/api/v1/datasets"
          description="Get all datasets for the authenticated user."
          responseBody={`[
  {
    "id": 1,
    "name": "Sales Data",
    "columns": ["Date", "Product", "Revenue", "Quantity"],
    "row_count": 1000,
    "created_at": "2023-05-01T12:00:00Z"
  },
  {
    "id": 2,
    "name": "Customer Data",
    "columns": ["ID", "Name", "Email", "SignupDate"],
    "row_count": 500,
    "created_at": "2023-05-02T14:30:00Z"
  }
]`}
        />
        
        <Endpoint
          method="GET"
          path="/api/v1/datasets/{dataset_id}"
          description="Get a specific dataset by ID."
          responseBody={`{
  "id": 1,
  "name": "Sales Data",
  "columns": ["Date", "Product", "Revenue", "Quantity"],
  "row_count": 1000,
  "created_at": "2023-05-01T12:00:00Z"
}`}
        />
        
        <Endpoint
          method="POST"
          path="/api/v1/datasets"
          description="Upload a new dataset. This endpoint accepts multipart/form-data with a file and name field."
          requestBody={`# Form data:
file: [binary file data]
name: "New Dataset Name"`}
          responseBody={`{
  "id": 3,
  "name": "New Dataset Name",
  "columns": ["Column1", "Column2", "Column3"],
  "row_count": 250,
  "created_at": "2023-05-10T09:15:00Z"
}`}
        />
        
        <Endpoint
          method="DELETE"
          path="/api/v1/datasets/{dataset_id}"
          description="Delete a dataset by ID."
          responseBody={`# No content (204 status code)`}
        />
      </div>
      
      <div className="mb-8">
        <h3 className="text-lg font-medium text-black mb-4">Queries</h3>
        
        <Endpoint
          method="POST"
          path="/api/v1/ask"
          description="Ask a natural language question about a dataset."
          requestBody={`{
  "user_id": "user_123",
  "dataset_id": 1,
  "question": "What is the average revenue by product?",
  "include_cot": true,
  "include_trust_score": true
}`}
          responseBody={`{
  "id": 42,
  "answer": "The average revenue by product is as follows: Product A: $1,200, Product B: $950, Product C: $1,500.",
  "chart_type": "bar",
  "chart_data": {
    "labels": ["Product A", "Product B", "Product C"],
    "datasets": [{
      "label": "Average Revenue",
      "data": [1200, 950, 1500]
    }]
  },
  "trust_score": {
    "trust_score": 0.92,
    "explanation": "High confidence in the answer based on clear data patterns.",
    "component_scores": {
      "data_quality": 0.95,
      "statistical_validity": 0.90,
      "answer_clarity": 0.92
    }
  },
  "reasoning_steps": [
    "First, I'll group the data by product",
    "Then, I'll calculate the average revenue for each product",
    "Finally, I'll format the results and generate a bar chart"
  ],
  "created_at": "2023-05-10T10:30:00Z"
}`}
        />
        
        <Endpoint
          method="GET"
          path="/api/v1/queries"
          description="Get queries for the authenticated user. You can filter by dataset_id and paginate with limit and offset parameters."
          responseBody={`[
  {
    "id": 42,
    "answer": "The average revenue by product is as follows: Product A: $1,200, Product B: $950, Product C: $1,500.",
    "chart_type": "bar",
    "chart_data": {
      "labels": ["Product A", "Product B", "Product C"],
      "datasets": [{
        "label": "Average Revenue",
        "data": [1200, 950, 1500]
      }]
    },
    "trust_score": {
      "trust_score": 0.92,
      "explanation": "High confidence in the answer based on clear data patterns.",
      "component_scores": {
        "data_quality": 0.95,
        "statistical_validity": 0.90,
        "answer_clarity": 0.92
      }
    },
    "created_at": "2023-05-10T10:30:00Z"
  },
  {
    "id": 41,
    "answer": "The total revenue for Q1 2023 was $125,000.",
    "chart_type": null,
    "chart_data": null,
    "created_at": "2023-05-09T15:45:00Z"
  }
]`}
        />
      </div>
      
      <div>
        <h3 className="text-lg font-medium text-black mb-4">Error Handling</h3>
        <p className="text-gray-700 mb-4">
          The API uses standard HTTP status codes to indicate the success or failure of a request.
        </p>
        
        <table className="min-w-full divide-y divide-gray-200 mb-4">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status Code
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            <tr>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                200 OK
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                The request was successful.
              </td>
            </tr>
            <tr>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                201 Created
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                The resource was successfully created.
              </td>
            </tr>
            <tr>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                204 No Content
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                The request was successful, but there is no content to return.
              </td>
            </tr>
            <tr>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                400 Bad Request
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                The request was invalid or cannot be otherwise served.
              </td>
            </tr>
            <tr>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                401 Unauthorized
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                Authentication failed or user doesn't have permissions.
              </td>
            </tr>
            <tr>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                404 Not Found
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                The requested resource could not be found.
              </td>
            </tr>
            <tr>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                500 Internal Server Error
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                An error occurred on the server.
              </td>
            </tr>
          </tbody>
        </table>
        
        <p className="text-gray-700 mb-2">
          Error responses include a detail message:
        </p>
        <pre className="bg-gray-800 text-white p-3 rounded-md text-xs overflow-x-auto">
{`{
  "detail": "Dataset not found"
}`}
        </pre>
      </div>
    </div>
  );
}
