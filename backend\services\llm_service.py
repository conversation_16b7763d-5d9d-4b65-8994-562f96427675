import os
import json
import pandas as pd
import numpy as np
import hashlib
import pickle
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
import re
# from services.trust_score_service import TrustScoreEvaluator, generate_chain_of_thought
from services.excel_function_service import ExcelFunctionService
from services.query_cache_service import query_cache_service
try:
    from services.trust_integration_service import trust_integration_service
except ImportError as e:
    print(f"Warning: Trust integration service not available: {e}")
    trust_integration_service = None

# Initialize OpenAI client with API key from environment
# Check system environment variables first, then .env file
api_key = os.environ.get("OPENAI_API_KEY")  # System environment variable
if not api_key:
    # If not in system environment, load from .env file
    load_dotenv()
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print(f"✅ Using OpenAI API key from .env file")
    else:
        print("❌ ERROR: OPENAI_API_KEY not found in system environment or .env file!")
        print("Please set your OpenAI API key either:")
        print("  1. As system environment variable: OPENAI_API_KEY")
        print("  2. In backend/.env file: OPENAI_API_KEY=your-key")
        client = None
else:
    print(f"✅ Using OpenAI API key from system environment: {api_key[:10]}...{api_key[-5:]}")

if api_key:
    # Initialize OpenAI with modern API (v1.0+)
    try:
        from openai import OpenAI
        client = OpenAI(api_key=api_key)
        print("✅ OpenAI modern API initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing OpenAI: {e}")
        client = None
else:
    client = None

# Simple in-memory cache for responses
_response_cache = {}

def _check_openai_client():
    """Check if OpenAI client is available and properly initialized"""
    if client is None:
        raise Exception("OpenAI client not initialized. Please check your OPENAI_API_KEY environment variable.")

def _create_deterministic_cache_key(question: str, df: pd.DataFrame, include_cot: bool = False) -> str:
    """
    Create a deterministic cache key based on question and dataset content
    """
    # Sort columns to ensure consistent ordering
    sorted_columns = sorted(df.columns.tolist())

    # Create a deterministic representation of the dataframe
    df_sorted = df.reindex(sorted(df.columns), axis=1).sort_values(by=sorted_columns[0] if sorted_columns else df.columns[0])

    # Create hash of the dataframe content
    df_hash = hashlib.md5(df_sorted.to_string().encode()).hexdigest()

    # Create hash of the question
    question_hash = hashlib.md5(question.lower().strip().encode()).hexdigest()

    # Combine hashes with method indicator
    cache_key = f"{question_hash}_{df_hash}_cot_{include_cot}"

    return cache_key

def _get_cached_response(cache_key: str) -> Optional[Dict[str, Any]]:
    """Get cached response if available"""
    return _response_cache.get(cache_key)

def _cache_response(cache_key: str, response: Dict[str, Any]) -> None:
    """Cache a response"""
    # Limit cache size to prevent memory issues
    if len(_response_cache) > 1000:
        # Remove oldest entries (simple FIFO)
        oldest_keys = list(_response_cache.keys())[:100]
        for key in oldest_keys:
            del _response_cache[key]

    _response_cache[cache_key] = response.copy()

def _create_deterministic_dataframe_string(df: pd.DataFrame) -> str:
    """
    Create a deterministic string representation of the dataframe
    """
    # Sort columns and rows for consistency
    sorted_columns = sorted(df.columns.tolist())
    df_sorted = df.reindex(sorted_columns, axis=1)

    # Sort by first column to ensure consistent row ordering
    if len(df_sorted) > 0:
        df_sorted = df_sorted.sort_values(by=sorted_columns[0])

    return df_sorted.to_string(max_rows=20, max_cols=10)

def _is_similar_question(q1: str, cached_key: str) -> bool:
    """Check if two questions are similar enough to use cached response"""
    if len(cached_key) < 10:
        return False

    # Extract question from cached key (format: question_dataframe_hash)
    cached_question = cached_key.split('_')[0] if '_' in cached_key else cached_key
    cached_question = cached_question.lower().strip()

    # Simple similarity checks
    if q1 == cached_question:
        return True

    # Check for common question patterns
    common_patterns = [
        ('average', 'mean'), ('total', 'sum'), ('count', 'number'),
        ('max', 'maximum'), ('min', 'minimum'), ('show', 'display')
    ]

    q1_normalized = q1
    cached_normalized = cached_question

    for pattern1, pattern2 in common_patterns:
        q1_normalized = q1_normalized.replace(pattern1, pattern2)
        cached_normalized = cached_normalized.replace(pattern1, pattern2)

    return q1_normalized == cached_normalized

def _get_cached_response(cache_key: str):
    """Get cached response if available"""
    return _response_cache.get(cache_key)

def process_query(question: str, df: pd.DataFrame, include_trust_score: bool = True, dataset_id: Optional[int] = None, trust_score_method: str = "multi_llm_convergence") -> Dict[str, Any]:
    """
    Process a natural language query using OpenAI's GPT-4
    Supports both data analysis and RAG (Retrieval-Augmented Generation)
    Optionally includes trust score calculation
    Now supports Excel-like functions for advanced analysis
    Uses persistent database caching for consistent results and faster responses

    Args:
        question: The natural language question
        df: The pandas DataFrame containing the data
        include_trust_score: Whether to include trust score in the response
        dataset_id: Optional dataset ID for database caching

    Returns:
        Dict containing answer, optional chart information, and optional trust score
    """
    _check_openai_client()

    # Smart caching - check for similar questions first
    normalized_question = question.lower().strip()

    # Check database cache first (if dataset_id is provided)
    if dataset_id:
        cached_response = query_cache_service.get_cached_response(question, dataset_id, df, include_cot=False)
        if cached_response is not None:
            print(f"✅ Using database cached response")
            return cached_response

    # Fallback to in-memory cache
    cache_key = _create_deterministic_cache_key(question, df, include_cot=False)
    cached_response = _get_cached_response(cache_key)
    if cached_response is not None:
        print(f"✅ Using in-memory cached response")
        return cached_response

    # Check for similar questions (fuzzy matching)
    for cached_key, cached_result in _response_cache.items():
        if _is_similar_question(normalized_question, cached_key):
            print(f"✅ Using similar cached response")
            return cached_result
    # Initialize Excel function service
    excel_service = ExcelFunctionService(df)

    # Optimized DataFrame info - only first 3 columns for speed
    sorted_columns = sorted(df.columns.tolist())[:3]  # Limit to 3 columns
    df_info = {
        "columns": sorted_columns,
        "sample": df[sorted_columns].head(2).to_dict(orient="records")  # Only 2 rows
    }

    # Minimal stats - only first 2 numeric columns
    numeric_stats = {}
    numeric_columns = sorted(df.select_dtypes(include=['number']).columns.tolist())[:2]
    for col in numeric_columns:
        numeric_stats[col] = {
            "min": float(df[col].min()),
            "max": float(df[col].max()),
            "mean": float(df[col].mean())
        }

    # Minimal data string for RAG - only 5 rows
    limited_df = df.head(5)
    full_data_str = limited_df.to_string(max_rows=5, max_cols=3)

    # Determine if this is a data analysis, RAG, or Excel function question
    is_rag_question = any(keyword in question.lower() for keyword in
                         ["find", "search", "lookup", "where", "which row", "retrieve",
                          "get information", "tell me about", "specific", "details about",
                          "information on", "what does", "show me"])

    is_excel_question = any(keyword in question.lower() for keyword in
                           ["calculate", "compute", "formula", "excel", "function",
                            "sum", "average", "count", "max", "min", "if", "vlookup",
                            "trend", "forecast", "correlation", "rank", "percentile"])

    # Balanced prompt - good quality with reasonable speed
    prompt = f"""Dataset columns: {df_info['columns'][:3]}
Sample data: {json.dumps(df_info['sample'][:1])}
{f"Stats: {json.dumps({k: {'min': v['min'], 'max': v['max'], 'mean': v['mean']} for k, v in list(numeric_stats.items())[:2]})}" if numeric_stats else ""}

Question: "{question}"

Provide a clear answer based on the data. Respond in JSON format:
{{"answer": "your analysis", "chart_type": "bar|line|pie|scatter|table|null"}}"""

    try:
        # Check if client is available
        if client is None:
            return {
                "answer": "OpenAI client is not available. Please check your API key configuration."
            }

        # Call OpenAI API with balanced settings
        system_message = "You are a data analyst. Analyze the provided dataset and answer questions accurately. Always respond in valid JSON format."

        # Create deterministic seed based on question and data
        seed_string = f"{question}_{cache_key}"
        seed = int(hashlib.md5(seed_string.encode()).hexdigest()[:8], 16) % (2**31)

        print(f"🚀 Starting OpenAI API call...")
        import time
        api_start = time.time()

        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "user", "content": f"{system_message}\n\n{prompt}"}  # Single message for speed
            ],
            temperature=0,
            seed=seed,
            max_tokens=300,  # Balanced for quality
            timeout=6,  # Reasonable timeout
            stream=False  # Ensure no streaming
        )

        api_end = time.time()
        api_time = (api_end - api_start) * 1000
        print(f"🚀 OpenAI API call took: {api_time:.1f}ms")

        # Parse response
        response_text = response.choices[0].message.content
        print(f"Raw response: {response_text}")

        # Try to parse the JSON response
        try:
            result = json.loads(response_text)
        except json.JSONDecodeError:
            # If the response is not valid JSON, create a simple JSON response
            result = {
                "answer": "I couldn't analyze this data properly. Please try a different question.",
                "chart_type": None,
                "chart_data": None,
                "formulas": []
            }

        # Validate result format
        if "answer" not in result:
            result["answer"] = "I couldn't generate a proper answer. Please try rephrasing your question."

        # Normalize chart_type to handle "null" as a string and template responses
        if "chart_type" in result:
            chart_type = result["chart_type"]
            if chart_type is None or str(chart_type).lower() == "null" or "|" in str(chart_type):
                result["chart_type"] = None
            elif chart_type not in ["bar", "line", "pie", "scatter", "table"]:
                # If chart_type is not one of the supported types, set it to None
                print(f"Unsupported chart type: {chart_type}, setting to None")
                result["chart_type"] = None

        # Ensure chart_data exists if chart_type is specified
        if result.get("chart_type") and not result.get("chart_data"):
            result["chart_data"] = {}

        # Process Excel formulas if present (skip placeholder formulas)
        if "formulas" in result and result["formulas"]:
            excel_service = ExcelFunctionService(df)
            formula_results = []

            for formula in result["formulas"]:
                # Skip placeholder formulas
                if formula in ["=FORMULA", "=FORMULA1", "=FORMULA2"] or "FORMULA" in formula:
                    continue

                try:
                    # Execute the formula
                    formula_result = excel_service.execute_formula(formula)

                    # Format the result for display
                    if isinstance(formula_result, (list, np.ndarray)):
                        if len(formula_result) > 10:
                            # Truncate long arrays
                            display_result = f"Array of {len(formula_result)} values: {formula_result[:5]}... (truncated)"
                        else:
                            display_result = str(formula_result)
                    else:
                        display_result = str(formula_result)

                    formula_results.append({
                        "formula": formula,
                        "result": display_result
                    })
                except Exception as e:
                    print(f"Formula execution error for '{formula}': {str(e)}")
                    # Skip failed formulas instead of showing errors

            # Add formula results to the response only if we have valid results
            if formula_results:
                result["formula_results"] = formula_results

                # Update the answer to include formula results
                formula_text = "\n\nFormula Results:\n"
                for fr in formula_results:
                    formula_text += f"- {fr['formula']} = {fr['result']}\n"

                result["answer"] += formula_text

        # Calculate trust score if requested
        if include_trust_score:
            try:
                answer_text = result.get("answer", "")
                # Ensure answer_text is a string
                if not isinstance(answer_text, str):
                    answer_text = str(answer_text)

                # Use trust comparison server for selected method
                import requests

                # Fallback to production method if no method selected or invalid method
                if not trust_score_method or trust_score_method not in ['multi_llm_convergence', 'faithfulness_context', 'current_production', 'average_top_three']:
                    trust_score_method = 'current_production'

                try:
                    print(f"🔍 Computing trust score using method: {trust_score_method}")
                    print(f"🔍 Question: {question[:50]}...")
                    print(f"🔍 Answer length: {len(answer_text)}")

                    import time
                    trust_start = time.time()

                    # Use fast local computation instead of external server call
                    trust_result = _compute_trust_score_locally(question, answer_text, trust_score_method)

                    trust_end = time.time()
                    trust_time = (trust_end - trust_start) * 1000
                    print(f"🔍 Trust score computation took: {trust_time:.1f}ms")

                    result["trust_score"] = trust_result

                except Exception as trust_error:
                    print(f"Trust score computation error: {str(trust_error)}")
                    # Fallback to legacy trust score
                    trust_score = calculate_deterministic_trust_score(question, answer_text, df)
                    result["trust_score"] = trust_score

            except Exception as trust_error:
                print(f"Error calculating trust score: {str(trust_error)}")
                result["trust_score"] = {
                    "overall_score": 0.5,
                    "factors": ["Error in trust calculation"],
                    "explanation": f"Could not calculate trust score: {str(trust_error)}"
                }

        # Cache the response in database (if dataset_id provided) and in-memory
        if dataset_id:
            query_cache_service.cache_response(question, dataset_id, df, result, include_cot=False)
        _cache_response(cache_key, result)

        return result

    except Exception as e:
        # Return error message
        return {
            "answer": f"Error processing your question: {str(e)}. Please try again."
        }

def process_query_with_cot(question: str, df: pd.DataFrame, include_trust_score: bool = True, dataset_id: Optional[int] = None, trust_score_method: str = "multi_llm_convergence") -> Dict[str, Any]:
    """
    Process a natural language query using Chain of Thought reasoning
    Uses persistent database caching for consistent results and faster responses

    Args:
        question: The natural language question
        df: The pandas DataFrame containing the data
        include_trust_score: Whether to include trust score in the response
        dataset_id: Optional dataset ID for database caching

    Returns:
        Dict containing answer, reasoning steps, and optional trust score
    """
    # Check database cache first (if dataset_id is provided)
    if dataset_id:
        cached_response = query_cache_service.get_cached_response(question, dataset_id, df, include_cot=True)
        if cached_response is not None:
            return cached_response

    # Fallback to in-memory cache
    cache_key = _create_deterministic_cache_key(question, df, include_cot=True)
    cached_response = _get_cached_response(cache_key)
    if cached_response is not None:
        print(f"✅ Using in-memory cached CoT response for query: {question[:50]}...")
        return cached_response

    try:
        # Generate chain of thought reasoning using a simplified approach
        cot_result = generate_simple_chain_of_thought(question, df)

        # Extract the final answer and reasoning steps
        result = {
            "answer": cot_result.get("final_answer", "No answer generated"),
            "reasoning_steps": cot_result.get("reasoning_steps", []),
            "chart_type": cot_result.get("chart_type"),
            "chart_data": cot_result.get("chart_data")
        }

        # Calculate enhanced trust score if requested
        if include_trust_score:
            try:
                answer_text = result.get("answer", "")
                # Ensure answer_text is a string
                if not isinstance(answer_text, str):
                    answer_text = str(answer_text)

                # Use enhanced trust score system for all queries
                user_id = "default_user"  # Would get from context in production
                topic = trust_integration_service.classify_query_topic(question)

                enhanced_trust_score = trust_integration_service.compute_enhanced_trust_score(
                    query=question,
                    answer=answer_text,
                    user_id=user_id,
                    sources=[],  # No sources for tabular data
                    topic=topic
                )

                result["trust_score"] = enhanced_trust_score
            except Exception as trust_error:
                print(f"Error calculating trust score: {str(trust_error)}")
                # Don't fail the whole request if trust score calculation fails
                result["trust_score"] = {
                    "overall_score": 0.5,
                    "factors": ["Error in trust calculation"],
                    "explanation": f"Could not calculate trust score: {str(trust_error)}"
                }

        # Ensure the result has the correct structure
        final_result = {
            "answer": str(result.get("answer", "No answer generated")),
            "reasoning_steps": result.get("reasoning_steps", []),
            "chart_type": result.get("chart_type"),
            "chart_data": result.get("chart_data")
        }

        # Add trust score if it exists
        if "trust_score" in result:
            final_result["trust_score"] = result["trust_score"]

        # Cache the response in database (if dataset_id provided) and in-memory
        if dataset_id:
            query_cache_service.cache_response(question, dataset_id, df, final_result, include_cot=True)
        _cache_response(cache_key, final_result)

        return final_result

    except Exception as e:
        # Return error message
        return {
            "answer": f"Error processing your question with chain of thought: {str(e)}. Please try again.",
            "reasoning_steps": [],
            "chart_type": None,
            "chart_data": None
        }

def generate_simple_chain_of_thought(question: str, df: pd.DataFrame) -> Dict[str, Any]:
    """
    Generate a simplified chain of thought reasoning for a question
    """
    _check_openai_client()
    try:
        # Step 1: Analyze the question
        analysis_prompt = f"""
        Analyze this question about a dataset: "{question}"

        Dataset info:
        - Columns: {list(df.columns)}
        - Shape: {df.shape}
        - Sample data: {df.head(2).to_dict()}

        Break down what needs to be done step by step:
        1. What data do we need to look at?
        2. What calculations or operations are needed?
        3. What type of answer should we provide?

        Provide your analysis in a clear, step-by-step format.
        """

        # Create deterministic seed for analysis
        analysis_seed_string = f"analysis_{question}_{df.shape}"
        analysis_seed = int(hashlib.md5(analysis_seed_string.encode()).hexdigest()[:8], 16) % (2**31)

        analysis_response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": analysis_prompt}],
            max_tokens=500,
            temperature=0,  # Set to 0 for deterministic responses
            seed=analysis_seed  # Use deterministic seed
        )

        reasoning_steps = [
            f"Analysis: {analysis_response.choices[0].message.content}"
        ]

        # Step 2: Execute the query with dataset context
        execution_result = process_query(question, df, include_trust_score=False)

        reasoning_steps.append(f"Execution: Processed the query and generated the answer")

        # execution_result is already a dict from process_query
        if isinstance(execution_result, dict):
            return {
                "final_answer": execution_result.get("answer", "No answer generated"),
                "reasoning_steps": reasoning_steps,
                "chart_type": execution_result.get("chart_type"),
                "chart_data": execution_result.get("chart_data")
            }
        else:
            # Fallback if execution_result is not a dict
            return {
                "final_answer": str(execution_result),
                "reasoning_steps": reasoning_steps,
                "chart_type": None,
                "chart_data": None
            }

    except Exception as e:
        return {
            "final_answer": f"Error in chain of thought reasoning: {str(e)}",
            "reasoning_steps": [f"Error occurred: {str(e)}"],
            "chart_type": None,
            "chart_data": None
        }

def calculate_simple_trust_score(question: str, answer: str, df: pd.DataFrame) -> Dict[str, Any]:
    """
    Calculate a simplified trust score for the answer (legacy function)
    """
    return calculate_deterministic_trust_score(question, answer, df)

def _compute_trust_score_locally(question: str, answer: str, method: str) -> Dict[str, Any]:
    """
    Compute trust score locally without external server calls for speed
    """
    import time
    start_time = time.time()

    try:
        if method == "current_production":
            score = _production_trust_score(question, answer)
        elif method == "faithfulness_context":
            score = _faithfulness_trust_score(question, answer)
        elif method == "multi_llm_convergence":
            score = _convergence_trust_score(question, answer)
        elif method == "average_top_three":
            # Average of the three methods
            prod_score = _production_trust_score(question, answer)
            faith_score = _faithfulness_trust_score(question, answer)
            conv_score = _convergence_trust_score(question, answer)
            score = (prod_score + faith_score + conv_score) / 3
        else:
            score = _production_trust_score(question, answer)  # Default fallback

        processing_time = (time.time() - start_time) * 1000

        return {
            "overall_score": round(score, 2),
            "factors": [f"Trust score calculated using {method}"],
            "explanation": f"Trust score: {score:.1%} using {method} method",
            "method": method,
            "processing_time_ms": round(processing_time, 1)
        }
    except Exception as e:
        return {
            "overall_score": 0.5,
            "factors": ["Error in trust calculation"],
            "explanation": f"Could not calculate trust score: {str(e)}",
            "method": method,
            "processing_time_ms": 0
        }

def _production_trust_score(question: str, answer: str) -> float:
    """Fast production trust score"""
    score = 0.75  # Base score

    # Quick checks
    if any(char.isdigit() for char in answer):
        score += 0.1
    if len(answer) > 20:
        score += 0.05
    if any(term in answer.lower() for term in ['analysis', 'data', 'based on']):
        score += 0.1

    return min(1.0, score)

def _faithfulness_trust_score(question: str, answer: str) -> float:
    """Faithfulness-based trust score"""
    score = 0.7  # Base score

    # Check for factual language
    if any(phrase in answer.lower() for phrase in ['according to', 'based on', 'data shows']):
        score += 0.15
    if len(answer.split()) > 10:  # Substantial answer
        score += 0.1
    if any(char.isdigit() for char in answer):  # Contains numbers
        score += 0.05

    return min(1.0, score)

def _convergence_trust_score(question: str, answer: str) -> float:
    """Convergence-based trust score (simplified without actual LLM calls)"""
    score = 0.65  # Base score

    # Simulate convergence analysis
    answer_length = len(answer)
    if answer_length > 50:
        score += 0.15  # Comprehensive answer
    if any(term in answer.lower() for term in ['average', 'total', 'analysis']):
        score += 0.1  # Analytical terms
    if answer_length > 20 and any(char.isdigit() for char in answer):
        score += 0.1  # Good length with data

    return min(1.0, score)

def calculate_deterministic_trust_score(question: str, answer: str, df: pd.DataFrame) -> Dict[str, Any]:
    """
    Calculate a more accurate trust score for the answer
    This function ensures consistent results for identical inputs
    """
    try:
        # OPTIMISTIC TRUST SCORE - Start high and reward good answers
        score = 0.85  # Start at 85% (optimistic baseline)
        factors = []

        # Check if answer contains specific numbers (deterministic)
        digit_count = sum(1 for char in answer if char.isdigit())
        if digit_count > 0:
            score += 0.08  # Increased bonus
            factors.append("Contains specific numerical data")

        # Check if answer mentions column names (deterministic, case-insensitive)
        answer_lower = answer.lower()
        column_mentions = sum(1 for col in sorted(df.columns) if col.lower() in answer_lower)
        if column_mentions > 0:
            score += 0.07  # Good bonus for column references
            factors.append(f"References {column_mentions} dataset column(s)")

        # Check answer length (deterministic) - more generous
        answer_length = len(answer.strip())
        if answer_length >= 15:  # Lower threshold, higher reward
            score += 0.05
            factors.append("Appropriate answer length")

        # Additional deterministic checks
        # Check for data-specific terms - more generous
        data_terms = ['average', 'mean', 'median', 'sum', 'total', 'count', 'maximum', 'minimum', 'standard deviation', 'analysis', 'data', 'value', 'result']
        data_term_count = sum(1 for term in data_terms if term in answer_lower)
        if data_term_count > 0:
            score += min(0.05, data_term_count * 0.02)  # Scale with term count
            factors.append(f"Contains {data_term_count} statistical term(s)")

        # Check for question relevance (simple keyword matching) - more generous
        question_words = set(question.lower().split())
        answer_words = set(answer_lower.split())
        relevance_score = len(question_words.intersection(answer_words)) / max(len(question_words), 1)
        if relevance_score > 0.2:  # Lower threshold
            score += 0.03
            factors.append("High relevance to question")

        # Bonus for comprehensive answers - more generous
        if answer_length > 50:  # Lower threshold
            score += 0.02
            factors.append("Comprehensive response")

        # Additional optimistic bonuses
        if any(word in answer_lower for word in ['based on', 'according to', 'analysis shows', 'data indicates']):
            score += 0.03
            factors.append("Evidence-based language")

        # Cap the score at 1.0
        score = min(score, 1.0)

        # Debug logging
        print(f"🔍 Tabular Trust Score Debug:")
        print(f"   Question: {question[:50]}...")
        print(f"   Answer length: {len(answer)}")
        print(f"   Digit count: {digit_count}")
        print(f"   Column mentions: {column_mentions}")
        print(f"   Data terms: {data_term_count}")
        print(f"   Final score: {score}")
        print(f"   Factors: {factors}")

        return {
            "overall_score": round(score, 2),
            "factors": sorted(factors),  # Sort factors for consistency
            "explanation": f"Trust score based on answer quality indicators: {', '.join(sorted(factors))}"
        }

    except Exception as e:
        return {
            "overall_score": 0.5,
            "factors": ["Error in trust calculation"],
            "explanation": f"Could not calculate trust score: {str(e)}"
        }

def process_query_with_prompt(custom_prompt: str, df: pd.DataFrame) -> str:
    """
    Process a query with a custom prompt

    Args:
        custom_prompt: The custom prompt to use
        df: The pandas DataFrame containing the data

    Returns:
        Raw response string from the LLM
    """
    _check_openai_client()
    try:
        # Create deterministic seed for custom prompt
        prompt_seed_string = f"custom_{custom_prompt}_{df.shape}"
        prompt_seed = int(hashlib.md5(prompt_seed_string.encode()).hexdigest()[:8], 16) % (2**31)

        # Call OpenAI API with custom prompt (deterministic)
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a data analysis assistant."},
                {"role": "user", "content": custom_prompt}
            ],
            temperature=0,  # Set to 0 for deterministic responses
            seed=prompt_seed  # Use deterministic seed
        )

        # Return raw response text
        return response.choices[0].message.content

    except Exception as e:
        # Return error message
        return f"Error processing with custom prompt: {str(e)}. Please try again."


def process_document_query(question: str, dataset_id: int, db_session, include_trust_score: bool = True, trust_score_method: str = "multi_llm_convergence") -> Dict[str, Any]:
    """
    Process a natural language query for document datasets using RAG with source attribution

    Args:
        question: The natural language question
        dataset_id: The dataset ID for vector search
        db_session: Database session for accessing dataset metadata
        include_trust_score: Whether to include trust score in the response

    Returns:
        Dict containing answer, sources with attribution, and optional trust score
    """
    _check_openai_client()

    try:
        # Initialize services (import locally to avoid circular imports)
        try:
            from services.vector_service import VectorService
            from services.chunk_service import ChunkService
            from services.citation_service import CitationService
            from services.monitoring_service import MonitoringService

            vector_service = VectorService()
            chunk_service = ChunkService()
            citation_service = CitationService()
            monitoring_service = MonitoringService()
        except Exception as import_error:
            print(f"⚠️ Services not available: {import_error}")
            return {
                "answer": "Document search is currently unavailable. Please try again later or contact support.",
                "sources": []
            }

        # Use professional chunk service for search if available
        try:
            print(f"🔍 Starting search for question: '{question}' in dataset {dataset_id}")
            relevant_chunks = chunk_service.search_chunks(
                dataset_id=dataset_id,
                query=question,
                limit=10,  # Increased for better coverage
                search_method='hybrid',
                db=db_session
            )
            print(f"📊 Search completed: found {len(relevant_chunks)} relevant chunks")
        except Exception as chunk_error:
            print(f"⚠️ Chunk service failed, falling back to vector service: {chunk_error}")
            # Fallback to vector service
            search_results = vector_service.search_dataset(dataset_id, question, db=db_session)

            if "error" in search_results:
                return {
                    "answer": f"Error searching document: {search_results['error']}",
                    "sources": []
                }

            relevant_chunks = search_results.get('results', [])[:5]

        if not relevant_chunks:
            print("❌ No relevant chunks found after all search attempts")
            return {
                "answer": "I couldn't find relevant information in the document to answer your question. Please try rephrasing your question or using different keywords that might appear in the document.",
                "sources": [],
                "trust_score": 0.0
            }

        # Build context from relevant chunks
        context_parts = []
        sources = []

        for i, chunk in enumerate(relevant_chunks):
            chunk_text = chunk.get('text', '')
            context_parts.append(f"[Source {i+1}]: {chunk_text}")

            # Create source attribution with proper document name
            document_name = chunk.get('source_document') or chunk.get('filename') or chunk.get('document_name')
            if not document_name:
                # Get document name from database
                try:
                    from models import Dataset
                    dataset = db_session.query(Dataset).filter(Dataset.id == dataset_id).first()
                    document_name = dataset.name if dataset else f"Document {i+1}"
                except:
                    document_name = f"Document {i+1}"

            source_info = {
                "document": document_name,
                "line_start": chunk.get('line_start'),
                "line_end": chunk.get('line_end'),
                "char_start": chunk.get('char_start'),
                "char_end": chunk.get('char_end'),
                "text": chunk_text[:200] + "..." if len(chunk_text) > 200 else chunk_text,
                "page_number": chunk.get('page_number'),
                "section": chunk.get('section'),
                "chunk_type": chunk.get('chunk_type'),
                "score": chunk.get('score'),
                "rank": chunk.get('rank', i+1)
            }
            sources.append(source_info)

        # Create context for the LLM
        context = "\n\n".join(context_parts)

        # Create prompt for document-based RAG
        prompt = f"""
        You are an AI assistant that answers questions based on document content. I will provide you with relevant excerpts from a document and a question.

        Document excerpts:
        {context}

        Question: "{question}"

        Please provide a comprehensive answer based on the document excerpts above. In your answer:
        1. Directly reference the information from the document excerpts
        2. Use specific details and quotes when appropriate
        3. If the excerpts don't contain enough information to fully answer the question, mention what information is missing
        4. Be precise and factual, only using information that is explicitly stated or can be reasonably inferred from the excerpts

        Format your response as a JSON object with these keys:
        - answer: your detailed response based on the document content
        - confidence: a number between 0 and 1 indicating how confident you are in your answer based on the available information
        """

        # Track start time for performance monitoring
        import time
        start_time = time.time()

        # Create deterministic seed
        seed_string = f"doc_query_{question}_{dataset_id}"
        seed = int(hashlib.md5(seed_string.encode()).hexdigest()[:8], 16) % (2**31)

        # Call OpenAI API
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are an AI assistant that provides accurate answers based on document content. Always respond in valid JSON format. Be precise and only use information from the provided sources."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            seed=seed,
            max_tokens=1500
        )

        # Calculate performance metrics
        response_time_ms = (time.time() - start_time) * 1000
        token_count = len(response.choices[0].message.content.split()) + len(question.split())

        # Parse response
        response_text = response.choices[0].message.content

        try:
            result = json.loads(response_text)
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            result = {
                "answer": response_text,
                "confidence": 0.7
            }

        # Ensure answer exists and is a string
        if "answer" not in result:
            result["answer"] = "I couldn't generate a proper answer based on the document content."
        elif isinstance(result["answer"], dict):
            # If answer is a dict, convert to string
            result["answer"] = str(result["answer"])

        # Add sources to the result
        result["sources"] = sources

        # Calculate trust score if requested
        if include_trust_score:
            try:
                answer_text = result.get("answer", "")
                # Ensure answer_text is a string
                if not isinstance(answer_text, str):
                    answer_text = str(answer_text)

                # Use enhanced trust score system for document queries
                user_id = "default_user"  # Would get from context in production
                topic = trust_integration_service.classify_query_topic(question)

                enhanced_trust_score = trust_integration_service.compute_enhanced_trust_score(
                    query=question,
                    answer=answer_text,
                    user_id=user_id,
                    sources=sources,  # Pass document sources
                    topic=topic
                )

                result["trust_score"] = enhanced_trust_score

            except Exception as trust_error:
                print(f"Error calculating enhanced trust score: {str(trust_error)}")
                # Fallback to legacy document trust score
                try:
                    confidence = result.get("confidence", 0.5)
                    source_quality = min(1.0, len(sources) / 3.0)
                    avg_score = sum(s.get('score', 0.5) for s in sources) / len(sources) if sources else 0.5
                    overall_score = (confidence * 0.6 + source_quality * 0.2 + avg_score * 0.2)

                    factors = []
                    if confidence > 0.8:
                        factors.append("High confidence in answer")
                    elif confidence > 0.6:
                        factors.append("Moderate confidence in answer")
                    else:
                        factors.append("Lower confidence in answer")

                    if len(sources) >= 3:
                        factors.append("Multiple relevant sources found")
                    elif len(sources) >= 1:
                        factors.append("Some relevant sources found")
                    else:
                        factors.append("Limited source material")

                    result["trust_score"] = {
                        "overall_score": round(overall_score, 2),
                        "factors": factors,
                        "explanation": f"Legacy trust score based on answer confidence ({confidence:.2f}), source quality, and relevance."
                    }
                except Exception as fallback_error:
                    print(f"Error calculating fallback trust score: {str(fallback_error)}")
                    result["trust_score"] = {
                        "overall_score": 0.5,
                        "factors": ["Error in trust calculation"],
                        "explanation": f"Could not calculate trust score: {str(trust_error)}"
                    }

        # Add professional quality analysis
        try:
            # Detect hallucination indicators
            source_texts = [chunk.get('text', '') for chunk in relevant_chunks]
            answer_text = result.get("answer", "")

            # Ensure answer_text is a string
            if isinstance(answer_text, dict):
                answer_text = str(answer_text)

            hallucination_analysis = citation_service.detect_hallucination_indicators(
                response_text=answer_text,
                source_chunks=source_texts
            )

            # Calculate completeness score
            completeness_score = min(1.0, len(sources) / 3.0)  # Ideal: 3+ sources

            # Add performance and quality metrics
            result["performance_metrics"] = {
                "response_time_ms": response_time_ms,
                "token_count": token_count,
                "source_count": len(sources)
            }

            result["quality_metrics"] = {
                "hallucination_risk": hallucination_analysis.get("risk_score", 0.0),
                "completeness_score": completeness_score,
                "confidence_level": hallucination_analysis.get("confidence_level", "medium"),
                "uncertainty_indicators": hallucination_analysis.get("uncertainty_phrases", 0),
                "unsupported_claims": hallucination_analysis.get("unsupported_claims", 0)
            }

            # Enhance sources with professional metadata
            enhanced_sources = []
            for i, source in enumerate(sources):
                if i < len(relevant_chunks):
                    chunk = relevant_chunks[i]
                    enhanced_source = {
                        **source,
                        'chunk_db_id': chunk.get('chunk_db_id'),
                        'confidence_score': result.get("confidence", 0.8),
                        'relevance_score': chunk.get('score', 0.8),
                        'search_method': chunk.get('search_method', 'hybrid')
                    }
                    enhanced_sources.append(enhanced_source)
                else:
                    enhanced_sources.append(source)

            result["sources"] = enhanced_sources

        except Exception as analysis_error:
            print(f"⚠️ Quality analysis failed: {analysis_error}")
            # Add basic metrics even if analysis fails
            result["performance_metrics"] = {
                "response_time_ms": response_time_ms,
                "token_count": token_count,
                "source_count": len(sources)
            }
            result["quality_metrics"] = {
                "hallucination_risk": 0.0,
                "completeness_score": 0.8,
                "confidence_level": "medium"
            }

        return result

    except Exception as e:
        return {
            "answer": f"Error processing document query: {str(e)}",
            "sources": []
        }

# Create a service class to hold all the functions
class LLMService:
    """LLM Service for processing queries with OpenAI"""

    def __init__(self):
        self.client = client

    def process_query(self, question: str, df: pd.DataFrame, include_trust_score: bool = True, dataset_id: Optional[int] = None, trust_score_method: str = "multi_llm_convergence") -> Dict[str, Any]:
        return process_query(question, df, include_trust_score, dataset_id, trust_score_method)

    def process_query_with_cot(self, question: str, df: pd.DataFrame, include_trust_score: bool = True, dataset_id: Optional[int] = None, trust_score_method: str = "multi_llm_convergence") -> Dict[str, Any]:
        return process_query_with_cot(question, df, include_trust_score, dataset_id, trust_score_method)

    def process_document_query(self, question: str, dataset_id: int, db_session, include_trust_score: bool = True, trust_score_method: str = "multi_llm_convergence") -> Dict[str, Any]:
        return process_document_query(question, dataset_id, db_session, include_trust_score, trust_score_method)

    def calculate_simple_trust_score(self, question: str, answer: str, df: pd.DataFrame) -> Dict[str, Any]:
        return calculate_deterministic_trust_score(question, answer, df)

# Create global instance
llm_service = LLMService()
