# AIthentiq Startup Guide

## 🚀 Quick Start

### Option 1: Start Everything (Recommended)
**Double-click:** `start_both.bat`
- Starts both backend and frontend
- Opens separate windows for each service
- One-click solution

### Option 2: Start Services Individually
**Backend only:** `start_backend.bat`
**Frontend only:** `start_frontend.bat`

### Option 3: Stop All Services
**Stop everything:** `stop_services.bat`
- Stops both frontend and backend
- Cleans up all related processes
- Safe shutdown

## 🌐 Access Your Application

Once both services are running:
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:8000

## 🎨 Your AIthentiq Logo

Your custom logo is now integrated and visible in:
- **Navbar**: Logo + "AIthentiq" text + "Data + RAG" badge
- **Home Page**: Logo in header
- **Consistent Branding**: Throughout the application

## ✅ What's Working

- ✅ Logo integration complete
- ✅ Rating system (refreshes for each query)
- ✅ Admin panel with feedback statistics
- ✅ OpenAI API with environment variables
- ✅ Database with query history
- ✅ Natural language queries with charts

## 🔧 Troubleshooting

If services don't start:
1. Check that Python is installed
2. Check that Node.js is installed at: `C:\Program Files\nodejs`
3. Make sure no other services are using ports 3000 or 8000

## 📁 Clean File Structure

**Working Scripts:**
- `start_both.bat` - Start everything
- `start_backend.bat` - Backend only
- `start_frontend.bat` - Frontend only
- `stop_services.bat` - Stop everything

**Removed Scripts:**
- All non-working startup scripts have been cleaned up

Your AIthentiq application is ready to use! 🎉
