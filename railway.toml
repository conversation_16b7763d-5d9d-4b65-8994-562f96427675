[build]
builder = "nixpacks"

[deploy]
startCommand = "npm start"
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

# Memory optimization for build process
[variables]
NODE_OPTIONS = "--max-old-space-size=4096"
NPM_CONFIG_PROGRESS = "false"
NPM_CONFIG_AUDIT = "false"

[environments.production]
variables = { NODE_ENV = "production" }

[environments.development]
variables = { NODE_ENV = "development" }
