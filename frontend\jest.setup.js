// Import Jest DOM matchers
import '@testing-library/jest-dom';

// Mock next/router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock environment variables
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8000';
process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY = 'test_key';
process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY = 'test_key';
process.env.NEXT_PUBLIC_PRO_PRICE_ID = 'test_price_id';
