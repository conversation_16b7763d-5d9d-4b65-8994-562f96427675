"""add_query_enhancements

Revision ID: a85d59dbe70c
Revises: 9c46a57d1aae
Create Date: 2025-06-12 10:00:16.830426

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a85d59dbe70c'
down_revision = '9c46a57d1aae'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add missing columns to queries table
    op.add_column('queries', sa.Column('query_name', sa.String(), nullable=True))
    op.add_column('queries', sa.Column('is_bookmarked', sa.<PERSON>(), nullable=True, default=False))
    op.add_column('queries', sa.Column('tags', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove the added columns
    op.drop_column('queries', 'tags')
    op.drop_column('queries', 'is_bookmarked')
    op.drop_column('queries', 'query_name')
