@echo off
echo ==========================================
echo     AIthentIQ Folder Rename Script
echo ==========================================
echo.
echo This script will help you rename the root folder
echo from "AskData - Copy (2)" to "AIthentiq"
echo.
echo ⚠️  IMPORTANT: Close all applications using this folder first!
echo    - Close VS Code, terminals, file explorers
echo    - Stop any running services
echo.
pause
echo.

echo 🔍 Checking current location...
cd
echo Current directory: %CD%
echo.

echo 📁 Current folder structure:
dir /b
echo.

echo 🛑 Stopping any running services...
call stop_services.bat
echo.

echo 📋 MANUAL STEPS REQUIRED:
echo ========================
echo.
echo 1. 🚪 Exit this script (press any key)
echo 2. 📂 Open File Explorer
echo 3. 🔼 Navigate to: C:\Users\<USER>\Documents\augment-projects\
echo 4. 🏷️  Right-click on "AskData - Copy (2)" folder
echo 5. ✏️  Select "Rename" 
echo 6. 📝 Type: AIthentiq
echo 7. ✅ Press Enter
echo 8. 🔄 Navigate into the renamed "AIthentiq" folder
echo 9. 🚀 Double-click "start_both.bat" to test
echo.
echo 💡 Alternative method using Command Prompt:
echo ==========================================
echo 1. Open Command Prompt as Administrator
echo 2. Navigate to: cd "C:\Users\<USER>\Documents\augment-projects"
echo 3. Rename: ren "AskData - Copy (2)" "AIthentiq"
echo 4. Navigate: cd AIthentiq
echo 5. Test: start_both.bat
echo.
echo ✅ After renaming, all internal references have been updated!
echo    - Virtual environment paths ✅
echo    - Database configurations ✅  
echo    - Deployment configurations ✅
echo    - Startup scripts ✅
echo.
echo 🎉 Your AIthentIQ application will be ready to use!
echo.
pause
