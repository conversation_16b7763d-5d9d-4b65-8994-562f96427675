"""
Complete Error Handling System for Phase 1
Proper error handling for FastAPI routers as required by development plan
"""

import logging
import traceback
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import uuid

logger = logging.getLogger(__name__)

class ErrorCode:
    """Standardized error codes"""
    
    # Authentication & Authorization
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    INVALID_TOKEN = "INVALID_TOKEN"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    
    # Validation
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INVALID_INPUT = "INVALID_INPUT"
    MISSING_FIELD = "MISSING_FIELD"
    
    # Database
    DATABASE_ERROR = "DATABASE_ERROR"
    CONSTRAINT_VIOLATION = "CONSTRAINT_VIOLATION"
    RECORD_NOT_FOUND = "RECORD_NOT_FOUND"
    DUPLICATE_RECORD = "DUPLICATE_RECORD"
    
    # Business Logic
    BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION"
    OPERATION_NOT_ALLOWED = "OPERATION_NOT_ALLOWED"
    RESOURCE_LIMIT_EXCEEDED = "RESOURCE_LIMIT_EXCEEDED"
    
    # External Services
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    LLM_SERVICE_ERROR = "LLM_SERVICE_ERROR"
    VECTOR_STORE_ERROR = "VECTOR_STORE_ERROR"
    
    # System
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"

class ErrorDetail:
    """Detailed error information"""
    
    def __init__(
        self,
        code: str,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.code = code
        self.message = message
        self.field = field
        self.details = details or {}

class APIError(Exception):
    """Custom API error with detailed information"""
    
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code: str = ErrorCode.INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
        errors: Optional[List[ErrorDetail]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        self.errors = errors or []
        self.error_id = str(uuid.uuid4())
        self.timestamp = datetime.utcnow()
        
        super().__init__(self.message)

class ValidationAPIError(APIError):
    """Validation-specific API error"""
    
    def __init__(self, message: str, field_errors: List[ErrorDetail]):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code=ErrorCode.VALIDATION_ERROR,
            errors=field_errors
        )

class AuthenticationError(APIError):
    """Authentication-specific error"""
    
    def __init__(self, message: str = "Authentication required"):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code=ErrorCode.UNAUTHORIZED
        )

class AuthorizationError(APIError):
    """Authorization-specific error"""
    
    def __init__(self, message: str = "Access forbidden"):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            error_code=ErrorCode.FORBIDDEN
        )

class NotFoundError(APIError):
    """Resource not found error"""
    
    def __init__(self, resource: str, identifier: str = None):
        message = f"{resource} not found"
        if identifier:
            message += f" (ID: {identifier})"
        
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            error_code=ErrorCode.RECORD_NOT_FOUND,
            details={"resource": resource, "identifier": identifier}
        )

class ConflictError(APIError):
    """Resource conflict error"""
    
    def __init__(self, message: str, resource: str = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            error_code=ErrorCode.DUPLICATE_RECORD,
            details={"resource": resource}
        )

class BusinessRuleError(APIError):
    """Business rule violation error"""
    
    def __init__(self, message: str, rule: str = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code=ErrorCode.BUSINESS_RULE_VIOLATION,
            details={"rule": rule}
        )

class ExternalServiceError(APIError):
    """External service error"""
    
    def __init__(self, service: str, message: str = None):
        default_message = f"{service} service unavailable"
        super().__init__(
            message=message or default_message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
            details={"service": service}
        )

class ErrorHandler:
    """Centralized error handling"""
    
    @staticmethod
    def format_error_response(error: APIError) -> Dict[str, Any]:
        """Format error for API response"""
        response = {
            "success": False,
            "error": {
                "id": error.error_id,
                "code": error.error_code,
                "message": error.message,
                "timestamp": error.timestamp.isoformat(),
                "details": error.details
            }
        }
        
        if error.errors:
            response["error"]["field_errors"] = [
                {
                    "field": err.field,
                    "code": err.code,
                    "message": err.message,
                    "details": err.details
                }
                for err in error.errors
            ]
        
        return response
    
    @staticmethod
    def log_error(error: APIError, request: Request = None):
        """Log error with context"""
        log_data = {
            "error_id": error.error_id,
            "error_code": error.error_code,
            "message": error.message,
            "status_code": error.status_code,
            "timestamp": error.timestamp.isoformat(),
            "details": error.details
        }
        
        if request:
            log_data.update({
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent"),
                "tenant_id": getattr(request.state, "tenant_id", None),
                "user_id": getattr(request.state, "user_id", None)
            })
        
        if error.status_code >= 500:
            logger.error("Server error occurred", extra=log_data)
        elif error.status_code >= 400:
            logger.warning("Client error occurred", extra=log_data)
        else:
            logger.info("Error handled", extra=log_data)

# Exception handlers for FastAPI
async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """Handle custom API errors"""
    ErrorHandler.log_error(exc, request)
    response = ErrorHandler.format_error_response(exc)
    return JSONResponse(
        status_code=exc.status_code,
        content=response
    )

async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""
    api_error = APIError(
        message=exc.detail,
        status_code=exc.status_code,
        error_code=ErrorCode.INTERNAL_SERVER_ERROR
    )
    
    ErrorHandler.log_error(api_error, request)
    response = ErrorHandler.format_error_response(api_error)
    return JSONResponse(
        status_code=exc.status_code,
        content=response
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle Pydantic validation errors"""
    field_errors = []
    
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        field_errors.append(ErrorDetail(
            code=ErrorCode.VALIDATION_ERROR,
            message=error["msg"],
            field=field_path,
            details={"type": error["type"], "input": error.get("input")}
        ))
    
    api_error = ValidationAPIError(
        message="Validation failed",
        field_errors=field_errors
    )
    
    ErrorHandler.log_error(api_error, request)
    response = ErrorHandler.format_error_response(api_error)
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=response
    )

async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """Handle SQLAlchemy database errors"""
    error_code = ErrorCode.DATABASE_ERROR
    message = "Database operation failed"
    
    if isinstance(exc, IntegrityError):
        error_code = ErrorCode.CONSTRAINT_VIOLATION
        message = "Data integrity constraint violated"
    
    api_error = APIError(
        message=message,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code=error_code,
        details={"database_error": str(exc)}
    )
    
    ErrorHandler.log_error(api_error, request)
    response = ErrorHandler.format_error_response(api_error)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=response
    )

async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions"""
    api_error = APIError(
        message="An unexpected error occurred",
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code=ErrorCode.INTERNAL_SERVER_ERROR,
        details={
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "traceback": traceback.format_exc()
        }
    )
    
    ErrorHandler.log_error(api_error, request)
    response = ErrorHandler.format_error_response(api_error)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=response
    )

# Utility functions for common error scenarios
def raise_not_found(resource: str, identifier: str = None):
    """Raise a not found error"""
    raise NotFoundError(resource, identifier)

def raise_validation_error(message: str, field: str = None):
    """Raise a validation error"""
    errors = [ErrorDetail(
        code=ErrorCode.VALIDATION_ERROR,
        message=message,
        field=field
    )]
    raise ValidationAPIError(message, errors)

def raise_business_rule_error(message: str, rule: str = None):
    """Raise a business rule violation error"""
    raise BusinessRuleError(message, rule)

def raise_unauthorized(message: str = "Authentication required"):
    """Raise an unauthorized error"""
    raise AuthenticationError(message)

def raise_forbidden(message: str = "Access forbidden"):
    """Raise a forbidden error"""
    raise AuthorizationError(message)

def raise_conflict(message: str, resource: str = None):
    """Raise a conflict error"""
    raise ConflictError(message, resource)

def raise_external_service_error(service: str, message: str = None):
    """Raise an external service error"""
    raise ExternalServiceError(service, message)

# Error handling decorators
def handle_database_errors(func):
    """Decorator to handle database errors"""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except IntegrityError as e:
            raise ConflictError("Data integrity constraint violated", str(e))
        except SQLAlchemyError as e:
            raise APIError(
                message="Database operation failed",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCode.DATABASE_ERROR,
                details={"database_error": str(e)}
            )
    return wrapper

def handle_external_service_errors(service_name: str):
    """Decorator to handle external service errors"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                raise ExternalServiceError(service_name, str(e))
        return wrapper
    return decorator
