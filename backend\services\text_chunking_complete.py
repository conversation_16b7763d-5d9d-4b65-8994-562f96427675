"""
Complete Text Chunking Service for Phase 2
Implements langchain text splitters with intelligent overlap as required by development plan
"""

import logging
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# LangChain imports
try:
    from langchain.text_splitter import (
        RecursiveCharacterTextSplitter,
        CharacterTextSplitter,
        TokenTextSplitter,
        MarkdownHeaderTextSplitter,
        PythonCodeTextSplitter,
        HTMLHeaderTextSplitter
    )
    from langchain.schema import Document
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

logger = logging.getLogger(__name__)

class ChunkingStrategy(str, Enum):
    RECURSIVE = "recursive"
    CHARACTER = "character"
    TOKEN = "token"
    SEMANTIC = "semantic"
    MARKDOWN = "markdown"
    CODE = "code"
    HTML = "html"
    INTELLIGENT = "intelligent"

@dataclass
class ChunkMetadata:
    """Metadata for a text chunk"""
    chunk_id: str
    chunk_index: int
    start_position: int
    end_position: int
    chunk_size: int
    overlap_size: int
    source_reference: str
    document_metadata: Dict[str, Any]
    chunking_strategy: str
    confidence_score: float = 1.0

@dataclass
class TextChunk:
    """A text chunk with metadata"""
    content: str
    metadata: ChunkMetadata

class CompleteTextChunkingService:
    """Complete text chunking service implementing all Phase 2 requirements"""
    
    def __init__(self):
        if not LANGCHAIN_AVAILABLE:
            logger.warning("LangChain not available, using fallback chunking")
        
        # Default chunking parameters
        self.default_chunk_size = 1000
        self.default_overlap = 200
        self.min_chunk_size = 100
        self.max_chunk_size = 4000
        
        # Intelligent overlap ratios
        self.overlap_ratios = {
            ChunkingStrategy.RECURSIVE: 0.2,
            ChunkingStrategy.CHARACTER: 0.1,
            ChunkingStrategy.TOKEN: 0.15,
            ChunkingStrategy.SEMANTIC: 0.25,
            ChunkingStrategy.MARKDOWN: 0.1,
            ChunkingStrategy.CODE: 0.3,
            ChunkingStrategy.HTML: 0.1,
            ChunkingStrategy.INTELLIGENT: 0.2
        }
    
    async def chunk_text(
        self,
        text: str,
        strategy: ChunkingStrategy = ChunkingStrategy.INTELLIGENT,
        chunk_size: Optional[int] = None,
        overlap: Optional[int] = None,
        document_metadata: Optional[Dict[str, Any]] = None,
        preserve_metadata: bool = True
    ) -> List[TextChunk]:
        """
        Chunk text using specified strategy with intelligent overlap
        """
        
        # Validate inputs
        if not text or not text.strip():
            return []
        
        chunk_size = chunk_size or self.default_chunk_size
        overlap = overlap or int(chunk_size * self.overlap_ratios.get(strategy, 0.2))
        document_metadata = document_metadata or {}
        
        # Validate chunk parameters
        chunk_size = max(self.min_chunk_size, min(self.max_chunk_size, chunk_size))
        overlap = max(0, min(overlap, chunk_size // 2))
        
        try:
            # Choose chunking method based on strategy
            if strategy == ChunkingStrategy.INTELLIGENT:
                chunks = await self._intelligent_chunking(text, chunk_size, overlap, document_metadata)
            elif strategy == ChunkingStrategy.SEMANTIC:
                chunks = await self._semantic_chunking(text, chunk_size, overlap, document_metadata)
            else:
                chunks = await self._langchain_chunking(text, strategy, chunk_size, overlap, document_metadata)
            
            # Post-process chunks
            processed_chunks = await self._post_process_chunks(chunks, preserve_metadata)
            
            logger.info(f"Created {len(processed_chunks)} chunks using {strategy.value} strategy")
            return processed_chunks
            
        except Exception as e:
            logger.error(f"Text chunking failed: {e}")
            # Fallback to simple chunking
            return await self._fallback_chunking(text, chunk_size, overlap, document_metadata)
    
    async def _intelligent_chunking(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        document_metadata: Dict[str, Any]
    ) -> List[TextChunk]:
        """
        Intelligent chunking that adapts to content type
        """
        
        # Detect content type
        content_type = self._detect_content_type(text, document_metadata)
        
        # Choose best strategy based on content
        if content_type == 'markdown':
            return await self._langchain_chunking(text, ChunkingStrategy.MARKDOWN, chunk_size, overlap, document_metadata)
        elif content_type == 'code':
            return await self._langchain_chunking(text, ChunkingStrategy.CODE, chunk_size, overlap, document_metadata)
        elif content_type == 'html':
            return await self._langchain_chunking(text, ChunkingStrategy.HTML, chunk_size, overlap, document_metadata)
        elif content_type == 'structured':
            return await self._semantic_chunking(text, chunk_size, overlap, document_metadata)
        else:
            return await self._langchain_chunking(text, ChunkingStrategy.RECURSIVE, chunk_size, overlap, document_metadata)
    
    async def _langchain_chunking(
        self,
        text: str,
        strategy: ChunkingStrategy,
        chunk_size: int,
        overlap: int,
        document_metadata: Dict[str, Any]
    ) -> List[TextChunk]:
        """
        Use LangChain text splitters for chunking
        """
        
        if not LANGCHAIN_AVAILABLE:
            return await self._fallback_chunking(text, chunk_size, overlap, document_metadata)
        
        try:
            # Create appropriate splitter
            splitter = self._create_langchain_splitter(strategy, chunk_size, overlap)
            
            # Create LangChain document
            doc = Document(page_content=text, metadata=document_metadata)
            
            # Split the document
            split_docs = splitter.split_documents([doc])
            
            # Convert to TextChunk objects
            chunks = []
            for i, split_doc in enumerate(split_docs):
                chunk_content = split_doc.page_content
                
                # Calculate positions
                start_pos = text.find(chunk_content)
                end_pos = start_pos + len(chunk_content) if start_pos != -1 else len(chunk_content)
                
                # Create chunk metadata
                chunk_id = hashlib.md5(f"{i}_{chunk_content[:50]}".encode()).hexdigest()
                
                metadata = ChunkMetadata(
                    chunk_id=chunk_id,
                    chunk_index=i,
                    start_position=start_pos,
                    end_position=end_pos,
                    chunk_size=len(chunk_content),
                    overlap_size=overlap if i > 0 else 0,
                    source_reference=document_metadata.get('source', ''),
                    document_metadata=split_doc.metadata,
                    chunking_strategy=strategy.value
                )
                
                chunks.append(TextChunk(content=chunk_content, metadata=metadata))
            
            return chunks
            
        except Exception as e:
            logger.error(f"LangChain chunking failed: {e}")
            return await self._fallback_chunking(text, chunk_size, overlap, document_metadata)
    
    def _create_langchain_splitter(self, strategy: ChunkingStrategy, chunk_size: int, overlap: int):
        """Create appropriate LangChain text splitter"""
        
        if strategy == ChunkingStrategy.RECURSIVE:
            return RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap,
                length_function=len,
                separators=["\n\n", "\n", " ", ""]
            )
        
        elif strategy == ChunkingStrategy.CHARACTER:
            return CharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap,
                separator="\n"
            )
        
        elif strategy == ChunkingStrategy.TOKEN:
            return TokenTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap
            )
        
        elif strategy == ChunkingStrategy.MARKDOWN:
            # First split by headers, then by size
            header_splitter = MarkdownHeaderTextSplitter(
                headers_to_split_on=[
                    ("#", "Header 1"),
                    ("##", "Header 2"),
                    ("###", "Header 3"),
                ]
            )
            return RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap
            )
        
        elif strategy == ChunkingStrategy.CODE:
            return PythonCodeTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap
            )
        
        elif strategy == ChunkingStrategy.HTML:
            return HTMLHeaderTextSplitter(
                headers_to_split_on=[
                    ("h1", "Header 1"),
                    ("h2", "Header 2"),
                    ("h3", "Header 3"),
                ]
            )
        
        else:
            # Default to recursive
            return RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap
            )
    
    async def _semantic_chunking(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        document_metadata: Dict[str, Any]
    ) -> List[TextChunk]:
        """
        Semantic chunking based on content structure
        """
        
        # Split by semantic boundaries
        semantic_separators = [
            "\n\n\n",  # Multiple line breaks
            "\n\n",    # Paragraph breaks
            ". ",      # Sentence endings
            "! ",      # Exclamation endings
            "? ",      # Question endings
        ]
        
        chunks = []
        current_chunk = ""
        chunk_index = 0
        position = 0
        
        sentences = self._split_by_semantic_boundaries(text, semantic_separators)
        
        for sentence in sentences:
            # Check if adding this sentence would exceed chunk size
            if len(current_chunk) + len(sentence) > chunk_size and current_chunk:
                # Create chunk
                chunk = self._create_chunk_from_text(
                    current_chunk.strip(),
                    chunk_index,
                    position - len(current_chunk),
                    position,
                    overlap if chunk_index > 0 else 0,
                    document_metadata,
                    ChunkingStrategy.SEMANTIC
                )
                chunks.append(chunk)
                
                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk, overlap)
                current_chunk = overlap_text + sentence
                chunk_index += 1
            else:
                current_chunk += sentence
            
            position += len(sentence)
        
        # Add final chunk
        if current_chunk.strip():
            chunk = self._create_chunk_from_text(
                current_chunk.strip(),
                chunk_index,
                position - len(current_chunk),
                position,
                overlap if chunk_index > 0 else 0,
                document_metadata,
                ChunkingStrategy.SEMANTIC
            )
            chunks.append(chunk)
        
        return chunks
    
    def _split_by_semantic_boundaries(self, text: str, separators: List[str]) -> List[str]:
        """Split text by semantic boundaries"""
        
        # Start with the text as one piece
        pieces = [text]
        
        # Apply each separator
        for separator in separators:
            new_pieces = []
            for piece in pieces:
                if separator in piece:
                    split_pieces = piece.split(separator)
                    for i, split_piece in enumerate(split_pieces):
                        if i < len(split_pieces) - 1:
                            split_piece += separator
                        if split_piece.strip():
                            new_pieces.append(split_piece)
                else:
                    new_pieces.append(piece)
            pieces = new_pieces
        
        return pieces
    
    def _get_overlap_text(self, text: str, overlap_size: int) -> str:
        """Get overlap text from the end of a chunk"""
        if overlap_size <= 0 or len(text) <= overlap_size:
            return ""
        
        # Try to find a good breaking point for overlap
        overlap_text = text[-overlap_size:]
        
        # Find the last sentence boundary
        for separator in [". ", "! ", "? ", "\n"]:
            last_sep = overlap_text.rfind(separator)
            if last_sep > overlap_size // 2:  # At least half the overlap
                return overlap_text[last_sep + len(separator):]
        
        return overlap_text
    
    def _create_chunk_from_text(
        self,
        content: str,
        index: int,
        start_pos: int,
        end_pos: int,
        overlap_size: int,
        document_metadata: Dict[str, Any],
        strategy: ChunkingStrategy
    ) -> TextChunk:
        """Create a TextChunk from text content"""
        
        chunk_id = hashlib.md5(f"{index}_{content[:50]}".encode()).hexdigest()
        
        metadata = ChunkMetadata(
            chunk_id=chunk_id,
            chunk_index=index,
            start_position=start_pos,
            end_position=end_pos,
            chunk_size=len(content),
            overlap_size=overlap_size,
            source_reference=document_metadata.get('source', ''),
            document_metadata=document_metadata,
            chunking_strategy=strategy.value
        )
        
        return TextChunk(content=content, metadata=metadata)
    
    async def _fallback_chunking(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        document_metadata: Dict[str, Any]
    ) -> List[TextChunk]:
        """
        Fallback chunking when LangChain is not available
        """
        
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            
            # Try to break at word boundary
            if end < len(text):
                while end > start and text[end] not in [' ', '\n', '.', '!', '?']:
                    end -= 1
                if end == start:  # No good break point found
                    end = min(start + chunk_size, len(text))
            
            chunk_content = text[start:end].strip()
            
            if chunk_content:
                chunk = self._create_chunk_from_text(
                    chunk_content,
                    chunk_index,
                    start,
                    end,
                    overlap if chunk_index > 0 else 0,
                    document_metadata,
                    ChunkingStrategy.CHARACTER
                )
                chunks.append(chunk)
                chunk_index += 1
            
            start = max(start + 1, end - overlap)
        
        return chunks
    
    async def _post_process_chunks(self, chunks: List[TextChunk], preserve_metadata: bool) -> List[TextChunk]:
        """Post-process chunks for quality and consistency"""
        
        processed_chunks = []
        
        for chunk in chunks:
            # Skip very small chunks
            if len(chunk.content.strip()) < self.min_chunk_size // 2:
                continue
            
            # Clean up content
            cleaned_content = chunk.content.strip()
            
            # Update chunk size in metadata
            chunk.metadata.chunk_size = len(cleaned_content)
            
            # Calculate confidence score based on chunk quality
            confidence = self._calculate_chunk_confidence(cleaned_content)
            chunk.metadata.confidence_score = confidence
            
            processed_chunks.append(TextChunk(content=cleaned_content, metadata=chunk.metadata))
        
        return processed_chunks
    
    def _calculate_chunk_confidence(self, content: str) -> float:
        """Calculate confidence score for a chunk"""
        
        # Base confidence
        confidence = 1.0
        
        # Penalize very short chunks
        if len(content) < 50:
            confidence *= 0.7
        
        # Penalize chunks with too many special characters
        special_char_ratio = sum(1 for c in content if not c.isalnum() and c not in ' \n\t.,!?') / len(content)
        if special_char_ratio > 0.3:
            confidence *= 0.8
        
        # Reward chunks with complete sentences
        sentence_endings = content.count('.') + content.count('!') + content.count('?')
        if sentence_endings > 0:
            confidence *= 1.1
        
        return min(1.0, confidence)
    
    def _detect_content_type(self, text: str, metadata: Dict[str, Any]) -> str:
        """Detect content type for intelligent chunking"""
        
        # Check metadata first
        content_type = metadata.get('content_type', '').lower()
        if content_type in ['markdown', 'code', 'html']:
            return content_type
        
        # Check file extension
        filename = metadata.get('filename', '').lower()
        if filename.endswith('.md'):
            return 'markdown'
        elif filename.endswith(('.py', '.js', '.java', '.cpp', '.c', '.html', '.css')):
            return 'code'
        elif filename.endswith('.html'):
            return 'html'
        
        # Analyze content
        if text.count('#') > 3 and text.count('\n') > 10:
            return 'markdown'
        elif text.count('<') > 5 and text.count('>') > 5:
            return 'html'
        elif any(keyword in text for keyword in ['def ', 'class ', 'import ', 'function', 'var ']):
            return 'code'
        elif text.count('\n\n') > text.count('\n') * 0.3:
            return 'structured'
        
        return 'text'

# Global text chunking service instance
complete_text_chunking_service = CompleteTextChunkingService()
