import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
import plotly.express as px
import plotly.graph_objects as go
from plotly.utils import PlotlyJSONEncoder
import base64
import io
import json

logger = logging.getLogger(__name__)

class StatisticalAnalysisService:
    """
    Service for performing advanced statistical analysis on datasets
    """
    
    def __init__(self, df: Optional[pd.DataFrame] = None):
        """
        Initialize the statistical analysis service
        
        Args:
            df: Optional DataFrame to analyze
        """
        self.df = df
    
    def set_dataframe(self, df: pd.DataFrame) -> None:
        """
        Set the DataFrame to analyze
        
        Args:
            df: DataFrame to analyze
        """
        self.df = df
    
    def get_summary_statistics(self, columns: Optional[List[str]] = None) -> Dict[str, Dict[str, float]]:
        """
        Get summary statistics for numeric columns
        
        Args:
            columns: Optional list of column names to analyze. If None, all numeric columns are analyzed.
            
        Returns:
            Dictionary mapping column names to their summary statistics
        """
        if self.df is None:
            raise ValueError("DataFrame not set")
        
        if columns is None:
            # Get all numeric columns
            columns = self.df.select_dtypes(include=np.number).columns.tolist()
        
        result = {}
        for col in columns:
            if col not in self.df.columns:
                continue
                
            if not pd.api.types.is_numeric_dtype(self.df[col]):
                continue
                
            # Calculate summary statistics
            data = self.df[col].dropna()
            if len(data) == 0:
                continue
                
            result[col] = {
                "count": len(data),
                "mean": float(data.mean()),
                "std": float(data.std()),
                "min": float(data.min()),
                "25%": float(data.quantile(0.25)),
                "50%": float(data.median()),
                "75%": float(data.quantile(0.75)),
                "max": float(data.max()),
                "skewness": float(stats.skew(data)),
                "kurtosis": float(stats.kurtosis(data))
            }
            
        return result

    def get_distribution_analysis(self, column: str) -> Dict[str, Any]:
        """
        Perform distribution analysis on a column
        """
        if column not in self.df.columns:
            raise ValueError(f"Column '{column}' not found in dataset")

        if not pd.api.types.is_numeric_dtype(self.df[column]):
            raise ValueError(f"Column '{column}' is not numeric")

        data = self.df[column].dropna()
        if len(data) == 0:
            raise ValueError(f"No valid data in column '{column}'")

        # Basic distribution statistics
        distribution_stats = {
            "column": column,
            "count": len(data),
            "mean": float(data.mean()),
            "median": float(data.median()),
            "mode": float(data.mode().iloc[0]) if len(data.mode()) > 0 else None,
            "std": float(data.std()),
            "variance": float(data.var()),
            "skewness": float(stats.skew(data)),
            "kurtosis": float(stats.kurtosis(data)),
            "min": float(data.min()),
            "max": float(data.max()),
            "range": float(data.max() - data.min()),
            "q1": float(data.quantile(0.25)),
            "q3": float(data.quantile(0.75)),
            "iqr": float(data.quantile(0.75) - data.quantile(0.25))
        }

        # Normality tests
        shapiro_stat, shapiro_p = stats.shapiro(data.sample(min(5000, len(data))))
        ks_stat, ks_p = stats.kstest(data, 'norm', args=(data.mean(), data.std()))

        normality_tests = {
            "shapiro_wilk": {
                "statistic": float(shapiro_stat),
                "p_value": float(shapiro_p),
                "is_normal": shapiro_p > 0.05
            },
            "kolmogorov_smirnov": {
                "statistic": float(ks_stat),
                "p_value": float(ks_p),
                "is_normal": ks_p > 0.05
            }
        }

        # Create histogram
        fig = px.histogram(
            x=data,
            nbins=30,
            title=f"Distribution of {column}",
            labels={'x': column, 'y': 'Frequency'}
        )
        fig.update_layout(showlegend=False)

        # Add normal distribution overlay
        x_range = np.linspace(data.min(), data.max(), 100)
        normal_curve = stats.norm.pdf(x_range, data.mean(), data.std())
        normal_curve = normal_curve * len(data) * (data.max() - data.min()) / 30  # Scale to histogram

        fig.add_trace(go.Scatter(
            x=x_range,
            y=normal_curve,
            mode='lines',
            name='Normal Distribution',
            line=dict(color='red', dash='dash')
        ))

        histogram_chart = {
            "plotly_json": json.loads(fig.to_json()),
            "image_base64": self._get_image_base64(fig)
        }

        return {
            "distribution_statistics": distribution_stats,
            "normality_tests": normality_tests,
            "histogram_chart": histogram_chart
        }

    def get_box_plot_analysis(self, columns: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Create box plot analysis for outlier detection
        """
        if columns is None:
            columns = [col for col in self.df.columns if pd.api.types.is_numeric_dtype(self.df[col])]

        if not columns:
            raise ValueError("No numeric columns found for box plot analysis")

        # Filter to existing numeric columns
        valid_columns = []
        for col in columns:
            if col in self.df.columns and pd.api.types.is_numeric_dtype(self.df[col]):
                valid_columns.append(col)

        if not valid_columns:
            raise ValueError("No valid numeric columns found")

        # Create box plot
        fig = go.Figure()

        outlier_summary = {}

        for col in valid_columns:
            data = self.df[col].dropna()
            if len(data) == 0:
                continue

            # Calculate outliers using IQR method
            q1 = data.quantile(0.25)
            q3 = data.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr

            outliers = data[(data < lower_bound) | (data > upper_bound)]

            outlier_summary[col] = {
                "total_values": len(data),
                "outlier_count": len(outliers),
                "outlier_percentage": len(outliers) / len(data) * 100 if len(data) > 0 else 0,
                "lower_bound": float(lower_bound),
                "upper_bound": float(upper_bound),
                "q1": float(q1),
                "q3": float(q3),
                "iqr": float(iqr)
            }

            fig.add_trace(go.Box(
                y=data,
                name=col,
                boxpoints='outliers'
            ))

        fig.update_layout(
            title="Box Plot Analysis - Outlier Detection",
            yaxis_title="Values",
            showlegend=True
        )

        box_plot_chart = {
            "plotly_json": json.loads(fig.to_json()),
            "image_base64": self._get_image_base64(fig)
        }

        return {
            "outlier_summary": outlier_summary,
            "box_plot_chart": box_plot_chart
        }

    def get_correlation_heatmap(self, columns: Optional[List[str]] = None, method: str = "pearson") -> Dict[str, Any]:
        """
        Create correlation heatmap visualization
        """
        if columns is None:
            columns = [col for col in self.df.columns if pd.api.types.is_numeric_dtype(self.df[col])]

        if len(columns) < 2:
            raise ValueError("At least 2 numeric columns required for correlation analysis")

        # Filter to existing numeric columns
        valid_columns = []
        for col in columns:
            if col in self.df.columns and pd.api.types.is_numeric_dtype(self.df[col]):
                valid_columns.append(col)

        if len(valid_columns) < 2:
            raise ValueError("At least 2 valid numeric columns required")

        # Calculate correlation matrix
        correlation_data = self.df[valid_columns].corr(method=method)

        # Create heatmap
        fig = px.imshow(
            correlation_data,
            text_auto=True,
            aspect="auto",
            title=f"Correlation Heatmap ({method.title()})",
            color_continuous_scale="RdBu_r",
            zmin=-1,
            zmax=1
        )

        fig.update_layout(
            xaxis_title="Variables",
            yaxis_title="Variables"
        )

        heatmap_chart = {
            "plotly_json": json.loads(fig.to_json()),
            "image_base64": self._get_image_base64(fig)
        }

        # Find strongest correlations (excluding self-correlations)
        correlation_pairs = []
        for i, col1 in enumerate(valid_columns):
            for j, col2 in enumerate(valid_columns):
                if i < j:  # Avoid duplicates and self-correlations
                    corr_value = correlation_data.loc[col1, col2]
                    if not pd.isna(corr_value):
                        correlation_pairs.append({
                            "column1": col1,
                            "column2": col2,
                            "correlation": float(corr_value),
                            "strength": self._get_correlation_strength(abs(corr_value))
                        })

        # Sort by absolute correlation value
        correlation_pairs.sort(key=lambda x: abs(x["correlation"]), reverse=True)

        return {
            "correlation_matrix": correlation_data.to_dict(),
            "method": method,
            "strongest_correlations": correlation_pairs[:10],  # Top 10
            "heatmap_chart": heatmap_chart
        }

    def create_pivot_table(self, index_col: str, columns_col: Optional[str] = None,
                          values_col: Optional[str] = None, aggfunc: str = "mean") -> Dict[str, Any]:
        """
        Create pivot table analysis
        """
        if index_col not in self.df.columns:
            raise ValueError(f"Index column '{index_col}' not found")

        # If no values column specified, use first numeric column
        if values_col is None:
            numeric_cols = [col for col in self.df.columns if pd.api.types.is_numeric_dtype(self.df[col])]
            if not numeric_cols:
                raise ValueError("No numeric columns found for pivot table values")
            values_col = numeric_cols[0]

        if values_col not in self.df.columns:
            raise ValueError(f"Values column '{values_col}' not found")

        # Create pivot table
        try:
            if columns_col and columns_col in self.df.columns:
                pivot_table = pd.pivot_table(
                    self.df,
                    index=index_col,
                    columns=columns_col,
                    values=values_col,
                    aggfunc=aggfunc,
                    fill_value=0
                )
            else:
                # Simple groupby if no columns specified
                pivot_table = self.df.groupby(index_col)[values_col].agg(aggfunc).reset_index()
                pivot_table = pivot_table.set_index(index_col)
        except Exception as e:
            raise ValueError(f"Error creating pivot table: {str(e)}")

        # Convert to dictionary for JSON serialization
        if isinstance(pivot_table, pd.DataFrame):
            pivot_data = pivot_table.to_dict('index')
        else:
            pivot_data = pivot_table.to_dict()

        # Create visualization
        if columns_col and columns_col in self.df.columns:
            # Heatmap for 2D pivot table
            fig = px.imshow(
                pivot_table,
                title=f"Pivot Table: {values_col} by {index_col} and {columns_col}",
                aspect="auto",
                text_auto=True
            )
        else:
            # Bar chart for 1D pivot table
            fig = px.bar(
                x=pivot_table.index,
                y=pivot_table.values,
                title=f"Pivot Table: {aggfunc.title()} of {values_col} by {index_col}",
                labels={'x': index_col, 'y': f"{aggfunc.title()} of {values_col}"}
            )

        pivot_chart = {
            "plotly_json": json.loads(fig.to_json()),
            "image_base64": self._get_image_base64(fig)
        }

        return {
            "pivot_table": pivot_data,
            "index_column": index_col,
            "columns_column": columns_col,
            "values_column": values_col,
            "aggregation_function": aggfunc,
            "pivot_chart": pivot_chart
        }

    def _get_correlation_strength(self, correlation: float) -> str:
        """Get correlation strength description"""
        abs_corr = abs(correlation)
        if abs_corr >= 0.8:
            return "Very Strong"
        elif abs_corr >= 0.6:
            return "Strong"
        elif abs_corr >= 0.4:
            return "Moderate"
        elif abs_corr >= 0.2:
            return "Weak"
        else:
            return "Very Weak"

    def _get_image_base64(self, fig) -> str:
        """Convert plotly figure to base64 image"""
        try:
            img_bytes = fig.to_image(format="png", width=800, height=600)
            img_base64 = base64.b64encode(img_bytes).decode()
            return img_base64
        except Exception as e:
            logger.warning(f"Failed to generate image: {e}")
            return ""
    
    def perform_hypothesis_test(
        self, 
        test_type: str, 
        column1: str, 
        column2: Optional[str] = None,
        alpha: float = 0.05,
        alternative: str = 'two-sided'
    ) -> Dict[str, Any]:
        """
        Perform a hypothesis test on the data
        
        Args:
            test_type: Type of test to perform ('ttest', 'anova', 'chi2', etc.)
            column1: First column to test
            column2: Second column to test (for two-sample tests)
            alpha: Significance level
            alternative: Alternative hypothesis ('two-sided', 'less', 'greater')
            
        Returns:
            Dictionary with test results
        """
        if self.df is None:
            raise ValueError("DataFrame not set")
            
        if column1 not in self.df.columns:
            raise ValueError(f"Column '{column1}' not found in DataFrame")
            
        if column2 is not None and column2 not in self.df.columns:
            raise ValueError(f"Column '{column2}' not found in DataFrame")
        
        # Prepare data
        data1 = self.df[column1].dropna()
        
        # Perform the appropriate test
        if test_type.lower() == 'ttest_1samp':
            # One-sample t-test
            if len(data1) < 2:
                raise ValueError("Not enough data for t-test")
                
            # Test if the mean of data1 is significantly different from 0
            stat, p_value = stats.ttest_1samp(data1, 0, alternative=alternative)
            
            return {
                "test_type": "One-sample t-test",
                "column": column1,
                "statistic": float(stat),
                "p_value": float(p_value),
                "alpha": alpha,
                "significant": p_value < alpha,
                "alternative": alternative,
                "null_hypothesis": "The mean is equal to 0",
                "alternative_hypothesis": f"The mean is {'not equal to' if alternative == 'two-sided' else 'less than' if alternative == 'less' else 'greater than'} 0"
            }
            
        elif test_type.lower() == 'ttest_ind':
            # Independent two-sample t-test
            if column2 is None:
                raise ValueError("Second column required for independent t-test")
                
            data2 = self.df[column2].dropna()
            
            if len(data1) < 2 or len(data2) < 2:
                raise ValueError("Not enough data for t-test")
                
            # Test if the means of two independent samples are significantly different
            stat, p_value = stats.ttest_ind(data1, data2, alternative=alternative)
            
            return {
                "test_type": "Independent two-sample t-test",
                "column1": column1,
                "column2": column2,
                "statistic": float(stat),
                "p_value": float(p_value),
                "alpha": alpha,
                "significant": p_value < alpha,
                "alternative": alternative,
                "null_hypothesis": "The means are equal",
                "alternative_hypothesis": f"The mean of {column1} is {'not equal to' if alternative == 'two-sided' else 'less than' if alternative == 'less' else 'greater than'} the mean of {column2}"
            }
            
        elif test_type.lower() == 'chi2':
            # Chi-square test of independence
            if column2 is None:
                raise ValueError("Second column required for chi-square test")
                
            # Create contingency table
            contingency = pd.crosstab(self.df[column1], self.df[column2])
            
            # Perform chi-square test
            chi2, p_value, dof, expected = stats.chi2_contingency(contingency)
            
            return {
                "test_type": "Chi-square test of independence",
                "column1": column1,
                "column2": column2,
                "statistic": float(chi2),
                "p_value": float(p_value),
                "degrees_of_freedom": int(dof),
                "alpha": alpha,
                "significant": p_value < alpha,
                "null_hypothesis": f"{column1} and {column2} are independent",
                "alternative_hypothesis": f"{column1} and {column2} are not independent"
            }
            
        elif test_type.lower() == 'anova':
            # One-way ANOVA
            if column2 is None:
                raise ValueError("Second column required for ANOVA")
                
            # Group data by the second column (categorical)
            groups = []
            labels = []
            
            for group_name, group_data in self.df.groupby(column2)[column1]:
                if len(group_data) > 0:
                    groups.append(group_data.dropna())
                    labels.append(group_name)
            
            if len(groups) < 2:
                raise ValueError("Not enough groups for ANOVA")
                
            # Perform one-way ANOVA
            stat, p_value = stats.f_oneway(*groups)
            
            return {
                "test_type": "One-way ANOVA",
                "value_column": column1,
                "group_column": column2,
                "groups": labels,
                "statistic": float(stat),
                "p_value": float(p_value),
                "alpha": alpha,
                "significant": p_value < alpha,
                "null_hypothesis": "All group means are equal",
                "alternative_hypothesis": "At least one group mean is different"
            }
            
        else:
            raise ValueError(f"Unsupported test type: {test_type}")
    
    def correlation_analysis(
        self, 
        columns: Optional[List[str]] = None,
        method: str = 'pearson'
    ) -> Dict[str, Any]:
        """
        Perform correlation analysis on numeric columns
        
        Args:
            columns: Optional list of column names to analyze. If None, all numeric columns are analyzed.
            method: Correlation method ('pearson', 'spearman', or 'kendall')
            
        Returns:
            Dictionary with correlation matrix and p-values
        """
        if self.df is None:
            raise ValueError("DataFrame not set")
            
        if columns is None:
            # Get all numeric columns
            columns = self.df.select_dtypes(include=np.number).columns.tolist()
        else:
            # Filter out non-numeric columns
            columns = [col for col in columns if col in self.df.columns and pd.api.types.is_numeric_dtype(self.df[col])]
            
        if len(columns) < 2:
            raise ValueError("Need at least two numeric columns for correlation analysis")
            
        # Calculate correlation matrix
        corr_matrix = self.df[columns].corr(method=method)
        
        # Calculate p-values
        p_values = pd.DataFrame(np.zeros_like(corr_matrix), index=corr_matrix.index, columns=corr_matrix.columns)
        
        for i, col1 in enumerate(columns):
            for j, col2 in enumerate(columns):
                if i == j:
                    p_values.loc[col1, col2] = 0.0
                else:
                    if method == 'pearson':
                        r, p = stats.pearsonr(self.df[col1].dropna(), self.df[col2].dropna())
                    elif method == 'spearman':
                        r, p = stats.spearmanr(self.df[col1].dropna(), self.df[col2].dropna())
                    elif method == 'kendall':
                        r, p = stats.kendalltau(self.df[col1].dropna(), self.df[col2].dropna())
                    else:
                        raise ValueError(f"Unsupported correlation method: {method}")
                        
                    p_values.loc[col1, col2] = p
        
        # Convert to dictionaries for JSON serialization
        corr_dict = corr_matrix.to_dict(orient='index')
        p_values_dict = p_values.to_dict(orient='index')
        
        # Find strongest correlations
        strongest_correlations = []
        for col1 in columns:
            for col2 in columns:
                if col1 != col2:
                    corr_value = corr_matrix.loc[col1, col2]
                    p_value = p_values.loc[col1, col2]
                    
                    strongest_correlations.append({
                        "column1": col1,
                        "column2": col2,
                        "correlation": float(corr_value),
                        "p_value": float(p_value),
                        "significant": p_value < 0.05
                    })
        
        # Sort by absolute correlation value
        strongest_correlations.sort(key=lambda x: abs(x["correlation"]), reverse=True)
        
        return {
            "method": method,
            "correlation_matrix": corr_dict,
            "p_values": p_values_dict,
            "strongest_correlations": strongest_correlations[:10]  # Top 10 strongest correlations
        }
    
    def detect_outliers(
        self, 
        column: str,
        method: str = 'zscore',
        threshold: float = 3.0
    ) -> Dict[str, Any]:
        """
        Detect outliers in a numeric column
        
        Args:
            column: Column name to analyze
            method: Method for outlier detection ('zscore', 'iqr', or 'percentile')
            threshold: Threshold for outlier detection (z-score or IQR multiplier)
            
        Returns:
            Dictionary with outlier information
        """
        if self.df is None:
            raise ValueError("DataFrame not set")
            
        if column not in self.df.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
            
        if not pd.api.types.is_numeric_dtype(self.df[column]):
            raise ValueError(f"Column '{column}' is not numeric")
            
        data = self.df[column].dropna()
        
        outlier_indices = []
        outlier_values = []
        
        if method == 'zscore':
            # Z-score method
            z_scores = np.abs(stats.zscore(data))
            outlier_mask = z_scores > threshold
            outlier_indices = np.where(outlier_mask)[0].tolist()
            outlier_values = data.iloc[outlier_indices].tolist()
            
            return {
                "method": "Z-score",
                "threshold": threshold,
                "column": column,
                "total_values": len(data),
                "outlier_count": len(outlier_indices),
                "outlier_percentage": len(outlier_indices) / len(data) * 100 if len(data) > 0 else 0,
                "outlier_indices": outlier_indices[:100],  # Limit to 100 indices
                "outlier_values": outlier_values[:100],  # Limit to 100 values
                "min_non_outlier": float(data[~outlier_mask].min()) if any(~outlier_mask) else None,
                "max_non_outlier": float(data[~outlier_mask].max()) if any(~outlier_mask) else None
            }
            
        elif method == 'iqr':
            # IQR method
            q1 = data.quantile(0.25)
            q3 = data.quantile(0.75)
            iqr = q3 - q1
            
            lower_bound = q1 - threshold * iqr
            upper_bound = q3 + threshold * iqr
            
            outlier_mask = (data < lower_bound) | (data > upper_bound)
            outlier_indices = np.where(outlier_mask)[0].tolist()
            outlier_values = data.iloc[outlier_indices].tolist()
            
            return {
                "method": "IQR",
                "threshold": threshold,
                "column": column,
                "total_values": len(data),
                "outlier_count": len(outlier_indices),
                "outlier_percentage": len(outlier_indices) / len(data) * 100 if len(data) > 0 else 0,
                "lower_bound": float(lower_bound),
                "upper_bound": float(upper_bound),
                "outlier_indices": outlier_indices[:100],  # Limit to 100 indices
                "outlier_values": outlier_values[:100]  # Limit to 100 values
            }
            
        elif method == 'percentile':
            # Percentile method
            lower_percentile = (100 - threshold) / 2
            upper_percentile = 100 - lower_percentile
            
            lower_bound = data.quantile(lower_percentile / 100)
            upper_bound = data.quantile(upper_percentile / 100)
            
            outlier_mask = (data < lower_bound) | (data > upper_bound)
            outlier_indices = np.where(outlier_mask)[0].tolist()
            outlier_values = data.iloc[outlier_indices].tolist()
            
            return {
                "method": "Percentile",
                "threshold": threshold,
                "column": column,
                "total_values": len(data),
                "outlier_count": len(outlier_indices),
                "outlier_percentage": len(outlier_indices) / len(data) * 100 if len(data) > 0 else 0,
                "lower_bound": float(lower_bound),
                "upper_bound": float(upper_bound),
                "outlier_indices": outlier_indices[:100],  # Limit to 100 indices
                "outlier_values": outlier_values[:100]  # Limit to 100 values
            }
            
        else:
            raise ValueError(f"Unsupported outlier detection method: {method}")
    
    def time_series_analysis(
        self,
        value_column: str,
        date_column: str,
        frequency: Optional[str] = None,
        analysis_type: str = 'decomposition'
    ) -> Dict[str, Any]:
        """
        Perform time series analysis
        
        Args:
            value_column: Column containing the values to analyze
            date_column: Column containing the dates
            frequency: Time series frequency (e.g., 'D', 'M', 'Y')
            analysis_type: Type of analysis ('decomposition', 'autocorrelation', 'seasonality')
            
        Returns:
            Dictionary with time series analysis results
        """
        if self.df is None:
            raise ValueError("DataFrame not set")
            
        if value_column not in self.df.columns:
            raise ValueError(f"Column '{value_column}' not found in DataFrame")
            
        if date_column not in self.df.columns:
            raise ValueError(f"Column '{date_column}' not found in DataFrame")
            
        # Ensure date column is datetime type
        try:
            date_series = pd.to_datetime(self.df[date_column])
        except:
            raise ValueError(f"Column '{date_column}' cannot be converted to datetime")
            
        # Create time series
        ts_df = self.df[[date_column, value_column]].copy()
        ts_df['date'] = date_series
        ts_df = ts_df.sort_values('date')
        
        # Drop rows with missing values
        ts_df = ts_df.dropna(subset=[value_column])
        
        if len(ts_df) < 4:
            raise ValueError("Not enough data points for time series analysis")
            
        # Set date as index
        ts_df = ts_df.set_index('date')
        
        # Resample if frequency is provided
        if frequency is not None:
            ts_df = ts_df.resample(frequency).mean()
            
        # Fill missing values after resampling
        ts_df = ts_df.fillna(method='ffill')
        
        if analysis_type == 'decomposition':
            try:
                from statsmodels.tsa.seasonal import seasonal_decompose
                
                # Perform seasonal decomposition
                result = seasonal_decompose(ts_df[value_column], model='additive')
                
                # Convert to lists for JSON serialization
                trend = result.trend.dropna().tolist()
                seasonal = result.seasonal.dropna().tolist()
                residual = result.resid.dropna().tolist()
                
                # Get dates as strings
                dates = result.trend.dropna().index.strftime('%Y-%m-%d').tolist()
                
                return {
                    "analysis_type": "Seasonal Decomposition",
                    "value_column": value_column,
                    "date_column": date_column,
                    "frequency": frequency,
                    "dates": dates,
                    "trend": trend,
                    "seasonal": seasonal,
                    "residual": residual,
                    "original": ts_df[value_column].loc[result.trend.dropna().index].tolist()
                }
            except Exception as e:
                logger.error(f"Error in seasonal decomposition: {str(e)}")
                raise ValueError(f"Error in seasonal decomposition: {str(e)}")
                
        elif analysis_type == 'autocorrelation':
            try:
                from statsmodels.tsa.stattools import acf, pacf
                
                # Calculate autocorrelation and partial autocorrelation
                acf_values = acf(ts_df[value_column], nlags=min(40, len(ts_df) // 2))
                pacf_values = pacf(ts_df[value_column], nlags=min(40, len(ts_df) // 2))
                
                return {
                    "analysis_type": "Autocorrelation Analysis",
                    "value_column": value_column,
                    "date_column": date_column,
                    "frequency": frequency,
                    "lags": list(range(len(acf_values))),
                    "acf": acf_values.tolist(),
                    "pacf": pacf_values.tolist()
                }
            except Exception as e:
                logger.error(f"Error in autocorrelation analysis: {str(e)}")
                raise ValueError(f"Error in autocorrelation analysis: {str(e)}")
                
        elif analysis_type == 'seasonality':
            # Simple seasonality detection
            if frequency is None:
                raise ValueError("Frequency is required for seasonality detection")
                
            # Calculate rolling statistics
            rolling_mean = ts_df[value_column].rolling(window=4).mean()
            rolling_std = ts_df[value_column].rolling(window=4).std()
            
            # Check for stationarity
            from statsmodels.tsa.stattools import adfuller
            
            adf_result = adfuller(ts_df[value_column].dropna())
            is_stationary = adf_result[1] < 0.05
            
            # Detect seasonality by checking autocorrelation at different lags
            from statsmodels.tsa.stattools import acf
            
            acf_values = acf(ts_df[value_column].dropna(), nlags=min(40, len(ts_df) // 2))
            
            # Find peaks in autocorrelation
            from scipy.signal import find_peaks
            
            peaks, _ = find_peaks(acf_values)
            
            if len(peaks) > 0:
                seasonal_period = peaks[0]
            else:
                seasonal_period = None
                
            return {
                "analysis_type": "Seasonality Detection",
                "value_column": value_column,
                "date_column": date_column,
                "frequency": frequency,
                "is_stationary": bool(is_stationary),
                "adf_p_value": float(adf_result[1]),
                "detected_seasonal_period": int(seasonal_period) if seasonal_period is not None else None,
                "rolling_mean": rolling_mean.dropna().tolist(),
                "rolling_std": rolling_std.dropna().tolist(),
                "dates": rolling_mean.dropna().index.strftime('%Y-%m-%d').tolist()
            }
        else:
            raise ValueError(f"Unsupported time series analysis type: {analysis_type}")
