"""
Vector Service for Document Retrieval with Source Attribution
Integrates with existing database and document processing
"""

import os
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from datetime import datetime
import hashlib
import re
from dataclasses import dataclass
from enum import Enum

# Disable sentence transformers to save memory (use lightweight alternatives)
EMBEDDINGS_AVAILABLE = False
print("Info: Using lightweight text matching instead of embeddings to save memory.")

class SearchMethod(Enum):
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"

@dataclass
class SearchResult:
    chunk_id: str
    text: str
    score: float
    rank: int
    method: str
    metadata: Dict[str, Any] = None

# Import database models
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import models
from database import get_db


class VectorService:
    """
    Vector service for document retrieval with source attribution
    """

    def __init__(self):
        self.cache_dir = os.path.join(os.path.dirname(__file__), '..', 'vector_cache')
        os.makedirs(self.cache_dir, exist_ok=True)

        # Initialize embedding model if available
        self.embedding_model = None
        if EMBEDDINGS_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                print("✅ Embedding model loaded for vector search")
            except Exception as e:
                print(f"⚠️ Failed to load embedding model: {e}")
                self.embedding_model = None
    
    def create_embeddings_for_dataset(self, dataset_id: int, db: Session) -> Dict[str, Any]:
        """
        Create embeddings for a dataset's chunks
        """
        try:
            # Get dataset from database
            dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
            if not dataset:
                return {"error": "Dataset not found"}
            
            # Check if dataset has document content
            if dataset.content_type != 'document':
                return {"error": "Dataset is not a document type"}
            
            # Get document chunks from embeddings_data
            if not dataset.embeddings_data:
                return {"error": "No embeddings data found for dataset"}
            
            try:
                embeddings_data = json.loads(dataset.embeddings_data)
            except json.JSONDecodeError:
                return {"error": "Invalid embeddings data format"}
            
            # Extract text chunks with source attribution
            chunks = []
            chunk_metadata = []

            for item in embeddings_data:
                if isinstance(item, dict) and 'text' in item:
                    chunks.append(item['text'])
                    metadata = {
                        'chunk_id': item.get('chunk_id', len(chunks) - 1),
                        'chunk_type': item.get('chunk_type', 'unknown'),
                        'word_count': item.get('word_count', 0),
                        'char_count': item.get('char_count', 0),
                        # Source attribution fields
                        'source_document': item.get('source_document'),
                        'line_start': item.get('line_start'),
                        'line_end': item.get('line_end'),
                        'char_start': item.get('char_start'),
                        'char_end': item.get('char_end'),
                        'page_number': item.get('page_number'),
                        'section': item.get('section')
                    }
                    chunk_metadata.append(metadata)

            if not chunks:
                return {"error": "No text chunks found in dataset"}

            # Generate embeddings if model is available
            embeddings = []
            if self.embedding_model:
                try:
                    embeddings = self.embedding_model.encode(chunks).tolist()
                    print(f"✅ Generated embeddings for {len(chunks)} chunks")
                except Exception as e:
                    print(f"⚠️ Failed to generate embeddings: {e}")
                    embeddings = []
            
            # Cache the indexed data
            cache_data = {
                'dataset_id': dataset_id,
                'chunks': chunks,
                'metadata': chunk_metadata,
                'embeddings': embeddings,
                'indexed_at': datetime.utcnow().isoformat(),
                'total_chunks': len(chunks),
                'has_embeddings': len(embeddings) > 0
            }
            
            cache_file = os.path.join(self.cache_dir, f'dataset_{dataset_id}_vectors.json')
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f)
            
            return {
                "success": True,
                "dataset_id": dataset_id,
                "total_chunks": len(chunks),
                "indexed_at": cache_data['indexed_at']
            }
            
        except Exception as e:
            return {"error": f"Failed to create embeddings: {str(e)}"}
    
    def search_dataset(self, dataset_id: int, query: str,
                      search_config: Optional[Dict[str, Any]] = None,
                      db: Optional[Session] = None) -> Dict[str, Any]:
        """
        Search within a specific dataset using semantic and keyword search
        """
        try:
            # Load cached vectors if available
            cache_file = os.path.join(self.cache_dir, f'dataset_{dataset_id}_vectors.json')
            if not os.path.exists(cache_file):
                # Try to create embeddings first
                if db:
                    result = self.create_embeddings_for_dataset(dataset_id, db)
                    if "error" in result:
                        return result
                else:
                    return {"error": "Dataset not indexed and no database session provided"}

            # Load cached data
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)

            # Verify dataset ID matches
            if cache_data['dataset_id'] != dataset_id:
                return {"error": "Cache mismatch for dataset"}

            # Get search parameters
            max_results = search_config.get('max_results', 5) if search_config else 5
            min_score_threshold = search_config.get('min_score_threshold', 0.1) if search_config else 0.1

            # Determine search method
            search_method = SearchMethod.HYBRID
            if search_config and 'method' in search_config:
                method_str = search_config['method'].lower()
                if method_str == 'semantic':
                    search_method = SearchMethod.SEMANTIC
                elif method_str == 'keyword':
                    search_method = SearchMethod.KEYWORD

            # Perform search
            results = self._search_chunks(
                query=query,
                chunks=cache_data['chunks'],
                metadata=cache_data['metadata'],
                embeddings=cache_data.get('embeddings', []),
                method=search_method,
                max_results=max_results,
                min_score_threshold=min_score_threshold
            )
            
            return {
                "success": True,
                "query": query,
                "dataset_id": dataset_id,
                "results": results,
                "search_config": {
                    "method": search_method.value,
                    "max_results": max_results,
                    "min_score_threshold": min_score_threshold
                }
            }
            
        except Exception as e:
            return {"error": f"Search failed: {str(e)}"}

    def _search_chunks(self, query: str, chunks: List[str], metadata: List[Dict],
                      embeddings: List[List[float]], method: SearchMethod,
                      max_results: int, min_score_threshold: float) -> List[Dict[str, Any]]:
        """
        Search chunks using semantic and/or keyword matching
        """
        results = []

        # Semantic search if embeddings are available
        if method in [SearchMethod.SEMANTIC, SearchMethod.HYBRID] and embeddings and self.embedding_model:
            try:
                # Generate query embedding
                query_embedding = self.embedding_model.encode([query])[0]

                # Calculate cosine similarity
                for i, chunk_embedding in enumerate(embeddings):
                    if i < len(chunks) and i < len(metadata):
                        similarity = self._cosine_similarity(query_embedding, chunk_embedding)
                        if similarity >= min_score_threshold:
                            results.append({
                                'chunk_id': f'chunk_{i}',
                                'text': chunks[i],
                                'score': similarity,
                                'rank': len(results) + 1,
                                'method': 'semantic',
                                'metadata': metadata[i],
                                # Source attribution fields
                                'source_document': metadata[i].get('source_document'),
                                'line_start': metadata[i].get('line_start'),
                                'line_end': metadata[i].get('line_end'),
                                'char_start': metadata[i].get('char_start'),
                                'char_end': metadata[i].get('char_end'),
                                'page_number': metadata[i].get('page_number'),
                                'section': metadata[i].get('section'),
                                'chunk_type': metadata[i].get('chunk_type')
                            })
            except Exception as e:
                print(f"⚠️ Semantic search failed: {e}")

        # Keyword search
        if method in [SearchMethod.KEYWORD, SearchMethod.HYBRID]:
            query_words = set(query.lower().split())

            for i, chunk in enumerate(chunks):
                if i < len(metadata):
                    chunk_words = set(chunk.lower().split())
                    # Simple keyword matching score
                    common_words = query_words.intersection(chunk_words)
                    if common_words:
                        score = len(common_words) / len(query_words)
                        if score >= min_score_threshold:
                            # Check if we already have this chunk from semantic search
                            existing = next((r for r in results if r['chunk_id'] == f'chunk_{i}'), None)
                            if existing:
                                # Combine scores for hybrid search
                                if method == SearchMethod.HYBRID:
                                    existing['score'] = (existing['score'] + score) / 2
                                    existing['method'] = 'hybrid'
                            else:
                                results.append({
                                    'chunk_id': f'chunk_{i}',
                                    'text': chunk,
                                    'score': score,
                                    'rank': len(results) + 1,
                                    'method': 'keyword',
                                    'metadata': metadata[i],
                                    # Source attribution fields
                                    'source_document': metadata[i].get('source_document'),
                                    'line_start': metadata[i].get('line_start'),
                                    'line_end': metadata[i].get('line_end'),
                                    'char_start': metadata[i].get('char_start'),
                                    'char_end': metadata[i].get('char_end'),
                                    'page_number': metadata[i].get('page_number'),
                                    'section': metadata[i].get('section'),
                                    'chunk_type': metadata[i].get('chunk_type')
                                })

        # Sort by score and limit results
        results.sort(key=lambda x: x['score'], reverse=True)
        results = results[:max_results]

        # Update ranks
        for i, result in enumerate(results):
            result['rank'] = i + 1

        return results

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors
        """
        try:
            import numpy as np
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)

            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)
        except Exception:
            return 0.0
    
    def get_dataset_vector_status(self, dataset_id: int) -> Dict[str, Any]:
        """
        Get vector indexing status for a dataset
        """
        cache_file = os.path.join(self.cache_dir, f'dataset_{dataset_id}_vectors.json')
        
        if not os.path.exists(cache_file):
            return {
                "indexed": False,
                "dataset_id": dataset_id,
                "message": "Dataset not indexed"
            }
        
        try:
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            
            return {
                "indexed": True,
                "dataset_id": dataset_id,
                "total_chunks": cache_data['total_chunks'],
                "indexed_at": cache_data['indexed_at'],
                "cache_file": cache_file
            }
            
        except Exception as e:
            return {
                "indexed": False,
                "dataset_id": dataset_id,
                "error": f"Failed to read cache: {str(e)}"
            }
    
    def delete_dataset_vectors(self, dataset_id: int) -> Dict[str, Any]:
        """
        Delete vector cache for a dataset
        """
        cache_file = os.path.join(self.cache_dir, f'dataset_{dataset_id}_vectors.json')
        
        try:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                return {
                    "success": True,
                    "dataset_id": dataset_id,
                    "message": "Vector cache deleted"
                }
            else:
                return {
                    "success": True,
                    "dataset_id": dataset_id,
                    "message": "No vector cache found"
                }
                
        except Exception as e:
            return {
                "success": False,
                "dataset_id": dataset_id,
                "error": f"Failed to delete cache: {str(e)}"
            }
    
    def list_indexed_datasets(self) -> Dict[str, Any]:
        """
        List all datasets that have been indexed
        """
        try:
            indexed_datasets = []
            
            for filename in os.listdir(self.cache_dir):
                if filename.startswith('dataset_') and filename.endswith('_vectors.json'):
                    # Extract dataset ID from filename
                    dataset_id_str = filename.replace('dataset_', '').replace('_vectors.json', '')
                    try:
                        dataset_id = int(dataset_id_str)
                        status = self.get_dataset_vector_status(dataset_id)
                        if status['indexed']:
                            indexed_datasets.append(status)
                    except ValueError:
                        continue
            
            return {
                "success": True,
                "total_indexed": len(indexed_datasets),
                "datasets": indexed_datasets
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to list indexed datasets: {str(e)}"
            }


# Global service instance
vector_service = VectorService()
