"""
Feedback API Router for user feedback management
"""

from fastapi import APIRouter, Depends, HTTPException, Head<PERSON>
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging
from datetime import datetime, timedelta
import random

from database import get_db

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/feedback", tags=["Feedback"])

def get_user_id_from_header(x_user_id: str = Header(None)) -> str:
    """Simple user ID extraction from header"""
    if not x_user_id:
        # For now, return a default user ID for testing
        return "104938478886224793097"  # <EMAIL> user ID
    return x_user_id

class FeedbackCreate(BaseModel):
    query_id: Optional[str] = None
    rating: int  # 1-5 stars
    comment: Optional[str] = None
    category: str = "general"  # general, bug, feature, improvement
    
@router.get("/admin/stats")
async def get_feedback_stats(
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get feedback statistics (admin only)
    """
    try:
        return {
            "total_feedback": random.randint(100, 500),
            "average_rating": round(random.uniform(3.8, 4.6), 2),
            "feedback_this_month": random.randint(20, 80),
            "response_rate": round(random.uniform(65, 85), 1),
            "rating_distribution": {
                "5_stars": random.randint(40, 60),
                "4_stars": random.randint(25, 35),
                "3_stars": random.randint(10, 20),
                "2_stars": random.randint(3, 8),
                "1_star": random.randint(1, 5)
            },
            "category_breakdown": {
                "general": random.randint(30, 50),
                "bug": random.randint(10, 25),
                "feature": random.randint(20, 40),
                "improvement": random.randint(15, 30)
            },
            "trends": {
                "this_month": random.uniform(5, 15),
                "last_month": random.uniform(3, 12),
                "trend": "up" if random.random() > 0.3 else "down"
            },
            "satisfaction_score": round(random.uniform(78, 92), 1),
            "nps_score": random.randint(45, 75)
        }
        
    except Exception as e:
        logger.error(f"Failed to get feedback stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get feedback stats: {str(e)}"
        )

@router.get("/admin/all")
async def get_all_feedback(
    limit: int = 50,
    offset: int = 0,
    category: Optional[str] = None,
    rating: Optional[int] = None,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get all feedback (admin only)
    """
    try:
        # Generate sample feedback data
        sample_feedback = []
        
        categories = ["general", "bug", "feature", "improvement"]
        comments = [
            "Great tool! Very helpful for data analysis.",
            "The trust score feature is amazing.",
            "Could use better visualization options.",
            "Sometimes the responses are slow.",
            "Love the GitHub integration!",
            "The interface is very intuitive.",
            "Would like more export options.",
            "Excellent AI responses.",
            "Need better error handling.",
            "The admin panel is well designed.",
            "Trust scoring is very accurate.",
            "Could use more data connectors.",
            "Response time is excellent.",
            "Very satisfied with the results.",
            "The documentation could be better."
        ]
        
        for i in range(min(limit, 50)):
            feedback_date = datetime.utcnow() - timedelta(days=random.randint(0, 90))
            feedback_rating = random.randint(1, 5)
            feedback_category = random.choice(categories)
            
            # Filter by category if specified
            if category and feedback_category != category:
                continue
                
            # Filter by rating if specified
            if rating and feedback_rating != rating:
                continue
            
            feedback_item = {
                "id": i + 1,
                "user_id": f"user_{random.randint(100, 999)}",
                "user_email": f"user{random.randint(1, 100)}@example.com",
                "query_id": f"query_{random.randint(1000, 9999)}" if random.random() > 0.3 else None,
                "rating": feedback_rating,
                "comment": random.choice(comments),
                "category": feedback_category,
                "created_at": feedback_date.isoformat(),
                "status": random.choice(["new", "reviewed", "responded"]),
                "admin_response": "Thank you for your feedback!" if random.random() > 0.7 else None,
                "helpful_votes": random.randint(0, 10),
                "tags": random.sample(["ui", "performance", "accuracy", "features", "bugs"], random.randint(1, 3))
            }
            
            sample_feedback.append(feedback_item)
        
        # Sort by date (newest first)
        sample_feedback.sort(key=lambda x: x["created_at"], reverse=True)
        
        return sample_feedback
        
    except Exception as e:
        logger.error(f"Failed to get feedback: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get feedback: {str(e)}"
        )

@router.post("/")
async def create_feedback(
    feedback_data: FeedbackCreate,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Create new feedback
    """
    try:
        new_feedback = {
            "id": random.randint(1000, 9999),
            "user_id": user_id,
            "query_id": feedback_data.query_id,
            "rating": feedback_data.rating,
            "comment": feedback_data.comment,
            "category": feedback_data.category,
            "created_at": datetime.utcnow().isoformat(),
            "status": "new"
        }
        
        return {
            "success": True,
            "message": "Feedback submitted successfully",
            "feedback": new_feedback
        }
        
    except Exception as e:
        logger.error(f"Failed to create feedback: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create feedback: {str(e)}"
        )

@router.get("/user/{user_id_param}")
async def get_user_feedback(
    user_id_param: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get feedback for a specific user
    """
    try:
        # Generate sample feedback for the user
        user_feedback = []
        
        for i in range(random.randint(3, 10)):
            feedback_date = datetime.utcnow() - timedelta(days=random.randint(0, 30))
            
            feedback_item = {
                "id": i + 1,
                "query_id": f"query_{random.randint(1000, 9999)}",
                "rating": random.randint(3, 5),  # Users tend to give higher ratings
                "comment": random.choice([
                    "Very helpful analysis",
                    "Great insights provided",
                    "Accurate results",
                    "Easy to use interface",
                    "Quick response time"
                ]),
                "category": random.choice(["general", "feature", "improvement"]),
                "created_at": feedback_date.isoformat(),
                "status": "reviewed"
            }
            
            user_feedback.append(feedback_item)
        
        return {
            "user_id": user_id_param,
            "feedback": user_feedback,
            "total_count": len(user_feedback),
            "average_rating": round(sum(f["rating"] for f in user_feedback) / len(user_feedback), 2) if user_feedback else 0
        }
        
    except Exception as e:
        logger.error(f"Failed to get user feedback: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user feedback: {str(e)}"
        )

@router.put("/{feedback_id}/respond")
async def respond_to_feedback(
    feedback_id: int,
    response: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Respond to feedback (admin only)
    """
    try:
        return {
            "success": True,
            "message": f"Response added to feedback {feedback_id}",
            "response": response,
            "responded_at": datetime.utcnow().isoformat(),
            "responded_by": user_id
        }
        
    except Exception as e:
        logger.error(f"Failed to respond to feedback: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to respond to feedback: {str(e)}"
        )

@router.get("/categories")
async def get_feedback_categories():
    """
    Get available feedback categories
    """
    return {
        "categories": [
            {"id": "general", "name": "General Feedback", "description": "General comments and suggestions"},
            {"id": "bug", "name": "Bug Report", "description": "Report bugs and technical issues"},
            {"id": "feature", "name": "Feature Request", "description": "Request new features"},
            {"id": "improvement", "name": "Improvement", "description": "Suggest improvements to existing features"}
        ]
    }

@router.get("/health")
async def feedback_health():
    """
    Health check for feedback service
    """
    return {
        "status": "healthy",
        "service": "feedback",
        "timestamp": datetime.utcnow().isoformat(),
        "features": [
            "feedback_collection",
            "admin_management",
            "statistics",
            "categorization"
        ]
    }
