name: aithentiq
services:
- name: web
  source_dir: /
  github:
    repo: your-username/aithentiq
    branch: main
  build_command: npm run install:all && npm run build:frontend
  run_command: npm run start:production
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  routes:
  - path: /
  envs:
  - key: NODE_ENV
    value: production
  - key: DATABASE_URL
    value: ${aithentiq-db.DATABASE_URL}
  - key: OPENAI_API_KEY
    value: ${OPENAI_API_KEY}
    type: SECRET
  - key: STRIPE_API_KEY
    value: ${STRIPE_API_KEY}
    type: SECRET
  - key: STRIPE_WEBHOOK_SECRET
    value: ${STRIPE_WEBHOOK_SECRET}
    type: SECRET

databases:
- engine: PG
  name: aithentiq-db
  num_nodes: 1
  size: db-s-dev-database
  version: "15"
