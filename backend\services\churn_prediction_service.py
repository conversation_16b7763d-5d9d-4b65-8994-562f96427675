"""
Churn Prediction Service
Predicts user churn probability using machine learning models
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc
import json
import logging

# ML imports
try:
    import xgboost as xgb
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import classification_report, roc_auc_score
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    logging.warning("ML libraries not available. Churn prediction will use fallback methods.")
    # Create dummy classes for fallback
    class StandardScaler:
        def __init__(self): pass
        def fit_transform(self, data): return data
        def transform(self, data): return data

import models
from models_predictive_analytics import UserBehaviorLog, ChurnPrediction, PredictionFeedback


class ChurnPredictionService:
    """Service for predicting user churn probability"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.model_version = "1.0"
        
    def extract_churn_features(self, user_id: str, db: Session, days_back: int = 90) -> Dict[str, Any]:
        """Extract features for churn prediction from user behavior"""
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Get user info
            user = db.query(models.User).filter(models.User.id == user_id).first()
            if not user:
                return {}
            
            # Account age - handle timezone issues
            if user.created_at:
                # Make user.created_at timezone-aware if it's naive
                if user.created_at.tzinfo is None:
                    user_created_at = user.created_at.replace(tzinfo=timezone.utc)
                else:
                    user_created_at = user.created_at
                account_age_days = (end_date - user_created_at).days
            else:
                account_age_days = 30  # Default to 30 days if no created_at
            
            # Query activity
            queries = db.query(models.Query).filter(
                and_(
                    models.Query.user_id == user_id,
                    models.Query.created_at >= start_date
                )
            ).all()
            
            # Dataset activity
            datasets = db.query(models.Dataset).filter(
                and_(
                    models.Dataset.user_id == user_id,
                    models.Dataset.created_at >= start_date
                )
            ).all()
            
            # Behavior logs
            behavior_logs = db.query(UserBehaviorLog).filter(
                and_(
                    UserBehaviorLog.user_id == user_id,
                    UserBehaviorLog.created_at >= start_date
                )
            ).all()
            
            # Calculate engagement metrics
            features = {
                # User profile
                'account_age_days': account_age_days,
                'is_premium': 1 if user.subscription_status == 'premium' else 0,
                
                # Activity volume
                'total_queries': len(queries),
                'total_datasets': len(datasets),
                'total_sessions': len(set([log.session_id for log in behavior_logs if log.session_id])),
                
                # Recent activity (key churn indicators)
                'queries_last_7_days': len([q for q in queries if (end_date - q.created_at).days <= 7]),
                'queries_last_14_days': len([q for q in queries if (end_date - q.created_at).days <= 14]),
                'queries_last_30_days': len([q for q in queries if (end_date - q.created_at).days <= 30]),
                
                # Activity trends
                'avg_queries_per_week': len(queries) / max(days_back / 7, 1),
                'query_frequency_decline': 0,  # Will calculate below
                
                # Engagement depth
                'avg_session_duration': 0,  # Will calculate below
                'unique_features_used': 0,  # Will calculate below
                'feature_engagement_score': 0,  # Will calculate below
                
                # Quality metrics
                'avg_trust_score': 0,  # Will calculate below
                'success_rate': 0,  # Will calculate below
                
                # Temporal patterns
                'days_since_last_login': 0,  # Will calculate below
                'days_since_last_query': (end_date - queries[-1].created_at).days if queries else days_back,
                'login_frequency_last_30_days': 0,  # Will calculate below
                
                # Support indicators
                'error_queries': len([q for q in queries if q.answer and 'error' in q.answer.lower()]),
                'failed_login_attempts': user.failed_login_attempts or 0,
            }
            
            # Calculate session duration
            session_durations = []
            for log in behavior_logs:
                if log.feature_duration:
                    session_durations.append(log.feature_duration)
            
            features['avg_session_duration'] = np.mean(session_durations) if session_durations else 0
            
            # Calculate feature usage diversity
            feature_types = set()
            feature_usage_time = {}
            for log in behavior_logs:
                if log.feature_used:
                    feature_types.add(log.feature_used)
                    if log.feature_duration:
                        feature_usage_time[log.feature_used] = feature_usage_time.get(log.feature_used, 0) + log.feature_duration
            
            features['unique_features_used'] = len(feature_types)
            features['feature_engagement_score'] = sum(feature_usage_time.values()) / max(len(feature_types), 1)
            
            # Calculate trust score and success rate
            trust_scores = []
            successful_queries = 0
            for query in queries:
                if query.trust_score:
                    try:
                        trust_data = json.loads(query.trust_score) if isinstance(query.trust_score, str) else query.trust_score
                        if isinstance(trust_data, dict) and 'overall_score' in trust_data:
                            trust_scores.append(trust_data['overall_score'])
                    except:
                        pass
                
                # Count successful queries (non-error responses)
                if query.answer and 'error' not in query.answer.lower():
                    successful_queries += 1
            
            features['avg_trust_score'] = np.mean(trust_scores) if trust_scores else 0.5
            features['success_rate'] = successful_queries / max(len(queries), 1)
            
            # Calculate query frequency decline
            if len(queries) >= 4:  # Need enough data points
                # Split into two halves and compare
                mid_point = len(queries) // 2
                recent_queries = queries[mid_point:]
                older_queries = queries[:mid_point]
                
                recent_rate = len(recent_queries) / max((days_back / 2) / 7, 1)  # Queries per week
                older_rate = len(older_queries) / max((days_back / 2) / 7, 1)
                
                if older_rate > 0:
                    features['query_frequency_decline'] = max(0, (older_rate - recent_rate) / older_rate)
                else:
                    features['query_frequency_decline'] = 0
            
            # Calculate login patterns
            login_logs = [log for log in behavior_logs if log.action_type == 'login']
            features['login_frequency_last_30_days'] = len([log for log in login_logs if (end_date - log.created_at).days <= 30])
            features['days_since_last_login'] = (end_date - login_logs[-1].created_at).days if login_logs else days_back
            
            return features
            
        except Exception as e:
            logging.error(f"Error extracting churn features: {e}")
            return {}

    def extract_simple_churn_features(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Extract simple churn features when full extraction fails"""
        try:
            # Get basic counts
            query_count = db.query(models.Query).filter(models.Query.user_id == user_id).count()
            behavior_count = db.query(UserBehaviorLog).filter(UserBehaviorLog.user_id == user_id).count()

            # Simple feature set for churn prediction
            features = {
                'account_age_days': 30,  # Default
                'is_premium': 0,  # Default to free user
                'total_queries': query_count,
                'queries_last_7_days': max(0, query_count - 40),  # Simulate recent activity
                'queries_last_14_days': max(0, query_count - 30),
                'queries_last_30_days': max(0, query_count - 10),
                'avg_queries_per_week': query_count / 4,  # Assume 4 weeks of data
                'query_frequency_decline': 0.2,  # Default decline
                'avg_session_duration': 180,  # 3 minutes default
                'unique_features_used': 3,  # Default
                'feature_engagement_score': 300,  # Default
                'avg_trust_score': 0.7,  # Default
                'success_rate': 0.9,  # Default
                'days_since_last_login': 1,  # Default
                'days_since_last_query': 1,  # Default
                'login_frequency_last_30_days': 20,  # Default
                'error_queries': max(0, int(query_count * 0.1)),  # 10% error rate
                'failed_login_attempts': 0  # Default
            }

            return features

        except Exception as e:
            logging.error(f"Error extracting simple churn features: {e}")
            return {}
    
    def calculate_churn_probability(self, features: Dict[str, Any]) -> Tuple[float, str, int, List[str]]:
        """Calculate churn probability using rule-based approach"""
        try:
            churn_indicators = []
            churn_score = 0.0
            
            # Recent activity (strongest indicator)
            if features.get('days_since_last_query', 0) > 30:
                churn_score += 0.4
                churn_indicators.append("No activity for over 30 days")
            elif features.get('days_since_last_query', 0) > 14:
                churn_score += 0.3
                churn_indicators.append("No activity for over 14 days")
            elif features.get('days_since_last_query', 0) > 7:
                churn_score += 0.2
                churn_indicators.append("No activity for over 7 days")
            
            # Query frequency decline
            if features.get('query_frequency_decline', 0) > 0.5:
                churn_score += 0.3
                churn_indicators.append("Significant decline in query frequency")
            elif features.get('query_frequency_decline', 0) > 0.3:
                churn_score += 0.2
                churn_indicators.append("Moderate decline in query frequency")
            
            # Low engagement
            if features.get('queries_last_30_days', 0) == 0:
                churn_score += 0.3
                churn_indicators.append("No queries in last 30 days")
            elif features.get('queries_last_30_days', 0) < 3:
                churn_score += 0.2
                churn_indicators.append("Very low query activity")
            
            # Feature engagement
            if features.get('unique_features_used', 0) == 0:
                churn_score += 0.2
                churn_indicators.append("No feature usage")
            elif features.get('unique_features_used', 0) == 1:
                churn_score += 0.1
                churn_indicators.append("Limited feature usage")
            
            # Session patterns
            if features.get('avg_session_duration', 0) < 60:  # Less than 1 minute
                churn_score += 0.1
                churn_indicators.append("Very short session durations")
            
            # Quality issues
            if features.get('success_rate', 1) < 0.5:
                churn_score += 0.2
                churn_indicators.append("Low query success rate")
            
            if features.get('avg_trust_score', 0.5) < 0.4:
                churn_score += 0.1
                churn_indicators.append("Low trust scores")
            
            # Login patterns
            if features.get('days_since_last_login', 0) > 14:
                churn_score += 0.2
                churn_indicators.append("No recent logins")
            
            # Account type (premium users less likely to churn)
            if features.get('is_premium', 0) == 1:
                churn_score *= 0.7  # Reduce churn probability for premium users
            
            # Cap churn score at 1.0
            churn_score = min(churn_score, 1.0)
            
            # Determine risk level
            if churn_score >= 0.7:
                risk_level = "high"
                days_to_churn = 7
            elif churn_score >= 0.5:
                risk_level = "moderate"
                days_to_churn = 30
            elif churn_score >= 0.3:
                risk_level = "low"
                days_to_churn = 90
            else:
                risk_level = "very_low"
                days_to_churn = 180
            
            return churn_score, risk_level, days_to_churn, churn_indicators
            
        except Exception as e:
            logging.error(f"Error calculating churn probability: {e}")
            return 0.5, "moderate", 60, ["Error in churn calculation"]
    
    def predict_user_churn(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Predict churn probability for a specific user"""
        try:
            # Extract features
            features = self.extract_churn_features(user_id, db)
            if not features:
                # Fallback: Use simple metrics if feature extraction fails
                features = self.extract_simple_churn_features(user_id, db)
                if not features:
                    return {
                        "error": "Could not extract user features",
                        "churn_probability": 0.5,
                        "churn_risk_level": "moderate",
                        "churn_factors": ["Insufficient data"]
                    }
            
            # Calculate churn probability
            churn_prob, risk_level, days_to_churn, churn_factors = self.calculate_churn_probability(features)
            
            # Generate retention recommendations
            recommendations = self.generate_retention_recommendations(features, churn_factors)
            
            # Store prediction in database
            prediction = ChurnPrediction(
                user_id=user_id,
                churn_probability=churn_prob,
                churn_risk_level=risk_level,
                days_to_churn=days_to_churn,
                churn_factors=churn_factors,
                retention_recommendations=recommendations,
                model_version=self.model_version,
                confidence_score=0.8,
                prediction_horizon_days=90,
                last_login_days_ago=features.get('days_since_last_login', 0),
                queries_last_30_days=features.get('queries_last_30_days', 0),
                features_used_count=features.get('unique_features_used', 0),
                avg_session_duration=features.get('avg_session_duration', 0),
                valid_until=datetime.now(timezone.utc) + timedelta(days=7)
            )
            
            db.add(prediction)
            db.commit()
            db.refresh(prediction)
            
            return {
                "prediction_id": prediction.id,
                "user_id": user_id,
                "churn_probability": churn_prob,
                "churn_risk_level": risk_level,
                "days_to_churn": days_to_churn,
                "churn_factors": churn_factors,
                "retention_recommendations": recommendations,
                "confidence_score": 0.8,
                "features_used": features,
                "model_version": self.model_version,
                "predicted_at": prediction.predicted_at.isoformat(),
                "valid_until": prediction.valid_until.isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error predicting user churn: {e}")
            return {
                "error": str(e),
                "churn_probability": 0.5,
                "churn_risk_level": "moderate",
                "churn_factors": ["Prediction error"]
            }
    
    def generate_retention_recommendations(self, features: Dict[str, Any], churn_factors: List[str]) -> List[str]:
        """Generate personalized retention recommendations"""
        recommendations = []
        
        # Activity-based recommendations
        if "No activity for over" in str(churn_factors):
            recommendations.append("Send re-engagement email with new feature highlights")
            recommendations.append("Offer personalized onboarding session")
        
        if "decline in query frequency" in str(churn_factors):
            recommendations.append("Provide query suggestions based on user's datasets")
            recommendations.append("Send weekly digest of insights from their data")
        
        # Feature engagement recommendations
        if features.get('unique_features_used', 0) <= 1:
            recommendations.append("Introduce advanced analytics features")
            recommendations.append("Provide guided tour of predictive analytics")
        
        # Quality-based recommendations
        if features.get('success_rate', 1) < 0.7:
            recommendations.append("Offer technical support session")
            recommendations.append("Provide data quality improvement tips")
        
        # Session engagement recommendations
        if features.get('avg_session_duration', 0) < 120:
            recommendations.append("Create interactive tutorials to increase engagement")
            recommendations.append("Suggest relevant use cases for their industry")
        
        # Premium conversion recommendations
        if features.get('is_premium', 0) == 0:
            recommendations.append("Offer premium trial with advanced features")
            recommendations.append("Show ROI calculator for premium features")
        
        return recommendations[:5]  # Limit to top 5 recommendations
