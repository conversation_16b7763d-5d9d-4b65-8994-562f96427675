"""
Trust Score Core Computation Engine
"""

import numpy as np
import hashlib
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import re

from ..config import trust_config
from .ml_models import QATRegressor, RefusalDetector, ConfidenceCalibrator


@dataclass
class ComponentScores:
    """Individual component scores"""
    model_confidence: float
    source_quality: float
    citation_accuracy: float
    question_match: float

    def to_dict(self) -> Dict[str, float]:
        return {
            "model_confidence": self.model_confidence,
            "source_quality": self.source_quality,
            "citation_accuracy": self.citation_accuracy,
            "question_match": self.question_match
        }


@dataclass
class TrustResult:
    """Trust score computation result"""
    overall_score: float
    components: ComponentScores
    explanation: str
    factors: List[str]
    confidence_interval: Optional[Tuple[float, float]] = None
    processing_time_ms: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "overall_score": self.overall_score,
            "components": self.components.to_dict(),
            "explanation": self.explanation,
            "factors": self.factors,
            "confidence_interval": self.confidence_interval,
            "processing_time_ms": self.processing_time_ms
        }


class TrustScoreComputer:
    """Advanced Trust Score Computation Engine"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or trust_config
        self.logger = logging.getLogger(__name__)

        # Initialize advanced ML models
        self.qat_regressor = QATRegressor()
        self.refusal_detector = RefusalDetector()
        self.confidence_calibrator = ConfidenceCalibrator()

        # Component weights (auto-calibrated)
        self.weights = {
            "model_confidence": self.config.model_confidence_weight,
            "source_quality": self.config.source_quality_weight,
            "citation_accuracy": self.config.citation_accuracy_weight,
            "question_match": self.config.question_match_weight
        }

        # Normalize weights
        total_weight = sum(self.weights.values())
        self.weights = {k: v / total_weight for k, v in self.weights.items()}

        # Performance tracking
        self.computation_history = []
    
    def compute(self, query: str, answer: str, context: Dict[str, Any]) -> TrustResult:
        """
        Compute trust score for a query-answer pair

        Args:
            query: User question
            answer: AI-generated answer
            context: Additional context (sources, metadata, etc.)

        Returns:
            TrustResult with overall score and component breakdown
        """
        start_time = datetime.now()

        try:
            # Detect question type and set adaptive base trust
            question_type = self._detect_question_type(query)
            base_trust_adjustment = self._get_base_trust_adjustment(question_type)

            # Extract individual component scores
            model_confidence = self._extract_model_confidence(answer, context)
            source_quality = self._compute_source_quality(context)
            citation_accuracy = self._compute_citation_accuracy(answer, context)
            question_match = self._compute_question_match(query, answer)

            # Create component scores object
            components = ComponentScores(
                model_confidence=model_confidence,
                source_quality=source_quality,
                citation_accuracy=citation_accuracy,
                question_match=question_match
            )

            # Fuse components with learned weights
            raw_score = self._fuse_components(components)

            # Apply question-type adaptive scoring
            adaptive_score = self._apply_adaptive_scoring(raw_score, base_trust_adjustment)

            # Apply source quality bonus (+10% for good sources)
            overall_score = self._apply_source_bonus(adaptive_score, context)

            # Apply confidence calibration
            calibrated_score, confidence_interval = self.confidence_calibrator.calibrate_score(overall_score)

            # Generate explanation and factors
            explanation, factors = self._generate_explanation(components, calibrated_score, context)

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            # Track computation history for auto-calibration
            self.computation_history.append({
                'timestamp': datetime.now(),
                'components': components.to_dict(),
                'overall_score': overall_score,
                'calibrated_score': calibrated_score,
                'processing_time_ms': processing_time
            })

            # Limit history size
            if len(self.computation_history) > 1000:
                self.computation_history = self.computation_history[-500:]

            return TrustResult(
                overall_score=calibrated_score,
                components=components,
                explanation=explanation,
                factors=factors,
                confidence_interval=confidence_interval,
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Trust score computation failed: {str(e)}")
            return self._fallback_trust_result(str(e))
    
    def _extract_model_confidence(self, answer: str, context: Dict[str, Any]) -> float:
        """Extract model confidence from LLM response"""
        # Placeholder implementation - would use actual LLM logits/probabilities
        
        # Check for uncertainty indicators
        uncertainty_patterns = [
            r'\b(might|may|could|possibly|perhaps|likely|probably)\b',
            r'\b(uncertain|unsure|unclear|ambiguous)\b',
            r'\b(approximately|around|about|roughly)\b',
            r'\b(seems|appears|suggests)\b'
        ]
        
        uncertainty_count = 0
        for pattern in uncertainty_patterns:
            uncertainty_count += len(re.findall(pattern, answer, re.IGNORECASE))
        
        # Base confidence adjusted by uncertainty indicators
        base_confidence = 0.8
        uncertainty_penalty = min(0.3, uncertainty_count * 0.05)
        
        # Adjust for answer length and specificity
        length_bonus = min(0.1, len(answer) / 1000)
        specificity_bonus = 0.1 if self._has_specific_details(answer) else 0
        
        confidence = base_confidence - uncertainty_penalty + length_bonus + specificity_bonus
        return max(0.0, min(1.0, confidence))
    
    def _compute_source_quality(self, context: Dict[str, Any]) -> float:
        """Compute source quality score (30% weight)"""
        sources = context.get("sources", [])

        if not sources:
            return 0.5  # Neutral score when no sources

        total_quality = 0
        for source in sources:
            source_quality = 0

            # Check for source metadata quality
            if source.get("document"):
                source_quality += 0.25  # Has document name
            if source.get("page_number"):
                source_quality += 0.25  # Has page reference
            if source.get("text") and len(source.get("text", "")) > 50:
                source_quality += 0.25  # Has substantial text content
            if source.get("confidence_score", 0) > 0.7:
                source_quality += 0.25  # High confidence source

            total_quality += source_quality

        # Average quality across all sources
        avg_quality = total_quality / len(sources)

        # Bonus for multiple diverse sources
        diversity_bonus = min(0.2, len(sources) * 0.05)

        return min(1.0, avg_quality + diversity_bonus)

    def _compute_citation_accuracy(self, answer: str, context: Dict[str, Any]) -> float:
        """Compute citation accuracy score (20% weight)"""
        sources = context.get("sources", [])

        if not sources:
            # No sources available - check if answer claims to have sources
            if re.search(r"(according to|based on|source|reference)", answer, re.IGNORECASE):
                return 0.3  # Claims sources but none provided
            return 0.7  # No sources claimed or provided

        # Count verifiable claims in answer
        verifiable_claims = self._extract_verifiable_claims(answer)

        if not verifiable_claims:
            return 0.8  # Good - has sources but makes no specific claims

        # Check how many claims can be attributed to sources
        attributed_claims = 0
        for claim in verifiable_claims:
            if self._claim_supported_by_sources(claim, sources):
                attributed_claims += 1

        precision = attributed_claims / len(verifiable_claims) if verifiable_claims else 1.0
        return max(0.0, min(1.0, precision))

    def _compute_question_match(self, query: str, answer: str) -> float:
        """Compute question-answer match score (10% weight)"""
        query_words = set(query.lower().split())
        answer_words = set(answer.lower().split())

        # Compute word overlap
        overlap = len(query_words.intersection(answer_words))
        total_unique = len(query_words.union(answer_words))

        if total_unique == 0:
            return 0.5

        overlap_score = overlap / total_unique

        # Adjust for answer completeness
        completeness_score = min(1.0, len(answer) / 200)

        # Combine scores
        match_score = 0.6 * overlap_score + 0.4 * completeness_score
        return max(0.0, min(1.0, match_score))

    def _compute_qat_score(self, query: str, answer: str) -> float:
        """Compute Question-Answer-Topic relevance score using ML model"""
        try:
            return self.qat_regressor.predict(query, answer)
        except Exception as e:
            self.logger.warning(f"QAT regressor failed, using fallback: {e}")
            return self._fallback_qat_score(query, answer)

    def _fallback_qat_score(self, query: str, answer: str) -> float:
        """Fallback QAT scoring when ML model fails"""
        query_words = set(query.lower().split())
        answer_words = set(answer.lower().split())

        # Compute word overlap
        overlap = len(query_words.intersection(answer_words))
        total_unique = len(query_words.union(answer_words))

        if total_unique == 0:
            return 0.5

        overlap_score = overlap / total_unique

        # Adjust for answer completeness
        completeness_score = min(1.0, len(answer) / 200)

        # Combine scores
        qat_score = 0.6 * overlap_score + 0.4 * completeness_score
        return max(0.0, min(1.0, qat_score))
    
    def _compute_refusal_probability(self, answer: str, context: Dict[str, Any]) -> float:
        """Compute probability that the model should have refused to answer using ML model"""
        try:
            return self.refusal_detector.predict(answer, context)
        except Exception as e:
            self.logger.warning(f"Refusal detector failed, using fallback: {e}")
            return self._fallback_refusal_score(answer)

    def _fallback_refusal_score(self, answer: str) -> float:
        """Fallback refusal scoring when ML model fails"""
        # Check for refusal indicators
        refusal_patterns = [
            r"i don't know",
            r"i cannot",
            r"i'm not sure",
            r"insufficient information",
            r"cannot determine",
            r"unable to answer",
            r"not enough data"
        ]

        refusal_indicators = 0
        for pattern in refusal_patterns:
            if re.search(pattern, answer, re.IGNORECASE):
                refusal_indicators += 1

        # Check for hallucination indicators
        hallucination_patterns = [
            r"according to my knowledge",
            r"based on my training",
            r"i believe",
            r"in my opinion"
        ]

        hallucination_indicators = 0
        for pattern in hallucination_patterns:
            if re.search(pattern, answer, re.IGNORECASE):
                hallucination_indicators += 1

        # Compute refusal probability
        refusal_prob = min(1.0, (refusal_indicators * 0.3) + (hallucination_indicators * 0.2))
        return refusal_prob
    
    def _compute_citation_precision(self, answer: str, context: Dict[str, Any]) -> float:
        """Compute precision of citations and source attribution"""
        sources = context.get("sources", [])
        
        if not sources:
            # No sources available - check if answer claims to have sources
            if re.search(r"(according to|based on|source|reference)", answer, re.IGNORECASE):
                return 0.3  # Claims sources but none provided
            return 0.7  # No sources claimed or provided
        
        # Count verifiable claims in answer
        verifiable_claims = self._extract_verifiable_claims(answer)
        
        if not verifiable_claims:
            return 0.8  # Good - has sources but makes no specific claims
        
        # Check how many claims can be attributed to sources
        attributed_claims = 0
        for claim in verifiable_claims:
            if self._claim_supported_by_sources(claim, sources):
                attributed_claims += 1
        
        precision = attributed_claims / len(verifiable_claims) if verifiable_claims else 1.0
        
        # Bonus for multiple diverse sources
        source_diversity_bonus = min(0.1, len(sources) * 0.02)
        
        return min(1.0, precision + source_diversity_bonus)
    
    def _detect_question_type(self, query: str) -> str:
        """Detect the type of question to apply adaptive scoring"""
        query_lower = query.lower().strip()

        # Simple factual questions (start at 70% base trust)
        simple_patterns = [
            r'^what is\b',
            r'^who is\b',
            r'^when did\b',
            r'^where is\b',
            r'^how many\b',
            r'^define\b',
            r'^what does\b.*mean',
            r'^\w+\?$',  # Single word questions
        ]

        for pattern in simple_patterns:
            if re.search(pattern, query_lower):
                return "simple_factual"

        # Complex analysis questions (start at 50% base trust)
        complex_patterns = [
            r'\banalyze\b',
            r'\bcompare\b',
            r'\bevaluate\b',
            r'\bexplain why\b',
            r'\bhow does.*work\b',
            r'\bwhat are the implications\b',
            r'\bwhat would happen if\b',
            r'\bpros and cons\b',
            r'\badvantages and disadvantages\b',
        ]

        for pattern in complex_patterns:
            if re.search(pattern, query_lower):
                return "complex_analysis"

        # Default to moderate complexity (start at 60% base trust)
        return "moderate"

    def _get_base_trust_adjustment(self, question_type: str) -> float:
        """Get base trust adjustment based on question type"""
        adjustments = {
            "simple_factual": 0.7,    # Start at 70% for simple questions
            "moderate": 0.6,          # Start at 60% for moderate questions
            "complex_analysis": 0.5   # Start at 50% for complex questions
        }
        return adjustments.get(question_type, 0.6)

    def _apply_adaptive_scoring(self, raw_score: float, base_trust: float) -> float:
        """Apply question-type adaptive scoring"""
        # Blend the raw component score with the adaptive base trust
        # Give more weight to base trust for simpler questions
        if base_trust >= 0.7:  # Simple questions
            blend_weight = 0.3  # 30% base trust, 70% component score
        elif base_trust >= 0.6:  # Moderate questions
            blend_weight = 0.2  # 20% base trust, 80% component score
        else:  # Complex questions
            blend_weight = 0.1  # 10% base trust, 90% component score

        adaptive_score = (blend_weight * base_trust) + ((1 - blend_weight) * raw_score)
        return max(0.0, min(1.0, adaptive_score))

    def _apply_source_bonus(self, score: float, context: Dict[str, Any]) -> float:
        """Apply +10% bonus for good quality sources"""
        sources = context.get("sources", [])

        if not sources:
            return score  # No sources, no bonus

        # Check for good source quality indicators
        good_source_indicators = 0
        total_sources = len(sources)

        for source in sources:
            source_quality = 0

            # Check for source metadata quality
            if source.get("document"):
                source_quality += 1  # Has document name
            if source.get("page_number"):
                source_quality += 1  # Has page reference
            if source.get("text") and len(source.get("text", "")) > 50:
                source_quality += 1  # Has substantial text content
            if source.get("confidence_score", 0) > 0.7:
                source_quality += 1  # High confidence source

            # Consider it a "good source" if it has 2+ quality indicators
            if source_quality >= 2:
                good_source_indicators += 1

        # Calculate source quality ratio
        good_source_ratio = good_source_indicators / total_sources if total_sources > 0 else 0

        # Apply bonus based on source quality
        if good_source_ratio >= 0.5:  # At least 50% of sources are good quality
            if total_sources >= 3:  # Multiple diverse sources
                bonus = 0.10  # Full 10% bonus
            elif total_sources >= 2:  # Two good sources
                bonus = 0.08  # 8% bonus
            else:  # One good source
                bonus = 0.05  # 5% bonus
        else:
            bonus = 0.0  # No bonus for poor quality sources

        # Apply the bonus
        final_score = score + bonus

        # Log the bonus for debugging
        if bonus > 0:
            self.logger.debug(f"Applied source bonus: +{bonus:.1%} (sources: {total_sources}, good: {good_source_indicators})")

        return max(0.0, min(1.0, final_score))

    def _fuse_components(self, components: ComponentScores) -> float:
        """Fuse component scores using learned weights"""
        weighted_sum = (
            self.weights["model_confidence"] * components.model_confidence +
            self.weights["source_quality"] * components.source_quality +
            self.weights["citation_accuracy"] * components.citation_accuracy +
            self.weights["question_match"] * components.question_match
        )

        return max(0.0, min(1.0, weighted_sum))
    
    def _generate_explanation(self, components: ComponentScores, overall_score: float, context: Dict[str, Any] = None) -> Tuple[str, List[str]]:
        """Generate human-readable explanation"""
        factors = []
        
        # Analyze component contributions
        if components.model_confidence >= 0.8:
            factors.append("High model confidence")
        elif components.model_confidence <= 0.4:
            factors.append("Low model confidence")
        
        if components.source_quality >= 0.8:
            factors.append("High quality sources")
        elif components.source_quality <= 0.4:
            factors.append("Low quality sources")

        if components.citation_accuracy >= 0.8:
            factors.append("Accurate citations")
        elif components.citation_accuracy <= 0.4:
            factors.append("Inaccurate citations")

        if components.question_match >= 0.8:
            factors.append("Strong question-answer match")
        elif components.question_match <= 0.4:
            factors.append("Weak question-answer match")

        # Check for source bonus
        if context:
            sources = context.get("sources", [])
            if sources and len(sources) >= 2:
                good_sources = sum(1 for s in sources if
                                 s.get("document") and
                                 s.get("text") and
                                 len(s.get("text", "")) > 50)
                if good_sources >= len(sources) * 0.5:  # 50%+ good sources
                    factors.append("Quality source bonus applied")

        # Generate overall assessment with new thresholds
        if overall_score >= self.config.high_trust_threshold:  # >= 70%
            assessment = "highly reliable"
        elif overall_score >= self.config.moderate_trust_threshold:  # >= 50%
            assessment = "moderately reliable"
        else:  # < 50%
            assessment = "low reliability"

        explanation = f"This response is {assessment} based on: {', '.join(factors) if factors else 'baseline assessment'}"
        
        return explanation, factors
    
    def _has_specific_details(self, answer: str) -> bool:
        """Check if answer contains specific details"""
        # Look for numbers, dates, names, etc.
        patterns = [
            r'\b\d+\.?\d*\b',  # Numbers
            r'\b\d{4}\b',      # Years
            r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',  # Proper names
            r'\$\d+',          # Currency
            r'\d+%'            # Percentages
        ]
        
        for pattern in patterns:
            if re.search(pattern, answer):
                return True
        return False
    
    def _extract_verifiable_claims(self, answer: str) -> List[str]:
        """Extract verifiable factual claims from answer"""
        # Simple implementation - would use NLP for better extraction
        sentences = re.split(r'[.!?]+', answer)
        
        verifiable_claims = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10 and self._has_specific_details(sentence):
                verifiable_claims.append(sentence)
        
        return verifiable_claims
    
    def _claim_supported_by_sources(self, claim: str, sources: List[Dict[str, Any]]) -> bool:
        """Check if a claim is supported by available sources"""
        # Simple keyword matching - would use semantic similarity in production
        claim_words = set(claim.lower().split())
        
        for source in sources:
            source_text = source.get("text", "").lower()
            source_words = set(source_text.split())
            
            # Check for significant word overlap
            overlap = len(claim_words.intersection(source_words))
            if overlap >= min(3, len(claim_words) * 0.3):
                return True
        
        return False
    
    def _fallback_trust_result(self, error_msg: str) -> TrustResult:
        """Generate fallback trust result on computation failure"""
        return TrustResult(
            overall_score=0.5,
            components=ComponentScores(0.5, 0.5, 0.5, 0.5),
            explanation=f"Trust score computation failed: {error_msg}",
            factors=["Computation error"],
            processing_time_ms=0.0
        )
    
    def update_weights(self, new_weights: Dict[str, float]) -> None:
        """Update component fusion weights (for auto-calibration)"""
        total_weight = sum(new_weights.values())
        self.weights = {k: v / total_weight for k, v in new_weights.items()}
        self.logger.info(f"Updated trust score weights: {self.weights}")

    def auto_calibrate_weights(self, validation_data: List[Dict[str, Any]]) -> None:
        """Auto-calibrate component weights using validation data"""
        try:
            if len(validation_data) < 50:
                self.logger.warning("Insufficient data for weight auto-calibration")
                return

            from scipy.optimize import minimize

            def objective(weights):
                # Temporarily update weights
                old_weights = self.weights.copy()
                self.weights = {
                    "model_confidence": weights[0],
                    "source_quality": weights[1],
                    "citation_accuracy": weights[2],
                    "question_match": weights[3]
                }

                # Normalize weights
                total = sum(self.weights.values())
                self.weights = {k: v / total for k, v in self.weights.items()}

                # Compute loss on validation data
                total_loss = 0
                for item in validation_data:
                    predicted_score = self._fuse_components(ComponentScores(
                        model_confidence=item['model_confidence'],
                        source_quality=item['source_quality'],
                        citation_accuracy=item['citation_accuracy'],
                        question_match=item['question_match']
                    ))
                    true_score = item['true_score']
                    total_loss += (predicted_score - true_score) ** 2

                # Restore old weights
                self.weights = old_weights

                return total_loss / len(validation_data)

            # Initial weights
            initial_weights = [
                self.weights["model_confidence"],
                self.weights["source_quality"],
                self.weights["citation_accuracy"],
                self.weights["question_match"]
            ]

            # Optimize weights
            result = minimize(
                objective,
                initial_weights,
                method='L-BFGS-B',
                bounds=[(0.1, 1.0)] * 4
            )

            if result.success:
                # Update weights with optimized values
                optimized_weights = {
                    "model_confidence": result.x[0],
                    "source_quality": result.x[1],
                    "citation_accuracy": result.x[2],
                    "question_match": result.x[3]
                }
                self.update_weights(optimized_weights)
                self.logger.info(f"Auto-calibrated weights: {self.weights}")
            else:
                self.logger.warning("Weight auto-calibration failed")

        except Exception as e:
            self.logger.error(f"Auto-calibration failed: {e}")

    def train_ml_models(self, training_data: Dict[str, List[Dict[str, Any]]]) -> None:
        """Train the ML models with provided data"""
        try:
            # Train QAT regressor
            if 'qat_data' in training_data:
                self.qat_regressor.train(training_data['qat_data'])

            # Train refusal detector
            if 'refusal_data' in training_data:
                self.refusal_detector.train(training_data['refusal_data'])

            # Calibrate confidence
            if 'calibration_data' in training_data:
                cal_data = training_data['calibration_data']
                predictions = np.array([item['predicted_score'] for item in cal_data])
                labels = np.array([item['true_label'] for item in cal_data])
                self.confidence_calibrator.calibrate(predictions, labels)

            self.logger.info("ML models training completed")

        except Exception as e:
            self.logger.error(f"ML model training failed: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics from computation history"""
        if not self.computation_history:
            return {"error": "No computation history available"}

        recent_history = self.computation_history[-100:]  # Last 100 computations

        # Calculate metrics
        processing_times = [h['processing_time_ms'] for h in recent_history]
        overall_scores = [h['calibrated_score'] for h in recent_history]

        return {
            "total_computations": len(self.computation_history),
            "recent_computations": len(recent_history),
            "avg_processing_time_ms": np.mean(processing_times),
            "avg_trust_score": np.mean(overall_scores),
            "trust_score_std": np.std(overall_scores),
            "min_trust_score": np.min(overall_scores),
            "max_trust_score": np.max(overall_scores),
            "last_computation": recent_history[-1]['timestamp'].isoformat() if recent_history else None
        }
