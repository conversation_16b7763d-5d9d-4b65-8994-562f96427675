import os
import stripe
from dotenv import load_dotenv
from typing import Dict, Any

# Load environment variables
load_dotenv()

# Set Stripe API key
stripe.api_key = os.getenv("STRIPE_API_KEY")
stripe_webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")

def create_checkout_session(user_id: str, price_id: str, success_url: str, cancel_url: str) -> Any:
    """
    Create a Stripe checkout session
    
    Args:
        user_id: The user's ID
        price_id: The Stripe price ID
        success_url: URL to redirect to on successful payment
        cancel_url: URL to redirect to on cancelled payment
    
    Returns:
        Stripe checkout session
    """
    try:
        # Create checkout session
        session = stripe.checkout.Session.create(
            client_reference_id=user_id,
            payment_method_types=["card"],
            line_items=[
                {
                    "price": price_id,
                    "quantity": 1,
                },
            ],
            mode="subscription",
            success_url=success_url,
            cancel_url=cancel_url,
        )
        
        return session
    
    except Exception as e:
        raise Exception(f"Error creating checkout session: {str(e)}")

def construct_event(payload: str, signature: str) -> Dict[str, Any]:
    """
    Construct a Stripe event from webhook payload
    
    Args:
        payload: The webhook payload
        signature: The Stripe signature
    
    Returns:
        Stripe event
    """
    try:
        event = stripe.Webhook.construct_event(
            payload=payload,
            sig_header=signature,
            secret=stripe_webhook_secret
        )
        
        return event
    
    except Exception as e:
        raise Exception(f"Error constructing Stripe event: {str(e)}")

def create_customer(email: str, name: str) -> str:
    """
    Create a Stripe customer
    
    Args:
        email: The customer's email
        name: The customer's name
    
    Returns:
        Stripe customer ID
    """
    try:
        customer = stripe.Customer.create(
            email=email,
            name=name
        )
        
        return customer.id
    
    except Exception as e:
        raise Exception(f"Error creating Stripe customer: {str(e)}")

def cancel_subscription(subscription_id: str) -> bool:
    """
    Cancel a Stripe subscription
    
    Args:
        subscription_id: The subscription ID
    
    Returns:
        True if successful
    """
    try:
        stripe.Subscription.delete(subscription_id)
        return True
    
    except Exception as e:
        raise Exception(f"Error cancelling subscription: {str(e)}")
