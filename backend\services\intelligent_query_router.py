#!/usr/bin/env python3
"""
🧠 Intelligent Query Router for AIthentiq
Revolutionary query routing system that determines the optimal processing path

Routes queries to:
1. Structured Data Engine (pandas) - for counts, aggregations, filters
2. Semantic RAG Engine - for meaning, context, insights  
3. Hybrid Processing - for complex analytical queries
4. AI Enhancement Layer - for explanations and insights
"""

import re
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from enum import Enum
from dataclasses import dataclass
import asyncio
from datetime import datetime

from .hybrid_data_engine import HybridDataEngine, QueryType, QueryResult, DataFormat
# from .llm_service import llm_service  # Import when needed to avoid circular imports

class ProcessingPath(Enum):
    """Available processing paths"""
    STRUCTURED_ONLY = "structured_only"
    SEMANTIC_ONLY = "semantic_only" 
    HYBRID_STRUCTURED_FIRST = "hybrid_structured_first"
    HYBRID_SEMANTIC_FIRST = "hybrid_semantic_first"
    AI_ENHANCED = "ai_enhanced"

@dataclass
class RoutingDecision:
    """Query routing decision with confidence and reasoning"""
    primary_path: ProcessingPath
    fallback_path: Optional[ProcessingPath]
    confidence: float
    reasoning: List[str]
    estimated_time_ms: float
    requires_ai: bool

class IntelligentQueryRouter:
    """
    🎯 Revolutionary Query Router
    
    Uses advanced pattern recognition and ML-like decision trees
    to route queries to the optimal processing engine
    """
    
    def __init__(self):
        self.hybrid_engine = HybridDataEngine()
        
        # Advanced routing patterns
        self.routing_patterns = {
            'pure_structured': {
                'patterns': [
                    r'\b(count|how many|number of)\b',
                    r'\b(sum|total|add up)\b',
                    r'\b(average|mean|avg)\b',
                    r'\b(maximum|minimum|max|min)\b',
                    r'\b(distinct|unique)\b',
                    r'\bshow me\b.*\b(top|bottom)\b',
                    r'\bfilter\b.*\bwhere\b',
                    r'\bgroup by\b|\bgrouped by\b'
                ],
                'confidence_boost': 0.4,
                'path': ProcessingPath.STRUCTURED_ONLY
            },
            'pure_semantic': {
                'patterns': [
                    r'\b(explain|why|how does|what does.*mean)\b',
                    r'\b(insights|patterns|trends|analysis)\b(?!.*\b(count|sum|average)\b)',
                    r'\b(compare|difference|versus|vs)\b(?!.*\b(numbers|values)\b)',
                    r'\b(recommend|suggest|advice|should)\b',
                    r'\b(meaning|significance|importance)\b',
                    r'\b(context|background|story)\b'
                ],
                'confidence_boost': 0.3,
                'path': ProcessingPath.SEMANTIC_ONLY
            },
            'hybrid_structured_first': {
                'patterns': [
                    r'\b(analyze|analysis)\b.*\b(count|sum|average|total)\b',
                    r'\b(statistics|stats)\b.*\b(show|display|tell)\b',
                    r'\b(correlation|relationship)\b.*\b(between|among)\b',
                    r'\b(trend|pattern)\b.*\b(in|of)\b.*\b(data|numbers)\b',
                    r'\b(breakdown|summary)\b.*\b(by|of)\b'
                ],
                'confidence_boost': 0.25,
                'path': ProcessingPath.HYBRID_STRUCTURED_FIRST
            },
            'ai_enhanced': {
                'patterns': [
                    r'\b(insights|recommendations|suggestions)\b.*\b(based on|from)\b',
                    r'\b(what can you tell me|tell me about)\b',
                    r'\b(business intelligence|bi|dashboard)\b',
                    r'\b(actionable|next steps|what should)\b',
                    r'\b(optimize|improve|enhance)\b'
                ],
                'confidence_boost': 0.2,
                'path': ProcessingPath.AI_ENHANCED
            }
        }
        
        # Performance estimates (in milliseconds)
        self.performance_estimates = {
            ProcessingPath.STRUCTURED_ONLY: 50,
            ProcessingPath.SEMANTIC_ONLY: 2000,
            ProcessingPath.HYBRID_STRUCTURED_FIRST: 300,
            ProcessingPath.HYBRID_SEMANTIC_FIRST: 2500,
            ProcessingPath.AI_ENHANCED: 3000
        }
    
    def analyze_query_complexity(self, query: str, dataset_info: Optional[Dict] = None) -> Dict[str, Any]:
        """🔍 Deep analysis of query complexity and requirements"""
        query_lower = query.lower()
        
        complexity_factors = {
            'word_count': len(query.split()),
            'has_numbers': bool(re.search(r'\d+', query)),
            'has_operators': bool(re.search(r'[><=!]', query)),
            'has_aggregations': bool(re.search(r'\b(sum|count|avg|max|min|total)\b', query_lower)),
            'has_filters': bool(re.search(r'\b(where|filter|only|exclude)\b', query_lower)),
            'has_grouping': bool(re.search(r'\b(group|grouped|by)\b', query_lower)),
            'has_semantic_words': bool(re.search(r'\b(explain|why|how|meaning|insights)\b', query_lower)),
            'has_comparison': bool(re.search(r'\b(compare|vs|versus|difference)\b', query_lower)),
            'mentions_columns': 0,
            'complexity_score': 0.0
        }
        
        # Check if query mentions specific columns (if dataset info available)
        if dataset_info and 'columns' in dataset_info:
            for col in dataset_info['columns']:
                if col.lower() in query_lower:
                    complexity_factors['mentions_columns'] += 1
        
        # Calculate complexity score
        score = 0.0
        score += complexity_factors['word_count'] * 0.1
        score += complexity_factors['has_numbers'] * 0.2
        score += complexity_factors['has_operators'] * 0.3
        score += complexity_factors['has_aggregations'] * 0.4
        score += complexity_factors['has_filters'] * 0.3
        score += complexity_factors['has_grouping'] * 0.3
        score += complexity_factors['has_semantic_words'] * 0.5
        score += complexity_factors['mentions_columns'] * 0.2
        
        complexity_factors['complexity_score'] = min(score, 10.0)  # Cap at 10
        
        return complexity_factors
    
    def route_query(self, query: str, dataset_info: Optional[Dict] = None) -> RoutingDecision:
        """🎯 Main routing decision engine"""
        query_lower = query.lower()
        
        # Analyze complexity
        complexity = self.analyze_query_complexity(query, dataset_info)
        
        # Score each processing path
        path_scores = {}
        reasoning = []
        
        for category, config in self.routing_patterns.items():
            score = 0.0
            matches = []
            
            for pattern in config['patterns']:
                if re.search(pattern, query_lower):
                    score += config['confidence_boost']
                    matches.append(pattern)
            
            if matches:
                path_scores[config['path']] = score
                reasoning.append(f"Matched {len(matches)} {category} patterns")
        
        # Apply complexity-based adjustments
        if complexity['complexity_score'] > 5.0:
            # High complexity - prefer hybrid or AI-enhanced
            if ProcessingPath.HYBRID_STRUCTURED_FIRST in path_scores:
                path_scores[ProcessingPath.HYBRID_STRUCTURED_FIRST] += 0.2
            if ProcessingPath.AI_ENHANCED in path_scores:
                path_scores[ProcessingPath.AI_ENHANCED] += 0.1
            reasoning.append("High complexity detected - boosting hybrid/AI paths")
        
        if complexity['has_aggregations'] and complexity['has_semantic_words']:
            # Mixed requirements - prefer hybrid
            if ProcessingPath.HYBRID_STRUCTURED_FIRST in path_scores:
                path_scores[ProcessingPath.HYBRID_STRUCTURED_FIRST] += 0.3
            reasoning.append("Mixed structured/semantic requirements - boosting hybrid")
        
        # Determine primary path
        if path_scores:
            primary_path = max(path_scores.keys(), key=lambda k: path_scores[k])
            confidence = min(path_scores[primary_path], 1.0)
        else:
            # Default fallback based on complexity
            if complexity['has_aggregations']:
                primary_path = ProcessingPath.STRUCTURED_ONLY
                confidence = 0.6
            elif complexity['has_semantic_words']:
                primary_path = ProcessingPath.SEMANTIC_ONLY
                confidence = 0.6
            else:
                primary_path = ProcessingPath.HYBRID_STRUCTURED_FIRST
                confidence = 0.5
            reasoning.append("No strong patterns - using complexity-based fallback")
        
        # Determine fallback path
        fallback_path = None
        if confidence < 0.8:
            if primary_path == ProcessingPath.STRUCTURED_ONLY:
                fallback_path = ProcessingPath.HYBRID_STRUCTURED_FIRST
            elif primary_path == ProcessingPath.SEMANTIC_ONLY:
                fallback_path = ProcessingPath.HYBRID_SEMANTIC_FIRST
            reasoning.append(f"Low confidence - setting fallback to {fallback_path}")
        
        # Estimate processing time
        estimated_time = self.performance_estimates.get(primary_path, 1000)
        if dataset_info and 'rows' in dataset_info:
            # Adjust for dataset size
            rows = dataset_info['rows']
            if rows > 10000:
                estimated_time *= 1.5
            elif rows > 100000:
                estimated_time *= 2.0
        
        # Determine if AI is required
        requires_ai = primary_path in [
            ProcessingPath.SEMANTIC_ONLY,
            ProcessingPath.HYBRID_SEMANTIC_FIRST,
            ProcessingPath.AI_ENHANCED
        ]
        
        return RoutingDecision(
            primary_path=primary_path,
            fallback_path=fallback_path,
            confidence=confidence,
            reasoning=reasoning,
            estimated_time_ms=estimated_time,
            requires_ai=requires_ai
        )
    
    async def execute_routing_decision(
        self, 
        decision: RoutingDecision, 
        query: str, 
        df: pd.DataFrame, 
        format_type: DataFormat
    ) -> QueryResult:
        """🚀 Execute the routing decision and process the query"""
        
        if decision.primary_path == ProcessingPath.STRUCTURED_ONLY:
            return await self.hybrid_engine.process_query(df, query, format_type)
        
        elif decision.primary_path == ProcessingPath.SEMANTIC_ONLY:
            # Route to RAG/semantic processing
            # This would integrate with existing RAG system
            result = QueryResult(query_type=QueryType.SEMANTIC)
            result.semantic_result = "Semantic processing would happen here"
            result.confidence_score = 0.8
            return result
        
        elif decision.primary_path == ProcessingPath.HYBRID_STRUCTURED_FIRST:
            # Process structured first, then enhance with AI
            structured_result = await self.hybrid_engine.process_query(df, query, format_type)
            
            # Add AI enhancement
            if structured_result.structured_result:
                natural_response = self.hybrid_engine.generate_natural_language_response(structured_result, query)
                structured_result.semantic_result = natural_response
                
                # Add insights
                structured_result.insights = [
                    "📊 Structured analysis completed successfully",
                    "🎯 Data patterns identified and quantified",
                    "💡 Consider exploring related metrics for deeper insights"
                ]
            
            return structured_result
        
        elif decision.primary_path == ProcessingPath.AI_ENHANCED:
            # Full AI-enhanced processing
            structured_result = await self.hybrid_engine.process_query(df, query, format_type)
            
            # Enhanced AI processing would go here
            natural_response = self.hybrid_engine.generate_natural_language_response(structured_result, query)
            structured_result.semantic_result = natural_response
            structured_result.insights = [
                "🧠 AI-enhanced analysis provides comprehensive insights",
                "📈 Advanced patterns and correlations identified",
                "🎯 Actionable recommendations generated"
            ]
            
            return structured_result
        
        else:
            # Fallback to hybrid processing
            return await self.hybrid_engine.process_query(df, query, format_type)

# Global instance
intelligent_router = IntelligentQueryRouter()

if __name__ == "__main__":
    print("🧠 Intelligent Query Router for AIthentiq")
    print("🎯 Revolutionary query routing with ML-like decision trees")
    print("⚡ Optimal path selection for maximum performance")
    print("🚀 Hybrid structured + semantic processing")
