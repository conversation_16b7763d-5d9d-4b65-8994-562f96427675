'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

interface RiskTrendChartProps {
  data: {
    dates: string[];
    scores: number[];
    levels: string[];
  };
  title?: string;
}

export default function RiskTrendChart({ data, title = "Risk Score Trend" }: RiskTrendChartProps) {
  if (!data || !data.dates || data.dates.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="flex items-center justify-center h-64 text-gray-500">
          No trend data available
        </div>
      </div>
    );
  }

  // Convert scores to percentages
  const percentageScores = data.scores.map(score => score * 100);

  // Create color array based on risk levels
  const colors = data.levels.map(level => {
    switch (level.toLowerCase()) {
      case 'low': return '#10B981'; // green
      case 'moderate': return '#F59E0B'; // yellow
      case 'high': return '#EF4444'; // red
      default: return '#6B7280'; // gray
    }
  });

  const plotData = [
    {
      x: data.dates.map(date => new Date(date).toLocaleDateString()),
      y: percentageScores,
      type: 'scatter' as const,
      mode: 'lines+markers' as const,
      name: 'Risk Score',
      line: {
        color: '#3B82F6',
        width: 3
      },
      marker: {
        color: colors,
        size: 8,
        line: {
          color: '#FFFFFF',
          width: 2
        }
      },
      hovertemplate: '<b>%{x}</b><br>Risk Score: %{y:.1f}%<br>Level: %{text}<extra></extra>',
      text: data.levels.map(level => level.charAt(0).toUpperCase() + level.slice(1))
    }
  ];

  const layout = {
    title: {
      text: title,
      font: { size: 16, color: '#1F2937' }
    },
    xaxis: {
      title: 'Date',
      gridcolor: '#F3F4F6',
      tickfont: { size: 12, color: '#6B7280' }
    },
    yaxis: {
      title: 'Risk Score (%)',
      range: [0, 100],
      gridcolor: '#F3F4F6',
      tickfont: { size: 12, color: '#6B7280' }
    },
    plot_bgcolor: '#FFFFFF',
    paper_bgcolor: '#FFFFFF',
    margin: { t: 50, r: 30, b: 50, l: 60 },
    height: 300,
    showlegend: false,
    hovermode: 'closest' as const
  };

  const config = {
    displayModeBar: false,
    responsive: true
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <Plot
        data={plotData}
        layout={layout}
        config={config}
        style={{ width: '100%', height: '300px' }}
      />
    </div>
  );
}
