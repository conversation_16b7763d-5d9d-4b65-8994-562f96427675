"""
Simplified Data Connectors API Router
Works without complex middleware dependencies
"""

from fastapi import APIRouter, Depends, HTTPException, Header
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
import logging
import json
from datetime import datetime, timedelta

from database import get_db
import models

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/data-connectors", tags=["Data Connectors"])

# Pydantic models for API

class DataConnectorCreate(BaseModel):
    """Create data connector request"""
    name: str = Field(..., description="Connector name")
    connector_type: str = Field(..., description="Connector type (sharepoint, onedrive, google_drive, etc.)")
    description: Optional[str] = Field(default=None, description="Connector description")
    config: Dict[str, Any] = Field(..., description="Connector configuration")
    auto_sync: bool = Field(default=True, description="Enable automatic sync")

class DataConnectorResponse(BaseModel):
    """Data connector response"""
    id: str
    name: str
    connector_type: str
    description: Optional[str]
    status: str
    auto_sync: bool
    last_sync_at: Optional[str]
    created_at: str
    config: Dict[str, Any]

class SyncRequest(BaseModel):
    """Manual sync request"""
    force_full_sync: bool = Field(default=False, description="Force full sync")

def get_user_id_from_header(x_user_id: str = Header(None)) -> str:
    """Simple user ID extraction from header"""
    if not x_user_id:
        # For now, return a default user ID for testing
        return "104938478886224793097"  # <EMAIL> user ID
    return x_user_id

@router.get("/", response_model=List[DataConnectorResponse])
async def list_data_connectors(
    connector_type: Optional[str] = None,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    List data connectors for user
    """
    try:
        # For now, return sample connectors for demonstration
        sample_connectors = [
            {
                "id": "sharepoint-1",
                "name": "Company SharePoint",
                "connector_type": "sharepoint",
                "description": "Main company SharePoint site",
                "status": "active",
                "auto_sync": True,
                "auto_sync_enabled": True,
                "sync_frequency": "hourly",
                "sync_count": 24,
                "last_sync_at": datetime.utcnow().isoformat(),
                "next_sync_at": (datetime.utcnow().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)).isoformat(),
                "created_at": datetime.utcnow().isoformat(),
                "config": {"site_url": "https://company.sharepoint.com"},
                "last_error": None
            },
            {
                "id": "onedrive-1",
                "name": "Personal OneDrive",
                "connector_type": "onedrive",
                "description": "Personal OneDrive files",
                "status": "active",
                "auto_sync": True,
                "auto_sync_enabled": True,
                "sync_frequency": "daily",
                "sync_count": 7,
                "last_sync_at": datetime.utcnow().isoformat(),
                "next_sync_at": (datetime.utcnow().replace(hour=2, minute=0, second=0, microsecond=0) + timedelta(days=1)).isoformat(),
                "created_at": datetime.utcnow().isoformat(),
                "config": {"drive_id": "personal"},
                "last_error": None
            },
            {
                "id": "gdrive-1",
                "name": "Google Drive",
                "connector_type": "google_drive",
                "description": "Google Drive documents",
                "status": "error",
                "auto_sync": False,
                "auto_sync_enabled": False,
                "sync_frequency": "weekly",
                "sync_count": 3,
                "last_sync_at": (datetime.utcnow() - timedelta(days=2)).isoformat(),
                "next_sync_at": None,
                "created_at": datetime.utcnow().isoformat(),
                "config": {"folder_id": "root"},
                "last_error": "Authentication failed: Invalid credentials"
            },
            {
                "id": "dropbox-1",
                "name": "Team Dropbox",
                "connector_type": "dropbox",
                "description": "Shared team files and documents",
                "status": "paused",
                "auto_sync": True,
                "auto_sync_enabled": True,
                "sync_frequency": "daily",
                "sync_count": 12,
                "last_sync_at": (datetime.utcnow() - timedelta(hours=6)).isoformat(),
                "next_sync_at": None,
                "created_at": datetime.utcnow().isoformat(),
                "config": {"team_folder": "shared"},
                "last_error": None
            }
        ]
        
        # Filter by connector type if specified
        if connector_type:
            sample_connectors = [c for c in sample_connectors if c.connector_type == connector_type]

        return {"connectors": sample_connectors}
        
    except Exception as e:
        logger.error(f"Failed to list data connectors: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list data connectors: {str(e)}"
        )

@router.post("/", response_model=DataConnectorResponse)
async def create_data_connector(
    connector_data: DataConnectorCreate,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Create a new data connector
    """
    try:
        # For now, return a mock response
        return DataConnectorResponse(
            id=f"{connector_data.connector_type}-{datetime.utcnow().timestamp()}",
            name=connector_data.name,
            connector_type=connector_data.connector_type,
            description=connector_data.description,
            status="active",
            auto_sync=connector_data.auto_sync,
            last_sync_at=None,
            created_at=datetime.utcnow().isoformat(),
            config=connector_data.config
        )
        
    except Exception as e:
        logger.error(f"Failed to create data connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create data connector: {str(e)}"
        )

@router.get("/{connector_id}", response_model=DataConnectorResponse)
async def get_data_connector(
    connector_id: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get data connector details
    """
    try:
        # For now, return a mock response based on connector_id
        connector_type = connector_id.split('-')[0] if '-' in connector_id else "unknown"
        
        return DataConnectorResponse(
            id=connector_id,
            name=f"Sample {connector_type.title()} Connector",
            connector_type=connector_type,
            description=f"Sample {connector_type} connector for demonstration",
            status="active",
            auto_sync=True,
            last_sync_at=datetime.utcnow().isoformat(),
            created_at=datetime.utcnow().isoformat(),
            config={"sample": "config"}
        )
        
    except Exception as e:
        logger.error(f"Failed to get data connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get data connector: {str(e)}"
        )

@router.post("/{connector_id}/sync")
async def sync_data_connector(
    connector_id: str,
    sync_request: SyncRequest,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Manually trigger sync for data connector
    """
    try:
        return {
            "success": True,
            "message": f"Sync started for data connector {connector_id}",
            "connector_id": connector_id,
            "force_full_sync": sync_request.force_full_sync,
            "sync_started_at": datetime.utcnow().isoformat(),
            "estimated_duration": "5-15 minutes"
        }
        
    except Exception as e:
        logger.error(f"Failed to sync data connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to sync data connector: {str(e)}"
        )

@router.delete("/{connector_id}")
async def delete_data_connector(
    connector_id: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Delete data connector
    """
    try:
        return {
            "success": True,
            "message": f"Data connector {connector_id} deleted successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to delete data connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete data connector: {str(e)}"
        )

@router.get("/types/supported")
async def get_supported_connector_types():
    """
    Get list of supported connector types
    """
    return {
        "supported_types": [
            {
                "type": "sharepoint",
                "name": "SharePoint",
                "description": "Microsoft SharePoint sites and document libraries",
                "features": ["incremental_sync", "permissions", "metadata"],
                "auth_required": True
            },
            {
                "type": "onedrive",
                "name": "OneDrive",
                "description": "Microsoft OneDrive files and folders",
                "features": ["incremental_sync", "file_watching", "sharing"],
                "auth_required": True
            },
            {
                "type": "google_drive",
                "name": "Google Drive",
                "description": "Google Drive documents and files",
                "features": ["incremental_sync", "real_time_updates", "collaboration"],
                "auth_required": True
            },
            {
                "type": "dropbox",
                "name": "Dropbox",
                "description": "Dropbox files and shared folders",
                "features": ["incremental_sync", "file_watching", "sharing"],
                "auth_required": True
            },
            {
                "type": "network_folder",
                "name": "Network Folder",
                "description": "Local network drives and shared folders",
                "features": ["file_watching", "permissions", "local_agent"],
                "auth_required": False
            }
        ]
    }

@router.get("/status/health")
async def data_connector_health():
    """
    Health check for data connector service
    """
    return {
        "status": "healthy",
        "service": "data-connectors",
        "timestamp": datetime.utcnow().isoformat(),
        "features": [
            "connector_management",
            "manual_sync",
            "multi_platform_support"
        ]
    }
