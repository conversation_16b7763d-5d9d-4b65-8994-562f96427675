import pandas as pd
import json
import uuid
import sys
from sqlalchemy.orm import Session
from database import get_db, engine
import models

print("Starting demo data creation script...")
print(f"Python version: {sys.version}")
print(f"Working directory: {sys.path[0]}")

# Create tables if they don't exist
models.Base.metadata.create_all(bind=engine)

# Get a database session
db = next(get_db())

# Create a demo user if it doesn't exist
demo_user_id = "demo-user-id"
demo_user = db.query(models.User).filter(models.User.id == demo_user_id).first()

if not demo_user:
    demo_user = models.User(
        id=demo_user_id,
        email="<EMAIL>",
        role="user",
        subscription_status="free",
        referral_code=str(uuid.uuid4())
    )
    db.add(demo_user)
    db.commit()
    db.refresh(demo_user)
    print(f"Created demo user: {demo_user.id}")
else:
    print(f"Demo user already exists: {demo_user.id}")

# Create a demo dataset if none exist
dataset_count = db.query(models.Dataset).filter(models.Dataset.user_id == demo_user_id).count()

if dataset_count == 0:
    # Create a sample sales dataset
    data = {
        'Date': pd.date_range(start='2023-01-01', periods=100, freq='D'),
        'Product': ['Product A', 'Product B', 'Product C', 'Product D'] * 25,
        'Region': ['North', 'South', 'East', 'West'] * 25,
        'Sales': [round(100 + 50 * i + 200 * (i % 7), 2) for i in range(100)],
        'Units': [int(10 + 5 * i + 20 * (i % 7)) for i in range(100)]
    }

    df = pd.DataFrame(data)

    # Create dataset in database
    dataset = models.Dataset(
        user_id=demo_user_id,
        name="Sample Sales Data",
        columns=json.dumps(df.columns.tolist()),
        row_count=len(df),
        parsed_data=df.to_json(orient="records")
    )

    db.add(dataset)
    db.commit()
    db.refresh(dataset)
    print(f"Created demo dataset: {dataset.name} (ID: {dataset.id})")
else:
    print(f"Demo user already has {dataset_count} datasets")

# Check datasets
datasets = db.query(models.Dataset).filter(models.Dataset.user_id == demo_user_id).all()
print(f"\nDatasets for demo user: {len(datasets)}")
for dataset in datasets:
    print(f"  - ID: {dataset.id}, Name: {dataset.name}")
    print(f"    Columns: {dataset.columns}")
    print(f"    Created: {dataset.created_at}")

# Close the session
db.close()
