'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { MessageSquare, Database, Zap, Brain, History, Settings, Upload, Plus, RefreshCw } from 'lucide-react';
import Header from '../../../components/ui/navbar';
import Footer from '../../../components/ui/footer';
import RAGChatInterface from '../../../components/chat/rag-chat-interface';
import ConversationHistory from '../../../components/chat/conversation-history';
import api, { createApiInstance } from '@/lib/api';

interface Dataset {
  id: number;
  name: string;
  columns: string[];
  row_count: number;
  created_at: string;
  file_type?: string;
  content_type?: string;
  processing_status?: string;
  word_count?: number;
  character_count?: number;
  total_chunks?: number;
  chunking_template?: string;
}

interface Conversation {
  id: string;
  title: string;
  datasetId: number;
  datasetName: string;
  lastMessage: string;
  messageCount: number;
  createdAt: Date;
  updatedAt: Date;
  isImportant: boolean;
  isTemporary?: boolean;
  savedQuery?: any;
}

export default function RAGChatPage() {
  const { data: session, status } = useSession();
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [historyRefreshTrigger, setHistoryRefreshTrigger] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUpload, setShowUpload] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Load datasets on component mount and when session becomes available
  useEffect(() => {
    if (session && status === 'authenticated') {
      loadDatasets();
    }
  }, [session, status, refreshTrigger]);

  const loadDatasets = async () => {
    setIsLoading(true);
    try {
      // Use session-aware API instance
      const sessionApi = createApiInstance(session);
      const response = await sessionApi.get('/api/v1/datasets');
      const allDatasets = response.data;
      setDatasets(allDatasets);

      // Auto-select first dataset if available
      if (allDatasets.length > 0 && !selectedDataset) {
        setSelectedDataset(allDatasets[0]);
      }
    } catch (error) {
      console.error('Failed to load datasets:', error);
      setError('Failed to load datasets');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDatasetChange = (dataset: Dataset | null) => {
    setSelectedDataset(dataset);
    setCurrentConversationId(null); // Reset conversation when dataset changes
  };

  const handleSelectConversation = (conversation: Conversation) => {
    // Find and select the dataset for this conversation
    const dataset = datasets.find(d => d.id === conversation.datasetId);
    if (dataset) {
      setSelectedDataset(dataset);
      setCurrentConversationId(conversation.id);
      // TODO: Load conversation messages
    }
  };

  const refreshConversationHistory = () => {
    setHistoryRefreshTrigger(prev => prev + 1);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', file.name);

      // Use session-aware API instance
      const sessionApi = createApiInstance(session);
      const response = await sessionApi.post('/api/v1/datasets', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data) {
        // Reload datasets and get the updated list
        await loadDatasets();
        setShowUpload(false);

        // Auto-select the newly uploaded dataset using the response data
        if (response.data.id) {
          // Use a small delay to ensure state is updated
          setTimeout(() => {
            setDatasets(prevDatasets => {
              const newDataset = prevDatasets.find(d => d.id === response.data.id);
              if (newDataset) {
                setSelectedDataset(newDataset);
              }
              return prevDatasets;
            });
          }, 500);
        }
      }
    } catch (error: any) {
      console.error('Upload failed:', error);
      setError(error.response?.data?.detail || 'Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading RAG Chat...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h1>
          <p className="text-gray-600">Please sign in to access RAG Chat.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="bg-gradient-to-r from-green-600 to-blue-600 p-3 rounded-lg">
                <MessageSquare className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">RAG Chat</h1>
                <p className="text-gray-600">Conversational AI for your documents with persistent context</p>
              </div>
            </div>
            
            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center space-x-2 mb-2">
                  <MessageSquare className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-gray-900">Conversational</span>
                </div>
                <p className="text-sm text-gray-600">Natural chat interface with context memory</p>
              </div>
              
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Brain className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-gray-900">Context Aware</span>
                </div>
                <p className="text-sm text-gray-600">Remembers previous questions and builds on them</p>
              </div>
              
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Zap className="h-5 w-5 text-purple-600" />
                  <span className="font-medium text-gray-900">Multi-Turn</span>
                </div>
                <p className="text-sm text-gray-600">Handle complex, multi-step reasoning</p>
              </div>
              
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center space-x-2 mb-2">
                  <History className="h-5 w-5 text-orange-600" />
                  <span className="font-medium text-gray-900">Persistent</span>
                </div>
                <p className="text-sm text-gray-600">Save and resume conversations</p>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* Main Chat Interface */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Conversation History Sidebar */}
            {showHistory && (
              <div className="lg:col-span-1">
                <ConversationHistory
                  onSelectConversation={handleSelectConversation}
                  currentConversationId={currentConversationId || undefined}
                  className="h-[600px]"
                  refreshTrigger={historyRefreshTrigger}
                />
              </div>
            )}
            
            {/* Chat Interface */}
            <div className={showHistory ? "lg:col-span-3" : "lg:col-span-4"}>
              <div className="bg-white rounded-lg border border-gray-200 h-[600px]">
                {datasets.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Documents Found</h3>
                      <p className="text-gray-600 mb-4">Upload a document to start chatting with your data.</p>
                      <div className="space-y-2">
                        <button
                          onClick={() => setShowUpload(true)}
                          className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          <Upload className="h-4 w-4" />
                          <span>Upload Document</span>
                        </button>
                        <div>
                          <button
                            onClick={() => setRefreshTrigger(prev => prev + 1)}
                            className="inline-flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                          >
                            <RefreshCw className="h-3 w-3" />
                            <span>Refresh</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <RAGChatInterface
                    selectedDataset={selectedDataset}
                    onDatasetChange={handleDatasetChange}
                    availableDatasets={datasets}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <button
                onClick={() => setShowHistory(!showHistory)}
                className="flex items-center space-x-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <History className="h-5 w-5 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">
                  {showHistory ? 'Hide' : 'Show'} History
                </span>
              </button>
              
              <button
                onClick={() => {
                  setCurrentConversationId(null);
                  setSelectedDataset(null);
                  window.location.reload();
                }}
                className="flex items-center space-x-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <MessageSquare className="h-5 w-5 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">New Conversation</span>
              </button>
              
              <button
                onClick={() => setShowUpload(true)}
                className="flex items-center space-x-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Upload className="h-5 w-5 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Upload Document</span>
              </button>

              <button
                onClick={() => setShowSettings(true)}
                className="flex items-center space-x-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Settings className="h-5 w-5 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">Chat Settings</span>
              </button>
            </div>
          </div>

          {/* Usage Tips */}
          <div className="mt-8 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">💡 Pro Tips for RAG Chat</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
              <div>
                <h4 className="font-medium mb-2">🎯 Effective Questioning:</h4>
                <ul className="space-y-1">
                  <li>• Ask follow-up questions to dive deeper</li>
                  <li>• Reference previous answers in new questions</li>
                  <li>• Use "Can you elaborate on..." for more detail</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">🧠 Context Building:</h4>
                <ul className="space-y-1">
                  <li>• Start with broad questions, then get specific</li>
                  <li>• Build on previous answers naturally</li>
                  <li>• Use conversation history to track insights</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Upload Modal */}
      {showUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Document</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select a file to upload
              </label>
              <input
                type="file"
                onChange={handleFileUpload}
                accept=".pdf,.doc,.docx,.txt,.md,.csv,.xlsx"
                disabled={isUploading}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50"
              />
            </div>

            {isUploading && (
              <div className="mb-4">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-sm text-gray-600">Uploading...</span>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowUpload(false)}
                disabled={isUploading}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Chat Settings</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Response Style
                </label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                  <option>Detailed</option>
                  <option>Concise</option>
                  <option>Technical</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Trust Score Display
                </label>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" defaultChecked className="rounded" />
                  <span className="text-sm text-gray-600">Show trust scores</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Auto-scroll
                </label>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" defaultChecked className="rounded" />
                  <span className="text-sm text-gray-600">Auto-scroll to new messages</span>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowSettings(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowSettings(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save Settings
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
