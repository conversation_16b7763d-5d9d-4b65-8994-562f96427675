'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Shield, 
  Mail, 
  Calendar,
  Search,
  Filter,
  Download,
  UserCheck,
  UserX,
  Settings,
  Eye,
  Lock,
  Unlock,
  Database,
  Key
} from 'lucide-react';
import { createApiInstance } from '@/lib/api';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user' | 'viewer';
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  last_login?: string;
  avatar_url?: string;
  permissions: {
    can_upload_datasets: boolean;
    can_create_queries: boolean;
    can_manage_connectors: boolean;
    can_view_analytics: boolean;
    can_manage_users: boolean;
  };
  data_sources_access: string[];
  subscription_status: string;
  email_verified: boolean;
}

interface UserFormData {
  name: string;
  email: string;
  role: 'admin' | 'user' | 'viewer';
  permissions: {
    can_upload_datasets: boolean;
    can_create_queries: boolean;
    can_manage_connectors: boolean;
    can_view_analytics: boolean;
    can_manage_users: boolean;
  };
  data_sources_access: string[];
}

export default function ComprehensiveUserManagement() {
  const { data: session } = useSession();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showUserModal, setShowUserModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const [userForm, setUserForm] = useState<UserFormData>({
    name: '',
    email: '',
    role: 'user',
    permissions: {
      can_upload_datasets: true,
      can_create_queries: true,
      can_manage_connectors: false,
      can_view_analytics: false,
      can_manage_users: false
    },
    data_sources_access: []
  });

  const availableDataSources = [
    { id: 'github', name: 'GitHub', icon: '🔗' },
    { id: 'sharepoint', name: 'SharePoint', icon: '📊' },
    { id: 'onedrive', name: 'OneDrive', icon: '☁️' },
    { id: 'google_drive', name: 'Google Drive', icon: '📁' },
    { id: 'dropbox', name: 'Dropbox', icon: '📦' },
    { id: 'database', name: 'Database', icon: '🗄️' },
    { id: 'api', name: 'API', icon: '🌐' },
    { id: 'file_upload', name: 'File Upload', icon: '📄' }
  ];

  useEffect(() => {
    if (session) {
      loadUsers();
    }
  }, [session]);

  const loadUsers = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.get('/admin/users');
      setUsers(response.data.users || []);
    } catch (err) {
      // Generate mock data for demonstration
      const mockUsers: User[] = [
        {
          id: '1',
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'admin',
          status: 'active',
          created_at: '2024-01-15T10:00:00Z',
          last_login: '2024-01-20T14:30:00Z',
          permissions: {
            can_upload_datasets: true,
            can_create_queries: true,
            can_manage_connectors: true,
            can_view_analytics: true,
            can_manage_users: true
          },
          data_sources_access: ['github', 'sharepoint', 'onedrive'],
          subscription_status: 'enterprise',
          email_verified: true
        },
        {
          id: '2',
          email: '<EMAIL>',
          name: 'John Smith',
          role: 'user',
          status: 'active',
          created_at: '2024-01-16T09:15:00Z',
          last_login: '2024-01-19T16:45:00Z',
          permissions: {
            can_upload_datasets: true,
            can_create_queries: true,
            can_manage_connectors: false,
            can_view_analytics: false,
            can_manage_users: false
          },
          data_sources_access: ['github'],
          subscription_status: 'pro',
          email_verified: true
        },
        {
          id: '3',
          email: '<EMAIL>',
          name: 'Jane Doe',
          role: 'viewer',
          status: 'active',
          created_at: '2024-01-17T11:20:00Z',
          permissions: {
            can_upload_datasets: false,
            can_create_queries: true,
            can_manage_connectors: false,
            can_view_analytics: false,
            can_manage_users: false
          },
          data_sources_access: ['sharepoint'],
          subscription_status: 'free',
          email_verified: false
        }
      ];
      setUsers(mockUsers);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async () => {
    if (!session) return;

    try {
      const api = createApiInstance(session);
      const response = await api.post('/admin/users', userForm);
      
      if (response.data.success) {
        await loadUsers();
        setShowUserModal(false);
        resetUserForm();
        alert('User created successfully!');
      }
    } catch (err) {
      alert(`Failed to create user: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleUpdateUser = async () => {
    if (!session || !editingUser) return;

    try {
      const api = createApiInstance(session);
      const response = await api.put(`/admin/users/${editingUser.id}`, userForm);
      
      if (response.data.success) {
        await loadUsers();
        setShowUserModal(false);
        setEditingUser(null);
        resetUserForm();
        alert('User updated successfully!');
      }
    } catch (err) {
      alert(`Failed to update user: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!session) return;
    
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return;
    }

    try {
      const api = createApiInstance(session);
      const response = await api.delete(`/admin/users/${userId}`);
      
      if (response.data.success) {
        await loadUsers();
        alert('User deleted successfully!');
      }
    } catch (err) {
      alert(`Failed to delete user: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: string) => {
    if (!session) return;

    const newStatus = currentStatus === 'active' ? 'suspended' : 'active';

    try {
      const api = createApiInstance(session);
      const response = await api.put(`/admin/users/${userId}/status`, {
        status: newStatus
      });
      
      if (response.data.success) {
        await loadUsers();
        alert(`User ${newStatus === 'active' ? 'activated' : 'suspended'} successfully!`);
      }
    } catch (err) {
      alert(`Failed to update user status: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const resetUserForm = () => {
    setUserForm({
      name: '',
      email: '',
      role: 'user',
      permissions: {
        can_upload_datasets: true,
        can_create_queries: true,
        can_manage_connectors: false,
        can_view_analytics: false,
        can_manage_users: false
      },
      data_sources_access: []
    });
  };

  const openEditModal = (user: User) => {
    setEditingUser(user);
    setUserForm({
      name: user.name,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      data_sources_access: user.data_sources_access
    });
    setShowUserModal(true);
  };

  const openPermissionsModal = (user: User) => {
    setSelectedUser(user);
    setShowPermissionsModal(true);
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-4 w-4 text-red-500" />;
      case 'user':
        return <Users className="h-4 w-4 text-blue-500" />;
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return <Users className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-4 w-4 text-green-500" />;
      case 'suspended':
        return <UserX className="h-4 w-4 text-red-500" />;
      case 'inactive':
        return <UserX className="h-4 w-4 text-gray-500" />;
      default:
        return <UserX className="h-4 w-4 text-gray-500" />;
    }
  };

  const exportUsers = () => {
    const csvContent = [
      ['Name', 'Email', 'Role', 'Status', 'Created At', 'Last Login', 'Data Sources', 'Subscription'],
      ...filteredUsers.map(user => [
        user.name,
        user.email,
        user.role,
        user.status,
        new Date(user.created_at).toLocaleDateString(),
        user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never',
        user.data_sources_access.join('; '),
        user.subscription_status
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'users-export.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Users className="h-8 w-8 text-gray-700" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
            <p className="text-sm text-gray-600">Manage users, roles, and permissions</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={exportUsers}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </button>
          <button
            onClick={() => {
              resetUserForm();
              setEditingUser(null);
              setShowUserModal(true);
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Add User</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow border p-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-gray-500" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Roles</option>
              <option value="admin">Admin</option>
              <option value="user">User</option>
              <option value="viewer">Viewer</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="suspended">Suspended</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow border overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Data Sources
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Login
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredUsers.map((user) => (
              <tr key={user.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                      <Users className="h-5 w-5 text-gray-500" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                      {!user.email_verified && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                          Unverified
                        </span>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    {getRoleIcon(user.role)}
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.role === 'admin' ? 'bg-red-100 text-red-800' :
                      user.role === 'user' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {user.role}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(user.status)}
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.status === 'active' ? 'bg-green-100 text-green-800' :
                      user.status === 'suspended' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {user.status}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex flex-wrap gap-1">
                    {user.data_sources_access.slice(0, 3).map((source) => (
                      <span key={source} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        {source}
                      </span>
                    ))}
                    {user.data_sources_access.length > 3 && (
                      <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">
                        +{user.data_sources_access.length - 3} more
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => openEditModal(user)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Edit User"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => openPermissionsModal(user)}
                      className="text-green-600 hover:text-green-900"
                      title="Manage Permissions"
                    >
                      <Key className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleToggleUserStatus(user.id, user.status)}
                      className="text-yellow-600 hover:text-yellow-900"
                      title={user.status === 'active' ? 'Suspend User' : 'Activate User'}
                    >
                      {user.status === 'active' ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
                    </button>
                    <button
                      onClick={() => handleDeleteUser(user.id)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete User"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Users Found</h3>
            <p className="text-gray-600 mb-4">No users match your current filters.</p>
          </div>
        )}
      </div>
    </div>
  );
}
