'use client';

import React, { useState, useEffect } from 'react';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Eye,
  TrendingUp,
  TrendingDown,
  Users,
  Zap,
  Shield,
  BarChart3,
  PieChart,
  LineChart,
  AlertCircle,
  Info,
  XCircle,
  RefreshCw,
  FileText,
  Star
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { createApiInstance } from '../../lib/api';

interface SystemHealth {
  status: string;
  total_queries: number;
  error_rate: number;
  source_attribution_rate: number;
  average_confidence: number;
}

interface Alert {
  level: string;
  message: string;
  recommendation: string;
}

interface MonitoringData {
  system_health: {
    system_health: SystemHealth;
    performance_metrics: any;
    dataset_performance: any[];
    alerts: Alert[];
  };
  user_analytics: any;
  timestamp: string;
}

export default function MonitoringDashboard() {
  const { data: session } = useSession();
  const [data, setData] = useState<MonitoringData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (session) {
      loadDashboardData();
    }
  }, [session]);

  const loadDashboardData = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.get('/monitoring/dashboard-metrics');
      setData(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load monitoring data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'unhealthy': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertIcon = (level: string) => {
    switch (level) {
      case 'critical': return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'info': return <Info className="h-5 w-5 text-blue-500" />;
      default: return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading monitoring data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6">
        <div className="flex items-center space-x-3 text-red-600">
          <AlertCircle className="h-6 w-6" />
          <div>
            <h3 className="font-semibold">Monitoring Error</h3>
            <p className="text-sm text-red-500">{error}</p>
          </div>
        </div>
        <button
          onClick={loadDashboardData}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12 text-gray-500">
          <Database className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p>No monitoring data available</p>
        </div>
      </div>
    );
  }

  const systemHealth = data.system_health.system_health;
  const alerts = data.system_health.alerts || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Monitoring</h1>
          <p className="text-gray-600">Real-time system health and performance metrics</p>
        </div>
        <button
          onClick={refreshData}
          disabled={refreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* System Status */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Activity className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">System Status</h2>
            <p className="text-gray-600">Overall system health and performance</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">System Status</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(systemHealth.status)}`}>
                {systemHealth.status.toUpperCase()}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {systemHealth.status === 'healthy' ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              )}
              <span className="text-lg font-bold text-gray-900">
                {systemHealth.status === 'healthy' ? 'Operational' : 'Issues Detected'}
              </span>
            </div>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <Database className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Total Queries</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{systemHealth.total_queries.toLocaleString()}</p>
            <p className="text-xs text-gray-500">Last 24 hours</p>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Success Rate</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{(100 - systemHealth.error_rate).toFixed(1)}%</p>
            <div className="flex items-center space-x-1 text-xs">
              {systemHealth.error_rate < 5 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
              <span className={systemHealth.error_rate < 5 ? 'text-green-600' : 'text-red-600'}>
                {systemHealth.error_rate.toFixed(1)}% error rate
              </span>
            </div>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <Eye className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Source Attribution</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{systemHealth.source_attribution_rate.toFixed(1)}%</p>
            <div className="flex items-center space-x-1 text-xs">
              {systemHealth.source_attribution_rate > 95 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-yellow-500" />
              )}
              <span className={systemHealth.source_attribution_rate > 95 ? 'text-green-600' : 'text-yellow-600'}>
                Avg confidence: {(systemHealth.average_confidence * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Professional Quality Metrics */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
            <Shield className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Professional Quality Metrics</h2>
            <p className="text-gray-600">Citation verification and quality control</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <Star className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Average Trust Score</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{(systemHealth.average_confidence * 100).toFixed(0)}%</p>
            <div className="flex items-center space-x-1 text-xs">
              <TrendingUp className="h-3 w-3 text-purple-500" />
              <span className="text-purple-600">Professional quality</span>
            </div>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium text-gray-700">Hallucination Risk</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {((1 - systemHealth.average_confidence) * 100).toFixed(1)}%
            </p>
            <div className="flex items-center space-x-1 text-xs">
              <Shield className="h-3 w-3 text-green-500" />
              <span className="text-green-600">Low risk detected</span>
            </div>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Citation Accuracy</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {(systemHealth.source_attribution_rate * 100).toFixed(0)}%
            </p>
            <div className="flex items-center space-x-1 text-xs">
              <FileText className="h-3 w-3 text-blue-500" />
              <span className="text-blue-600">Sources verified</span>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="h-6 w-6 text-yellow-600" />
            <h3 className="text-lg font-semibold text-gray-900">System Alerts</h3>
            <span className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">
              {alerts.length} active
            </span>
          </div>

          <div className="space-y-3">
            {alerts.map((alert, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  alert.level === 'critical' 
                    ? 'border-red-200 bg-red-50' 
                    : alert.level === 'warning'
                    ? 'border-yellow-200 bg-yellow-50'
                    : 'border-blue-200 bg-blue-50'
                }`}
              >
                <div className="flex items-start space-x-3">
                  {getAlertIcon(alert.level)}
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{alert.message}</h4>
                    <p className="text-sm text-gray-600 mt-1">{alert.recommendation}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Query Volume */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Query Volume</h3>
          </div>
          <div className="text-center py-8">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {data.system_health.performance_metrics?.queries_per_hour?.toFixed(1) || '0'}
            </div>
            <p className="text-gray-600">Queries per hour</p>
          </div>
        </div>

        {/* User Activity */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Users className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">User Activity</h3>
          </div>
          <div className="text-center py-8">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {data.user_analytics?.usage_summary?.queries_per_day?.toFixed(1) || '0'}
            </div>
            <p className="text-gray-600">Your queries per day</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-sm text-gray-500">
        Last updated: {new Date(data.timestamp).toLocaleString()}
      </div>
    </div>
  );
}
