import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class TimeSeriesService:
    """
    Service for time series analysis and forecasting
    """

    def __init__(self, df: Optional[pd.DataFrame] = None):
        """
        Initialize the time series service

        Args:
            df: Optional DataFrame to analyze
        """
        self.df = df

    def set_dataframe(self, df: pd.DataFrame) -> None:
        """
        Set the DataFrame to analyze

        Args:
            df: DataFrame to analyze
        """
        self.df = df

    def prepare_time_series(
        self,
        value_column: str,
        date_column: str,
        frequency: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Prepare time series data for analysis

        Args:
            value_column: Column containing the values to analyze
            date_column: Column containing the dates
            frequency: Time series frequency (e.g., 'D', 'M', 'Y')

        Returns:
            DataFrame with prepared time series
        """
        if self.df is None:
            raise ValueError("DataFrame not set")

        if value_column not in self.df.columns:
            raise ValueError(f"Column '{value_column}' not found in DataFrame")

        if date_column not in self.df.columns:
            raise ValueError(f"Column '{date_column}' not found in DataFrame")

        # Ensure date column is datetime type
        try:
            date_series = pd.to_datetime(self.df[date_column])
        except:
            raise ValueError(f"Column '{date_column}' cannot be converted to datetime")

        # Create time series
        ts_df = self.df[[date_column, value_column]].copy()
        ts_df['date'] = date_series
        ts_df = ts_df.sort_values('date')

        # Drop rows with missing values
        ts_df = ts_df.dropna(subset=[value_column])

        if len(ts_df) < 4:
            raise ValueError("Not enough data points for time series analysis")

        # Set date as index
        ts_df = ts_df.set_index('date')

        # Resample if frequency is provided
        if frequency is not None:
            ts_df = ts_df.resample(frequency).mean()

        # Fill missing values after resampling
        ts_df = ts_df.fillna(method='ffill')

        return ts_df

    def forecast(
        self,
        value_column: str,
        date_column: str,
        forecast_periods: int = 5,
        frequency: Optional[str] = None,
        method: str = 'auto'
    ) -> Dict[str, Any]:
        """
        Forecast future values

        Args:
            value_column: Column containing the values to forecast
            date_column: Column containing the dates
            forecast_periods: Number of periods to forecast
            frequency: Time series frequency (e.g., 'D', 'M', 'Y')
            method: Forecasting method ('auto', 'arima', 'exponential_smoothing', 'prophet')

        Returns:
            Dictionary with forecast results
        """
        if self.df is None:
            raise ValueError("DataFrame not set")

        # Prepare time series
        ts_df = self.prepare_time_series(value_column, date_column, frequency)

        # Determine the best method if auto is selected
        if method == 'auto':
            # Use a simple heuristic to choose the method
            if len(ts_df) < 10:
                method = 'exponential_smoothing'  # Simple method for small datasets
            else:
                method = 'exponential_smoothing'  # Use exponential smoothing as default

        # Perform forecasting using the selected method
        if method == 'arima':
            return self._forecast_arima(ts_df, value_column, forecast_periods, frequency)
        elif method == 'exponential_smoothing':
            return self._forecast_exponential_smoothing(ts_df, value_column, forecast_periods, frequency)
        elif method == 'prophet':
            try:
                return self._forecast_prophet(ts_df, value_column, forecast_periods, frequency)
            except ImportError:
                logger.warning("Prophet not available, falling back to exponential smoothing")
                return self._forecast_exponential_smoothing(ts_df, value_column, forecast_periods, frequency)
        else:
            raise ValueError(f"Unsupported forecasting method: {method}")

    def _forecast_arima(
        self,
        ts_df: pd.DataFrame,
        value_column: str,
        forecast_periods: int,
        frequency: Optional[str]
    ) -> Dict[str, Any]:
        """
        Forecast using ARIMA model

        Args:
            ts_df: Prepared time series DataFrame
            value_column: Column containing the values to forecast
            forecast_periods: Number of periods to forecast
            frequency: Time series frequency

        Returns:
            Dictionary with forecast results
        """
        try:
            from statsmodels.tsa.arima.model import ARIMA
            from pmdarima import auto_arima

            # Automatically find the best ARIMA parameters
            auto_model = auto_arima(
                ts_df[value_column],
                seasonal=False,  # Disable seasonal for simplicity
                suppress_warnings=True,
                error_action='ignore',
                max_p=3,
                max_d=2,
                max_q=3,
                stepwise=True
            )

            # Get the best parameters
            order = auto_model.order

            # Fit ARIMA model
            model = ARIMA(
                ts_df[value_column],
                order=order
            )
            model_fit = model.fit()

            # Generate forecast
            forecast = model_fit.forecast(steps=forecast_periods)
            forecast_index = self._generate_future_dates(ts_df.index, forecast_periods, frequency)

            # Get confidence intervals
            pred_conf = model_fit.get_forecast(steps=forecast_periods).conf_int()
            lower_bound = pred_conf.iloc[:, 0]
            upper_bound = pred_conf.iloc[:, 1]

            # Prepare historical data for plotting
            historical_dates = ts_df.index.strftime('%Y-%m-%d').tolist()
            historical_values = ts_df[value_column].tolist()

            # Prepare forecast data for plotting
            forecast_dates = forecast_index.strftime('%Y-%m-%d').tolist()
            forecast_values = forecast.tolist()
            lower_bound_values = lower_bound.tolist()
            upper_bound_values = upper_bound.tolist()

            return {
                "method": "ARIMA",
                "model_params": {
                    "order": order
                },
                "historical_dates": historical_dates,
                "historical_values": historical_values,
                "forecast_dates": forecast_dates,
                "forecast_values": forecast_values,
                "lower_bound": lower_bound_values,
                "upper_bound": upper_bound_values,
                "frequency": frequency
            }

        except Exception as e:
            logger.error(f"Error in ARIMA forecasting: {str(e)}")
            raise ValueError(f"Error in ARIMA forecasting: {str(e)}")

    def _forecast_exponential_smoothing(
        self,
        ts_df: pd.DataFrame,
        value_column: str,
        forecast_periods: int,
        frequency: Optional[str]
    ) -> Dict[str, Any]:
        """
        Forecast using Exponential Smoothing

        Args:
            ts_df: Prepared time series DataFrame
            value_column: Column containing the values to forecast
            forecast_periods: Number of periods to forecast
            frequency: Time series frequency

        Returns:
            Dictionary with forecast results
        """
        try:
            from statsmodels.tsa.holtwinters import ExponentialSmoothing

            # Fit simple Exponential Smoothing model (no seasonality for simplicity)
            model = ExponentialSmoothing(
                ts_df[value_column],
                trend='add'
            )
            model_fit = model.fit()

            # Generate forecast
            forecast = model_fit.forecast(steps=forecast_periods)
            forecast_index = self._generate_future_dates(ts_df.index, forecast_periods, frequency)

            # Prepare historical data for plotting
            historical_dates = ts_df.index.strftime('%Y-%m-%d').tolist()
            historical_values = ts_df[value_column].tolist()

            # Prepare forecast data for plotting
            forecast_dates = forecast_index.strftime('%Y-%m-%d').tolist()
            forecast_values = forecast.tolist()

            return {
                "method": "Exponential Smoothing",
                "model_params": {
                    "trend": "add"
                },
                "historical_dates": historical_dates,
                "historical_values": historical_values,
                "forecast_dates": forecast_dates,
                "forecast_values": forecast_values,
                "frequency": frequency
            }

        except Exception as e:
            logger.error(f"Error in Exponential Smoothing forecasting: {str(e)}")
            raise ValueError(f"Error in Exponential Smoothing forecasting: {str(e)}")

    def _forecast_prophet(
        self,
        ts_df: pd.DataFrame,
        value_column: str,
        forecast_periods: int,
        frequency: Optional[str]
    ) -> Dict[str, Any]:
        """
        Forecast using Facebook Prophet

        Args:
            ts_df: Prepared time series DataFrame
            value_column: Column containing the values to forecast
            forecast_periods: Number of periods to forecast
            frequency: Time series frequency

        Returns:
            Dictionary with forecast results
        """
        try:
            from prophet import Prophet

            # Prepare data for Prophet
            prophet_df = pd.DataFrame({
                'ds': ts_df.index,
                'y': ts_df[value_column]
            })

            # Fit Prophet model
            model = Prophet(
                yearly_seasonality=True if frequency in ['M', 'MS', 'Q', 'QS', 'D'] else False,
                weekly_seasonality=True if frequency in ['D'] else False,
                daily_seasonality=False
            )
            model.fit(prophet_df)

            # Create future dataframe
            future = model.make_future_dataframe(
                periods=forecast_periods,
                freq=frequency
            )

            # Generate forecast
            forecast = model.predict(future)

            # Prepare historical data for plotting
            historical_dates = prophet_df['ds'].dt.strftime('%Y-%m-%d').tolist()
            historical_values = prophet_df['y'].tolist()

            # Prepare forecast data for plotting
            forecast_dates = forecast['ds'].dt.strftime('%Y-%m-%d').tolist()[-forecast_periods:]
            forecast_values = forecast['yhat'].tolist()[-forecast_periods:]
            lower_bound = forecast['yhat_lower'].tolist()[-forecast_periods:]
            upper_bound = forecast['yhat_upper'].tolist()[-forecast_periods:]

            # Get components
            components = model.plot_components(forecast)

            return {
                "method": "Prophet",
                "historical_dates": historical_dates,
                "historical_values": historical_values,
                "forecast_dates": forecast_dates,
                "forecast_values": forecast_values,
                "lower_bound": lower_bound,
                "upper_bound": upper_bound,
                "frequency": frequency,
                "components": {
                    "trend": forecast['trend'].tolist()[-forecast_periods:],
                    "yearly": forecast['yearly'].tolist()[-forecast_periods:] if 'yearly' in forecast.columns else None,
                    "weekly": forecast['weekly'].tolist()[-forecast_periods:] if 'weekly' in forecast.columns else None
                }
            }

        except Exception as e:
            logger.error(f"Error in Prophet forecasting: {str(e)}")
            raise ValueError(f"Error in Prophet forecasting: {str(e)}")

    def _generate_future_dates(
        self,
        last_date_index: pd.DatetimeIndex,
        periods: int,
        frequency: Optional[str]
    ) -> pd.DatetimeIndex:
        """
        Generate future dates for forecasting

        Args:
            last_date_index: DatetimeIndex of the last date in the time series
            periods: Number of periods to generate
            frequency: Time series frequency

        Returns:
            DatetimeIndex of future dates
        """
        last_date = last_date_index[-1]

        if frequency is None:
            # Try to infer frequency
            frequency = pd.infer_freq(last_date_index)

            if frequency is None:
                # Default to daily if frequency cannot be inferred
                frequency = 'D'

        # Generate future dates
        future_dates = pd.date_range(
            start=last_date + pd.Timedelta(days=1),
            periods=periods,
            freq=frequency
        )

        return future_dates

    def detect_anomalies(
        self,
        value_column: str,
        date_column: str,
        method: str = 'zscore',
        threshold: float = 3.0,
        frequency: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Detect anomalies in time series data

        Args:
            value_column: Column containing the values to analyze
            date_column: Column containing the dates
            method: Method for anomaly detection ('zscore', 'iqr', or 'prophet')
            threshold: Threshold for anomaly detection
            frequency: Time series frequency

        Returns:
            Dictionary with anomaly detection results
        """
        if self.df is None:
            raise ValueError("DataFrame not set")

        # Prepare time series
        ts_df = self.prepare_time_series(value_column, date_column, frequency)

        if method == 'zscore':
            # Z-score method
            from scipy import stats

            z_scores = np.abs(stats.zscore(ts_df[value_column]))
            anomaly_mask = z_scores > threshold

            anomalies = ts_df[anomaly_mask]

            return {
                "method": "Z-score",
                "threshold": threshold,
                "total_points": len(ts_df),
                "anomaly_count": len(anomalies),
                "anomaly_percentage": len(anomalies) / len(ts_df) * 100 if len(ts_df) > 0 else 0,
                "dates": ts_df.index.strftime('%Y-%m-%d').tolist(),
                "values": ts_df[value_column].tolist(),
                "anomaly_dates": anomalies.index.strftime('%Y-%m-%d').tolist(),
                "anomaly_values": anomalies[value_column].tolist(),
                "z_scores": z_scores.tolist()
            }

        elif method == 'iqr':
            # IQR method
            q1 = ts_df[value_column].quantile(0.25)
            q3 = ts_df[value_column].quantile(0.75)
            iqr = q3 - q1

            lower_bound = q1 - threshold * iqr
            upper_bound = q3 + threshold * iqr

            anomaly_mask = (ts_df[value_column] < lower_bound) | (ts_df[value_column] > upper_bound)
            anomalies = ts_df[anomaly_mask]

            return {
                "method": "IQR",
                "threshold": threshold,
                "total_points": len(ts_df),
                "anomaly_count": len(anomalies),
                "anomaly_percentage": len(anomalies) / len(ts_df) * 100 if len(ts_df) > 0 else 0,
                "lower_bound": float(lower_bound),
                "upper_bound": float(upper_bound),
                "dates": ts_df.index.strftime('%Y-%m-%d').tolist(),
                "values": ts_df[value_column].tolist(),
                "anomaly_dates": anomalies.index.strftime('%Y-%m-%d').tolist(),
                "anomaly_values": anomalies[value_column].tolist()
            }

        elif method == 'prophet':
            try:
                from prophet import Prophet

                # Prepare data for Prophet
                prophet_df = pd.DataFrame({
                    'ds': ts_df.index,
                    'y': ts_df[value_column]
                })

                # Fit Prophet model
                model = Prophet(
                    yearly_seasonality=True if frequency in ['M', 'MS', 'Q', 'QS', 'D'] else False,
                    weekly_seasonality=True if frequency in ['D'] else False,
                    daily_seasonality=False
                )
                model.fit(prophet_df)

                # Create dataframe for prediction
                future = model.make_future_dataframe(
                    periods=0,
                    freq=frequency
                )

                # Generate forecast
                forecast = model.predict(future)

                # Merge with original data
                results = pd.merge(
                    prophet_df,
                    forecast[['ds', 'yhat', 'yhat_lower', 'yhat_upper']],
                    on='ds'
                )

                # Detect anomalies
                results['anomaly'] = (results['y'] < results['yhat_lower']) | (results['y'] > results['yhat_upper'])
                anomalies = results[results['anomaly']]

                return {
                    "method": "Prophet",
                    "total_points": len(results),
                    "anomaly_count": len(anomalies),
                    "anomaly_percentage": len(anomalies) / len(results) * 100 if len(results) > 0 else 0,
                    "dates": results['ds'].dt.strftime('%Y-%m-%d').tolist(),
                    "values": results['y'].tolist(),
                    "predicted": results['yhat'].tolist(),
                    "lower_bound": results['yhat_lower'].tolist(),
                    "upper_bound": results['yhat_upper'].tolist(),
                    "anomaly_dates": anomalies['ds'].dt.strftime('%Y-%m-%d').tolist(),
                    "anomaly_values": anomalies['y'].tolist()
                }

            except Exception as e:
                logger.error(f"Error in Prophet anomaly detection: {str(e)}")
                raise ValueError(f"Error in Prophet anomaly detection: {str(e)}")

        else:
            raise ValueError(f"Unsupported anomaly detection method: {method}")

    def analyze_seasonality(
        self,
        value_column: str,
        date_column: str,
        frequency: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze seasonality in time series data

        Args:
            value_column: Column containing the values to analyze
            date_column: Column containing the dates
            frequency: Time series frequency

        Returns:
            Dictionary with seasonality analysis results
        """
        if self.df is None:
            raise ValueError("DataFrame not set")

        # Prepare time series
        ts_df = self.prepare_time_series(value_column, date_column, frequency)

        try:
            from statsmodels.tsa.seasonal import seasonal_decompose
            from statsmodels.tsa.stattools import acf, pacf, adfuller

            # Perform seasonal decomposition
            result = seasonal_decompose(ts_df[value_column], model='additive')

            # Check for stationarity
            adf_result = adfuller(ts_df[value_column].dropna())
            is_stationary = adf_result[1] < 0.05

            # Calculate autocorrelation
            acf_values = acf(ts_df[value_column].dropna(), nlags=min(40, len(ts_df) // 2))

            # Find peaks in autocorrelation
            from scipy.signal import find_peaks

            peaks, _ = find_peaks(acf_values)

            if len(peaks) > 0:
                seasonal_period = peaks[0]
            else:
                seasonal_period = None

            # Convert to lists for JSON serialization
            trend = result.trend.dropna().tolist()
            seasonal = result.seasonal.dropna().tolist()
            residual = result.resid.dropna().tolist()

            # Get dates as strings
            dates = result.trend.dropna().index.strftime('%Y-%m-%d').tolist()

            return {
                "analysis_type": "Seasonality Analysis",
                "value_column": value_column,
                "date_column": date_column,
                "frequency": frequency,
                "is_stationary": bool(is_stationary),
                "adf_p_value": float(adf_result[1]),
                "detected_seasonal_period": int(seasonal_period) if seasonal_period is not None else None,
                "dates": dates,
                "trend": trend,
                "seasonal": seasonal,
                "residual": residual,
                "original": ts_df[value_column].loc[result.trend.dropna().index].tolist(),
                "acf": acf_values.tolist(),
                "lags": list(range(len(acf_values)))
            }

        except Exception as e:
            logger.error(f"Error in seasonality analysis: {str(e)}")
            raise ValueError(f"Error in seasonality analysis: {str(e)}")

    def analyze_trend(
        self,
        value_column: str,
        date_column: str,
        frequency: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze trend in time series data

        Args:
            value_column: Column containing the values to analyze
            date_column: Column containing the dates
            frequency: Time series frequency

        Returns:
            Dictionary with trend analysis results
        """
        if self.df is None:
            raise ValueError("DataFrame not set")

        # Prepare time series
        ts_df = self.prepare_time_series(value_column, date_column, frequency)

        # Calculate rolling statistics
        rolling_mean = ts_df[value_column].rolling(window=min(12, len(ts_df) // 3)).mean()
        rolling_std = ts_df[value_column].rolling(window=min(12, len(ts_df) // 3)).std()

        # Calculate overall trend
        from scipy import stats

        x = np.arange(len(ts_df))
        y = ts_df[value_column].values

        slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

        trend_line = intercept + slope * x

        # Determine trend direction
        if p_value < 0.05:
            if slope > 0:
                trend_direction = "Increasing"
            else:
                trend_direction = "Decreasing"
        else:
            trend_direction = "No significant trend"

        # Calculate percentage change
        first_value = ts_df[value_column].iloc[0]
        last_value = ts_df[value_column].iloc[-1]

        if first_value != 0:
            percentage_change = (last_value - first_value) / first_value * 100
        else:
            percentage_change = np.nan

        return {
            "analysis_type": "Trend Analysis",
            "value_column": value_column,
            "date_column": date_column,
            "frequency": frequency,
            "trend_direction": trend_direction,
            "slope": float(slope),
            "intercept": float(intercept),
            "r_squared": float(r_value ** 2),
            "p_value": float(p_value),
            "percentage_change": float(percentage_change) if not np.isnan(percentage_change) else None,
            "dates": ts_df.index.strftime('%Y-%m-%d').tolist(),
            "values": ts_df[value_column].tolist(),
            "trend_line": trend_line.tolist(),
            "rolling_mean": rolling_mean.dropna().tolist(),
            "rolling_std": rolling_std.dropna().tolist(),
            "rolling_dates": rolling_mean.dropna().index.strftime('%Y-%m-%d').tolist()
        }
