'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import {
  Shield,
  UserX,
  Eye,
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Clock,
  RefreshCw
} from 'lucide-react';

interface OverviewData {
  risk: {
    score: number;
    level: string;
    trend: string;
  };
  churn: {
    probability: number;
    risk_level: string;
    days_to_churn: number;
  };
  anomalies: {
    total: number;
    severity: string;
    recent_count: number;
  };
}

export default function PredictiveOverviewDashboard() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [overviewData, setOverviewData] = useState<OverviewData | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Create simple axios instance for predictive analytics (no API key needed)
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    timeout: 30000,
  });

  useEffect(() => {
    if (session?.user?.id) {
      fetchOverviewData(session.user.id);
    }
  }, [session]);

  const fetchOverviewData = async (userId: string) => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);

    try {
      // Fetch all predictive analytics data
      const [riskResponse, churnResponse, anomalyResponse] = await Promise.allSettled([
        api.get(`/predictive/user-risk/${userId}`),
        api.get(`/predictive/churn-prediction/${userId}`),
        api.get(`/predictive/anomaly-detection/${userId}`)
      ]);

      const overview: OverviewData = {
        risk: {
          score: 0.5,
          level: 'moderate',
          trend: 'stable'
        },
        churn: {
          probability: 0.3,
          risk_level: 'low',
          days_to_churn: 90
        },
        anomalies: {
          total: 0,
          severity: 'low',
          recent_count: 0
        }
      };

      // Process risk data
      if (riskResponse.status === 'fulfilled') {
        const riskData = riskResponse.value.data;
        overview.risk = {
          score: riskData.risk_score || 0.5,
          level: riskData.risk_level || 'moderate',
          trend: 'stable' // Would come from trends endpoint
        };
      }

      // Process churn data
      if (churnResponse.status === 'fulfilled') {
        const churnData = churnResponse.value.data;
        overview.churn = {
          probability: churnData.churn_probability || 0.3,
          risk_level: churnData.churn_risk_level || 'low',
          days_to_churn: churnData.days_to_churn || 90
        };
      }

      // Process anomaly data
      if (anomalyResponse.status === 'fulfilled') {
        const anomalyData = anomalyResponse.value.data;
        overview.anomalies = {
          total: anomalyData.total_anomalies || 0,
          severity: anomalyData.overall_severity || 'low',
          recent_count: anomalyData.anomalies?.length || 0
        };
      }

      setOverviewData(overview);

    } catch (err: any) {
      console.error('Error fetching overview data:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching overview data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    if (!session?.user?.id) return;
    
    setRefreshing(true);
    await fetchOverviewData(session.user.id);
    setRefreshing(false);
  };

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'moderate': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getChurnRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'very_low': return 'text-green-700 bg-green-100';
      case 'low': return 'text-green-600 bg-green-100';
      case 'moderate': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
          <p className="text-lg font-medium">Error Loading Overview</p>
          <p className="text-sm mt-2">{error}</p>
          <button
            onClick={refreshData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BarChart3 className="h-8 w-8 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">Predictive Analytics Overview</h2>
        </div>
        <button
          onClick={refreshData}
          disabled={refreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Overview Cards */}
      {overviewData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Risk Assessment Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`p-3 rounded-full ${getRiskLevelColor(overviewData.risk.level)}`}>
                  <Shield className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Risk Assessment</h3>
                  <p className="text-sm text-gray-600">Current risk level</p>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Risk Score:</span>
                <span className="text-lg font-bold text-gray-900">
                  {(overviewData.risk.score * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Level:</span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(overviewData.risk.level)}`}>
                  {overviewData.risk.level.toUpperCase()}
                </span>
              </div>
            </div>
          </div>

          {/* Churn Prediction Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`p-3 rounded-full ${getChurnRiskColor(overviewData.churn.risk_level)}`}>
                  <UserX className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Churn Prediction</h3>
                  <p className="text-sm text-gray-600">Likelihood to churn</p>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Probability:</span>
                <span className="text-lg font-bold text-gray-900">
                  {(overviewData.churn.probability * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Est. Days:</span>
                <span className="text-lg font-bold text-gray-900">
                  {overviewData.churn.days_to_churn}
                </span>
              </div>
            </div>
          </div>

          {/* Anomaly Detection Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`p-3 rounded-full ${getSeverityColor(overviewData.anomalies.severity)}`}>
                  <Eye className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Anomaly Detection</h3>
                  <p className="text-sm text-gray-600">Unusual patterns</p>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Anomalies:</span>
                <span className="text-lg font-bold text-gray-900">
                  {overviewData.anomalies.total}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Severity:</span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(overviewData.anomalies.severity)}`}>
                  {overviewData.anomalies.severity.toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center space-x-2 p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Shield className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium">View Risk Details</span>
          </button>
          <button className="flex items-center justify-center space-x-2 p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
            <UserX className="h-5 w-5 text-red-600" />
            <span className="text-sm font-medium">Churn Analysis</span>
          </button>
          <button className="flex items-center justify-center space-x-2 p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Eye className="h-5 w-5 text-purple-600" />
            <span className="text-sm font-medium">Anomaly Report</span>
          </button>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-sm text-gray-700">All models operational</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-blue-500" />
            <span className="text-sm text-gray-700">Last updated: {new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
