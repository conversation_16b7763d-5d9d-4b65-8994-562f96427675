'use client';

import { useState, useEffect, useRef } from 'react';
import api from '@/lib/api';

// Import Plotly dynamically to avoid SSR issues
let Plotly: any;
if (typeof window !== 'undefined') {
  import('plotly.js-dist-min').then((module: any) => {
    Plotly = module.default || module;
  }).catch(err => {
    console.error('Failed to load Plotly:', err);
  });
}

interface TimeSeriesAnalysisProps {
  datasetId: number;
}

export default function TimeSeriesAnalysis({ datasetId }: TimeSeriesAnalysisProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [columns, setColumns] = useState<string[]>([]);
  const [dateColumns, setDateColumns] = useState<string[]>([]);
  const [valueColumn, setValueColumn] = useState<string>('');
  const [dateColumn, setDateColumn] = useState<string>('');
  const [frequency, setFrequency] = useState<string>('');
  const [forecastPeriods, setForecastPeriods] = useState<number>(5);
  const [forecastMethod, setForecastMethod] = useState<string>('auto');
  const [forecastResults, setForecastResults] = useState<any>(null);
  const [seasonalityResults, setSeasonalityResults] = useState<any>(null);
  const [trendResults, setTrendResults] = useState<any>(null);
  const [anomalyResults, setAnomalyResults] = useState<any>(null);

  const forecastChartRef = useRef<HTMLDivElement>(null);
  const seasonalityChartRef = useRef<HTMLDivElement>(null);
  const trendChartRef = useRef<HTMLDivElement>(null);
  const anomalyChartRef = useRef<HTMLDivElement>(null);

  // Fetch dataset columns
  useEffect(() => {
    if (!datasetId) return;

    const fetchDatasetColumns = async () => {
      try {
        // Use demo ID that matches backend expectation
        const userId = 'demo-user-id';
        const response = await api.get(`/datasets/${userId}`);
        const datasets = response.data;
        const currentDataset = datasets.find((d: any) => d.id === datasetId);

        if (currentDataset) {
          setColumns(currentDataset.columns);

          // Try to identify date columns
          const possibleDateColumns = currentDataset.columns.filter((col: string) => {
            return col.toLowerCase().includes('date') ||
                   col.toLowerCase().includes('time') ||
                   col.toLowerCase().includes('day') ||
                   col.toLowerCase().includes('month') ||
                   col.toLowerCase().includes('year');
          });

          setDateColumns(possibleDateColumns);

          // Set default columns if available
          if (possibleDateColumns.length > 0) {
            setDateColumn(possibleDateColumns[0]);
          }

          // Try to find a numeric column for value
          const numericColumns = currentDataset.columns.filter((col: string) => {
            return !possibleDateColumns.includes(col);
          });

          if (numericColumns.length > 0) {
            setValueColumn(numericColumns[0]);
          }
        }
      } catch (err: any) {
        console.error('Error fetching dataset columns:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching dataset columns');
      }
    };

    fetchDatasetColumns();
  }, [datasetId]);

  // Generate forecast
  const generateForecast = async () => {
    if (!datasetId || !valueColumn || !dateColumn) {
      setError('Please select both value and date columns');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/analytics/time-series/forecast/${datasetId}`, {
        value_column: valueColumn,
        date_column: dateColumn,
        forecast_periods: forecastPeriods,
        frequency: frequency || undefined,
        method: forecastMethod
      });

      setForecastResults(response.data.forecast);

      // Create forecast chart
      setTimeout(() => {
        if (forecastChartRef.current && Plotly && response.data.forecast) {
          const forecast = response.data.forecast;

          const data: any[] = [
            {
              x: forecast.historical_dates,
              y: forecast.historical_values,
              type: 'scatter',
              mode: 'lines',
              name: 'Historical',
              line: { color: 'blue' }
            },
            {
              x: forecast.forecast_dates,
              y: forecast.forecast_values,
              type: 'scatter',
              mode: 'lines',
              name: 'Forecast',
              line: { color: 'red', dash: 'dash' }
            }
          ];

          // Add confidence intervals if available
          if (forecast.lower_bound && forecast.upper_bound) {
            data.push({
              x: forecast.forecast_dates,
              y: forecast.lower_bound,
              type: 'scatter',
              mode: 'lines',
              name: 'Lower Bound',
              line: { color: 'transparent' },
              showlegend: false
            } as any);

            data.push({
              x: forecast.forecast_dates,
              y: forecast.upper_bound,
              type: 'scatter',
              mode: 'lines',
              name: 'Upper Bound',
              line: { color: 'transparent' },
              fill: 'tonexty',
              fillcolor: 'rgba(255, 0, 0, 0.1)',
              showlegend: false
            } as any);
          }

          const layout = {
            title: `Forecast for ${valueColumn}`,
            xaxis: { title: dateColumn },
            yaxis: { title: valueColumn },
            hovermode: 'closest',
            legend: { orientation: 'h', y: -0.2 }
          };

          Plotly.newPlot(forecastChartRef.current, data, layout);
        }
      }, 100);
    } catch (err: any) {
      console.error('Error generating forecast:', err);
      setError(err.response?.data?.detail || err.message || 'Error generating forecast');
    } finally {
      setLoading(false);
    }
  };

  // Analyze seasonality
  const analyzeSeasonality = async () => {
    if (!datasetId || !valueColumn || !dateColumn) {
      setError('Please select both value and date columns');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/analytics/time-series/seasonality/${datasetId}`, {
        value_column: valueColumn,
        date_column: dateColumn,
        frequency: frequency || undefined
      });

      setSeasonalityResults(response.data.seasonality_analysis);

      // Create seasonality chart
      setTimeout(() => {
        if (seasonalityChartRef.current && Plotly && response.data.seasonality_analysis) {
          const seasonality = response.data.seasonality_analysis;

          const data: any[] = [
            {
              x: seasonality.dates,
              y: seasonality.original,
              type: 'scatter',
              mode: 'lines',
              name: 'Original',
              line: { color: 'blue' }
            },
            {
              x: seasonality.dates,
              y: seasonality.trend,
              type: 'scatter',
              mode: 'lines',
              name: 'Trend',
              line: { color: 'red' }
            },
            {
              x: seasonality.dates,
              y: seasonality.seasonal,
              type: 'scatter',
              mode: 'lines',
              name: 'Seasonal',
              line: { color: 'green' }
            },
            {
              x: seasonality.dates,
              y: seasonality.residual,
              type: 'scatter',
              mode: 'lines',
              name: 'Residual',
              line: { color: 'purple' }
            }
          ];

          const layout = {
            title: `Seasonal Decomposition for ${valueColumn}`,
            xaxis: { title: dateColumn },
            yaxis: { title: valueColumn },
            hovermode: 'closest',
            legend: { orientation: 'h', y: -0.2 }
          };

          Plotly.newPlot(seasonalityChartRef.current, data, layout);
        }
      }, 100);
    } catch (err: any) {
      console.error('Error analyzing seasonality:', err);
      setError(err.response?.data?.detail || err.message || 'Error analyzing seasonality');
    } finally {
      setLoading(false);
    }
  };

  // Analyze trend
  const analyzeTrend = async () => {
    if (!datasetId || !valueColumn || !dateColumn) {
      setError('Please select both value and date columns');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/analytics/time-series/trend/${datasetId}`, {
        value_column: valueColumn,
        date_column: dateColumn,
        frequency: frequency || undefined
      });

      setTrendResults(response.data.trend_analysis);

      // Create trend chart
      setTimeout(() => {
        if (trendChartRef.current && Plotly && response.data.trend_analysis) {
          const trend = response.data.trend_analysis;

          const data: any[] = [
            {
              x: trend.dates,
              y: trend.values,
              type: 'scatter',
              mode: 'lines',
              name: 'Original',
              line: { color: 'blue' }
            },
            {
              x: trend.dates,
              y: trend.trend_line,
              type: 'scatter',
              mode: 'lines',
              name: 'Trend Line',
              line: { color: 'red', dash: 'dash' }
            },
            {
              x: trend.rolling_dates,
              y: trend.rolling_mean,
              type: 'scatter',
              mode: 'lines',
              name: 'Rolling Mean',
              line: { color: 'green' }
            }
          ];

          const layout = {
            title: `Trend Analysis for ${valueColumn}`,
            xaxis: { title: dateColumn },
            yaxis: { title: valueColumn },
            hovermode: 'closest',
            legend: { orientation: 'h', y: -0.2 }
          };

          Plotly.newPlot(trendChartRef.current, data, layout);
        }
      }, 100);
    } catch (err: any) {
      console.error('Error analyzing trend:', err);
      setError(err.response?.data?.detail || err.message || 'Error analyzing trend');
    } finally {
      setLoading(false);
    }
  };

  // Detect anomalies
  const detectAnomalies = async () => {
    if (!datasetId || !valueColumn || !dateColumn) {
      setError('Please select both value and date columns');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/analytics/time-series/anomalies/${datasetId}`, {
        value_column: valueColumn,
        date_column: dateColumn,
        method: 'zscore',
        threshold: 3.0,
        frequency: frequency || undefined
      });

      setAnomalyResults(response.data.anomaly_detection);

      // Create anomaly chart
      setTimeout(() => {
        if (anomalyChartRef.current && Plotly && response.data.anomaly_detection) {
          const anomalies = response.data.anomaly_detection;

          const data: any[] = [
            {
              x: anomalies.dates,
              y: anomalies.values,
              type: 'scatter',
              mode: 'lines',
              name: 'Original',
              line: { color: 'blue' }
            },
            {
              x: anomalies.anomaly_dates,
              y: anomalies.anomaly_values,
              type: 'scatter',
              mode: 'markers',
              name: 'Anomalies',
              marker: { color: 'red', size: 10 }
            }
          ];

          const layout = {
            title: `Anomaly Detection for ${valueColumn}`,
            xaxis: { title: dateColumn },
            yaxis: { title: valueColumn },
            hovermode: 'closest',
            legend: { orientation: 'h', y: -0.2 }
          };

          Plotly.newPlot(anomalyChartRef.current, data, layout);
        }
      }, 100);
    } catch (err: any) {
      console.error('Error detecting anomalies:', err);
      setError(err.response?.data?.detail || err.message || 'Error detecting anomalies');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-black mb-4">Time Series Analysis</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Value Column
          </label>
          <select
            value={valueColumn}
            onChange={(e) => setValueColumn(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Select a column</option>
            {columns.filter(col => !dateColumns.includes(col)).map(column => (
              <option key={column} value={column}>{column}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date Column
          </label>
          <select
            value={dateColumn}
            onChange={(e) => setDateColumn(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Select a column</option>
            {columns.map(column => (
              <option key={column} value={column}>{column}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Frequency (Optional)
          </label>
          <select
            value={frequency}
            onChange={(e) => setFrequency(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Auto-detect</option>
            <option value="D">Daily</option>
            <option value="W">Weekly</option>
            <option value="M">Monthly</option>
            <option value="Q">Quarterly</option>
            <option value="Y">Yearly</option>
          </select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Forecast Periods
            </label>
            <input
              type="number"
              value={forecastPeriods}
              onChange={(e) => setForecastPeriods(parseInt(e.target.value))}
              min={1}
              max={50}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Forecast Method
            </label>
            <select
              value={forecastMethod}
              onChange={(e) => setForecastMethod(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="auto">Auto-select</option>
              <option value="arima">ARIMA</option>
              <option value="exponential_smoothing">Exponential Smoothing</option>
              <option value="prophet">Prophet</option>
            </select>
          </div>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={generateForecast}
          disabled={loading}
          className={`px-4 py-2 rounded-md text-sm font-medium ${
            loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          Generate Forecast
        </button>

        <button
          onClick={analyzeSeasonality}
          disabled={loading}
          className={`px-4 py-2 rounded-md text-sm font-medium ${
            loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          Analyze Seasonality
        </button>

        <button
          onClick={analyzeTrend}
          disabled={loading}
          className={`px-4 py-2 rounded-md text-sm font-medium ${
            loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          Analyse Trend
        </button>

        <button
          onClick={detectAnomalies}
          disabled={loading}
          className={`px-4 py-2 rounded-md text-sm font-medium ${
            loading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          Detect Anomalies
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Forecast Results */}
      {forecastResults && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">Forecast Results ({forecastResults.method})</h3>
          <div ref={forecastChartRef} className="w-full h-[400px]" />

          {forecastResults.model_params && (
            <div className="mt-2 text-sm text-gray-700">
              <p className="font-medium">Model Parameters:</p>
              <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                {JSON.stringify(forecastResults.model_params, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* Seasonality Results */}
      {seasonalityResults && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">Seasonality Analysis</h3>
          <div ref={seasonalityChartRef} className="w-full h-[400px]" />

          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="text-sm text-gray-700">
              <p className="font-medium">Stationarity:</p>
              <p>
                {seasonalityResults.is_stationary
                  ? 'The time series is stationary (p-value: ' + seasonalityResults.adf_p_value.toFixed(4) + ')'
                  : 'The time series is not stationary (p-value: ' + seasonalityResults.adf_p_value.toFixed(4) + ')'
                }
              </p>
            </div>

            <div className="text-sm text-gray-700">
              <p className="font-medium">Detected Seasonal Period:</p>
              <p>
                {seasonalityResults.detected_seasonal_period
                  ? seasonalityResults.detected_seasonal_period + ' periods'
                  : 'No clear seasonality detected'
                }
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Trend Results */}
      {trendResults && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">Trend Analysis</h3>
          <div ref={trendChartRef} className="w-full h-[400px]" />

          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="text-sm text-gray-700">
              <p className="font-medium">Trend Direction:</p>
              <p className={
                trendResults.trend_direction === 'Increasing' ? 'text-green-600' :
                trendResults.trend_direction === 'Decreasing' ? 'text-red-600' :
                'text-gray-600'
              }>
                {trendResults.trend_direction}
              </p>
            </div>

            <div className="text-sm text-gray-700">
              <p className="font-medium">Percentage Change:</p>
              <p className={trendResults.percentage_change > 0 ? 'text-green-600' : 'text-red-600'}>
                {trendResults.percentage_change !== null ? trendResults.percentage_change.toFixed(2) + '%' : 'N/A'}
              </p>
            </div>

            <div className="text-sm text-gray-700">
              <p className="font-medium">Slope:</p>
              <p>{trendResults.slope.toFixed(6)}</p>
            </div>

            <div className="text-sm text-gray-700">
              <p className="font-medium">R-squared:</p>
              <p>{trendResults.r_squared.toFixed(4)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Anomaly Results */}
      {anomalyResults && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">Anomaly Detection ({anomalyResults.method})</h3>
          <div ref={anomalyChartRef} className="w-full h-[400px]" />

          <div className="mt-2 text-sm text-gray-700">
            <p className="font-medium">Anomalies Found:</p>
            <p>
              {anomalyResults.anomaly_count} anomalies detected
              ({anomalyResults.anomaly_percentage.toFixed(2)}% of data points)
            </p>

            {anomalyResults.anomaly_count > 0 && (
              <div className="mt-2">
                <p className="font-medium">Anomaly Details:</p>
                <div className="max-h-40 overflow-y-auto mt-1">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Value
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {anomalyResults.anomaly_dates.map((date: string, index: number) => (
                        <tr key={index}>
                          <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                            {date}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                            {anomalyResults.anomaly_values[index].toFixed(4)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
