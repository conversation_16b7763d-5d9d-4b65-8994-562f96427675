@echo off
echo ==========================================
echo           AIthentiq Application
echo ==========================================
echo.
echo Starting both backend and frontend...
echo.
echo Backend:  http://localhost:8000
echo Frontend: http://localhost:3000
echo.
echo Your AIthentiq logo will be visible!
echo.
pause
echo.
echo [1/2] Starting Backend...
start "AIthentiq Backend" cmd /k "cd /d \"%~dp0\" && start_backend.bat"
echo.
echo Waiting 3 seconds for backend to start...
ping 127.0.0.1 -n 4 > nul 2>&1
echo.
echo [2/2] Starting Frontend...
start "AIthentiq Frontend" cmd /k "cd /d \"%~dp0\" && start_frontend.bat"
echo.
echo ==========================================
echo   Both services are starting!
echo ==========================================
echo.
echo Check the opened windows for status.
echo.
echo To view your application:
echo 1. Wait for both services to fully start
echo 2. Open: http://localhost:3000
echo 3. Your AIthentiq logo should be visible!
echo.
pause
