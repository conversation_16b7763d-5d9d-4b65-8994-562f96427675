"""
Trust Score Comparison API
Endpoint for comparing different trust score algorithms
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio

from services.trust_comparison_service import TrustComparisonService
from middleware.api_auth import get_current_user_from_api_key

router = APIRouter()

class TrustComparisonRequest(BaseModel):
    query: str
    answer: str
    sources: List[Dict] = []
    selected_methods: List[str]
    user_id: Optional[str] = None

class TrustComparisonResponse(BaseModel):
    results: List[Dict]
    analysis: Dict[str, Any]
    query: str
    answer: str
    timestamp: float

@router.post("/compare", response_model=TrustComparisonResponse)
async def compare_trust_scores(
    request: TrustComparisonRequest
):
    """
    Compare multiple trust score algorithms
    """
    try:
        # Initialize comparison service
        comparison_service = TrustComparisonService()

        # Validate selected methods
        valid_methods = set(comparison_service.methods.keys())
        invalid_methods = set(request.selected_methods) - valid_methods

        if invalid_methods:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid methods: {list(invalid_methods)}. Valid methods: {list(valid_methods)}"
            )

        if not request.selected_methods:
            raise HTTPException(
                status_code=400,
                detail="At least one method must be selected"
            )

        # Use provided user ID or default
        user_id = request.user_id or "demo_user"

        # Compare trust scores
        result = await comparison_service.compare_trust_scores(
            query=request.query,
            answer=request.answer,
            sources=request.sources,
            selected_methods=request.selected_methods,
            user_id=user_id
        )

        return TrustComparisonResponse(**result)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Trust score comparison failed: {str(e)}"
        )

@router.get("/methods")
async def get_available_methods():
    """
    Get list of available trust score methods
    """
    comparison_service = TrustComparisonService()
    
    return {
        "methods": [
            {
                "id": method_id,
                "name": method_name,
                "description": _get_method_description(method_id)
            }
            for method_id, method_name in comparison_service.methods.items()
        ]
    }

def _get_method_description(method_id: str) -> str:
    """Get description for each method"""
    descriptions = {
        "legacy_deterministic": "Original deterministic trust score system currently used as fallback",
        "enhanced_v25": "Advanced trust score system with multi-component analysis and calibration",
        "current_production": "Currently deployed production system (enhanced with legacy fallback)",
        "independent_rag": "Evaluation without dependencies (groundedness, relevance, source quality)",
        "aithentiq_convergence": "AIthentiq's proprietary convergence-based algorithm (semantic stability, model consensus, temporal consistency)"
    }
    return descriptions.get(method_id, "No description available")

@router.post("/demo")
async def demo_comparison(
    query: str = "What is the average sales for Q1?",
    answer: str = "Based on the data analysis, the average sales for Q1 is $45,000.",
    methods: str = "legacy_deterministic,independent_rag,aithentiq_convergence"
):
    """
    Demo endpoint for quick testing
    """
    try:
        comparison_service = TrustComparisonService()
        
        selected_methods = [m.strip() for m in methods.split(",")]
        sources = [
            {"text": "Q1 sales data shows total revenue of $135,000 across 3 months"},
            {"text": "Sales performance report indicates consistent monthly averages"}
        ]
        
        result = await comparison_service.compare_trust_scores(
            query=query,
            answer=answer,
            sources=sources,
            selected_methods=selected_methods,
            user_id="demo_user"
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Demo comparison failed: {str(e)}"
        )
