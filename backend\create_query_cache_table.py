"""
Simple migration script to create query_cache table
"""
import sqlite3
from datetime import datetime

def create_query_cache_table():
    """Create query_cache table for persistent caching"""
    try:
        # Connect to the database
        conn = sqlite3.connect('aithentiq.db')
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='query_cache'")
        if cursor.fetchone():
            print("✅ Query cache table already exists")
            return
        
        # Create query_cache table
        cursor.execute('''
            CREATE TABLE query_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cache_key VARCHAR UNIQUE NOT NULL,
                dataset_id INTEGER NOT NULL,
                question TEXT NOT NULL,
                question_hash VARCHAR NOT NULL,
                dataset_hash VARCHAR NOT NULL,
                answer TEXT NOT NULL,
                chart_type VARCHAR,
                chart_data TEXT,
                trust_score TEXT,
                reasoning_steps TEXT,
                formula_results TEXT,
                include_cot BOOLEAN DEFAULT 0,
                hit_count INTEGER DEFAULT 1,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (dataset_id) REFERENCES datasets(id)
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX ix_query_cache_cache_key ON query_cache(cache_key)')
        cursor.execute('CREATE INDEX ix_query_cache_question_hash ON query_cache(question_hash)')
        cursor.execute('CREATE INDEX ix_query_cache_dataset_hash ON query_cache(dataset_hash)')
        cursor.execute('CREATE INDEX ix_query_cache_dataset_id ON query_cache(dataset_id)')
        
        # Commit changes
        conn.commit()
        print("✅ Query cache table created successfully")
        print("✅ Indexes created for optimal performance")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    create_query_cache_table()
