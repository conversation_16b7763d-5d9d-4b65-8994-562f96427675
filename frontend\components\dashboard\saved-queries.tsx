'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { createApiInstance } from '@/lib/api';

interface SavedQuery {
  id: string;
  question: string;
  answer: string;
  dataset_id: number;
  dataset_name: string;
  name?: string;
  description?: string;
  tags?: string;
  is_favorite: boolean;
  trust_score?: number;
  processing_time?: number;
  created_at: string;
  updated_at: string;
}

interface SavedQueriesProps {
  onSelectQuery: (query: SavedQuery) => void;
  currentDatasetId: number | null;
}

export default function SavedQueries({ onSelectQuery, currentDatasetId }: SavedQueriesProps) {
  const { data: session } = useSession();
  const [queries, setQueries] = useState<SavedQuery[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'current' | 'favorites'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);
  const [viewQuery, setViewQuery] = useState<SavedQuery | null>(null);

  // Load saved queries from API
  useEffect(() => {
    loadSavedQueries();
  }, [session]);

  const loadSavedQueries = async () => {
    if (!session) return;

    setIsLoading(true);
    setError(null);

    try {
      const sessionApi = createApiInstance(session);
      const response = await sessionApi.get('/api/v1/saved-queries/');

      if (response && response.data) {
        console.log('✅ Loaded saved queries:', response.data);
        setQueries(response.data);
      }
    } catch (err: any) {
      console.error('Error loading saved queries:', err);
      setError('Failed to load saved queries. Please sign out and sign back in.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and sort queries
  const filteredQueries = queries
    .filter(query => {
      // Apply dataset filter
      if (filter === 'current' && query.dataset_id !== currentDatasetId) {
        return false;
      }

      // Apply favorites filter
      if (filter === 'favorites' && !query.is_favorite) {
        return false;
      }

      // Apply search term
      if (searchTerm && !query.question.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  // Toggle favorite status
  const toggleFavorite = async (id: string) => {
    if (!session) return;

    try {
      const sessionApi = createApiInstance(session);
      const query = queries.find(q => q.id === id);
      if (!query) return;

      // Update in database
      await sessionApi.patch(`/api/v1/saved-queries/${id}`, {
        is_favorite: !query.is_favorite
      });

      // Update local state
      const updatedQueries = queries.map(query =>
        query.id === id ? { ...query, is_favorite: !query.is_favorite } : query
      );
      setQueries(updatedQueries);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  // Delete query
  const deleteQuery = async (id: string) => {
    if (!session) return;

    try {
      const sessionApi = createApiInstance(session);

      // Delete from database
      await sessionApi.delete(`/api/v1/saved-queries/${id}`);

      // Update local state
      const updatedQueries = queries.filter(query => query.id !== id);
      setQueries(updatedQueries);
      setDeleteConfirm(null);
    } catch (error) {
      console.error('Failed to delete query:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-sm text-gray-600">Loading saved queries...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-600">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-black">Saved Queries</h3>
        <button
          onClick={loadSavedQueries}
          disabled={isLoading}
          className="p-1 text-gray-400 hover:text-blue-600 rounded"
          title="Refresh saved queries"
        >
          <svg className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      <div className="mb-4">
        <div className="flex mb-2">
          <input
            type="text"
            placeholder="Search queries..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 text-xs rounded-full ${
              filter === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('current')}
            className={`px-3 py-1 text-xs rounded-full ${
              filter === 'current'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            disabled={!currentDatasetId}
          >
            Current Dataset
          </button>
          <button
            onClick={() => setFilter('favorites')}
            className={`px-3 py-1 text-xs rounded-full ${
              filter === 'favorites'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Favorites
          </button>
        </div>
      </div>

      {filteredQueries.length === 0 ? (
        <div className="text-center py-6 text-gray-500">
          <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="mt-2">No saved queries found</p>
          {searchTerm && <p className="text-sm mt-1">Try a different search term</p>}
        </div>
      ) : (
        <div className="space-y-2 max-h-64 overflow-y-auto pr-1">
          {filteredQueries.map((query) => (
            <div
              key={query.id}
              className="p-3 border rounded-md hover:bg-gray-50 transition-colors relative group"
            >
              <div className="flex justify-between items-start">
                <button
                  onClick={() => onSelectQuery(query)}
                  className="text-left text-sm text-black font-medium hover:text-blue-600 truncate max-w-[80%]"
                >
                  {query.question}
                </button>
                <div className="flex space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setViewQuery(query);
                    }}
                    className="text-gray-400 hover:text-green-500 focus:outline-none"
                    title="View details"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => toggleFavorite(query.id)}
                    className="text-gray-400 hover:text-yellow-500 focus:outline-none"
                    title={query.is_favorite ? "Remove from favorites" : "Add to favorites"}
                  >
                    <svg className={`w-4 h-4 ${query.is_favorite ? 'text-yellow-500 fill-current' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setDeleteConfirm(query.id);
                    }}
                    className="text-gray-400 hover:text-red-500 focus:outline-none"
                    title="Delete query"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-1 flex items-center">
                <span className="mr-2">
                  {new Date(query.created_at).toLocaleDateString()} {new Date(query.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
                <span className="bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full text-xs">
                  {query.dataset_name || `Dataset #${query.dataset_id}`}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Saved Query</h3>
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete this saved query? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteQuery(deleteConfirm)}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Query Modal */}
      {viewQuery && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Saved Query Details</h3>
              <button
                onClick={() => setViewQuery(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Question</label>
                <p className="text-gray-900 bg-gray-50 p-3 rounded">{viewQuery.question}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Answer</label>
                <p className="text-gray-900 bg-gray-50 p-3 rounded max-h-40 overflow-y-auto">{viewQuery.answer}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Dataset</label>
                  <p className="text-gray-900">{viewQuery.dataset_name || `Dataset #${viewQuery.dataset_id}`}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Created</label>
                  <p className="text-gray-900">{new Date(viewQuery.created_at).toLocaleString()}</p>
                </div>
              </div>

              {viewQuery.processing_time && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Processing Time</label>
                  <p className="text-gray-900">{viewQuery.processing_time.toFixed(2)}s</p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <div className="flex items-center space-x-2">
                  {viewQuery.is_favorite && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                      <svg className="w-3 h-3 mr-1 fill-current" viewBox="0 0 24 24">
                        <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                      </svg>
                      Favorite
                    </span>
                  )}
                  {viewQuery.tags && (
                    <span className="inline-block px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                      {viewQuery.tags}
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  onSelectQuery(viewQuery);
                  setViewQuery(null);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Use This Query
              </button>
              <button
                onClick={() => setViewQuery(null)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
