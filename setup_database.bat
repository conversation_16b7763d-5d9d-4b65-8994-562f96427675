@echo off
setlocal enabledelayedexpansion

echo 🚀 AIthentiq Database Setup Script
echo ==================================
echo.

REM Check if PostgreSQL is available
where psql >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL (psql) is not installed or not in PATH
    echo Please install PostgreSQL and try again
    pause
    exit /b 1
)

REM Default values
set DEFAULT_HOST=localhost
set DEFAULT_PORT=5432
set DEFAULT_DATABASE=aithentiq
set DEFAULT_USER=postgres

echo 📋 Database Configuration
echo ========================

REM Get database connection details
set /p DB_HOST="Database host [%DEFAULT_HOST%]: "
if "%DB_HOST%"=="" set DB_HOST=%DEFAULT_HOST%

set /p DB_PORT="Database port [%DEFAULT_PORT%]: "
if "%DB_PORT%"=="" set DB_PORT=%DEFAULT_PORT%

set /p DB_NAME="Database name [%DEFAULT_DATABASE%]: "
if "%DB_NAME%"=="" set DB_NAME=%DEFAULT_DATABASE%

set /p DB_USER="Database user [%DEFAULT_USER%]: "
if "%DB_USER%"=="" set DB_USER=%DEFAULT_USER%

echo Database password (input will be hidden):
powershell -Command "$password = Read-Host -AsSecureString; [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))" > temp_password.txt
set /p DB_PASSWORD=<temp_password.txt
del temp_password.txt
echo.

REM Set password environment variable
set PGPASSWORD=%DB_PASSWORD%

echo 🔗 Testing database connection...
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%DB_NAME%" -c "SELECT 1;" >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Failed to connect to database
    echo Please check your connection details and try again
    pause
    exit /b 1
)

echo ✅ Database connection successful!
echo.
echo 📊 Running database migration...
echo ===============================

REM Run the migration script
psql -h "%DB_HOST%" -p "%DB_PORT%" -U "%DB_USER%" -d "%DB_NAME%" -f database_migration.sql
if %errorlevel% equ 0 (
    echo.
    echo ✅ Database migration completed successfully!
    echo.
    echo 🎉 Your AIthentiq database is now ready for predictive analytics!
    echo.
    echo 📋 Summary of created tables:
    echo   • predictive_models - Store trained ML models
    echo   • predictive_predictions - Store prediction results
    echo   • time_series_forecasts - Store forecasting results
    echo   • anomaly_detections - Store anomaly detection results
    echo   • background_jobs - Handle async processing
    echo.
    echo 🔧 Next steps:
    echo   1. Update your backend .env file with database credentials
    echo   2. Start your backend server: cd backend ^&^& uvicorn main:app --reload --port 8000
    echo   3. Start your frontend server: cd frontend ^&^& npm run dev
    echo   4. Visit http://localhost:3000/dashboard/predictive to test the features
    echo.
) else (
    echo.
    echo ❌ Database migration failed!
    echo Please check the error messages above and try again
    pause
    exit /b 1
)

REM Clean up
set PGPASSWORD=

echo 🔒 Database setup completed securely!
pause
