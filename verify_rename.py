#!/usr/bin/env python3
"""
Verification script for AIthentIQ repository rename
Checks that all references have been properly updated
"""

import os
import sys
from pathlib import Path

def check_file_content(file_path, old_terms, new_terms):
    """Check if file contains old terms that should be replaced"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read().lower()

        issues = []
        for old_term in old_terms:
            if old_term.lower() in content:
                issues.append(f"Found '{old_term}' in {file_path}")

        return issues
    except Exception as e:
        return [f"Error reading {file_path}: {e}"]

def main():
    print("🔍 AIthentIQ Repository Rename Verification")
    print("=" * 50)

    # Check current folder name
    current_folder = os.path.basename(os.getcwd())
    print(f"\n📁 Current folder: {current_folder}")

    if current_folder == "AIthentiq":
        print("✅ Root folder correctly named!")
    elif current_folder == "AskData - Copy (2)":
        print("⚠️  Root folder still has old name")
        print("   Please rename the folder to 'AIthentiq'")
    else:
        print(f"ℹ️  Unexpected folder name: {current_folder}")

    print(f"📍 Full path: {os.getcwd()}")

    # Terms to check for
    old_terms = ["askdata", "AskData"]
    new_terms = ["aithentiq", "AIthentIQ"]

    # Files to check
    files_to_check = [
        "README.md",
        "package.json",
        "backend/.env",
        "backend/alembic.ini",
        "backend/migrations/env.py",
        "docker-compose.yml",
        "railway.toml",
        "render.yaml",
        ".do/app.yaml"
    ]

    # Check startup scripts
    batch_files = [
        "start_both.bat",
        "start_backend.bat",
        "start_frontend.bat",
        "stop_services.bat"
    ]

    all_issues = []

    print("\n📋 Checking configuration files...")
    for file_path in files_to_check:
        if os.path.exists(file_path):
            issues = check_file_content(file_path, old_terms, new_terms)
            if issues:
                all_issues.extend(issues)
            else:
                print(f"✅ {file_path}")
        else:
            print(f"⚠️  {file_path} (not found)")

    print("\n📋 Checking startup scripts...")
    for file_path in batch_files:
        if os.path.exists(file_path):
            issues = check_file_content(file_path, old_terms, new_terms)
            if issues:
                all_issues.extend(issues)
            else:
                print(f"✅ {file_path}")
        else:
            print(f"⚠️  {file_path} (not found)")

    print("\n📋 Checking database files...")
    if os.path.exists("aithentiq.db"):
        print("✅ aithentiq.db (root)")
    else:
        print("⚠️  aithentiq.db (root) - not found")

    if os.path.exists("backend/aithentiq.db"):
        print("✅ backend/aithentiq.db")
    else:
        print("⚠️  backend/aithentiq.db - not found")

    print("\n📋 Checking deployment configurations...")
    deployment_files = ["Dockerfile", "railway.toml", "render.yaml", ".do/app.yaml"]
    for file_path in deployment_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (missing)")

    print("\n" + "=" * 50)

    if all_issues:
        print("❌ ISSUES FOUND:")
        for issue in all_issues:
            print(f"  - {issue}")
        print(f"\n🔧 Please fix {len(all_issues)} issue(s) before proceeding.")
        return 1
    else:
        print("✅ ALL CHECKS PASSED!")
        print("\n🎉 Repository successfully renamed to AIthentIQ!")
        print("\n📋 Next steps:")
        print("  1. Test the application locally")
        print("  2. Commit all changes to git")
        print("  3. Deploy using your preferred method")
        print("  4. Update any external references")
        return 0

if __name__ == "__main__":
    sys.exit(main())
