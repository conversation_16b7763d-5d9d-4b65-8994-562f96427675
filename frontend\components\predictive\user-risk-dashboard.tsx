'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import {
  AlertTriangle,
  Shield,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  Users,
  BarChart3,
  RefreshCw,
  Info
} from 'lucide-react';
import RiskTrendChart from './risk-trend-chart';

interface RiskPrediction {
  prediction_id: number;
  user_id: string;
  risk_score: number;
  risk_level: string;
  risk_factors: string[];
  confidence_score: number;
  features_used: Record<string, any>;
  model_version: string;
  predicted_at: string;
  valid_until: string;
}

interface RiskTrends {
  user_id: string;
  trend_period_days: number;
  total_predictions: number;
  current_risk_score: number;
  current_risk_level: string;
  trend_direction: string;
  risk_score_history: {
    dates: string[];
    scores: number[];
    levels: string[];
  };
  statistics: {
    min_risk_score: number;
    max_risk_score: number;
    avg_risk_score: number;
    risk_volatility: number;
  };
}

// Tooltip component for information icons
const InfoTooltip = ({ content, title }: { content: string; title: string }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="relative inline-block">
      <button
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="ml-1 text-gray-400 hover:text-gray-600"
      >
        <Info className="h-4 w-4" />
      </button>
      {showTooltip && (
        <div className="absolute z-10 w-64 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg -top-2 left-6">
          <div className="font-semibold mb-1">{title}</div>
          <div>{content}</div>
          <div className="absolute top-3 -left-1 w-2 h-2 bg-gray-900 rotate-45"></div>
        </div>
      )}
    </div>
  );
};

export default function UserRiskDashboard() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [riskPrediction, setRiskPrediction] = useState<RiskPrediction | null>(null);
  const [riskTrends, setRiskTrends] = useState<RiskTrends | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);

  // Create simple axios instance for predictive analytics (no API key needed)
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    timeout: 30000,
  });

  useEffect(() => {
    if (session?.user?.id) {
      setSelectedUserId(session.user.id);
      fetchRiskData(session.user.id);
    }
  }, [session]);

  const fetchRiskData = async (userId: string) => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);

    try {
      // Fetch current risk prediction
      const riskResponse = await api.get(`/predictive/user-risk/${userId}`);
      setRiskPrediction(riskResponse.data);

      // Fetch risk trends
      const trendsResponse = await api.get(`/predictive/user-risk/${userId}/trends`);
      setRiskTrends(trendsResponse.data);

    } catch (err: any) {
      console.error('Error fetching risk data:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching risk data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    if (!selectedUserId) return;
    
    setRefreshing(true);
    await fetchRiskData(selectedUserId);
    setRefreshing(false);
  };

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'moderate': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 0.7) return 'text-red-600';
    if (score >= 0.4) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'decreasing': return <TrendingDown className="h-4 w-4 text-green-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
          <p className="text-lg font-medium">Error Loading Risk Data</p>
          <p className="text-sm mt-2">{error}</p>
          <button
            onClick={refreshData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Shield className="h-8 w-8 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">User Risk Analysis</h2>
          <InfoTooltip
            title="User Risk Analysis"
            content="Comprehensive assessment of user risk levels using AI models that analyze behavior patterns, usage statistics, and historical data to predict potential security or operational risks."
          />
        </div>
        <button
          onClick={refreshData}
          disabled={refreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Current Risk Status */}
      {riskPrediction && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Risk Score Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center">
                  <p className="text-sm font-medium text-gray-600">Risk Assessment</p>
                  <InfoTooltip
                    title="Risk Assessment"
                    content="AI-calculated risk score from 0-100% based on user behavior analysis, security patterns, and usage anomalies. Higher scores indicate greater potential risk requiring attention."
                  />
                </div>
                <p className={`text-3xl font-bold ${getRiskScoreColor(riskPrediction.risk_score)}`}>
                  {(riskPrediction.risk_score * 100).toFixed(1)}%
                </p>
              </div>
              <div className={`p-3 rounded-full ${getRiskLevelColor(riskPrediction.risk_level)}`}>
                <AlertTriangle className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-4">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(riskPrediction.risk_level)}`}>
                {riskPrediction.risk_level.toUpperCase()} RISK
              </span>
            </div>
          </div>

          {/* Confidence Score Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center">
                  <p className="text-sm font-medium text-gray-600">Prediction Confidence</p>
                  <InfoTooltip
                    title="Prediction Confidence"
                    content="Indicates how confident the AI model is about the risk assessment. Higher confidence means the prediction is more reliable based on available data and model certainty."
                  />
                </div>
                <p className="text-3xl font-bold text-blue-600">
                  {(riskPrediction.confidence_score * 100).toFixed(1)}%
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <BarChart3 className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-gray-500">
                Model: {riskPrediction.model_version}
              </p>
            </div>
          </div>

          {/* Trend Direction Card */}
          {riskTrends && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Trend</p>
                  <p className="text-lg font-semibold text-gray-900 capitalize">
                    {riskTrends.trend_direction.replace('_', ' ')}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-gray-100">
                  {getTrendIcon(riskTrends.trend_direction)}
                </div>
              </div>
              <div className="mt-4">
                <p className="text-xs text-gray-500">
                  Based on {riskTrends.total_predictions} predictions
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Risk Factors */}
      {riskPrediction && riskPrediction.risk_factors.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Factors</h3>
          <div className="space-y-3">
            {riskPrediction.risk_factors.map((factor, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                <p className="text-sm text-gray-700">{factor}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Risk Statistics */}
      {riskTrends && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Statistics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {(riskTrends.statistics.min_risk_score * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">Minimum</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {(riskTrends.statistics.max_risk_score * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">Maximum</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {(riskTrends.statistics.avg_risk_score * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">Average</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {(riskTrends.statistics.risk_volatility * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">Volatility</p>
            </div>
          </div>
        </div>
      )}

      {/* Risk Trend Chart */}
      {riskTrends && riskTrends.risk_score_history && (
        <RiskTrendChart
          data={riskTrends.risk_score_history}
          title="Risk Score Trend (30 Days)"
        />
      )}

      {/* Prediction Details */}
      {riskPrediction && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Prediction Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Predicted At:</p>
              <p className="font-medium">{new Date(riskPrediction.predicted_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-600">Valid Until:</p>
              <p className="font-medium">{new Date(riskPrediction.valid_until).toLocaleString()}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
