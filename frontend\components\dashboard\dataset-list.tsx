'use client';

import { useState, useEffect } from 'react';
import { Trash2, Bar<PERSON>hart3, FileText, Image, Table, Eye } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { createApiInstance, getUserId } from '@/lib/api';
import DocumentViewer from './document-viewer';


interface Dataset {
  id: number;
  name: string;
  columns: string[];
  row_count: number;
  created_at: string;
  file_type?: string;
  content_type?: string;
  processing_status?: string;
  word_count?: number;
  character_count?: number;
  chunking_template?: string;
  total_chunks?: number;
}

interface DatasetListProps {
  onSelectDataset: (datasetId: number) => void;
  onRefreshNeeded: () => void;
  refreshTrigger: number;
  selectedDatasetId?: number | null;
}

export default function DatasetList({ onSelectDataset, onRefreshNeeded, refreshTrigger, selectedDatasetId }: DatasetListProps) {
  const { data: session } = useSession();
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'documents' | 'excel' | 'images'>('all');
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(selectedDatasetId || null);
  const [deleteConfirm, setDeleteConfirm] = useState<number | null>(null);
  const [viewDataset, setViewDataset] = useState<Dataset | null>(null);

  // Filter datasets based on active tab
  const filteredDatasets = datasets.filter(dataset => {
    if (activeTab === 'all') return true;

    const fileType = dataset.file_type?.toLowerCase();

    switch (activeTab) {
      case 'documents':
        return ['pdf', 'docx', 'txt', 'md'].includes(fileType || '');
      case 'excel':
        return ['csv', 'xlsx', 'xls'].includes(fileType || '');
      case 'images':
        return ['jpg', 'jpeg', 'png'].includes(fileType || '');
      default:
        return true;
    }
  });

  // Get counts for each category
  const getCounts = () => {
    const all = datasets.length;
    const documents = datasets.filter(d => ['pdf', 'docx', 'txt', 'md'].includes(d.file_type?.toLowerCase() || '')).length;
    const excel = datasets.filter(d => ['csv', 'xlsx', 'xls'].includes(d.file_type?.toLowerCase() || '')).length;
    const images = datasets.filter(d => ['jpg', 'jpeg', 'png'].includes(d.file_type?.toLowerCase() || '')).length;

    return { all, documents, excel, images };
  };

  const counts = getCounts();

  const truncateFileName = (filename: string, maxLength: number = 10): string => {
    // Remove extension since we show it separately
    const lastDotIndex = filename.lastIndexOf('.');
    const nameWithoutExt = lastDotIndex !== -1 ? filename.substring(0, lastDotIndex) : filename;

    if (nameWithoutExt.length <= maxLength) return nameWithoutExt;

    // Just truncate the name without extension and add dots
    return nameWithoutExt.substring(0, maxLength) + '...';
  };

  const handleViewDocument = (dataset: Dataset) => {
    setSelectedDataset(dataset);
    setViewerOpen(true);
  };

  // Update selectedId when selectedDatasetId prop changes
  useEffect(() => {
    if (selectedDatasetId !== undefined) {
      setSelectedId(selectedDatasetId);
    }
  }, [selectedDatasetId]);

  useEffect(() => {
    const fetchDatasets = async () => {
      // Don't fetch if session is not available
      if (!session) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Use session-aware API instance
        const sessionApi = createApiInstance(session);
        const response = await sessionApi.get('/api/v1/datasets');
        console.log('Datasets response:', response);

        if (response && response.data) {
          setDatasets(response.data);

          // Select the first dataset if available and none is selected (only for basic dashboard)
          if (response.data.length > 0 && !selectedId && selectedDatasetId === undefined) {
            setSelectedId(response.data[0].id);
            onSelectDataset(response.data[0].id);
          }
        } else {
          console.error('Invalid response format:', response);
          setError('Invalid response format from server');
        }
      } catch (err: any) {
        console.error('Error fetching datasets:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching datasets');
      } finally {
        setLoading(false);
      }
    };

    fetchDatasets();
  }, [refreshTrigger, session]); // Added session dependency to refetch when session becomes available



  const handleSelect = (datasetId: number) => {
    setSelectedId(datasetId);
    onSelectDataset(datasetId);
  };

  // Update selectedId when selectedDatasetId prop changes
  useEffect(() => {
    if (selectedDatasetId !== undefined && selectedDatasetId !== selectedId) {
      setSelectedId(selectedDatasetId);
    }
  }, [selectedDatasetId]);

  const handleDelete = async (datasetId: number) => {
    try {
      const sessionApi = createApiInstance(session);
      const response = await sessionApi.delete(`/api/v1/datasets/${datasetId}`);
      if (response.status === 200 || response.status === 204) {
        // Remove from local state
        setDatasets(prev => prev.filter(d => d.id !== datasetId));

        // If deleted dataset was selected, clear selection
        if (selectedId === datasetId) {
          setSelectedId(null);
          // Select first remaining dataset if any
          const remaining = datasets.filter(d => d.id !== datasetId);
          if (remaining.length > 0) {
            setSelectedId(remaining[0].id);
            onSelectDataset(remaining[0].id);
          }
        }

        setDeleteConfirm(null);
        onRefreshNeeded();
      }
    } catch (err: any) {
      console.error('Error deleting dataset:', err);
      setError(err.response?.data?.detail || err.message || 'Error deleting dataset');
    }
  };

  const handleView = (dataset: Dataset) => {
    setViewDataset(dataset);
  };

  // Add escape key listener for details modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && viewDataset) {
        setViewDataset(null);
      }
    };

    if (viewDataset) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [viewDataset]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 p-4">
        <p>{error}</p>
        <button
          onClick={onRefreshNeeded}
          className="mt-2 text-blue-600 hover:underline"
        >
          Try again
        </button>
      </div>
    );
  }

  if (datasets.length === 0) {
    return (
      <div className="text-center text-black p-4">
        <p>No datasets found. Upload a CSV file to get started.</p>
      </div>
    );
  }

  return (
    <>
      <div className="w-full">
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-black">Your Datasets</h3>
          <p className="text-sm text-gray-600 mt-1">Manage and analyse your uploaded data</p>

          {/* Filter Buttons */}
          <div className="mt-3 flex flex-wrap gap-2">
            <button
              onClick={() => setActiveTab('all')}
              className={`inline-flex items-center px-2 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'all'
                  ? 'bg-blue-100 text-blue-700 border-2 border-blue-200 shadow-sm'
                  : 'bg-gray-50 text-gray-600 border-2 border-transparent hover:bg-gray-100 hover:text-gray-800'
              }`}
            >
              <FileText className="w-4 h-4 mr-2" />
              All Files
              <span className="ml-1.5 px-1.5 py-0.5 bg-white rounded-full text-xs font-semibold">
                {counts.all}
              </span>
            </button>

            <button
              onClick={() => setActiveTab('documents')}
              className={`inline-flex items-center px-2 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'documents'
                  ? 'bg-green-100 text-green-700 border-2 border-green-200 shadow-sm'
                  : 'bg-gray-50 text-gray-600 border-2 border-transparent hover:bg-gray-100 hover:text-gray-800'
              }`}
            >
              <FileText className="w-4 h-4 mr-2" />
              Documents
              <span className="ml-1.5 px-1.5 py-0.5 bg-white rounded-full text-xs font-semibold">
                {counts.documents}
              </span>
            </button>

            <button
              onClick={() => setActiveTab('excel')}
              className={`inline-flex items-center px-2 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'excel'
                  ? 'bg-emerald-100 text-emerald-700 border-2 border-emerald-200 shadow-sm'
                  : 'bg-gray-50 text-gray-600 border-2 border-transparent hover:bg-gray-100 hover:text-gray-800'
              }`}
            >
              <Table className="w-4 h-4 mr-1.5" />
              Spreadsheets
              <span className="ml-1.5 px-1.5 py-0.5 bg-white rounded-full text-xs font-semibold">
                {counts.excel}
              </span>
            </button>

            <button
              onClick={() => setActiveTab('images')}
              className={`inline-flex items-center px-2 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'images'
                  ? 'bg-orange-100 text-orange-700 border-2 border-orange-200 shadow-sm'
                  : 'bg-gray-50 text-gray-600 border-2 border-transparent hover:bg-gray-100 hover:text-gray-800'
              }`}
            >
              <Image className="w-4 h-4 mr-1.5" />
              Images
              <span className="ml-1.5 px-1.5 py-0.5 bg-white rounded-full text-xs font-semibold">
                {counts.images}
              </span>
            </button>
          </div>

          {/* Active Filter Indicator */}
          <div className="mt-2 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {activeTab === 'all' && `Showing all ${filteredDatasets.length} files`}
              {activeTab === 'documents' && `Showing ${filteredDatasets.length} document files`}
              {activeTab === 'excel' && `Showing ${filteredDatasets.length} spreadsheet files`}
              {activeTab === 'images' && `Showing ${filteredDatasets.length} image files`}
            </div>
            {activeTab !== 'all' && (
              <button
                onClick={() => setActiveTab('all')}
                className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
              >
                Clear filter
              </button>
            )}
          </div>
        </div>

        <div className="overflow-y-auto max-h-80 pr-2">
            {filteredDatasets.map((dataset) => (
            <div
              key={dataset.id}
              className={`p-2.5 rounded-lg transition-colors mb-2 border ${
                selectedId === dataset.id
                  ? 'bg-blue-50 border-blue-300'
                  : 'bg-gray-50 hover:bg-gray-100 border-gray-200'
              }`}
            >
              <div
                onClick={() => handleSelect(dataset.id)}
                className="cursor-pointer"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    {/* File Type Icon */}
                    <div className={`p-2 rounded-lg ${
                      ['pdf', 'docx', 'txt', 'md'].includes(dataset.file_type || '') ? 'bg-green-100' :
                      ['csv', 'xlsx', 'xls'].includes(dataset.file_type || '') ? 'bg-emerald-100' :
                      ['jpg', 'jpeg', 'png'].includes(dataset.file_type || '') ? 'bg-orange-100' :
                      'bg-gray-100'
                    }`}>
                      {['pdf', 'docx', 'txt', 'md'].includes(dataset.file_type || '') ? (
                        <FileText className={`w-4 h-4 ${
                          ['pdf', 'docx', 'txt', 'md'].includes(dataset.file_type || '') ? 'text-green-600' : 'text-gray-600'
                        }`} />
                      ) : ['csv', 'xlsx', 'xls'].includes(dataset.file_type || '') ? (
                        <Table className="w-4 h-4 text-emerald-600" />
                      ) : ['jpg', 'jpeg', 'png'].includes(dataset.file_type || '') ? (
                        <Image className="w-4 h-4 text-orange-600" />
                      ) : (
                        <FileText className="w-4 h-4 text-gray-600" />
                      )}
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-black text-sm mb-1 truncate overflow-hidden" title={dataset.name}>
                        {truncateFileName(dataset.name)}
                      </div>
                      <div className="text-xs text-gray-500 mb-1.5">
                        {new Date(dataset.created_at).toLocaleDateString()} {new Date(dataset.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Extension and OCR badges - moved to left */}
                <div className="flex items-center space-x-1.5 mb-2">
                  <span className={`px-2 py-1 text-sm rounded-md font-medium ${
                    dataset.file_type === 'pdf' ? 'bg-red-50 text-red-700 border border-red-200' :
                    dataset.file_type === 'docx' ? 'bg-blue-50 text-blue-700 border border-blue-200' :
                    dataset.file_type === 'txt' ? 'bg-gray-50 text-gray-700 border border-gray-200' :
                    dataset.file_type === 'md' ? 'bg-purple-50 text-purple-700 border border-purple-200' :
                    ['jpg', 'jpeg', 'png'].includes(dataset.file_type || '') ? 'bg-orange-50 text-orange-700 border border-orange-200' :
                    'bg-green-50 text-green-700 border border-green-200'
                  }`}>
                    {dataset.file_type?.toUpperCase() || 'UNKNOWN'}
                  </span>
                  {dataset.content_type === 'document' && (
                    <span className="px-2 py-1 text-sm bg-blue-50 text-blue-700 border border-blue-200 rounded-md font-medium">
                      {['jpg', 'jpeg', 'png'].includes(dataset.file_type || '') ? 'OCR' : 'TEXT'}
                    </span>
                  )}
                </div>

                {/* Stats */}
                <div className="text-gray-600 text-sm mb-2">
                  {dataset.content_type === 'document' ? (
                    // For images, show simpler status
                    ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(dataset.file_type || '') ? (
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center space-x-1">
                          <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                          <span>Image</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                          <span>Ready to view</span>
                        </span>
                      </div>
                    ) : (
                      // For text documents, show word/char count only if meaningful (not OCR error messages)
                      (dataset.word_count && dataset.word_count > 10 && dataset.character_count && dataset.character_count > 100) ? (
                        <div className="flex items-center space-x-3 flex-wrap">
                          <span className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                            <span>{dataset.word_count} words</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                            <span>{dataset.character_count} chars</span>
                          </span>
                          {dataset.total_chunks && (
                            <span className="flex items-center space-x-1">
                              <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                              <span>{dataset.total_chunks} chunks</span>
                            </span>
                          )}
                          {dataset.chunking_template && (
                            <span className="flex items-center space-x-1">
                              <span className="w-2 h-2 bg-indigo-400 rounded-full"></span>
                              <span>{dataset.chunking_template}</span>
                            </span>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                            <span>Document</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                            <span>Ready to view</span>
                          </span>
                          {dataset.total_chunks && (
                            <span className="flex items-center space-x-1">
                              <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                              <span>{dataset.total_chunks} chunks</span>
                            </span>
                          )}
                        </div>
                      )
                    )
                  ) : (
                    <div className="flex items-center space-x-3">
                      <span className="flex items-center space-x-1">
                        <span className="w-2 h-2 bg-emerald-400 rounded-full"></span>
                        <span>{dataset.row_count} rows</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <span className="w-2 h-2 bg-teal-400 rounded-full"></span>
                        <span>{dataset.columns.length} columns</span>
                      </span>
                    </div>
                  )}
                </div>

                {/* Actions - moved below stats */}
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewDocument(dataset);
                    }}
                    className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    <Eye className="w-3 h-3" />
                    <span>View</span>
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleView(dataset);
                    }}
                    className="text-sm text-gray-600 hover:text-gray-800 hover:underline"
                  >
                    Details
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setDeleteConfirm(dataset.id);
                    }}
                    className="text-sm text-red-600 hover:text-red-800 hover:underline"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Delete Dataset</h3>
            <p className="text-base text-gray-700 mb-4">
              Are you sure you want to delete this dataset? This action cannot be undone and will also delete all associated queries.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="px-4 py-2 text-base text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDelete(deleteConfirm)}
                className="px-4 py-2 text-base bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Dataset Modal */}
      {viewDataset && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Dataset Details</h3>
              <button
                onClick={() => setViewDataset(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <p className="text-gray-900">{viewDataset.name}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">File Type</label>
                <p className="text-gray-900">{viewDataset.file_type?.toUpperCase() || 'UNKNOWN'} • {viewDataset.content_type || 'tabular'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Statistics</label>
                <div className="text-gray-900">
                  {viewDataset.content_type === 'document' ? (
                    <div className="space-y-1">
                      {viewDataset.word_count && <p>{viewDataset.word_count} words</p>}
                      {viewDataset.character_count && <p>{viewDataset.character_count} characters</p>}
                      {viewDataset.total_chunks && <p>{viewDataset.total_chunks} intelligent chunks</p>}
                    </div>
                  ) : (
                    <p>{viewDataset.row_count} rows • {viewDataset.columns.length} columns</p>
                  )}
                </div>
              </div>

              {viewDataset.chunking_template && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Document Processing</label>
                  <div className="text-gray-900">
                    <p className="flex items-center space-x-2">
                      <span className="px-2 py-1 bg-indigo-100 text-indigo-700 rounded text-sm font-medium">
                        {viewDataset.chunking_template.charAt(0).toUpperCase() + viewDataset.chunking_template.slice(1)} Template
                      </span>
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      Advanced chunking applied for better semantic understanding
                    </p>
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700">Created</label>
                <p className="text-gray-900">{new Date(viewDataset.created_at).toLocaleString()}</p>
              </div>


            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={() => setViewDataset(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Document Viewer */}
      {selectedDataset && (
        <DocumentViewer
          dataset={selectedDataset}
          isOpen={viewerOpen}
          onClose={() => {
            setViewerOpen(false);
            setSelectedDataset(null);
          }}
        />
      )}
    </>
  );
}
