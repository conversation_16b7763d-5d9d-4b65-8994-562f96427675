"""
Database Models for Trust System
"""

from sqlalchemy import Column, Integer, String, Float, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from database import Base


class TrustScore(Base):
    """Trust score records with full component breakdown"""
    __tablename__ = "trust_scores"
    
    id = Column(Integer, primary_key=True, index=True)
    query_id = Column(Integer, ForeignKey("queries.id"), nullable=True)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Core scores
    overall_score = Column(Float)
    calibrated_score = Column(Float)
    base_score = Column(Float)
    
    # Component scores
    model_confidence = Column(Float)
    qat_regressor = Column(Float)
    refusal_detector = Column(Float)
    citation_precision = Column(Float)
    
    # Bayesian components
    user_posterior = Column(Float)
    topic_posterior = Column(Float)
    bayesian_adjustment = Column(Float)
    
    # Metadata
    topic = Column(String)
    explanation = Column(Text)
    factors = Column(JSON)  # List of contributing factors
    confidence_interval_lower = Column(Float)
    confidence_interval_upper = Column(Float)
    processing_time_ms = Column(Float)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="trust_scores")
    query = relationship("Query", back_populates="trust_score_record")


class UserTrustPrior(Base):
    """Bayesian priors for individual users"""
    __tablename__ = "user_trust_priors"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), unique=True)
    
    # Beta distribution parameters
    alpha = Column(Float, default=2.0)
    beta = Column(Float, default=2.0)
    
    # Statistics
    total_interactions = Column(Integer, default=0)
    mean_trust = Column(Float)
    variance = Column(Float)
    
    # Metadata
    last_updated = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="trust_prior")


class TopicTrustPrior(Base):
    """Bayesian priors for different topics/domains"""
    __tablename__ = "topic_trust_priors"
    
    id = Column(Integer, primary_key=True, index=True)
    topic = Column(String, unique=True, index=True)
    
    # Beta distribution parameters
    alpha = Column(Float, default=1.5)
    beta = Column(Float, default=1.5)
    
    # Statistics
    total_interactions = Column(Integer, default=0)
    mean_trust = Column(Float)
    variance = Column(Float)
    
    # Topic metadata
    keywords = Column(JSON)  # List of topic keywords
    
    # Timestamps
    last_updated = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class TrustFeedback(Base):
    """User feedback for trust score learning"""
    __tablename__ = "trust_feedback"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    query_id = Column(Integer, ForeignKey("queries.id"), nullable=True)
    trust_score_id = Column(Integer, ForeignKey("trust_scores.id"), nullable=True)
    
    # Feedback data
    predicted_trust = Column(Float)
    actual_correctness = Column(Boolean)
    feedback_weight = Column(Float, default=1.0)
    topic = Column(String)
    
    # User feedback details
    user_rating = Column(String)  # 'up', 'down', 'neutral'
    user_comment = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="trust_feedback")
    trust_score = relationship("TrustScore")


class TrustAuditReport(Base):
    """Audit reports for trust system monitoring"""
    __tablename__ = "trust_audit_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Audit metrics
    explainability_score = Column(Float)
    source_diversity_score = Column(Float)
    fairness_score = Column(Float)
    robustness_score = Column(Float)
    
    # Audit metadata
    sample_size = Column(Integer)
    audit_period_start = Column(DateTime(timezone=True))
    audit_period_end = Column(DateTime(timezone=True))
    
    # Results
    threshold_breaches = Column(JSON)  # List of threshold breaches
    recommendations = Column(JSON)  # List of recommendations
    risk_level = Column(String)  # 'LOW', 'MEDIUM', 'HIGH'
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class TrustEvaluationReport(Base):
    """Offline evaluation reports"""
    __tablename__ = "trust_evaluation_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Evaluation metrics
    roc_auc = Column(Float)
    calibration_error = Column(Float)
    accuracy = Column(Float)
    f1_score = Column(Float)
    
    # Precision/recall at thresholds
    precision_at_thresholds = Column(JSON)
    recall_at_thresholds = Column(JSON)
    
    # Evaluation metadata
    sample_size = Column(Integer)
    evaluation_type = Column(String, default='nightly')  # 'nightly', 'manual', 'continuous'
    
    # Performance tracking
    threshold_alerts = Column(JSON)  # List of performance alerts
    recommendations = Column(JSON)  # List of recommendations
    performance_trend = Column(String)  # 'IMPROVING', 'STABLE', 'DEGRADING'
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class TrustSystemConfig(Base):
    """Trust system configuration history"""
    __tablename__ = "trust_system_config"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Component weights
    model_confidence_weight = Column(Float)
    qat_regressor_weight = Column(Float)
    refusal_detector_weight = Column(Float)
    citation_precision_weight = Column(Float)
    
    # Bayesian parameters
    user_prior_alpha = Column(Float)
    user_prior_beta = Column(Float)
    topic_prior_alpha = Column(Float)
    topic_prior_beta = Column(Float)
    
    # Thresholds
    high_trust_threshold = Column(Float)
    moderate_trust_threshold = Column(Float)
    low_trust_threshold = Column(Float)
    
    # Configuration metadata
    config_version = Column(String)
    is_active = Column(Boolean, default=False)
    applied_by = Column(String)  # User who applied this config
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class MLModelMetadata(Base):
    """Metadata for ML models used in trust scoring"""
    __tablename__ = "ml_model_metadata"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Model identification
    model_name = Column(String)  # 'qat_regressor', 'refusal_detector', 'confidence_calibrator'
    model_version = Column(String)
    model_path = Column(String)
    
    # Training metadata
    training_data_size = Column(Integer)
    training_accuracy = Column(Float, nullable=True)
    validation_accuracy = Column(Float, nullable=True)
    
    # Model status
    is_active = Column(Boolean, default=False)
    is_trained = Column(Boolean, default=False)
    
    # Performance metrics
    performance_metrics = Column(JSON)  # Model-specific metrics
    
    # Timestamps
    trained_at = Column(DateTime(timezone=True), nullable=True)
    deployed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


# Add relationships to existing models (would be added to models.py)
"""
# Add these to the User model:
trust_scores = relationship("TrustScore", back_populates="user")
trust_prior = relationship("UserTrustPrior", back_populates="user", uselist=False)
trust_feedback = relationship("TrustFeedback", back_populates="user")

# Add this to the Query model:
trust_score_record = relationship("TrustScore", back_populates="query", uselist=False)
"""
