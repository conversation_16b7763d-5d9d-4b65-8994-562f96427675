# 🚀 AIthentiq Deployment Guide - Railway (Recommended)

## 💰 **Cost: $5/month for everything!**

Railway is the easiest and cheapest option for deploying your full-stack AIthentiq application.

---

## 📋 **Pre-Deployment Checklist**

### 1. **Gather Your API Keys**
You'll need these environment variables:

**Backend (.env):**
```env
OPENAI_API_KEY=sk-your_openai_key_here
STRIPE_API_KEY=sk_test_your_stripe_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
DATABASE_URL=postgresql://... (Railway will provide this)
PORT=8000
HOST=0.0.0.0
```

**Frontend (.env.local):**
```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_key
CLERK_SECRET_KEY=sk_test_your_clerk_secret
CLERK_WEBHOOK_SECRET=whsec_your_clerk_webhook
NEXT_PUBLIC_API_URL=https://your-backend-url.railway.app
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
NEXT_PUBLIC_PRO_PRICE_ID=price_your_price_id
```

---

## 🚀 **Railway Deployment Steps**

### **Step 1: Sign Up for Railway**
1. Go to https://railway.app
2. Click "Login" → "Login with GitHub"
3. Authorize Railway to access your repositories

### **Step 2: Create New Project**
1. Click "New Project"
2. Select "Deploy from GitHub repo"
3. Choose your `AIthentiq` repository
4. Railway will automatically detect:
   - ✅ Next.js frontend
   - ✅ FastAPI backend
   - ✅ PostgreSQL database needed

### **Step 3: Configure Services**

Railway will create 3 services:
- **Frontend** (Next.js)
- **Backend** (FastAPI)  
- **Database** (PostgreSQL)

### **Step 4: Set Environment Variables**

#### **For Backend Service:**
1. Click on "Backend" service
2. Go to "Variables" tab
3. Add these variables:

```env
OPENAI_API_KEY=your_openai_key_here
STRIPE_API_KEY=your_stripe_key_here
STRIPE_WEBHOOK_SECRET=your_webhook_secret
PORT=8000
HOST=0.0.0.0
```

#### **For Frontend Service:**
1. Click on "Frontend" service
2. Go to "Variables" tab
3. Add these variables:

```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_key
CLERK_SECRET_KEY=your_clerk_secret
CLERK_WEBHOOK_SECRET=your_clerk_webhook
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_key
NEXT_PUBLIC_PRO_PRICE_ID=your_price_id
```

**Note:** You'll get the backend URL after deployment to set `NEXT_PUBLIC_API_URL`

### **Step 5: Deploy**
1. Railway automatically starts building
2. Wait for both services to deploy (5-10 minutes)
3. You'll get URLs like:
   - Frontend: `https://aithentiq-frontend-production.railway.app`
   - Backend: `https://aithentiq-backend-production.railway.app`

### **Step 6: Update Frontend API URL**
1. Copy your backend URL
2. Go to Frontend service → Variables
3. Add: `NEXT_PUBLIC_API_URL=https://your-backend-url.railway.app`
4. Redeploy frontend

### **Step 7: Set Up Database**
1. Railway automatically creates PostgreSQL
2. The `DATABASE_URL` is automatically set
3. Your backend will run migrations automatically

---

## 🔧 **Alternative: Quick Deploy with Render**

If you prefer Render.com:

### **Render Deployment:**
1. Go to https://render.com
2. Connect your GitHub account
3. Create "Web Service" for backend
4. Create "Static Site" for frontend
5. Add environment variables
6. Deploy!

**Cost:** Free tier available, $7/month for production

---

## 🌐 **Alternative: Vercel + Railway Backend**

For maximum performance:

### **Frontend on Vercel (Free):**
1. Go to https://vercel.com
2. Import your GitHub repository
3. Vercel auto-detects Next.js
4. Add environment variables
5. Deploy!

### **Backend on Railway ($5/month):**
1. Deploy only backend to Railway
2. Point frontend to Railway backend URL

---

## 🔍 **Post-Deployment Checklist**

### **1. Test Your Deployment:**
- ✅ Frontend loads correctly
- ✅ Authentication works (Clerk)
- ✅ Backend API responds
- ✅ Database connections work
- ✅ File uploads work
- ✅ Charts render properly

### **2. Set Up Custom Domain (Optional):**
- Railway: Go to service → Settings → Domains
- Add your custom domain
- Update DNS records

### **3. Monitor Your App:**
- Railway provides built-in monitoring
- Check logs in Railway dashboard
- Set up error tracking (optional)

---

## 💡 **Pro Tips**

### **1. Environment Management:**
- Keep development and production API keys separate
- Use Stripe test keys for development
- Use Stripe live keys for production

### **2. Database Management:**
- Railway PostgreSQL is automatically backed up
- You can access database via Railway dashboard
- Use migrations for schema changes

### **3. Scaling:**
- Railway auto-scales based on usage
- Monitor usage in dashboard
- Upgrade plan if needed

---

## 🆘 **Troubleshooting**

### **Common Issues:**

**1. Build Failures:**
- Check build logs in Railway dashboard
- Ensure all dependencies are in package.json/requirements.txt

**2. Environment Variable Issues:**
- Double-check variable names (case-sensitive)
- Ensure no extra spaces in values
- Restart services after adding variables

**3. Database Connection Issues:**
- Railway automatically provides DATABASE_URL
- Check if migrations ran successfully
- Verify database service is running

**4. CORS Issues:**
- Ensure backend allows frontend domain
- Check CORS configuration in FastAPI

---

## 📞 **Support**

- **Railway:** https://docs.railway.app
- **Render:** https://render.com/docs
- **Vercel:** https://vercel.com/docs

---

## 🎯 **Recommended Choice: Railway**

**Why Railway is best for AIthentiq:**
- ✅ **Cheapest** ($5/month total)
- ✅ **Easiest** (zero configuration)
- ✅ **Fastest** to deploy
- ✅ **Includes everything** (database, hosting, domains)
- ✅ **Auto-deploys** from GitHub
- ✅ **Great for startups**

**Start here:** https://railway.app

Your AIthentiq will be live in under 30 minutes! 🚀
