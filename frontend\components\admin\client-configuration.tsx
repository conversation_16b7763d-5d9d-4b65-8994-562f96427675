'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Database, 
  Brain, 
  Cloud, 
  Shield,
  Globe,
  Zap,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { createApiInstance } from '@/lib/api';

interface ClientConfig {
  tenant_id: string;
  tenant_name: string;
  
  // LLM Configuration
  preferred_llm: string;
  llm_settings: {
    temperature: number;
    max_tokens: number;
    top_p: number;
    frequency_penalty: number;
    presence_penalty: number;
  };
  
  // Embedding Configuration
  preferred_embedding: string;
  embedding_settings: {
    chunk_size: number;
    chunk_overlap: number;
    similarity_threshold: number;
  };
  
  // Vector Store Configuration
  preferred_vector_store: string;
  vector_store_settings: {
    index_name: string;
    dimensions: number;
    metric: string;
  };
  
  // Data Connector Configuration
  allowed_connectors: string[];
  connector_settings: {
    max_file_size: number;
    max_files_per_sync: number;
    sync_frequency: string;
    auto_sync_enabled: boolean;
  };
  
  // Security Configuration
  security_settings: {
    require_2fa: boolean;
    allowed_domains: string[];
    ip_whitelist: string[];
    session_timeout: number;
  };
  
  // Feature Configuration
  features_enabled: {
    trust_scoring: boolean;
    source_attribution: boolean;
    query_history: boolean;
    data_export: boolean;
    api_access: boolean;
    advanced_analytics: boolean;
  };
}

export default function ClientConfiguration() {
  const { data: session } = useSession();
  const [config, setConfig] = useState<ClientConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const llmProviders = [
    { id: 'openai', name: 'OpenAI GPT-4', description: 'Advanced language model with excellent reasoning' },
    { id: 'anthropic', name: 'Anthropic Claude', description: 'Safe and helpful AI assistant' },
    { id: 'google', name: 'Google Gemini', description: 'Multimodal AI with strong performance' },
    { id: 'azure', name: 'Azure OpenAI', description: 'Enterprise-grade OpenAI models' }
  ];

  const embeddingProviders = [
    { id: 'openai', name: 'OpenAI Embeddings', description: 'High-quality text embeddings' },
    { id: 'sentence-transformers', name: 'Sentence Transformers', description: 'Open-source embedding models' },
    { id: 'cohere', name: 'Cohere Embeddings', description: 'Multilingual embedding models' },
    { id: 'azure', name: 'Azure OpenAI Embeddings', description: 'Enterprise OpenAI embeddings' }
  ];

  const vectorStores = [
    { id: 'pinecone', name: 'Pinecone', description: 'Managed vector database' },
    { id: 'weaviate', name: 'Weaviate', description: 'Open-source vector database' },
    { id: 'qdrant', name: 'Qdrant', description: 'High-performance vector search' },
    { id: 'chroma', name: 'ChromaDB', description: 'AI-native open-source embedding database' }
  ];

  const availableConnectors = [
    { id: 'github', name: 'GitHub', icon: '🔗', description: 'Git repositories and source code' },
    { id: 'sharepoint', name: 'SharePoint', icon: '📊', description: 'SharePoint sites and libraries' },
    { id: 'onedrive', name: 'OneDrive', icon: '☁️', description: 'OneDrive files and folders' },
    { id: 'google_drive', name: 'Google Drive', icon: '📁', description: 'Google Drive documents' },
    { id: 'dropbox', name: 'Dropbox', icon: '📦', description: 'Dropbox files and folders' },
    { id: 'database', name: 'Database', icon: '🗄️', description: 'SQL databases and warehouses' },
    { id: 'api', name: 'API', icon: '🌐', description: 'REST APIs and web services' },
    { id: 'file_upload', name: 'File Upload', icon: '📄', description: 'Manual file uploads' }
  ];

  useEffect(() => {
    if (session) {
      loadConfiguration();
    }
  }, [session]);

  const loadConfiguration = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.get('/admin/client-config');
      setConfig(response.data);
    } catch (err) {
      // Generate mock configuration for demonstration
      const mockConfig: ClientConfig = {
        tenant_id: 'demo-tenant',
        tenant_name: 'Demo Organization',
        preferred_llm: 'openai',
        llm_settings: {
          temperature: 0.7,
          max_tokens: 2048,
          top_p: 1.0,
          frequency_penalty: 0.0,
          presence_penalty: 0.0
        },
        preferred_embedding: 'openai',
        embedding_settings: {
          chunk_size: 1000,
          chunk_overlap: 200,
          similarity_threshold: 0.7
        },
        preferred_vector_store: 'pinecone',
        vector_store_settings: {
          index_name: 'aithentiq-demo',
          dimensions: 1536,
          metric: 'cosine'
        },
        allowed_connectors: ['github', 'sharepoint', 'onedrive', 'file_upload'],
        connector_settings: {
          max_file_size: 10485760, // 10MB
          max_files_per_sync: 1000,
          sync_frequency: 'hourly',
          auto_sync_enabled: true
        },
        security_settings: {
          require_2fa: false,
          allowed_domains: ['company.com'],
          ip_whitelist: [],
          session_timeout: 3600
        },
        features_enabled: {
          trust_scoring: true,
          source_attribution: true,
          query_history: true,
          data_export: true,
          api_access: false,
          advanced_analytics: true
        }
      };
      setConfig(mockConfig);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConfiguration = async () => {
    if (!session || !config) return;

    setSaving(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const api = createApiInstance(session);
      const response = await api.put('/admin/client-config', config);
      
      if (response.data.success) {
        setSuccessMessage('Configuration saved successfully!');
        setTimeout(() => setSuccessMessage(null), 3000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save configuration');
    } finally {
      setSaving(false);
    }
  };

  const updateConfig = (path: string, value: any) => {
    if (!config) return;

    const keys = path.split('.');
    const newConfig = { ...config };
    let current: any = newConfig;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setConfig(newConfig);
  };

  const toggleConnector = (connectorId: string) => {
    if (!config) return;

    const newConnectors = config.allowed_connectors.includes(connectorId)
      ? config.allowed_connectors.filter(id => id !== connectorId)
      : [...config.allowed_connectors, connectorId];

    updateConfig('allowed_connectors', newConnectors);
  };

  const toggleFeature = (featureKey: string) => {
    if (!config) return;
    updateConfig(`features_enabled.${featureKey}`, !config.features_enabled[featureKey as keyof typeof config.features_enabled]);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">Failed to load configuration</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Settings className="h-8 w-8 text-gray-700" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Client Configuration</h2>
            <p className="text-sm text-gray-600">Configure AI models, data sources, and features for {config.tenant_name}</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadConfiguration}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          <button
            onClick={handleSaveConfiguration}
            disabled={saving}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <Save className={`h-4 w-4 ${saving ? 'animate-spin' : ''}`} />
            <span>{saving ? 'Saving...' : 'Save Configuration'}</span>
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-700">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* LLM Configuration */}
        <div className="bg-white rounded-lg shadow border p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Brain className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">Language Model</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Provider</label>
              <select
                value={config.preferred_llm}
                onChange={(e) => updateConfig('preferred_llm', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {llmProviders.map(provider => (
                  <option key={provider.id} value={provider.id}>{provider.name}</option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                {llmProviders.find(p => p.id === config.preferred_llm)?.description}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Temperature</label>
                <input
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={config.llm_settings.temperature}
                  onChange={(e) => updateConfig('llm_settings.temperature', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Tokens</label>
                <input
                  type="number"
                  min="1"
                  max="8192"
                  value={config.llm_settings.max_tokens}
                  onChange={(e) => updateConfig('llm_settings.max_tokens', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Embedding Configuration */}
        <div className="bg-white rounded-lg shadow border p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Zap className="h-6 w-6 text-yellow-600" />
            <h3 className="text-lg font-semibold text-gray-900">Embeddings</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Provider</label>
              <select
                value={config.preferred_embedding}
                onChange={(e) => updateConfig('preferred_embedding', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {embeddingProviders.map(provider => (
                  <option key={provider.id} value={provider.id}>{provider.name}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Chunk Size</label>
                <input
                  type="number"
                  min="100"
                  max="2000"
                  value={config.embedding_settings.chunk_size}
                  onChange={(e) => updateConfig('embedding_settings.chunk_size', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Chunk Overlap</label>
                <input
                  type="number"
                  min="0"
                  max="500"
                  value={config.embedding_settings.chunk_overlap}
                  onChange={(e) => updateConfig('embedding_settings.chunk_overlap', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Vector Store Configuration */}
        <div className="bg-white rounded-lg shadow border p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Database className="h-6 w-6 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Vector Store</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Provider</label>
              <select
                value={config.preferred_vector_store}
                onChange={(e) => updateConfig('preferred_vector_store', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {vectorStores.map(store => (
                  <option key={store.id} value={store.id}>{store.name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Index Name</label>
              <input
                type="text"
                value={config.vector_store_settings.index_name}
                onChange={(e) => updateConfig('vector_store_settings.index_name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Data Connectors */}
        <div className="bg-white rounded-lg shadow border p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Cloud className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Data Connectors</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Allowed Connectors</label>
              <div className="grid grid-cols-2 gap-2">
                {availableConnectors.map(connector => (
                  <label key={connector.id} className="flex items-center space-x-2 p-2 border rounded-md hover:bg-gray-50">
                    <input
                      type="checkbox"
                      checked={config.allowed_connectors.includes(connector.id)}
                      onChange={() => toggleConnector(connector.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">{connector.icon} {connector.name}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max File Size (MB)</label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={config.connector_settings.max_file_size / 1048576}
                  onChange={(e) => updateConfig('connector_settings.max_file_size', parseInt(e.target.value) * 1048576)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sync Frequency</label>
                <select
                  value={config.connector_settings.sync_frequency}
                  onChange={(e) => updateConfig('connector_settings.sync_frequency', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="realtime">Real-time</option>
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Configuration */}
      <div className="bg-white rounded-lg shadow border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Shield className="h-6 w-6 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">Features & Security</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(config.features_enabled).map(([key, enabled]) => (
            <label key={key} className="flex items-center space-x-3 p-3 border rounded-md hover:bg-gray-50">
              <input
                type="checkbox"
                checked={enabled}
                onChange={() => toggleFeature(key)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm font-medium text-gray-700">
                {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            </label>
          ))}
        </div>

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Allowed Domains</label>
            <textarea
              value={config.security_settings.allowed_domains.join('\n')}
              onChange={(e) => updateConfig('security_settings.allowed_domains', e.target.value.split('\n').filter(d => d.trim()))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="company.com&#10;subdomain.company.com"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (seconds)</label>
            <input
              type="number"
              min="300"
              max="86400"
              value={config.security_settings.session_timeout}
              onChange={(e) => updateConfig('security_settings.session_timeout', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
