"""
Multi-tenant database models for AIthentiq
Implements proper tenant isolation with foreign key relationships and CASCADE DELETE
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, JSON, Index, UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base
import uuid
import secrets

def generate_api_key():
    """Generate a secure API key"""
    return secrets.token_urlsafe(32)

def generate_uuid():
    """Generate a UUID string"""
    return str(uuid.uuid4())

class Tenant(Base):
    """
    Core tenant model for multi-tenant architecture
    Each tenant represents a separate organization/client
    """
    __tablename__ = "tenants"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    name = Column(String, nullable=False)
    domain = Column(String, unique=True, index=True)  # e.g., "acme-corp"
    display_name = Column(String, nullable=False)  # e.g., "ACME Corporation"
    
    # Tenant Configuration
    subscription_plan = Column(String, default="free")  # free, basic, premium, enterprise
    max_users = Column(Integer, default=5)
    max_datasets = Column(Integer, default=10)
    max_storage_gb = Column(Float, default=1.0)
    
    # Feature Flags
    features_enabled = Column(JSON, default=lambda: {
        "github_connector": False,
        "sharepoint_connector": False,
        "onedrive_connector": False,
        "advanced_trust_scoring": False,
        "api_access": True,
        "custom_branding": False
    })
    
    # LLM and Embedding Preferences
    preferred_llm = Column(String, default="gpt-4")  # gpt-4, claude, gemini, mistral
    preferred_embedding = Column(String, default="openai")  # openai, cohere, huggingface, instructorxl
    preferred_vector_store = Column(String, default="faiss")  # faiss, pinecone, weaviate
    
    # Security Settings
    require_2fa = Column(Boolean, default=False)
    allowed_domains = Column(JSON, nullable=True)  # Email domains allowed for this tenant
    ip_whitelist = Column(JSON, nullable=True)  # IP addresses allowed
    
    # Status
    is_active = Column(Boolean, default=True)
    is_trial = Column(Boolean, default=True)
    trial_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    users = relationship("TenantUser", back_populates="tenant", cascade="all, delete-orphan")
    datasets = relationship("TenantDataset", back_populates="tenant", cascade="all, delete-orphan")
    queries = relationship("TenantQuery", back_populates="tenant", cascade="all, delete-orphan")
    connectors = relationship("DataConnector", back_populates="tenant", cascade="all, delete-orphan")

class TenantUser(Base):
    """
    User model with tenant isolation
    Each user belongs to exactly one tenant
    """
    __tablename__ = "tenant_users"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # User Identity
    email = Column(String, nullable=False, index=True)
    name = Column(String, nullable=True)
    password_hash = Column(String, nullable=True)  # Nullable for OAuth users
    
    # OAuth Integration
    oauth_provider = Column(String, nullable=True)  # google, github, microsoft
    oauth_id = Column(String, nullable=True)
    oauth_data = Column(JSON, nullable=True)
    
    # Role and Permissions
    role = Column(String, default="user")  # admin, user, guest
    permissions = Column(JSON, default=lambda: {
        "can_upload_datasets": True,
        "can_create_queries": True,
        "can_manage_connectors": False,
        "can_view_analytics": False,
        "can_manage_users": False
    })
    
    # Account Status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    
    # Subscription
    subscription_status = Column(String, default="active")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="users")
    datasets = relationship("TenantDataset", back_populates="user", cascade="all, delete-orphan")
    queries = relationship("TenantQuery", back_populates="user", cascade="all, delete-orphan")
    api_keys = relationship("TenantApiKey", back_populates="user", cascade="all, delete-orphan")
    
    # Composite unique constraint: email must be unique within tenant
    __table_args__ = (
        Index('ix_tenant_user_email', 'tenant_id', 'email', unique=True),
    )

class TenantApiKey(Base):
    """
    API Keys with tenant isolation
    """
    __tablename__ = "tenant_api_keys"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String, ForeignKey("tenant_users.id", ondelete="CASCADE"), nullable=False, index=True)
    
    key = Column(String, unique=True, index=True, default=generate_api_key)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    
    # Permissions and Scope
    scopes = Column(JSON, default=lambda: ["read", "write"])  # read, write, admin
    rate_limit_per_hour = Column(Integer, default=1000)
    
    # Status
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    last_used = Column(DateTime(timezone=True), nullable=True)
    usage_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    user = relationship("TenantUser", back_populates="api_keys")

class DataConnector(Base):
    """
    Data source connectors with tenant isolation
    """
    __tablename__ = "data_connectors"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Connector Details
    name = Column(String, nullable=False)
    connector_type = Column(String, nullable=False)  # github, sharepoint, onedrive, google_drive, dropbox, network_folder
    description = Column(Text, nullable=True)
    
    # Configuration
    config = Column(JSON, nullable=False)  # Connector-specific configuration
    credentials = Column(JSON, nullable=True)  # Encrypted credentials
    
    # Sync Settings
    auto_sync_enabled = Column(Boolean, default=True)
    sync_frequency = Column(String, default="daily")  # hourly, daily, weekly
    last_sync_at = Column(DateTime(timezone=True), nullable=True)
    next_sync_at = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    status = Column(String, default="active")  # active, paused, error, disconnected
    last_error = Column(Text, nullable=True)
    sync_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="connectors")
    datasets = relationship("TenantDataset", back_populates="connector")

class TenantDataset(Base):
    """
    Datasets with tenant isolation
    """
    __tablename__ = "tenant_datasets"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String, ForeignKey("tenant_users.id", ondelete="CASCADE"), nullable=False, index=True)
    connector_id = Column(String, ForeignKey("data_connectors.id", ondelete="SET NULL"), nullable=True, index=True)
    
    # Dataset Details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    source_type = Column(String, nullable=False)  # upload, github, sharepoint, etc.
    source_path = Column(String, nullable=True)  # Path in the source system
    
    # File Information
    file_type = Column(String, nullable=True)  # csv, xlsx, pdf, docx, txt, md
    file_size = Column(Integer, nullable=True)  # Size in bytes
    content_type = Column(String, default='tabular')  # tabular, document, mixed
    
    # Data Structure
    columns = Column(JSON, nullable=True)  # Column names and types
    row_count = Column(Integer, default=0)
    parsed_data = Column(Text, nullable=True)  # JSON string of parsed data (for small datasets)
    
    # Processing Status
    processing_status = Column(String, default='pending')  # pending, processing, completed, failed
    processing_error = Column(Text, nullable=True)
    embeddings_status = Column(String, default='pending')  # pending, processing, completed, failed
    
    # Content Metrics
    word_count = Column(Integer, default=0)
    character_count = Column(Integer, default=0)
    chunk_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="datasets")
    user = relationship("TenantUser", back_populates="datasets")
    connector = relationship("DataConnector", back_populates="datasets")
    queries = relationship("TenantQuery", back_populates="dataset", cascade="all, delete-orphan")
    chunks = relationship("TenantDocumentChunk", back_populates="dataset", cascade="all, delete-orphan")

class TenantQuery(Base):
    """
    Queries with tenant isolation
    """
    __tablename__ = "tenant_queries"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String, ForeignKey("tenant_users.id", ondelete="CASCADE"), nullable=False, index=True)
    dataset_id = Column(String, ForeignKey("tenant_datasets.id", ondelete="CASCADE"), nullable=True, index=True)
    
    # Query Content
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=True)
    
    # Response Data
    chart_type = Column(String, nullable=True)
    chart_data = Column(JSON, nullable=True)
    trust_score_data = Column(JSON, nullable=True)
    reasoning_steps = Column(JSON, nullable=True)
    formula_results = Column(JSON, nullable=True)
    
    # Query Metadata
    query_type = Column(String, default="general")  # general, analytical, chart, formula
    tags = Column(JSON, nullable=True)
    is_bookmarked = Column(Boolean, default=False)
    
    # Performance Metrics
    processing_time_ms = Column(Integer, nullable=True)
    token_count = Column(Integer, nullable=True)
    source_count = Column(Integer, nullable=True)
    
    # Quality Metrics
    trust_score = Column(Float, nullable=True)
    confidence_score = Column(Float, nullable=True)
    hallucination_risk = Column(Float, nullable=True)
    completeness_score = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="queries")
    user = relationship("TenantUser", back_populates="queries")
    dataset = relationship("TenantDataset", back_populates="queries")
    source_attributions = relationship("TenantSourceAttribution", back_populates="query", cascade="all, delete-orphan")

class TenantDocumentChunk(Base):
    """
    Document chunks with tenant isolation
    """
    __tablename__ = "tenant_document_chunks"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    dataset_id = Column(String, ForeignKey("tenant_datasets.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Content
    text = Column(Text, nullable=False)
    text_hash = Column(String, index=True)
    chunk_index = Column(Integer, index=True)
    
    # Source Attribution
    source_document = Column(String, nullable=True)
    page_number = Column(Integer, nullable=True)
    line_start = Column(Integer, nullable=True)
    line_end = Column(Integer, nullable=True)
    
    # Embeddings
    embedding_vector = Column(JSON, nullable=True)
    embedding_model = Column(String, nullable=True)
    
    # Metadata
    chunk_type = Column(String, default='paragraph')
    word_count = Column(Integer, default=0)
    char_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    dataset = relationship("TenantDataset", back_populates="chunks")
    source_attributions = relationship("TenantSourceAttribution", back_populates="chunk")

class TenantSourceAttribution(Base):
    """
    Source attribution with tenant isolation
    """
    __tablename__ = "tenant_source_attributions"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    query_id = Column(String, ForeignKey("tenant_queries.id", ondelete="CASCADE"), nullable=False, index=True)
    chunk_id = Column(String, ForeignKey("tenant_document_chunks.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Attribution Details
    relevance_score = Column(Float, nullable=False)
    confidence_score = Column(Float, nullable=False)
    rank_position = Column(Integer, nullable=False)
    
    # Usage Context
    quoted_text = Column(Text, nullable=True)
    search_method = Column(String, nullable=False)  # semantic, keyword, hybrid
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    query = relationship("TenantQuery", back_populates="source_attributions")
    chunk = relationship("TenantDocumentChunk", back_populates="source_attributions")
