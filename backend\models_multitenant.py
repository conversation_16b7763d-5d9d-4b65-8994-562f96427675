"""
Advanced Enterprise Multi-tenant Database Models for AIthentiq
Features: Encryption, Audit Trails, Performance Optimization, GDPR Compliance
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, JSON, Index, UUID, LargeBinary, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates
from sqlalchemy.sql import func
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.postgresql import JSONB
from database import Base
import uuid
import secrets
import hashlib
import json
from datetime import datetime, timezone
from enum import Enum
from cryptography.fernet import Fernet
import os
import logging

logger = logging.getLogger(__name__)

# Encryption key for sensitive data
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY", Fernet.generate_key())
cipher_suite = Fernet(ENCRYPTION_KEY)

class AuditAction(Enum):
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    LOGIN = "login"
    LOGOUT = "logout"
    EXPORT = "export"
    IMPORT = "import"

class DataClassification(Enum):
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"

def generate_api_key():
    """Generate a cryptographically secure API key"""
    return secrets.token_urlsafe(48)  # 64 characters

def generate_uuid():
    """Generate a UUID string"""
    return str(uuid.uuid4())

def encrypt_data(data: str) -> str:
    """Encrypt sensitive data"""
    if not data:
        return data
    try:
        return cipher_suite.encrypt(data.encode()).decode()
    except Exception as e:
        logger.error(f"Encryption failed: {e}")
        return data

def decrypt_data(encrypted_data: str) -> str:
    """Decrypt sensitive data"""
    if not encrypted_data:
        return encrypted_data
    try:
        return cipher_suite.decrypt(encrypted_data.encode()).decode()
    except Exception as e:
        logger.error(f"Decryption failed: {e}")
        return encrypted_data

class AuditMixin:
    """Mixin for audit trail functionality"""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String, nullable=True)
    updated_by = Column(String, nullable=True)
    version = Column(Integer, default=1, nullable=False)

    # GDPR compliance
    data_classification = Column(SQLEnum(DataClassification), default=DataClassification.INTERNAL)
    retention_period_days = Column(Integer, default=2555)  # 7 years default
    anonymized_at = Column(DateTime(timezone=True), nullable=True)

    def update_version(self):
        """Increment version for optimistic locking"""
        self.version += 1

class Tenant(Base, AuditMixin):
    """
    Advanced Enterprise Tenant Model
    Features: Encryption, Compliance, Performance Monitoring, Security
    """
    __tablename__ = "tenants"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    name = Column(String, nullable=False, index=True)
    domain = Column(String, unique=True, index=True)  # e.g., "acme-corp"
    display_name = Column(String, nullable=False)

    # Enterprise Configuration
    subscription_plan = Column(String, default="free", index=True)
    subscription_tier = Column(String, default="standard")  # standard, professional, enterprise
    max_users = Column(Integer, default=5)
    max_datasets = Column(Integer, default=10)
    max_storage_gb = Column(Float, default=1.0)
    max_api_calls_per_hour = Column(Integer, default=1000)
    max_concurrent_queries = Column(Integer, default=5)

    # Advanced Feature Flags with Usage Tracking
    features_enabled = Column(JSONB, default=lambda: {
        "github_connector": False,
        "sharepoint_connector": False,
        "onedrive_connector": False,
        "advanced_trust_scoring": False,
        "api_access": True,
        "custom_branding": False,
        "white_label": False,
        "sso_integration": False,
        "advanced_analytics": False,
        "data_export": True,
        "audit_logs": True,
        "backup_restore": False
    })

    # AI/ML Configuration
    preferred_llm = Column(String, default="gpt-4")
    preferred_embedding = Column(String, default="openai")
    preferred_vector_store = Column(String, default="faiss")
    llm_temperature = Column(Float, default=0.7)
    max_tokens = Column(Integer, default=2048)

    # Enterprise Security Settings
    require_2fa = Column(Boolean, default=False)
    require_sso = Column(Boolean, default=False)
    session_timeout_minutes = Column(Integer, default=60)
    password_policy = Column(JSONB, default=lambda: {
        "min_length": 8,
        "require_uppercase": True,
        "require_lowercase": True,
        "require_numbers": True,
        "require_symbols": True,
        "max_age_days": 90
    })

    # Network Security
    allowed_domains = Column(JSONB, nullable=True)
    ip_whitelist = Column(JSONB, nullable=True)
    allowed_countries = Column(JSONB, nullable=True)

    # Compliance & Data Governance
    compliance_frameworks = Column(JSONB, default=lambda: [])  # GDPR, HIPAA, SOC2, etc.
    data_residency_region = Column(String, default="us-east-1")
    encryption_at_rest = Column(Boolean, default=True)
    encryption_in_transit = Column(Boolean, default=True)

    # Business Information
    company_size = Column(String, nullable=True)  # startup, small, medium, large, enterprise
    industry = Column(String, nullable=True)
    contact_email = Column(String, nullable=True)
    billing_email = Column(String, nullable=True)

    # Status & Health
    is_active = Column(Boolean, default=True, index=True)
    is_trial = Column(Boolean, default=True)
    is_suspended = Column(Boolean, default=False)
    suspension_reason = Column(Text, nullable=True)
    trial_expires_at = Column(DateTime(timezone=True), nullable=True)
    last_activity_at = Column(DateTime(timezone=True), nullable=True)

    # Performance Metrics
    total_queries = Column(Integer, default=0)
    total_storage_used_gb = Column(Float, default=0.0)
    avg_query_time_ms = Column(Float, default=0.0)

    # Relationships with proper indexing
    users = relationship("TenantUser", back_populates="tenant", cascade="all, delete-orphan", lazy="dynamic")
    datasets = relationship("TenantDataset", back_populates="tenant", cascade="all, delete-orphan", lazy="dynamic")
    queries = relationship("TenantQuery", back_populates="tenant", cascade="all, delete-orphan", lazy="dynamic")
    connectors = relationship("DataConnector", back_populates="tenant", cascade="all, delete-orphan", lazy="dynamic")
    audit_logs = relationship("AuditLog", back_populates="tenant", cascade="all, delete-orphan", lazy="dynamic")

    # Indexes for performance
    __table_args__ = (
        Index('ix_tenant_domain_active', 'domain', 'is_active'),
        Index('ix_tenant_subscription', 'subscription_plan', 'subscription_tier'),
        Index('ix_tenant_activity', 'last_activity_at'),
    )

    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled for this tenant"""
        return self.features_enabled.get(feature, False)

    def get_usage_stats(self) -> dict:
        """Get tenant usage statistics"""
        return {
            "total_users": self.users.count(),
            "total_datasets": self.datasets.count(),
            "total_queries": self.total_queries,
            "storage_used_gb": self.total_storage_used_gb,
            "storage_limit_gb": self.max_storage_gb,
            "storage_usage_percent": (self.total_storage_used_gb / self.max_storage_gb) * 100 if self.max_storage_gb > 0 else 0
        }

    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity_at = datetime.now(timezone.utc)

class AuditLog(Base, AuditMixin):
    """
    Comprehensive audit logging for compliance and security
    """
    __tablename__ = "audit_logs"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String, ForeignKey("tenant_users.id", ondelete="SET NULL"), nullable=True, index=True)

    # Audit Details
    action = Column(SQLEnum(AuditAction), nullable=False, index=True)
    resource_type = Column(String, nullable=False, index=True)  # tenant, user, dataset, query, etc.
    resource_id = Column(String, nullable=True, index=True)

    # Request Context
    ip_address = Column(String, nullable=True, index=True)
    user_agent = Column(Text, nullable=True)
    session_id = Column(String, nullable=True, index=True)
    request_id = Column(String, nullable=True, index=True)

    # Change Details
    old_values = Column(JSONB, nullable=True)
    new_values = Column(JSONB, nullable=True)
    changes_summary = Column(Text, nullable=True)

    # Security Context
    risk_score = Column(Integer, default=0)  # 0-100 risk assessment
    security_flags = Column(JSONB, default=lambda: [])

    # Performance Metrics
    execution_time_ms = Column(Integer, nullable=True)

    # Compliance
    compliance_tags = Column(JSONB, default=lambda: [])
    retention_until = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    tenant = relationship("Tenant", back_populates="audit_logs")
    user = relationship("TenantUser", back_populates="audit_logs")

    # Indexes for performance and compliance queries
    __table_args__ = (
        Index('ix_audit_tenant_action_time', 'tenant_id', 'action', 'created_at'),
        Index('ix_audit_user_time', 'user_id', 'created_at'),
        Index('ix_audit_resource', 'resource_type', 'resource_id'),
        Index('ix_audit_security', 'risk_score', 'created_at'),
        Index('ix_audit_compliance', 'compliance_tags'),
    )

    @classmethod
    def log_action(
        cls,
        tenant_id: str,
        action: AuditAction,
        resource_type: str,
        user_id: str = None,
        resource_id: str = None,
        old_values: dict = None,
        new_values: dict = None,
        ip_address: str = None,
        user_agent: str = None,
        session_id: str = None,
        request_id: str = None,
        risk_score: int = 0,
        compliance_tags: list = None
    ):
        """Create an audit log entry"""
        from database import SessionLocal

        db = SessionLocal()
        try:
            audit_entry = cls(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                old_values=old_values,
                new_values=new_values,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                request_id=request_id,
                risk_score=risk_score,
                compliance_tags=compliance_tags or []
            )

            db.add(audit_entry)
            db.commit()

        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")
            db.rollback()
        finally:
            db.close()

class TenantUser(Base):
    """
    User model with tenant isolation
    Each user belongs to exactly one tenant
    """
    __tablename__ = "tenant_users"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # User Identity
    email = Column(String, nullable=False, index=True)
    name = Column(String, nullable=True)
    password_hash = Column(String, nullable=True)  # Nullable for OAuth users
    
    # OAuth Integration
    oauth_provider = Column(String, nullable=True)  # google, github, microsoft
    oauth_id = Column(String, nullable=True)
    oauth_data = Column(JSON, nullable=True)
    
    # Role and Permissions
    role = Column(String, default="user")  # admin, user, guest
    permissions = Column(JSON, default=lambda: {
        "can_upload_datasets": True,
        "can_create_queries": True,
        "can_manage_connectors": False,
        "can_view_analytics": False,
        "can_manage_users": False
    })
    
    # Account Status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    
    # Subscription
    subscription_status = Column(String, default="active")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="users")
    datasets = relationship("TenantDataset", back_populates="user", cascade="all, delete-orphan")
    queries = relationship("TenantQuery", back_populates="user", cascade="all, delete-orphan")
    api_keys = relationship("TenantApiKey", back_populates="user", cascade="all, delete-orphan")
    
    # Composite unique constraint: email must be unique within tenant
    __table_args__ = (
        Index('ix_tenant_user_email', 'tenant_id', 'email', unique=True),
    )

class TenantApiKey(Base):
    """
    API Keys with tenant isolation
    """
    __tablename__ = "tenant_api_keys"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String, ForeignKey("tenant_users.id", ondelete="CASCADE"), nullable=False, index=True)
    
    key = Column(String, unique=True, index=True, default=generate_api_key)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    
    # Permissions and Scope
    scopes = Column(JSON, default=lambda: ["read", "write"])  # read, write, admin
    rate_limit_per_hour = Column(Integer, default=1000)
    
    # Status
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    last_used = Column(DateTime(timezone=True), nullable=True)
    usage_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    user = relationship("TenantUser", back_populates="api_keys")

class DataConnector(Base):
    """
    Data source connectors with tenant isolation
    """
    __tablename__ = "data_connectors"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Connector Details
    name = Column(String, nullable=False)
    connector_type = Column(String, nullable=False)  # github, sharepoint, onedrive, google_drive, dropbox, network_folder
    description = Column(Text, nullable=True)
    
    # Configuration
    config = Column(JSON, nullable=False)  # Connector-specific configuration
    credentials = Column(JSON, nullable=True)  # Encrypted credentials
    
    # Sync Settings
    auto_sync_enabled = Column(Boolean, default=True)
    sync_frequency = Column(String, default="daily")  # hourly, daily, weekly
    last_sync_at = Column(DateTime(timezone=True), nullable=True)
    next_sync_at = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    status = Column(String, default="active")  # active, paused, error, disconnected
    last_error = Column(Text, nullable=True)
    sync_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="connectors")
    datasets = relationship("TenantDataset", back_populates="connector")

class TenantDataset(Base):
    """
    Datasets with tenant isolation
    """
    __tablename__ = "tenant_datasets"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String, ForeignKey("tenant_users.id", ondelete="CASCADE"), nullable=False, index=True)
    connector_id = Column(String, ForeignKey("data_connectors.id", ondelete="SET NULL"), nullable=True, index=True)
    
    # Dataset Details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    source_type = Column(String, nullable=False)  # upload, github, sharepoint, etc.
    source_path = Column(String, nullable=True)  # Path in the source system
    
    # File Information
    file_type = Column(String, nullable=True)  # csv, xlsx, pdf, docx, txt, md
    file_size = Column(Integer, nullable=True)  # Size in bytes
    content_type = Column(String, default='tabular')  # tabular, document, mixed
    
    # Data Structure
    columns = Column(JSON, nullable=True)  # Column names and types
    row_count = Column(Integer, default=0)
    parsed_data = Column(Text, nullable=True)  # JSON string of parsed data (for small datasets)
    
    # Processing Status
    processing_status = Column(String, default='pending')  # pending, processing, completed, failed
    processing_error = Column(Text, nullable=True)
    embeddings_status = Column(String, default='pending')  # pending, processing, completed, failed
    
    # Content Metrics
    word_count = Column(Integer, default=0)
    character_count = Column(Integer, default=0)
    chunk_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="datasets")
    user = relationship("TenantUser", back_populates="datasets")
    connector = relationship("DataConnector", back_populates="datasets")
    queries = relationship("TenantQuery", back_populates="dataset", cascade="all, delete-orphan")
    chunks = relationship("TenantDocumentChunk", back_populates="dataset", cascade="all, delete-orphan")

class TenantQuery(Base):
    """
    Queries with tenant isolation
    """
    __tablename__ = "tenant_queries"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(String, ForeignKey("tenant_users.id", ondelete="CASCADE"), nullable=False, index=True)
    dataset_id = Column(String, ForeignKey("tenant_datasets.id", ondelete="CASCADE"), nullable=True, index=True)
    
    # Query Content
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=True)
    
    # Response Data
    chart_type = Column(String, nullable=True)
    chart_data = Column(JSON, nullable=True)
    trust_score_data = Column(JSON, nullable=True)
    reasoning_steps = Column(JSON, nullable=True)
    formula_results = Column(JSON, nullable=True)
    
    # Query Metadata
    query_type = Column(String, default="general")  # general, analytical, chart, formula
    tags = Column(JSON, nullable=True)
    is_bookmarked = Column(Boolean, default=False)
    
    # Performance Metrics
    processing_time_ms = Column(Integer, nullable=True)
    token_count = Column(Integer, nullable=True)
    source_count = Column(Integer, nullable=True)
    
    # Quality Metrics
    trust_score = Column(Float, nullable=True)
    confidence_score = Column(Float, nullable=True)
    hallucination_risk = Column(Float, nullable=True)
    completeness_score = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="queries")
    user = relationship("TenantUser", back_populates="queries")
    dataset = relationship("TenantDataset", back_populates="queries")
    source_attributions = relationship("TenantSourceAttribution", back_populates="query", cascade="all, delete-orphan")

class TenantDocumentChunk(Base):
    """
    Document chunks with tenant isolation
    """
    __tablename__ = "tenant_document_chunks"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    dataset_id = Column(String, ForeignKey("tenant_datasets.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Content
    text = Column(Text, nullable=False)
    text_hash = Column(String, index=True)
    chunk_index = Column(Integer, index=True)
    
    # Source Attribution
    source_document = Column(String, nullable=True)
    page_number = Column(Integer, nullable=True)
    line_start = Column(Integer, nullable=True)
    line_end = Column(Integer, nullable=True)
    
    # Embeddings
    embedding_vector = Column(JSON, nullable=True)
    embedding_model = Column(String, nullable=True)
    
    # Metadata
    chunk_type = Column(String, default='paragraph')
    word_count = Column(Integer, default=0)
    char_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    dataset = relationship("TenantDataset", back_populates="chunks")
    source_attributions = relationship("TenantSourceAttribution", back_populates="chunk")

class TenantSourceAttribution(Base):
    """
    Source attribution with tenant isolation
    """
    __tablename__ = "tenant_source_attributions"

    id = Column(String, primary_key=True, index=True, default=generate_uuid)
    tenant_id = Column(String, ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)
    query_id = Column(String, ForeignKey("tenant_queries.id", ondelete="CASCADE"), nullable=False, index=True)
    chunk_id = Column(String, ForeignKey("tenant_document_chunks.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # Attribution Details
    relevance_score = Column(Float, nullable=False)
    confidence_score = Column(Float, nullable=False)
    rank_position = Column(Integer, nullable=False)
    
    # Usage Context
    quoted_text = Column(Text, nullable=True)
    search_method = Column(String, nullable=False)  # semantic, keyword, hybrid
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    query = relationship("TenantQuery", back_populates="source_attributions")
    chunk = relationship("TenantDocumentChunk", back_populates="source_attributions")
