// Test frontend API connection
const axios = require('axios');

async function testFrontendAPI() {
  console.log('🧪 Testing Frontend API Connection...');
  
  const baseURL = 'http://localhost:8000';
  
  try {
    // Test 1: Basic connection
    console.log('\n1. Testing basic connection...');
    const healthResponse = await axios.get(`${baseURL}/`);
    console.log('✅ Basic connection successful');
    
    // Test 2: Get feedback stats
    console.log('\n2. Testing feedback stats...');
    const statsResponse = await axios.get(`${baseURL}/feedback/admin/stats`);
    console.log('✅ Stats response:', statsResponse.data);
    
    // Test 3: Get all feedback
    console.log('\n3. Testing feedback list...');
    const feedbackResponse = await axios.get(`${baseURL}/feedback/admin/all`);
    console.log('✅ Feedback response:', feedbackResponse.data);
    
    // Test 4: CORS headers
    console.log('\n4. Testing CORS headers...');
    console.log('Response headers:', feedbackResponse.headers);
    
    console.log('\n🎉 All API tests passed!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testFrontendAPI();
