"""
Conversation Service for RAG Chat
Handles conversation context, memory, and multi-turn reasoning
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict

@dataclass
class ConversationMessage:
    """Represents a single message in a conversation"""
    id: str
    conversation_id: str
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def to_dict(self):
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

@dataclass
class ConversationContext:
    """Represents conversation context and memory"""
    conversation_id: str
    dataset_id: int
    user_id: str
    title: str
    messages: List[ConversationMessage]
    created_at: datetime
    updated_at: datetime
    is_important: bool = False
    is_starred: bool = False
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    def to_dict(self):
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['messages'] = [msg.to_dict() for msg in self.messages]
        return data

class ConversationService:
    """Service for managing conversations and context"""
    
    def __init__(self):
        # In-memory storage for demo - in production, use database
        self.conversations: Dict[str, ConversationContext] = {}
        self.user_conversations: Dict[str, List[str]] = defaultdict(list)
        
    def create_conversation(
        self, 
        user_id: str, 
        dataset_id: int, 
        title: str = None
    ) -> str:
        """Create a new conversation"""
        conversation_id = str(uuid.uuid4())
        
        if not title:
            title = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        conversation = ConversationContext(
            conversation_id=conversation_id,
            dataset_id=dataset_id,
            user_id=user_id,
            title=title,
            messages=[],
            created_at=datetime.now(),
            updated_at=datetime.now(),
            is_important=False,
            is_starred=False,
            tags=[],
            metadata={}
        )
        
        self.conversations[conversation_id] = conversation
        self.user_conversations[user_id].append(conversation_id)
        
        return conversation_id
    
    def add_message(
        self, 
        conversation_id: str, 
        role: str, 
        content: str,
        metadata: Dict[str, Any] = None
    ) -> ConversationMessage:
        """Add a message to a conversation"""
        if conversation_id not in self.conversations:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        message_id = str(uuid.uuid4())
        message = ConversationMessage(
            id=message_id,
            conversation_id=conversation_id,
            role=role,
            content=content,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        
        conversation = self.conversations[conversation_id]
        conversation.messages.append(message)
        conversation.updated_at = datetime.now()
        
        # Update conversation title based on first user message
        if role == 'user' and len(conversation.messages) == 1:
            conversation.title = self._generate_title(content)
        
        return message
    
    def get_conversation(self, conversation_id: str) -> Optional[ConversationContext]:
        """Get a conversation by ID"""
        return self.conversations.get(conversation_id)
    
    def get_user_conversations(
        self, 
        user_id: str, 
        limit: int = 50
    ) -> List[ConversationContext]:
        """Get all conversations for a user"""
        conversation_ids = self.user_conversations.get(user_id, [])
        conversations = [
            self.conversations[conv_id] 
            for conv_id in conversation_ids 
            if conv_id in self.conversations
        ]
        
        # Sort by most recent first
        conversations.sort(key=lambda x: x.updated_at, reverse=True)
        
        return conversations[:limit]
    
    def get_conversation_context(
        self, 
        conversation_id: str, 
        max_messages: int = 10
    ) -> str:
        """Get conversation context as formatted string"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return ""
        
        # Get recent messages for context
        recent_messages = conversation.messages[-max_messages:]
        
        context_parts = []
        for message in recent_messages:
            role_label = "Human" if message.role == "user" else "Assistant"
            context_parts.append(f"{role_label}: {message.content}")
        
        return "\n".join(context_parts)
    
    def build_contextual_query(
        self, 
        conversation_id: str, 
        current_query: str,
        max_context_messages: int = 5
    ) -> str:
        """Build a contextual query including conversation history"""
        context = self.get_conversation_context(conversation_id, max_context_messages)
        
        if not context:
            return current_query
        
        # Format the contextual query
        contextual_query = f"""Previous conversation context:
{context}

Current question: {current_query}

Please answer the current question taking into account the conversation context above."""
        
        return contextual_query
    
    def delete_conversation(self, conversation_id: str, user_id: str) -> bool:
        """Delete a conversation"""
        conversation = self.conversations.get(conversation_id)
        if not conversation or conversation.user_id != user_id:
            return False
        
        del self.conversations[conversation_id]
        if user_id in self.user_conversations:
            self.user_conversations[user_id] = [
                conv_id for conv_id in self.user_conversations[user_id] 
                if conv_id != conversation_id
            ]
        
        return True
    
    def update_conversation_title(
        self,
        conversation_id: str,
        title: str,
        user_id: str
    ) -> bool:
        """Update conversation title"""
        conversation = self.conversations.get(conversation_id)
        if not conversation or conversation.user_id != user_id:
            return False

        conversation.title = title
        conversation.updated_at = datetime.now()
        return True

    def mark_conversation_important(
        self,
        conversation_id: str,
        is_important: bool,
        user_id: str
    ) -> bool:
        """Mark conversation as important"""
        conversation = self.conversations.get(conversation_id)
        if not conversation or conversation.user_id != user_id:
            return False

        conversation.is_important = is_important
        conversation.updated_at = datetime.now()
        return True

    def star_conversation(
        self,
        conversation_id: str,
        is_starred: bool,
        user_id: str
    ) -> bool:
        """Star/unstar conversation"""
        conversation = self.conversations.get(conversation_id)
        if not conversation or conversation.user_id != user_id:
            return False

        conversation.is_starred = is_starred
        conversation.updated_at = datetime.now()
        return True



    def add_conversation_tags(
        self,
        conversation_id: str,
        tags: List[str],
        user_id: str
    ) -> bool:
        """Add tags to conversation"""
        conversation = self.conversations.get(conversation_id)
        if not conversation or conversation.user_id != user_id:
            return False

        if not conversation.tags:
            conversation.tags = []

        for tag in tags:
            if tag not in conversation.tags:
                conversation.tags.append(tag)

        conversation.updated_at = datetime.now()
        return True
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """Get conversation summary statistics"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return {}
        
        user_messages = [msg for msg in conversation.messages if msg.role == 'user']
        assistant_messages = [msg for msg in conversation.messages if msg.role == 'assistant']
        
        return {
            'conversation_id': conversation_id,
            'title': conversation.title,
            'dataset_id': conversation.dataset_id,
            'total_messages': len(conversation.messages),
            'user_messages': len(user_messages),
            'assistant_messages': len(assistant_messages),
            'created_at': conversation.created_at.isoformat(),
            'updated_at': conversation.updated_at.isoformat(),
            'last_message': conversation.messages[-1].content if conversation.messages else None,
            'last_message_time': conversation.messages[-1].timestamp.isoformat() if conversation.messages else None
        }
    
    def cleanup_old_conversations(self, days_old: int = 30):
        """Clean up conversations older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        conversations_to_delete = [
            conv_id for conv_id, conv in self.conversations.items()
            if conv.updated_at < cutoff_date
        ]
        
        for conv_id in conversations_to_delete:
            conversation = self.conversations[conv_id]
            self.delete_conversation(conv_id, conversation.user_id)
        
        return len(conversations_to_delete)
    
    def _generate_title(self, first_message: str) -> str:
        """Generate a conversation title from the first message"""
        # Simple title generation - take first 6 words
        words = first_message.split()[:6]
        title = " ".join(words)
        
        if len(first_message.split()) > 6:
            title += "..."
        
        return title or "New Conversation"
    
    def search_conversations(
        self, 
        user_id: str, 
        query: str, 
        limit: int = 20
    ) -> List[ConversationContext]:
        """Search conversations by content"""
        user_conversations = self.get_user_conversations(user_id, limit=1000)
        
        query_lower = query.lower()
        matching_conversations = []
        
        for conversation in user_conversations:
            # Search in title
            if query_lower in conversation.title.lower():
                matching_conversations.append(conversation)
                continue
            
            # Search in message content
            for message in conversation.messages:
                if query_lower in message.content.lower():
                    matching_conversations.append(conversation)
                    break
        
        return matching_conversations[:limit]
    
    def export_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """Export conversation data"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return {}
        
        return conversation.to_dict()
    
    def get_conversation_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get analytics for user's conversations"""
        conversations = self.get_user_conversations(user_id, limit=1000)
        
        if not conversations:
            return {
                'total_conversations': 0,
                'total_messages': 0,
                'avg_messages_per_conversation': 0,
                'most_active_day': None,
                'conversation_frequency': {}
            }
        
        total_messages = sum(len(conv.messages) for conv in conversations)
        
        # Group by day for frequency analysis
        day_counts = defaultdict(int)
        for conv in conversations:
            day = conv.created_at.date().isoformat()
            day_counts[day] += 1
        
        most_active_day = max(day_counts.items(), key=lambda x: x[1]) if day_counts else None
        
        return {
            'total_conversations': len(conversations),
            'total_messages': total_messages,
            'avg_messages_per_conversation': total_messages / len(conversations) if conversations else 0,
            'most_active_day': most_active_day[0] if most_active_day else None,
            'conversation_frequency': dict(day_counts)
        }

# Global instance
conversation_service = ConversationService()
