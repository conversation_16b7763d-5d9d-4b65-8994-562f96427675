import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
import json
import os
import pickle
import uuid
from datetime import datetime
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    mean_absolute_error, mean_squared_error, r2_score
)

# Import models
from sklearn.linear_model import LogisticRegression, LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, GradientBoostingClassifier, GradientBoostingRegressor
from sklearn.svm import SVC, SVR
from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor

logger = logging.getLogger(__name__)

# Define model storage directory
MODEL_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "models")
os.makedirs(MODEL_DIR, exist_ok=True)

class PredictiveModelService:
    """
    Service for training and using predictive models
    """
    
    def __init__(self, df: Optional[pd.DataFrame] = None):
        """
        Initialize the predictive model service
        
        Args:
            df: Optional DataFrame to analyze
        """
        self.df = df
    
    def set_dataframe(self, df: pd.DataFrame) -> None:
        """
        Set the DataFrame to analyze
        
        Args:
            df: DataFrame to analyze
        """
        self.df = df
    
    def train_model(
        self,
        target_column: str,
        feature_columns: List[str],
        model_type: str = "auto",
        model_params: Optional[Dict[str, Any]] = None,
        test_size: float = 0.2,
        random_state: int = 42,
        user_id: Optional[str] = None,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Train a predictive model
        
        Args:
            target_column: Column containing the target variable
            feature_columns: List of columns to use as features
            model_type: Type of model to train ('auto', 'classification', 'regression')
            model_params: Optional parameters for the model
            test_size: Proportion of data to use for testing
            random_state: Random seed for reproducibility
            user_id: Optional user ID for model tracking
            model_name: Optional name for the model
            
        Returns:
            Dictionary with model metadata and performance metrics
        """
        if self.df is None:
            raise ValueError("DataFrame not set")
            
        if target_column not in self.df.columns:
            raise ValueError(f"Target column '{target_column}' not found in DataFrame")
            
        for col in feature_columns:
            if col not in self.df.columns:
                raise ValueError(f"Feature column '{col}' not found in DataFrame")
        
        # Prepare data
        X = self.df[feature_columns]
        y = self.df[target_column]
        
        # Determine problem type if auto
        if model_type == "auto":
            model_type = self._determine_problem_type(y)
            logger.info(f"Auto-detected problem type: {model_type}")
        
        # Split data into train and test sets
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
        
        # Create preprocessing pipeline
        preprocessor = self._create_preprocessor(X)
        
        # Select and train model
        if model_type == "classification":
            model, metrics = self._train_classification_model(
                X_train, X_test, y_train, y_test, preprocessor, model_params
            )
        elif model_type == "regression":
            model, metrics = self._train_regression_model(
                X_train, X_test, y_train, y_test, preprocessor, model_params
            )
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
        
        # Generate model ID and name
        model_id = str(uuid.uuid4())
        if model_name is None:
            model_name = f"{model_type.capitalize()} Model - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        # Save model
        model_path = os.path.join(MODEL_DIR, f"{model_id}.pkl")
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        # Prepare feature importance if available
        feature_importance = self._get_feature_importance(model, feature_columns)
        
        # Prepare model metadata
        model_metadata = {
            "id": model_id,
            "name": model_name,
            "user_id": user_id,
            "model_type": model_type,
            "model_class": model.__class__.__name__,
            "target_column": target_column,
            "feature_columns": feature_columns,
            "metrics": metrics,
            "feature_importance": feature_importance,
            "created_at": datetime.now().isoformat(),
            "model_path": model_path
        }
        
        # Save metadata
        metadata_path = os.path.join(MODEL_DIR, f"{model_id}_metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(model_metadata, f, indent=2)
        
        return model_metadata
    
    def predict(
        self,
        model_id: str,
        input_data: Union[pd.DataFrame, Dict[str, Any], List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """
        Generate predictions using a trained model
        
        Args:
            model_id: ID of the model to use
            input_data: Input data for prediction (DataFrame or dict)
            
        Returns:
            Dictionary with prediction results
        """
        # Load model metadata
        metadata_path = os.path.join(MODEL_DIR, f"{model_id}_metadata.json")
        if not os.path.exists(metadata_path):
            raise ValueError(f"Model metadata not found for ID: {model_id}")
            
        with open(metadata_path, 'r') as f:
            model_metadata = json.load(f)
        
        # Load model
        model_path = model_metadata["model_path"]
        if not os.path.exists(model_path):
            raise ValueError(f"Model file not found at: {model_path}")
            
        with open(model_path, 'rb') as f:
            model = pickle.load(f)
        
        # Prepare input data
        if isinstance(input_data, dict):
            # Single prediction
            input_df = pd.DataFrame([input_data])
        elif isinstance(input_data, list):
            # Multiple predictions
            input_df = pd.DataFrame(input_data)
        else:
            # Already a DataFrame
            input_df = input_data
        
        # Check if all required features are present
        required_features = model_metadata["feature_columns"]
        missing_features = [feat for feat in required_features if feat not in input_df.columns]
        if missing_features:
            raise ValueError(f"Missing required features: {missing_features}")
        
        # Generate predictions
        X = input_df[required_features]
        predictions = model.predict(X)
        
        # For classification, also get probabilities if available
        probabilities = None
        if model_metadata["model_type"] == "classification" and hasattr(model, "predict_proba"):
            try:
                probabilities = model.predict_proba(X)
            except:
                logger.warning("Could not generate prediction probabilities")
        
        # Prepare response
        result = {
            "model_id": model_id,
            "model_name": model_metadata["name"],
            "model_type": model_metadata["model_type"],
            "predictions": predictions.tolist()
        }
        
        # Add probabilities for classification
        if probabilities is not None:
            # Get class labels
            if hasattr(model, "classes_"):
                class_labels = model.classes_.tolist()
            else:
                class_labels = [f"class_{i}" for i in range(probabilities.shape[1])]
                
            # Format probabilities
            prob_list = []
            for i in range(len(predictions)):
                prob_dict = {class_labels[j]: float(probabilities[i, j]) for j in range(len(class_labels))}
                prob_list.append(prob_dict)
                
            result["probabilities"] = prob_list
        
        return result
    
    def get_model_metadata(self, model_id: str) -> Dict[str, Any]:
        """
        Get metadata for a trained model
        
        Args:
            model_id: ID of the model
            
        Returns:
            Dictionary with model metadata
        """
        metadata_path = os.path.join(MODEL_DIR, f"{model_id}_metadata.json")
        if not os.path.exists(metadata_path):
            raise ValueError(f"Model metadata not found for ID: {model_id}")
            
        with open(metadata_path, 'r') as f:
            model_metadata = json.load(f)
            
        return model_metadata
    
    def list_models(self, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all trained models
        
        Args:
            user_id: Optional user ID to filter models
            
        Returns:
            List of model metadata
        """
        models = []
        
        # List all metadata files
        for filename in os.listdir(MODEL_DIR):
            if filename.endswith("_metadata.json"):
                metadata_path = os.path.join(MODEL_DIR, filename)
                
                with open(metadata_path, 'r') as f:
                    model_metadata = json.load(f)
                
                # Filter by user_id if provided
                if user_id is None or model_metadata.get("user_id") == user_id:
                    models.append(model_metadata)
        
        # Sort by creation date (newest first)
        models.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        
        return models
    
    def delete_model(self, model_id: str) -> bool:
        """
        Delete a trained model
        
        Args:
            model_id: ID of the model to delete
            
        Returns:
            True if successful, False otherwise
        """
        metadata_path = os.path.join(MODEL_DIR, f"{model_id}_metadata.json")
        if not os.path.exists(metadata_path):
            raise ValueError(f"Model metadata not found for ID: {model_id}")
            
        # Load metadata to get model path
        with open(metadata_path, 'r') as f:
            model_metadata = json.load(f)
            
        model_path = model_metadata.get("model_path")
        
        # Delete files
        success = True
        
        try:
            if model_path and os.path.exists(model_path):
                os.remove(model_path)
                
            os.remove(metadata_path)
        except Exception as e:
            logger.error(f"Error deleting model: {str(e)}")
            success = False
            
        return success
    
    def _determine_problem_type(self, y: pd.Series) -> str:
        """
        Determine whether the problem is classification or regression
        
        Args:
            y: Target variable
            
        Returns:
            Problem type ('classification' or 'regression')
        """
        # Check if target is numeric
        if pd.api.types.is_numeric_dtype(y):
            # Check number of unique values
            unique_values = y.nunique()
            
            # If few unique values, likely classification
            if unique_values < 10 and unique_values / len(y) < 0.05:
                return "classification"
            else:
                return "regression"
        else:
            # Non-numeric targets are classification
            return "classification"
    
    def _create_preprocessor(self, X: pd.DataFrame) -> ColumnTransformer:
        """
        Create a preprocessing pipeline for the features
        
        Args:
            X: Feature DataFrame
            
        Returns:
            ColumnTransformer for preprocessing
        """
        # Identify numeric and categorical columns
        numeric_features = X.select_dtypes(include=['int64', 'float64']).columns
        categorical_features = X.select_dtypes(include=['object', 'category']).columns
        
        # Create preprocessing pipelines
        numeric_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='median')),
            ('scaler', StandardScaler())
        ])
        
        categorical_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='most_frequent')),
            ('onehot', OneHotEncoder(handle_unknown='ignore'))
        ])
        
        # Combine preprocessing steps
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', numeric_transformer, numeric_features),
                ('cat', categorical_transformer, categorical_features)
            ]
        )
        
        return preprocessor
    
    def _train_classification_model(
        self,
        X_train: pd.DataFrame,
        X_test: pd.DataFrame,
        y_train: pd.Series,
        y_test: pd.Series,
        preprocessor: ColumnTransformer,
        model_params: Optional[Dict[str, Any]] = None
    ) -> Tuple[Pipeline, Dict[str, float]]:
        """
        Train a classification model
        
        Args:
            X_train: Training features
            X_test: Testing features
            y_train: Training target
            y_test: Testing target
            preprocessor: Feature preprocessor
            model_params: Optional model parameters
            
        Returns:
            Tuple of (trained model pipeline, performance metrics)
        """
        # Define models to try
        models = {
            'logistic_regression': LogisticRegression(max_iter=1000),
            'random_forest': RandomForestClassifier(),
            'gradient_boosting': GradientBoostingClassifier(),
            'svm': SVC(probability=True),
            'knn': KNeighborsClassifier(),
            'decision_tree': DecisionTreeClassifier()
        }
        
        # If model_params is provided, use it to select a specific model
        if model_params and 'model_name' in model_params:
            model_name = model_params['model_name']
            if model_name in models:
                selected_models = {model_name: models[model_name]}
            else:
                raise ValueError(f"Unsupported model name: {model_name}")
        else:
            # Try all models
            selected_models = models
        
        # Train and evaluate models
        best_model = None
        best_score = -1
        best_metrics = {}
        
        for name, model in selected_models.items():
            logger.info(f"Training {name} model")
            
            # Apply any model-specific parameters
            if model_params and name in model_params:
                model.set_params(**model_params[name])
            
            # Create pipeline with preprocessor and model
            pipeline = Pipeline(steps=[
                ('preprocessor', preprocessor),
                ('model', model)
            ])
            
            # Train model
            pipeline.fit(X_train, y_train)
            
            # Evaluate model
            y_pred = pipeline.predict(X_test)
            
            # Calculate metrics
            metrics = {
                'accuracy': accuracy_score(y_test, y_pred),
                'precision': precision_score(y_test, y_pred, average='weighted'),
                'recall': recall_score(y_test, y_pred, average='weighted'),
                'f1': f1_score(y_test, y_pred, average='weighted')
            }
            
            logger.info(f"{name} metrics: {metrics}")
            
            # Check if this is the best model
            if metrics['f1'] > best_score:
                best_model = pipeline
                best_score = metrics['f1']
                best_metrics = metrics
        
        return best_model, best_metrics
    
    def _train_regression_model(
        self,
        X_train: pd.DataFrame,
        X_test: pd.DataFrame,
        y_train: pd.Series,
        y_test: pd.Series,
        preprocessor: ColumnTransformer,
        model_params: Optional[Dict[str, Any]] = None
    ) -> Tuple[Pipeline, Dict[str, float]]:
        """
        Train a regression model
        
        Args:
            X_train: Training features
            X_test: Testing features
            y_train: Training target
            y_test: Testing target
            preprocessor: Feature preprocessor
            model_params: Optional model parameters
            
        Returns:
            Tuple of (trained model pipeline, performance metrics)
        """
        # Define models to try
        models = {
            'linear_regression': LinearRegression(),
            'ridge': Ridge(),
            'lasso': Lasso(),
            'random_forest': RandomForestRegressor(),
            'gradient_boosting': GradientBoostingRegressor(),
            'svr': SVR(),
            'knn': KNeighborsRegressor(),
            'decision_tree': DecisionTreeRegressor()
        }
        
        # If model_params is provided, use it to select a specific model
        if model_params and 'model_name' in model_params:
            model_name = model_params['model_name']
            if model_name in models:
                selected_models = {model_name: models[model_name]}
            else:
                raise ValueError(f"Unsupported model name: {model_name}")
        else:
            # Try all models
            selected_models = models
        
        # Train and evaluate models
        best_model = None
        best_score = float('inf')
        best_metrics = {}
        
        for name, model in selected_models.items():
            logger.info(f"Training {name} model")
            
            # Apply any model-specific parameters
            if model_params and name in model_params:
                model.set_params(**model_params[name])
            
            # Create pipeline with preprocessor and model
            pipeline = Pipeline(steps=[
                ('preprocessor', preprocessor),
                ('model', model)
            ])
            
            # Train model
            pipeline.fit(X_train, y_train)
            
            # Evaluate model
            y_pred = pipeline.predict(X_test)
            
            # Calculate metrics
            metrics = {
                'mae': mean_absolute_error(y_test, y_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred)),
                'r2': r2_score(y_test, y_pred)
            }
            
            logger.info(f"{name} metrics: {metrics}")
            
            # Check if this is the best model (lower RMSE is better)
            if metrics['rmse'] < best_score:
                best_model = pipeline
                best_score = metrics['rmse']
                best_metrics = metrics
        
        return best_model, best_metrics
    
    def _get_feature_importance(
        self,
        model: Pipeline,
        feature_columns: List[str]
    ) -> Optional[Dict[str, float]]:
        """
        Extract feature importance from the model if available
        
        Args:
            model: Trained model pipeline
            feature_columns: Original feature column names
            
        Returns:
            Dictionary mapping feature names to importance scores, or None if not available
        """
        # Get the actual model from the pipeline
        if not hasattr(model, 'named_steps') or 'model' not in model.named_steps:
            return None
            
        model_step = model.named_steps['model']
        
        # Check if model has feature importance
        if hasattr(model_step, 'feature_importances_'):
            importances = model_step.feature_importances_
        elif hasattr(model_step, 'coef_'):
            importances = np.abs(model_step.coef_)
            if importances.ndim > 1:
                importances = np.mean(importances, axis=0)
        else:
            return None
        
        # Get feature names after preprocessing
        preprocessor = model.named_steps['preprocessor']
        
        try:
            # For newer scikit-learn versions
            if hasattr(preprocessor, 'get_feature_names_out'):
                feature_names = preprocessor.get_feature_names_out()
            # For older scikit-learn versions
            elif hasattr(preprocessor, 'get_feature_names'):
                feature_names = preprocessor.get_feature_names()
            else:
                # If we can't get transformed feature names, use original ones
                # This won't be accurate but better than nothing
                feature_names = feature_columns
                if len(importances) != len(feature_names):
                    logger.warning("Feature importance dimensions don't match feature names")
                    return None
        except:
            logger.warning("Could not get feature names from preprocessor")
            return None
        
        # Create feature importance dictionary
        importance_dict = {}
        for i, importance in enumerate(importances):
            if i < len(feature_names):
                importance_dict[str(feature_names[i])] = float(importance)
        
        # Normalize to sum to 1
        total = sum(importance_dict.values())
        if total > 0:
            importance_dict = {k: v / total for k, v in importance_dict.items()}
        
        return importance_dict
