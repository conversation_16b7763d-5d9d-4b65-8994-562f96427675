'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { createApiInstance, getUserId } from '@/lib/api';

interface ModelTrainerProps {
  datasetId: number;
  onModelTrained?: (modelId: string) => void;
}

export default function ModelTrainer({ datasetId, onModelTrained }: ModelTrainerProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [columns, setColumns] = useState<string[]>([]);
  const [targetColumn, setTargetColumn] = useState<string>('');
  const [featureColumns, setFeatureColumns] = useState<string[]>([]);
  const [modelType, setModelType] = useState<string>('auto');
  const [modelName, setModelName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [testSize, setTestSize] = useState<number>(0.2);
  const [advancedOptions, setAdvancedOptions] = useState<boolean>(false);

  // Fetch dataset columns
  useEffect(() => {
    if (!datasetId || !session) return;

    const fetchDatasetColumns = async () => {
      try {
        const userId = getUserId(session);
        const api = createApiInstance(session);
        const response = await api.get(`/datasets/${userId}`);
        const datasets = response.data;
        const currentDataset = datasets.find((d: any) => d.id === datasetId);

        if (currentDataset) {
          setColumns(currentDataset.columns);

          // Set default model name
          if (!modelName) {
            setModelName(`Model for ${currentDataset.name}`);
          }
        }
      } catch (err: any) {
        console.error('Error fetching dataset columns:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching dataset columns');
      }
    };

    fetchDatasetColumns();
  }, [datasetId, session, modelName]);

  // Handle feature column selection
  const handleFeatureColumnChange = (column: string) => {
    setFeatureColumns(prev => {
      if (prev.includes(column)) {
        return prev.filter(c => c !== column);
      } else {
        return [...prev, column];
      }
    });
  };

  // Select all feature columns
  const selectAllFeatures = () => {
    const allExceptTarget = columns.filter(col => col !== targetColumn);
    setFeatureColumns(allExceptTarget);
  };

  // Deselect all feature columns
  const deselectAllFeatures = () => {
    setFeatureColumns([]);
  };

  // Train model
  const trainModel = async () => {
    if (!datasetId || !targetColumn || featureColumns.length === 0) {
      setError('Please select target column and at least one feature column');
      return;
    }

    if (!session) {
      setError('Please log in to train models');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const api = createApiInstance(session);
      const response = await api.post(`/predictive/train-model/${datasetId}`, {
        model_type: modelType,
        target_column: targetColumn,
        feature_columns: featureColumns,
        test_size: testSize,
        model_name: modelName || undefined,
        description: description || undefined
      });
      
      setSuccess(`Model training started! Job ID: ${response.data.job_id}`);

      // Call the onModelTrained callback if provided
      if (onModelTrained) {
        onModelTrained(response.data.job_id);
      }
      
      // Reset form
      setTargetColumn('');
      setFeatureColumns([]);
      setModelName('');
      setDescription('');
      setModelType('auto');
      setTestSize(0.2);
      setAdvancedOptions(false);
    } catch (err: any) {
      console.error('Error training model:', err);
      setError(err.response?.data?.detail || err.message || 'Error training model');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-black mb-4">Train Predictive Model</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}
      
      <div className="space-y-6">
        {/* Basic Settings */}
        <div>
          <h3 className="text-md font-medium text-black mb-2">Model Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Model Name
              </label>
              <input
                type="text"
                value={modelName}
                onChange={(e) => setModelName(e.target.value)}
                placeholder="Enter model name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Model Type
              </label>
              <select
                value={modelType}
                onChange={(e) => setModelType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="auto">Auto-detect</option>
                <option value="classification">Classification</option>
                <option value="regression">Regression</option>
              </select>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter model description"
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
        
        {/* Column Selection */}
        <div>
          <h3 className="text-md font-medium text-black mb-2">Column Selection</h3>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Target Column (What to Predict)
            </label>
            <select
              value={targetColumn}
              onChange={(e) => {
                setTargetColumn(e.target.value);
                // Remove target from feature columns if it's selected
                if (featureColumns.includes(e.target.value)) {
                  setFeatureColumns(prev => prev.filter(col => col !== e.target.value));
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">Select a column</option>
              {columns.map(column => (
                <option key={column} value={column}>{column}</option>
              ))}
            </select>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-700">
                Feature Columns
              </label>
              <div className="space-x-2">
                <button
                  type="button"
                  onClick={selectAllFeatures}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Select All
                </button>
                <button
                  type="button"
                  onClick={deselectAllFeatures}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Deselect All
                </button>
              </div>
            </div>
            
            <div className="max-h-48 overflow-y-auto border border-gray-300 rounded-md p-2">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {columns
                  .filter(col => col !== targetColumn)
                  .map(column => (
                    <div key={column} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`feature-${column}`}
                        checked={featureColumns.includes(column)}
                        onChange={() => handleFeatureColumnChange(column)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`feature-${column}`} className="ml-2 block text-sm text-gray-900 truncate">
                        {column}
                      </label>
                    </div>
                  ))}
              </div>
            </div>
            
            <p className="mt-1 text-xs text-gray-500">
              Selected {featureColumns.length} of {columns.length - (targetColumn ? 1 : 0)} available features
            </p>
          </div>
        </div>
        
        {/* Advanced Options */}
        <div>
          <button
            type="button"
            onClick={() => setAdvancedOptions(!advancedOptions)}
            className="flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            <svg
              className={`w-4 h-4 mr-1 transform ${advancedOptions ? 'rotate-90' : ''} transition-transform`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            Advanced Options
          </button>
          
          {advancedOptions && (
            <div className="mt-2 p-4 bg-gray-50 rounded-md">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Test Size (0-1)
                  </label>
                  <input
                    type="number"
                    value={testSize}
                    onChange={(e) => setTestSize(parseFloat(e.target.value))}
                    min={0.1}
                    max={0.5}
                    step={0.05}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Proportion of data used for testing (e.g., 0.2 = 20%)
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Submit Button */}
        <div className="flex justify-center">
          <button
            onClick={trainModel}
            disabled={loading || !targetColumn || featureColumns.length === 0}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              loading || !targetColumn || featureColumns.length === 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? 'Training...' : 'Train Model'}
          </button>
        </div>
      </div>
    </div>
  );
}
