-- AIthentiq Database Migration Script
-- This script sets up all necessary tables for the predictive analytics system
-- Run this script on your PostgreSQL database to enable all features

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create predictive_models table
CREATE TABLE IF NOT EXISTS predictive_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    dataset_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    model_type VARCHAR(100) NOT NULL,
    target_column VARCHAR(255) NOT NULL,
    feature_columns TEXT[], -- Array of column names
    hyperparameters JSONB DEFAULT '{}',
    metrics JSONB DEFAULT '{}',
    model_data BYTEA, -- Serialized model
    status VARCHAR(50) DEFAULT 'training',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dataset_id) REFERENCES datasets(id) ON DELETE CASCADE
);

-- Create predictive_predictions table
CREATE TABLE IF NOT EXISTS predictive_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id UUID NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    input_data JSONB NOT NULL,
    prediction_result JSONB NOT NULL,
    confidence_score DECIMAL(5,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES predictive_models(id) ON DELETE CASCADE
);

-- Create time_series_forecasts table
CREATE TABLE IF NOT EXISTS time_series_forecasts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    dataset_id UUID NOT NULL,
    target_column VARCHAR(255) NOT NULL,
    date_column VARCHAR(255) NOT NULL,
    forecast_horizon INTEGER NOT NULL,
    method VARCHAR(100) NOT NULL,
    forecast_data JSONB NOT NULL,
    accuracy_metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dataset_id) REFERENCES datasets(id) ON DELETE CASCADE
);

-- Create anomaly_detections table
CREATE TABLE IF NOT EXISTS anomaly_detections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    dataset_id UUID NOT NULL,
    method VARCHAR(100) NOT NULL,
    parameters JSONB DEFAULT '{}',
    anomalies JSONB NOT NULL,
    summary_stats JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dataset_id) REFERENCES datasets(id) ON DELETE CASCADE
);

-- Create background_jobs table for async processing
CREATE TABLE IF NOT EXISTS background_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    job_type VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    parameters JSONB DEFAULT '{}',
    result JSONB,
    error_message TEXT,
    progress INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_predictive_models_user_id ON predictive_models(user_id);
CREATE INDEX IF NOT EXISTS idx_predictive_models_dataset_id ON predictive_models(dataset_id);
CREATE INDEX IF NOT EXISTS idx_predictive_models_status ON predictive_models(status);

CREATE INDEX IF NOT EXISTS idx_predictive_predictions_model_id ON predictive_predictions(model_id);
CREATE INDEX IF NOT EXISTS idx_predictive_predictions_user_id ON predictive_predictions(user_id);
CREATE INDEX IF NOT EXISTS idx_predictive_predictions_created_at ON predictive_predictions(created_at);

CREATE INDEX IF NOT EXISTS idx_time_series_forecasts_user_id ON time_series_forecasts(user_id);
CREATE INDEX IF NOT EXISTS idx_time_series_forecasts_dataset_id ON time_series_forecasts(dataset_id);
CREATE INDEX IF NOT EXISTS idx_time_series_forecasts_created_at ON time_series_forecasts(created_at);

CREATE INDEX IF NOT EXISTS idx_anomaly_detections_user_id ON anomaly_detections(user_id);
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_dataset_id ON anomaly_detections(dataset_id);
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_created_at ON anomaly_detections(created_at);

CREATE INDEX IF NOT EXISTS idx_background_jobs_user_id ON background_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_background_jobs_status ON background_jobs(status);
CREATE INDEX IF NOT EXISTS idx_background_jobs_job_type ON background_jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_background_jobs_created_at ON background_jobs(created_at);

-- Create triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_predictive_models_updated_at 
    BEFORE UPDATE ON predictive_models 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_background_jobs_updated_at 
    BEFORE UPDATE ON background_jobs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing (optional)
-- Uncomment the following lines if you want to add sample data

/*
-- Sample predictive model
INSERT INTO predictive_models (user_id, dataset_id, name, model_type, target_column, feature_columns, status)
VALUES (
    'sample_user_id',
    (SELECT id FROM datasets LIMIT 1),
    'Sample Classification Model',
    'classification',
    'target',
    ARRAY['feature1', 'feature2', 'feature3'],
    'trained'
) ON CONFLICT DO NOTHING;

-- Sample background job
INSERT INTO background_jobs (user_id, job_type, status, parameters)
VALUES (
    'sample_user_id',
    'model_training',
    'completed',
    '{"model_type": "classification", "target_column": "target"}'
) ON CONFLICT DO NOTHING;
*/

-- Verify tables were created successfully
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name IN (
        'predictive_models',
        'predictive_predictions', 
        'time_series_forecasts',
        'anomaly_detections',
        'background_jobs'
    )
ORDER BY table_name;

-- Display success message
SELECT 'AIthentiq Predictive Analytics Database Migration Completed Successfully!' as status;
