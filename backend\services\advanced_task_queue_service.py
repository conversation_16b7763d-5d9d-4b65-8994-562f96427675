"""
Advanced Enterprise Task Queue Service for AIthentiq
Features: Circuit Breakers, Retry Logic, Monitoring, Dead Letter Queues
"""

import os
import json
import logging
import asyncio
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta, timezone
from enum import Enum
from dataclasses import dataclass, asdict
import threading
from concurrent.futures import ThreadPoolExecutor
import redis
import hashlib
import uuid

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    DEAD_LETTER = "dead_letter"

class TaskPriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

@dataclass
class TaskMetrics:
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    avg_processing_time_ms: float
    success_rate: float
    queue_depth: int
    workers_active: int
    last_processed_at: Optional[datetime]

@dataclass
class RetryConfig:
    max_attempts: int = 3
    initial_delay_ms: int = 1000
    max_delay_ms: int = 60000
    exponential_base: float = 2.0
    jitter: bool = True

@dataclass
class CircuitBreakerConfig:
    failure_threshold: int = 5
    recovery_timeout_ms: int = 60000
    success_threshold: int = 3

class CircuitBreaker:
    """Circuit breaker for task queue reliability"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                else:
                    raise Exception("Circuit breaker is OPEN")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except Exception as e:
                self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset"""
        if not self.last_failure_time:
            return False
        
        time_since_failure = time.time() - self.last_failure_time
        return time_since_failure >= (self.config.recovery_timeout_ms / 1000)
    
    def _on_success(self):
        """Handle successful execution"""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
        else:
            self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed execution"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN

class AdvancedTaskQueue:
    """Enterprise-grade task queue with reliability features"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.circuit_breaker = CircuitBreaker(CircuitBreakerConfig())
        self.retry_config = RetryConfig()
        self.metrics = TaskMetrics(0, 0, 0, 0.0, 0.0, 0, 0, None)
        self.workers = ThreadPoolExecutor(max_workers=8)
        self.monitoring_enabled = True
        
        # Queue names
        self.main_queue = "aithentiq:tasks:main"
        self.priority_queue = "aithentiq:tasks:priority"
        self.dead_letter_queue = "aithentiq:tasks:dead_letter"
        self.processing_queue = "aithentiq:tasks:processing"
        
        # Start monitoring
        if self.monitoring_enabled:
            self._start_monitoring()
    
    def _init_redis(self) -> redis.Redis:
        """Initialize Redis connection with retry logic"""
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        
        for attempt in range(3):
            try:
                client = redis.from_url(redis_url, decode_responses=True)
                client.ping()
                logger.info("Redis connection established")
                return client
            except Exception as e:
                logger.warning(f"Redis connection attempt {attempt + 1} failed: {e}")
                if attempt == 2:
                    raise
                time.sleep(2 ** attempt)
    
    async def enqueue_task(
        self,
        task_type: str,
        task_data: Dict[str, Any],
        tenant_id: str,
        user_id: str,
        priority: TaskPriority = TaskPriority.NORMAL,
        delay_seconds: int = 0,
        retry_config: RetryConfig = None
    ) -> str:
        """Enqueue task with advanced features"""
        
        task_id = str(uuid.uuid4())
        
        task_payload = {
            "task_id": task_id,
            "task_type": task_type,
            "task_data": task_data,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "priority": priority.value,
            "status": TaskStatus.PENDING.value,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "retry_config": asdict(retry_config or self.retry_config),
            "attempt_count": 0,
            "max_attempts": (retry_config or self.retry_config).max_attempts
        }
        
        try:
            # Use circuit breaker for Redis operations
            self.circuit_breaker.call(self._enqueue_to_redis, task_payload, priority, delay_seconds)
            
            # Update metrics
            self.metrics.total_tasks += 1
            self.metrics.queue_depth += 1
            
            logger.info(f"Task {task_id} enqueued successfully")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to enqueue task {task_id}: {e}")
            raise
    
    def _enqueue_to_redis(self, task_payload: Dict[str, Any], priority: TaskPriority, delay_seconds: int):
        """Enqueue task to Redis with priority handling"""
        
        task_json = json.dumps(task_payload)
        
        if delay_seconds > 0:
            # Schedule for later execution
            execute_at = time.time() + delay_seconds
            self.redis_client.zadd("aithentiq:tasks:scheduled", {task_json: execute_at})
        else:
            # Add to appropriate queue based on priority
            if priority == TaskPriority.CRITICAL:
                self.redis_client.lpush(self.priority_queue, task_json)
            else:
                self.redis_client.rpush(self.main_queue, task_json)
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get comprehensive task status"""
        try:
            # Check processing queue
            status_key = f"aithentiq:task_status:{task_id}"
            status_data = self.redis_client.get(status_key)
            
            if status_data:
                return json.loads(status_data)
            
            # Check if task is in queues
            for queue_name in [self.main_queue, self.priority_queue, self.dead_letter_queue]:
                queue_length = self.redis_client.llen(queue_name)
                # In production, you'd implement more efficient queue searching
                
            return {
                "task_id": task_id,
                "status": TaskStatus.PENDING.value,
                "message": "Task not found in system"
            }
            
        except Exception as e:
            logger.error(f"Failed to get task status for {task_id}: {e}")
            return {
                "task_id": task_id,
                "status": "error",
                "error": str(e)
            }
    
    def _start_monitoring(self):
        """Start background monitoring and maintenance"""
        def monitor():
            while True:
                try:
                    self._update_metrics()
                    self._process_scheduled_tasks()
                    self._cleanup_old_tasks()
                    time.sleep(30)  # Monitor every 30 seconds
                except Exception as e:
                    logger.error(f"Monitoring error: {e}")
                    time.sleep(60)
        
        monitoring_thread = threading.Thread(target=monitor, daemon=True)
        monitoring_thread.start()
    
    def _update_metrics(self):
        """Update queue metrics"""
        try:
            # Get queue depths
            main_depth = self.redis_client.llen(self.main_queue)
            priority_depth = self.redis_client.llen(self.priority_queue)
            dead_letter_depth = self.redis_client.llen(self.dead_letter_queue)
            
            self.metrics.queue_depth = main_depth + priority_depth
            
            # Calculate success rate
            if self.metrics.total_tasks > 0:
                self.metrics.success_rate = self.metrics.completed_tasks / self.metrics.total_tasks
            
            logger.debug(f"Queue metrics updated: {asdict(self.metrics)}")
            
        except Exception as e:
            logger.error(f"Failed to update metrics: {e}")
    
    def _process_scheduled_tasks(self):
        """Move scheduled tasks to main queue when ready"""
        try:
            current_time = time.time()
            
            # Get tasks ready for execution
            ready_tasks = self.redis_client.zrangebyscore(
                "aithentiq:tasks:scheduled", 
                0, 
                current_time,
                withscores=True
            )
            
            for task_json, score in ready_tasks:
                # Move to main queue
                self.redis_client.rpush(self.main_queue, task_json)
                self.redis_client.zrem("aithentiq:tasks:scheduled", task_json)
                
        except Exception as e:
            logger.error(f"Failed to process scheduled tasks: {e}")
    
    def _cleanup_old_tasks(self):
        """Clean up old completed/failed tasks"""
        try:
            # Remove task status entries older than 7 days
            cutoff_time = datetime.now(timezone.utc) - timedelta(days=7)
            cutoff_timestamp = cutoff_time.timestamp()
            
            # This is a simplified cleanup - in production you'd use more sophisticated methods
            logger.debug("Cleanup completed")
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
    
    def get_metrics(self) -> TaskMetrics:
        """Get current queue metrics"""
        self._update_metrics()
        return self.metrics
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        try:
            redis_healthy = self.redis_client.ping()
            circuit_state = self.circuit_breaker.state.value
            
            return {
                "healthy": redis_healthy and circuit_state != CircuitState.OPEN.value,
                "redis_connected": redis_healthy,
                "circuit_breaker_state": circuit_state,
                "metrics": asdict(self.metrics),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

# Global advanced task queue instance
advanced_task_queue = AdvancedTaskQueue()
