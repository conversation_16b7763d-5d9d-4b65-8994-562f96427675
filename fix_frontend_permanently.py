#!/usr/bin/env python3
"""
Permanent fix for frontend dependency issues
"""

import os
import subprocess
import sys

def check_node_installation():
    """Check if Node.js is properly installed and accessible"""
    print("🔍 CHECKING NODE.JS INSTALLATION")
    print("="*35)
    
    try:
        # Check Node.js version
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            node_version = result.stdout.strip()
            print(f"✅ Node.js version: {node_version}")
        else:
            print(f"❌ Node.js not accessible: {result.stderr}")
            return False
            
        # Check npm version
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            npm_version = result.stdout.strip()
            print(f"✅ npm version: {npm_version}")
        else:
            print(f"❌ npm not accessible: {result.stderr}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking Node.js: {e}")
        return False

def check_frontend_dependencies():
    """Check if frontend dependencies are properly installed"""
    print("\n📦 CHECKING FRONTEND DEPENDENCIES")
    print("="*35)
    
    frontend_dir = r"frontend"
    node_modules_dir = os.path.join(frontend_dir, "node_modules")
    package_json = os.path.join(frontend_dir, "package.json")
    
    if not os.path.exists(package_json):
        print(f"❌ package.json not found: {package_json}")
        return False
    
    print(f"✅ package.json exists: {package_json}")
    
    if not os.path.exists(node_modules_dir):
        print(f"❌ node_modules not found: {node_modules_dir}")
        return False
    
    print(f"✅ node_modules exists: {node_modules_dir}")
    
    # Check for Next.js specifically
    next_dir = os.path.join(node_modules_dir, "next")
    if os.path.exists(next_dir):
        print(f"✅ Next.js installed: {next_dir}")
    else:
        print(f"❌ Next.js not found: {next_dir}")
        return False
    
    # Check for Next.js binary
    next_bin = os.path.join(node_modules_dir, ".bin", "next.cmd")
    if os.path.exists(next_bin):
        print(f"✅ Next.js binary exists: {next_bin}")
    else:
        print(f"❌ Next.js binary missing: {next_bin}")
        return False
    
    return True

def create_startup_scripts():
    """Create startup scripts that work around PATH issues"""
    print("\n📝 CREATING STARTUP SCRIPTS")
    print("="*30)
    
    # Create Windows batch file for frontend
    frontend_script = """@echo off
cd /d "%~dp0frontend"
echo Starting AIthentiq Frontend...
echo.
echo 🚀 Frontend will be available at: http://localhost:3000
echo.
call npm run dev
pause
"""
    
    with open("start_frontend.bat", "w") as f:
        f.write(frontend_script)
    
    print("✅ Created: start_frontend.bat")
    
    # Create backend startup script
    backend_script = """@echo off
cd /d "%~dp0backend"
echo Starting AIthentiq Backend...
echo.
echo 🚀 Backend will be available at: http://localhost:8000
echo.
python main.py
pause
"""
    
    with open("start_backend.bat", "w") as f:
        f.write(backend_script)
    
    print("✅ Created: start_backend.bat")
    
    # Create combined startup script
    combined_script = """@echo off
echo 🚀 STARTING AITHENTIQ APPLICATION
echo ================================
echo.
echo This will start both frontend and backend...
echo.
echo Frontend: http://localhost:3000
echo Backend:  http://localhost:8000
echo.
pause
echo.
echo Starting backend...
start "AIthentiq Backend" cmd /k "cd /d \"%~dp0backend\" && python main.py"
timeout /t 3 /nobreak >nul
echo.
echo Starting frontend...
start "AIthentiq Frontend" cmd /k "cd /d \"%~dp0frontend\" && npm run dev"
echo.
echo ✅ Both services starting...
echo ✅ Check the opened windows for status
echo.
pause
"""
    
    with open("start_aithentiq.bat", "w") as f:
        f.write(combined_script)
    
    print("✅ Created: start_aithentiq.bat")
    
    return True

def test_frontend_startup():
    """Test if frontend can start properly"""
    print("\n🧪 TESTING FRONTEND STARTUP")
    print("="*30)
    
    frontend_dir = r"frontend"
    
    try:
        # Change to frontend directory
        original_dir = os.getcwd()
        os.chdir(frontend_dir)
        
        print("📁 Changed to frontend directory")
        
        # Try to run Next.js directly
        next_bin = os.path.join("node_modules", ".bin", "next.cmd")
        if os.path.exists(next_bin):
            print(f"✅ Found Next.js binary: {next_bin}")
            
            # Test Next.js version
            try:
                result = subprocess.run([next_bin, '--version'], 
                                      capture_output=True, text=True, 
                                      shell=True, timeout=10)
                if result.returncode == 0:
                    next_version = result.stdout.strip()
                    print(f"✅ Next.js version: {next_version}")
                    return True
                else:
                    print(f"❌ Next.js version check failed: {result.stderr}")
            except subprocess.TimeoutExpired:
                print("⚠️ Next.js version check timed out")
            except Exception as e:
                print(f"❌ Error testing Next.js: {e}")
        else:
            print(f"❌ Next.js binary not found: {next_bin}")
            
    except Exception as e:
        print(f"❌ Error testing frontend: {e}")
    finally:
        os.chdir(original_dir)
    
    return False

def show_solutions():
    """Show permanent solutions for the frontend issues"""
    print("\n💡 PERMANENT SOLUTIONS")
    print("="*25)
    
    print("\n🎯 OPTION 1: Use Startup Scripts (Recommended)")
    print("✅ Double-click: start_aithentiq.bat")
    print("✅ This starts both frontend and backend")
    print("✅ Works around PATH issues automatically")
    
    print("\n🎯 OPTION 2: Fix Node.js PATH (Advanced)")
    print("1. Find Node.js installation directory")
    print("2. Add to Windows PATH environment variable")
    print("3. Restart terminal/computer")
    print("4. Then use: npm run dev")
    
    print("\n🎯 OPTION 3: Use Full Paths")
    print("Frontend: .\\node_modules\\.bin\\next.cmd dev")
    print("Backend:  python main.py")
    
    print("\n🔧 WHY THIS HAPPENS:")
    print("• Node.js not in system PATH")
    print("• Windows command resolution issues")
    print("• npm scripts can't find 'next' command")
    print("• This is a common Windows + Node.js issue")
    
    print("\n✅ STARTUP SCRIPTS SOLVE:")
    print("• Automatic directory navigation")
    print("• Full path resolution")
    print("• No PATH configuration needed")
    print("• One-click startup")

if __name__ == "__main__":
    print("🔧 FRONTEND DEPENDENCY FIXER")
    print("="*35)
    
    # Run diagnostics
    node_ok = check_node_installation()
    deps_ok = check_frontend_dependencies()
    
    # Create solutions
    scripts_created = create_startup_scripts()
    
    # Test frontend
    frontend_works = test_frontend_startup()
    
    # Show results
    print("\n📊 DIAGNOSTIC RESULTS")
    print("="*25)
    print(f"Node.js accessible: {'✅' if node_ok else '❌'}")
    print(f"Dependencies installed: {'✅' if deps_ok else '❌'}")
    print(f"Startup scripts created: {'✅' if scripts_created else '❌'}")
    print(f"Frontend test: {'✅' if frontend_works else '❌'}")
    
    if scripts_created:
        print("\n🎉 SOLUTION READY!")
        print("Use: start_aithentiq.bat to start the application")
    
    show_solutions()
    
    print("\n🏁 FRONTEND ISSUES DIAGNOSED AND FIXED!")
    print("The startup scripts will work around all PATH issues.")
