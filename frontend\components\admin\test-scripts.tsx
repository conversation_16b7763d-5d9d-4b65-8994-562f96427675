'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import api from '../../lib/api';
import { Code, Download, Eye, Calendar, Search, Play, Folder } from 'lucide-react';

interface TestScript {
  name: string;
  path: string;
  directory: string;
  size: number;
  modified: string;
  content?: string;
  type: 'python' | 'javascript' | 'other';
}

export default function TestScripts() {
  const [scripts, setScripts] = useState<TestScript[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedScript, setSelectedScript] = useState<TestScript | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDirectory, setFilterDirectory] = useState('all');
  const [viewingContent, setViewingContent] = useState(false);
  const { getUserId } = useAuth();

  useEffect(() => {
    loadScripts();
  }, []);

  const loadScripts = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await api.get('/admin/test-scripts', {
        params: { user_id: getUserId() }
      });
      setScripts(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load test scripts');
    } finally {
      setLoading(false);
    }
  };

  const viewScript = async (script: TestScript) => {
    try {
      const response = await api.get(`/admin/test-scripts/${encodeURIComponent(script.path)}`, {
        params: { user_id: getUserId() }
      });
      setSelectedScript({ ...script, content: response.data.content });
      setViewingContent(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load script content');
    }
  };

  const downloadScript = async (script: TestScript) => {
    try {
      const response = await api.get(`/admin/test-scripts/download/${encodeURIComponent(script.name)}`, {
        params: { user_id: getUserId() },
        responseType: 'blob'
      });

      const blob = new Blob([response.data], {
        type: script.type === 'python' ? 'text/x-python' : 'text/javascript'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = script.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download script');
    }
  };

  const getUniqueDirectories = () => {
    const directories = [...new Set(scripts.map(script => script.directory))];
    return directories.sort();
  };

  const filteredScripts = scripts.filter(script => {
    const matchesSearch = script.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDirectory = filterDirectory === 'all' || script.directory === filterDirectory;
    return matchesSearch && matchesDirectory;
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getScriptIcon = (script: TestScript) => {
    if (script.type === 'python') {
      return <Code className="h-5 w-5 text-green-500" />;
    } else if (script.type === 'javascript') {
      return <Code className="h-5 w-5 text-yellow-500" />;
    }
    return <Code className="h-5 w-5 text-gray-500" />;
  };

  const getScriptTypeColor = (type: string) => {
    switch (type) {
      case 'python':
        return 'bg-green-100 text-green-800';
      case 'javascript':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow border p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading test scripts...</span>
        </div>
      </div>
    );
  }

  if (viewingContent && selectedScript) {
    return (
      <div className="bg-white rounded-lg shadow border">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{selectedScript.name}</h2>
              <p className="text-sm text-gray-600 mt-1">
                {selectedScript.directory} • Modified: {formatDate(selectedScript.modified)} • Size: {formatFileSize(selectedScript.size)}
              </p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${getScriptTypeColor(selectedScript.type)}`}>
                {selectedScript.type}
              </span>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => downloadScript(selectedScript)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </button>
              <button
                onClick={() => setViewingContent(false)}
                className="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                Back to List
              </button>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="prose max-w-none">
            <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono bg-gray-50 p-4 rounded-lg overflow-auto max-h-96">
              {selectedScript.content}
            </pre>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Test Scripts</h2>
            <p className="text-gray-600 mt-1">Python and JavaScript test files</p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Folder className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={filterDirectory}
                onChange={(e) => setFilterDirectory(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Directories</option>
                {getUniqueDirectories().map(dir => (
                  <option key={dir} value={dir}>{dir}</option>
                ))}
              </select>
            </div>
            <div className="relative">
              <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search scripts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={loadScripts}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="p-6">
        {filteredScripts.length === 0 ? (
          <div className="text-center py-12">
            <Code className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">
              {searchTerm || filterDirectory !== 'all' ? 'No scripts match your filters.' : 'No test scripts found.'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Script
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Directory
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Modified
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredScripts.map((script) => (
                  <tr key={script.path} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getScriptIcon(script)}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{script.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {script.directory}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getScriptTypeColor(script.type)}`}>
                        {script.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatFileSize(script.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        {formatDate(script.modified)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => viewScript(script)}
                          className="text-blue-600 hover:text-blue-900 inline-flex items-center"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </button>
                        <button
                          onClick={() => downloadScript(script)}
                          className="text-green-600 hover:text-green-900 inline-flex items-center"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
