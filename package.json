{"name": "aithentiq", "version": "1.0.0", "description": "AIthentIQ - AI + Authentic + IQ - Transform Data into Actionable Insights", "main": "index.js", "scripts": {"install:all": "npm install && cd frontend && npm install && cd ../backend && pip install -r requirements.txt", "build:frontend": "cd frontend && npm run build", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && /app/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000", "start:both": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:production": "npm run build:frontend && npm run start:both", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && /app/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --reload", "dev:both": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "test:frontend": "cd frontend && npm test", "clean": "rm -rf frontend/node_modules backend/__pycache__ frontend/.next", "setup": "npm run install:all"}, "repository": {"type": "git", "url": "https://github.com/yourusername/aithentiq.git"}, "keywords": ["ai", "data-analysis", "machine-learning", "analytics", "dashboard", "nextjs", "python", "<PERSON><PERSON><PERSON>", "openai", "charts", "visualization"], "author": "AIthentIQ Team", "license": "MIT", "dependencies": {"concurrently": "^7.6.0", "framer-motion": "^12.18.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0", "python": ">=3.9.0"}, "homepage": "https://aithentiq.com", "bugs": {"url": "https://github.com/yourusername/aithentiq/issues"}}