# Quick Curl Commands to Test AIthentiq Backend Issues
# Run these from the AIthentiq project root directory

# 1. Test Backend Health
curl -X GET "http://localhost:8000/test"

# 2. Test Database Queries (should work)
curl -X GET "http://localhost:8000/debug/queries"

# 3. Create Admin User
curl -X POST "http://localhost:8000/make-admin/<EMAIL>"

# 4. Test User Management (THE FAILING ONE - should show database error)
curl -X GET "http://localhost:8000/admin/users?user_id=<EMAIL>"

# 5. Test Scripts Visibility (should show test files from Test folder)
curl -X GET "http://localhost:8000/admin/test-scripts?user_id=<EMAIL>"

# 6. Debug Users
curl -X GET "http://localhost:8000/debug/users"

# 7. Test Admin Access
curl -X GET "http://localhost:8000/admin/debug-test?user_id=<EMAIL>"

# 8. Check Research Documents
curl -X GET "http://localhost:8000/admin/research-documents?user_id=<EMAIL>"

# Alternative User Management Test (with different parameter format)
curl -X GET "http://localhost:8000/admin/users" -G -d "user_id=<EMAIL>"

# Test with JSON headers
curl -X GET "http://localhost:8000/admin/users?user_id=<EMAIL>" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"

# Manual Database Migration Test (run from AIthentiq root directory)
# cd backend && python migrations\add_monitoring_fields.py

# Verify Migration
# cd backend && python migrations\add_monitoring_fields.py verify

# PowerShell versions (for Windows):
# Invoke-RestMethod -Uri "http://localhost:8000/test" -Method GET
# Invoke-RestMethod -Uri "http://localhost:8000/admin/users" -Method GET -Body @{user_id="<EMAIL>"}
# Invoke-RestMethod -Uri "http://localhost:8000/admin/test-scripts" -Method GET -Body @{user_id="<EMAIL>"}
