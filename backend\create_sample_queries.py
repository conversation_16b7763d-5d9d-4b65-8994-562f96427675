#!/usr/bin/env python3
"""
Quick script to create sample queries for testing predictive analytics
"""

import sys
import os
sys.path.append('.')

from database import get_db
import models
import json
from datetime import datetime, timedelta, timezone
import random

def create_sample_queries(user_id=None):
    """Create sample queries for specified user or demo user"""
    db = next(get_db())

    if not user_id:
        user_id = "demo-user-12345"
    
    # Sample questions
    questions = [
        "What is the total revenue for this month?",
        "Show me the user growth trend",
        "What's the average conversion rate?",
        "Which day had the highest revenue?",
        "How many users signed up last week?",
        "What's the correlation between users and revenue?",
        "Show me the revenue by day",
        "What's the trend in conversion rates?",
        "What are the top performing products?",
        "How is our customer retention rate?",
        "What's the monthly recurring revenue?",
        "Show me the churn analysis",
        "What's the lifetime value of customers?",
        "How many active users do we have?",
        "What's the cost per acquisition?",
        "Show me the sales funnel conversion",
        "What's the average order value?",
        "How is our market share trending?",
        "What are the seasonal patterns?",
        "Show me the geographic distribution"
    ]
    
    queries_created = 0
    end_date = datetime.now(timezone.utc)
    
    print(f"Creating sample queries for user {user_id}...")
    
    for i in range(50):  # Create 50 queries
        # Generate query at random time in past 60 days
        query_time = end_date - timedelta(
            days=random.randint(0, 60),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )
        
        question = random.choice(questions)
        
        # Generate trust score with some variation
        base_score = random.uniform(0.4, 0.95)
        trust_score = {
            "overall_score": base_score,
            "model_confidence": random.uniform(0.3, 0.9),
            "source_quality": random.uniform(0.4, 0.95),
            "citation_accuracy": random.uniform(0.5, 0.9),
            "question_match": random.uniform(0.6, 0.95),
            "factors": ["data_quality", "model_confidence"],
            "explanation": "Generated trust score for sample data"
        }
        
        # Some queries have errors
        has_error = random.random() < 0.1  # 10% error rate
        
        if has_error:
            answer = "Error: Unable to process query due to data formatting issues"
            trust_score["overall_score"] = random.uniform(0.1, 0.3)
        else:
            answer = f"Sample answer for: {question}. Based on the analysis, here are the key insights..."
        
        try:
            query = models.Query(
                user_id=user_id,
                dataset_id=1,  # Use default dataset ID
                question=question,
                answer=answer,
                trust_score=json.dumps(trust_score),
                response_time_ms=random.uniform(500, 3000),
                created_at=query_time
            )
            db.add(query)
            queries_created += 1
            
            if i % 10 == 0:
                print(f"Created {i+1} queries...")
                
        except Exception as e:
            print(f"Error creating query {i}: {e}")
    
    try:
        db.commit()
        print(f"Successfully created {queries_created} sample queries!")
        return queries_created
    except Exception as e:
        print(f"Error committing queries: {e}")
        db.rollback()
        return 0

if __name__ == "__main__":
    import sys
    user_id = sys.argv[1] if len(sys.argv) > 1 else None
    created = create_sample_queries(user_id)
    print(f"Total queries created: {created} for user: {user_id or 'demo-user-12345'}")
