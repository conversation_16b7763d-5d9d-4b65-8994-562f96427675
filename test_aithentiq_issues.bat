@echo off
echo Running AIthentiq Backend Issue Testing Script...
echo.

REM Check if we're in the right directory
if not exist "backend\main.py" (
    echo Error: Please run this from the AIthentiq project root directory
    echo Expected file: backend\main.py
    pause
    exit /b 1
)

if not exist "Test" (
    echo Error: Test folder not found
    echo Expected folder: Test
    pause
    exit /b 1
)

echo Starting PowerShell script...
powershell.exe -ExecutionPolicy Bypass -File "test_backend_issues.ps1"

echo.
echo Script completed. Press any key to exit...
pause > nul
