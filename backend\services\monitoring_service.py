"""
Professional Monitoring and Observability Service
Provides comprehensive monitoring for RAG systems in production
"""

import json
import time
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from models import Query, SourceAttribution, DocumentChunk, Dataset, User


class MonitoringService:
    """
    Production-grade monitoring and observability for RAG systems
    Tracks performance, quality, and system health metrics
    """
    
    def __init__(self):
        self.performance_thresholds = {
            "response_time_ms": 5000,  # 5 seconds
            "relevance_score": 0.7,
            "confidence_score": 0.8,
            "source_attribution_rate": 0.95
        }
    
    def track_query_performance(self, query_id: int, response_time_ms: float,
                              token_count: int, db: Session) -> Dict[str, Any]:
        """
        Track performance metrics for a query
        """
        try:
            query = db.query(Query).filter(Query.id == query_id).first()
            if not query:
                return {"error": "Query not found"}
            
            # Update query with performance metrics
            performance_data = {
                "response_time_ms": response_time_ms,
                "token_count": token_count,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "performance_grade": self._calculate_performance_grade(response_time_ms)
            }
            
            # Store in query metadata (assuming we add a performance_metrics field)
            # For now, we'll track this separately
            
            return {
                "success": True,
                "performance_grade": performance_data["performance_grade"],
                "meets_sla": response_time_ms < self.performance_thresholds["response_time_ms"]
            }
            
        except Exception as e:
            return {"error": f"Failed to track performance: {str(e)}"}
    
    def get_system_health_metrics(self, dataset_id: Optional[int] = None,
                                hours: int = 24, db: Session = None) -> Dict[str, Any]:
        """
        Get comprehensive system health metrics
        """
        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=hours)
            
            # Base query filter
            query_filter = Query.created_at >= start_time
            if dataset_id:
                query_filter = and_(query_filter, Query.dataset_id == dataset_id)
            
            # Query volume metrics
            total_queries = db.query(func.count(Query.id)).filter(query_filter).scalar()
            
            # Error rate (queries without responses)
            error_queries = db.query(func.count(Query.id)).filter(
                and_(query_filter, Query.answer.is_(None))
            ).scalar()
            
            error_rate = (error_queries / total_queries * 100) if total_queries > 0 else 0
            
            # Source attribution metrics
            queries_with_sources = db.query(func.count(Query.id.distinct())).filter(
                and_(
                    query_filter,
                    Query.id.in_(
                        db.query(SourceAttribution.query_id).filter(
                            SourceAttribution.query_id.in_(
                                db.query(Query.id).filter(query_filter)
                            )
                        )
                    )
                )
            ).scalar()
            
            attribution_rate = (queries_with_sources / total_queries * 100) if total_queries > 0 else 0
            
            # Average confidence scores
            avg_confidence = db.query(func.avg(SourceAttribution.confidence_score)).filter(
                SourceAttribution.query_id.in_(
                    db.query(Query.id).filter(query_filter)
                )
            ).scalar() or 0
            
            # Top performing datasets
            dataset_performance = db.query(
                Query.dataset_id,
                func.count(Query.id).label('query_count'),
                func.avg(SourceAttribution.confidence_score).label('avg_confidence')
            ).join(
                SourceAttribution, Query.id == SourceAttribution.query_id, isouter=True
            ).filter(query_filter).group_by(Query.dataset_id).order_by(
                desc('query_count')
            ).limit(5).all()
            
            # System health status
            health_status = "healthy"
            if error_rate > 5:
                health_status = "degraded"
            elif error_rate > 10 or attribution_rate < 90:
                health_status = "unhealthy"
            
            return {
                "period": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "hours": hours
                },
                "system_health": {
                    "status": health_status,
                    "total_queries": total_queries,
                    "error_rate": round(error_rate, 2),
                    "source_attribution_rate": round(attribution_rate, 2),
                    "average_confidence": round(avg_confidence, 3)
                },
                "performance_metrics": {
                    "queries_per_hour": round(total_queries / hours, 2),
                    "successful_queries": total_queries - error_queries,
                    "queries_with_sources": queries_with_sources
                },
                "dataset_performance": [
                    {
                        "dataset_id": dp.dataset_id,
                        "query_count": dp.query_count,
                        "avg_confidence": round(dp.avg_confidence or 0, 3)
                    }
                    for dp in dataset_performance
                ],
                "alerts": self._generate_health_alerts(error_rate, attribution_rate, avg_confidence)
            }
            
        except Exception as e:
            return {"error": f"Failed to get health metrics: {str(e)}"}
    
    def get_quality_metrics(self, dataset_id: int, days: int = 7,
                          db: Session = None) -> Dict[str, Any]:
        """
        Get comprehensive quality metrics for a dataset
        """
        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)
            
            # Get queries for this dataset in time period
            queries = db.query(Query).filter(
                and_(
                    Query.dataset_id == dataset_id,
                    Query.created_at >= start_time
                )
            ).all()
            
            if not queries:
                return {"error": "No queries found for analysis"}
            
            query_ids = [q.id for q in queries]
            
            # Source quality metrics
            source_stats = db.query(
                func.count(SourceAttribution.id).label('total_sources'),
                func.avg(SourceAttribution.relevance_score).label('avg_relevance'),
                func.avg(SourceAttribution.confidence_score).label('avg_confidence'),
                func.count(SourceAttribution.id).filter(SourceAttribution.human_verified == True).label('verified_sources')
            ).filter(SourceAttribution.query_id.in_(query_ids)).first()
            
            # Response completeness (queries with multiple sources)
            queries_with_multiple_sources = db.query(
                func.count(func.distinct(SourceAttribution.query_id))
            ).filter(
                and_(
                    SourceAttribution.query_id.in_(query_ids),
                    SourceAttribution.query_id.in_(
                        db.query(SourceAttribution.query_id).filter(
                            SourceAttribution.query_id.in_(query_ids)
                        ).group_by(SourceAttribution.query_id).having(
                            func.count(SourceAttribution.id) > 1
                        )
                    )
                )
            ).scalar()
            
            # Search method effectiveness
            search_method_stats = db.query(
                SourceAttribution.search_method,
                func.count(SourceAttribution.id).label('count'),
                func.avg(SourceAttribution.relevance_score).label('avg_relevance')
            ).filter(
                SourceAttribution.query_id.in_(query_ids)
            ).group_by(SourceAttribution.search_method).all()
            
            # Calculate quality scores
            completeness_score = (queries_with_multiple_sources / len(queries) * 100) if queries else 0
            verification_rate = ((source_stats.verified_sources or 0) / (source_stats.total_sources or 1) * 100)
            
            return {
                "dataset_id": dataset_id,
                "analysis_period": {
                    "start_date": start_time.isoformat(),
                    "end_date": end_time.isoformat(),
                    "days": days
                },
                "query_metrics": {
                    "total_queries": len(queries),
                    "queries_with_sources": len([q for q in queries if any(sa.query_id == q.id for sa in db.query(SourceAttribution).filter(SourceAttribution.query_id.in_(query_ids)).all())]),
                    "queries_with_multiple_sources": queries_with_multiple_sources
                },
                "source_quality": {
                    "total_sources": source_stats.total_sources or 0,
                    "average_relevance": round(source_stats.avg_relevance or 0, 3),
                    "average_confidence": round(source_stats.avg_confidence or 0, 3),
                    "verification_rate": round(verification_rate, 2),
                    "verified_sources": source_stats.verified_sources or 0
                },
                "completeness_metrics": {
                    "completeness_score": round(completeness_score, 2),
                    "single_source_queries": len(queries) - queries_with_multiple_sources,
                    "multi_source_queries": queries_with_multiple_sources
                },
                "search_effectiveness": [
                    {
                        "method": sms.search_method,
                        "usage_count": sms.count,
                        "average_relevance": round(sms.avg_relevance or 0, 3)
                    }
                    for sms in search_method_stats
                ],
                "quality_grade": self._calculate_quality_grade(
                    source_stats.avg_relevance or 0,
                    source_stats.avg_confidence or 0,
                    completeness_score,
                    verification_rate
                )
            }
            
        except Exception as e:
            return {"error": f"Failed to get quality metrics: {str(e)}"}
    
    def get_user_analytics(self, user_id: str, days: int = 30,
                         db: Session = None) -> Dict[str, Any]:
        """
        Get user-specific analytics and usage patterns
        """
        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)
            
            # User query patterns
            user_queries = db.query(Query).filter(
                and_(
                    Query.user_id == user_id,
                    Query.created_at >= start_time
                )
            ).all()
            
            if not user_queries:
                return {"message": "No activity found for user"}
            
            # Dataset usage
            dataset_usage = db.query(
                Query.dataset_id,
                func.count(Query.id).label('query_count'),
                Dataset.name.label('dataset_name')
            ).join(Dataset).filter(
                and_(
                    Query.user_id == user_id,
                    Query.created_at >= start_time
                )
            ).group_by(Query.dataset_id, Dataset.name).order_by(
                desc('query_count')
            ).all()
            
            # Query complexity analysis
            query_lengths = [len(q.question.split()) for q in user_queries if q.question]
            avg_query_length = sum(query_lengths) / len(query_lengths) if query_lengths else 0
            
            # Time-based patterns
            hourly_distribution = {}
            for query in user_queries:
                if query.created_at:
                    hour = query.created_at.hour
                    hourly_distribution[hour] = hourly_distribution.get(hour, 0) + 1
            
            return {
                "user_id": user_id,
                "analysis_period": {
                    "start_date": start_time.isoformat(),
                    "end_date": end_time.isoformat(),
                    "days": days
                },
                "usage_summary": {
                    "total_queries": len(user_queries),
                    "queries_per_day": round(len(user_queries) / days, 2),
                    "average_query_length": round(avg_query_length, 1),
                    "datasets_used": len(dataset_usage)
                },
                "dataset_preferences": [
                    {
                        "dataset_id": du.dataset_id,
                        "dataset_name": du.dataset_name,
                        "query_count": du.query_count,
                        "percentage": round(du.query_count / len(user_queries) * 100, 1)
                    }
                    for du in dataset_usage
                ],
                "activity_patterns": {
                    "hourly_distribution": hourly_distribution,
                    "most_active_hour": max(hourly_distribution.items(), key=lambda x: x[1])[0] if hourly_distribution else None,
                    "peak_activity_count": max(hourly_distribution.values()) if hourly_distribution else 0
                }
            }
            
        except Exception as e:
            return {"error": f"Failed to get user analytics: {str(e)}"}
    
    def _calculate_performance_grade(self, response_time_ms: float) -> str:
        """Calculate performance grade based on response time"""
        if response_time_ms < 1000:
            return "excellent"
        elif response_time_ms < 3000:
            return "good"
        elif response_time_ms < 5000:
            return "fair"
        else:
            return "poor"
    
    def _calculate_quality_grade(self, avg_relevance: float, avg_confidence: float,
                               completeness_score: float, verification_rate: float) -> str:
        """Calculate overall quality grade"""
        # Weighted quality score
        quality_score = (
            avg_relevance * 0.3 +
            avg_confidence * 0.3 +
            (completeness_score / 100) * 0.2 +
            (verification_rate / 100) * 0.2
        )
        
        if quality_score >= 0.9:
            return "excellent"
        elif quality_score >= 0.8:
            return "good"
        elif quality_score >= 0.7:
            return "fair"
        else:
            return "poor"
    
    def _generate_health_alerts(self, error_rate: float, attribution_rate: float,
                              avg_confidence: float) -> List[Dict[str, str]]:
        """Generate health alerts based on metrics"""
        alerts = []
        
        if error_rate > 10:
            alerts.append({
                "level": "critical",
                "message": f"High error rate: {error_rate:.1f}%",
                "recommendation": "Check system logs and investigate query processing issues"
            })
        elif error_rate > 5:
            alerts.append({
                "level": "warning",
                "message": f"Elevated error rate: {error_rate:.1f}%",
                "recommendation": "Monitor system performance and consider scaling"
            })
        
        if attribution_rate < 90:
            alerts.append({
                "level": "warning",
                "message": f"Low source attribution rate: {attribution_rate:.1f}%",
                "recommendation": "Review document chunking and search algorithms"
            })
        
        if avg_confidence < 0.7:
            alerts.append({
                "level": "warning",
                "message": f"Low average confidence: {avg_confidence:.2f}",
                "recommendation": "Consider improving source quality or search relevance"
            })
        
        return alerts
