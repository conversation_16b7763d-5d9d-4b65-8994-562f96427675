"""
AIthentiq Advanced Enterprise Multi-tenant Backend API
Features: Security, Monitoring, Performance, Compliance, Reliability
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status, UploadFile, File, Form, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any, List, Optional
import os
import json
import uuid
import time
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
import logging
from contextlib import asynccontextmanager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/aithentiq_enterprise.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import enterprise components
from database import get_db, engine
from models_multitenant import (
    Tenant, TenantUser, TenantApiKey, TenantDataset, TenantQuery, 
    TenantDocumentChunk, DataConnector, AuditLog, AuditAction
)
from middleware.tenant_isolation import (
    AdvancedTenantContext, tenant_context, get_current_tenant, get_current_user,
    get_current_tenant_id, get_current_user_id
)
from services.advanced_task_queue_service import advanced_task_queue
from services.advanced_monitoring_service import monitoring_service
from services.document_processing_service import document_processor
from services.text_chunking_service import text_chunking_service
from services.vector_storage_service import vector_storage_service
from services.embedding_service import create_embedding_service

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Starting AIthentiq Enterprise API")
    
    # Initialize monitoring
    monitoring_service.record_metric("app.startup", 1)
    
    # Health check on startup
    try:
        # Test database connection
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db.close()
        logger.info("Database connection verified")
        
        # Test Redis connection
        health = advanced_task_queue.get_health_status()
        if health["healthy"]:
            logger.info("Task queue system healthy")
        else:
            logger.warning("Task queue system issues detected")
        
    except Exception as e:
        logger.error(f"Startup health check failed: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AIthentiq Enterprise API")
    monitoring_service.record_metric("app.shutdown", 1)

# Create FastAPI app with enterprise configuration
app = FastAPI(
    title="AIthentiq Enterprise API",
    description="Advanced Enterprise Multi-tenant RAG System",
    version="2.0.0",
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
    lifespan=lifespan
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1,*.aithentiq.com").split(",")
)

# Performance middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)

# CORS middleware with security
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["X-Request-ID", "X-Response-Time"]
)

# Request tracking middleware
@app.middleware("http")
async def request_tracking_middleware(request: Request, call_next):
    """Track request metrics and performance"""
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    # Add request ID to headers
    request.state.request_id = request_id
    
    # Record request start
    monitoring_service.increment_counter(
        "http.requests.total",
        {"method": request.method, "endpoint": request.url.path}
    )
    
    try:
        response = await call_next(request)
        
        # Calculate response time
        response_time = (time.time() - start_time) * 1000
        
        # Record metrics
        monitoring_service.record_timer(
            f"http.request.duration",
            response_time,
            {"method": request.method, "status": str(response.status_code)}
        )
        
        # Add headers
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Response-Time"] = f"{response_time:.2f}ms"
        
        return response
        
    except Exception as e:
        # Record error
        monitoring_service.increment_counter(
            "http.requests.errors",
            {"method": request.method, "error": type(e).__name__}
        )
        raise

# Health and monitoring endpoints
@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    try:
        health_data = monitoring_service.get_health_status()
        
        # Test database
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db.close()
        health_data["database"] = "healthy"
        
        # Test task queue
        queue_health = advanced_task_queue.get_health_status()
        health_data["task_queue"] = queue_health
        
        return health_data
        
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@app.get("/metrics")
async def get_metrics(tenant_id: str = Depends(get_current_tenant_id)):
    """Get tenant-specific metrics"""
    try:
        # Get metrics summary
        metrics = monitoring_service.get_metrics_summary(tenant_id, hours=24)
        
        # Get active alerts
        alerts = monitoring_service.get_active_alerts(tenant_id)
        
        return {
            "metrics": metrics,
            "alerts": [
                {
                    "id": alert.id,
                    "name": alert.name,
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "triggered_at": alert.triggered_at.isoformat()
                }
                for alert in alerts
            ],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get metrics: {str(e)}"
        )

# Tenant management endpoints
@app.post("/tenants/register")
async def register_tenant(
    tenant_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Register a new tenant with enterprise features"""
    try:
        # Validate required fields
        required_fields = ["name", "domain", "display_name", "admin_email", "admin_name"]
        for field in required_fields:
            if field not in tenant_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required field: {field}"
                )
        
        # Check if domain already exists
        existing_tenant = db.query(Tenant).filter(Tenant.domain == tenant_data["domain"]).first()
        if existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Domain already exists"
            )
        
        # Create tenant with enterprise defaults
        tenant = Tenant(
            name=tenant_data["name"],
            domain=tenant_data["domain"],
            display_name=tenant_data["display_name"],
            subscription_plan=tenant_data.get("subscription_plan", "trial"),
            subscription_tier=tenant_data.get("subscription_tier", "standard"),
            max_users=tenant_data.get("max_users", 10),
            max_datasets=tenant_data.get("max_datasets", 50),
            max_storage_gb=tenant_data.get("max_storage_gb", 10.0),
            max_api_calls_per_hour=tenant_data.get("max_api_calls_per_hour", 5000),
            company_size=tenant_data.get("company_size"),
            industry=tenant_data.get("industry"),
            contact_email=tenant_data["admin_email"],
            data_residency_region=tenant_data.get("data_residency_region", "us-east-1"),
            trial_expires_at=datetime.now(timezone.utc) + timedelta(days=30)
        )
        
        db.add(tenant)
        db.commit()
        db.refresh(tenant)
        
        # Create admin user
        admin_user = TenantUser(
            tenant_id=tenant.id,
            email=tenant_data["admin_email"],
            name=tenant_data["admin_name"],
            role="admin",
            is_active=True,
            oauth_provider="email"
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # Generate API key
        api_key = TenantApiKey(
            tenant_id=tenant.id,
            user_id=admin_user.id,
            name="Default Admin Key",
            key_hash=tenant_data.get("api_key", uuid.uuid4().hex),
            permissions={"admin": True, "read": True, "write": True}
        )
        
        db.add(api_key)
        db.commit()
        db.refresh(api_key)
        
        # Log audit event
        AuditLog.log_action(
            tenant_id=tenant.id,
            action=AuditAction.CREATE,
            resource_type="tenant",
            resource_id=tenant.id,
            user_id=admin_user.id,
            new_values={"name": tenant.name, "domain": tenant.domain}
        )
        
        # Record metrics
        monitoring_service.increment_counter("tenants.registered")
        
        # Background tasks
        background_tasks.add_task(
            setup_tenant_resources,
            tenant.id,
            admin_user.id
        )
        
        return {
            "status": "success",
            "tenant_id": tenant.id,
            "api_key": api_key.key_hash,
            "message": "Tenant registered successfully",
            "trial_expires_at": tenant.trial_expires_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Tenant registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

async def setup_tenant_resources(tenant_id: str, user_id: str):
    """Background task to setup tenant resources"""
    try:
        # Initialize vector store
        vector_store = vector_storage_service.get_vector_store(tenant_id, "faiss")
        
        # Setup monitoring for tenant
        monitoring_service.record_metric(
            "tenant.setup.completed",
            1,
            labels={"tenant_id": tenant_id}
        )
        
        logger.info(f"Tenant resources setup completed for {tenant_id}")
        
    except Exception as e:
        logger.error(f"Failed to setup tenant resources for {tenant_id}: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main_enterprise:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
