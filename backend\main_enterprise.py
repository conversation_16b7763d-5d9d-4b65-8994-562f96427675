"""
AIthentiq Advanced Enterprise Multi-tenant Backend API
Features: Security, Monitoring, Performance, Compliance, Reliability
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status, UploadFile, File, Form, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any, List, Optional
import os
import json
import uuid
import time
import base64
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
import logging
from contextlib import asynccontextmanager
from dataclasses import asdict

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/aithentiq_enterprise.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import enterprise components
from database import get_db, engine
from models_multitenant import (
    Tenant, TenantUser, TenantApiKey, TenantDataset, TenantQuery, 
    TenantDocumentChunk, DataConnector, AuditLog, AuditAction
)
from middleware.tenant_isolation import (
    AdvancedTenantContext, tenant_context, get_current_tenant, get_current_user,
    get_current_tenant_id, get_current_user_id
)
from services.advanced_task_queue_service import advanced_task_queue
from services.advanced_monitoring_service import monitoring_service
from services.document_processing_service import document_processor
from services.text_chunking_service import text_chunking_service
from services.vector_storage_service import vector_storage_service
from services.embedding_service import create_embedding_service
from services.enterprise_rag_service import enterprise_rag_service, RAGContext, RAGStrategy, LLMProvider
from services.enterprise_llm_service import enterprise_llm_service, LLMRequest, LLMModel
from services.enterprise_trust_scoring_service import enterprise_trust_scoring_service, TrustScoringMethod
from services.enterprise_analytics_service import enterprise_analytics_service, AnalyticsTimeframe, MetricType
from services.enterprise_insights_service import enterprise_insights_service, PredictionType, RecommendationType
from services.enterprise_reporting_service import enterprise_reporting_service, ReportType, ReportConfiguration, ExportFormat
from services.revolutionary_analytics_engine import revolutionary_analytics_engine

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Starting AIthentiq Enterprise API")
    
    # Initialize monitoring
    monitoring_service.record_metric("app.startup", 1)
    
    # Health check on startup
    try:
        # Test database connection
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db.close()
        logger.info("Database connection verified")
        
        # Test Redis connection
        health = advanced_task_queue.get_health_status()
        if health["healthy"]:
            logger.info("Task queue system healthy")
        else:
            logger.warning("Task queue system issues detected")
        
    except Exception as e:
        logger.error(f"Startup health check failed: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AIthentiq Enterprise API")
    monitoring_service.record_metric("app.shutdown", 1)

# Create FastAPI app with enterprise configuration
app = FastAPI(
    title="AIthentiq Enterprise API",
    description="Advanced Enterprise Multi-tenant RAG System",
    version="2.0.0",
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
    lifespan=lifespan
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1,*.aithentiq.com").split(",")
)

# Performance middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)

# CORS middleware with security
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["X-Request-ID", "X-Response-Time"]
)

# Request tracking middleware
@app.middleware("http")
async def request_tracking_middleware(request: Request, call_next):
    """Track request metrics and performance"""
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    # Add request ID to headers
    request.state.request_id = request_id
    
    # Record request start
    monitoring_service.increment_counter(
        "http.requests.total",
        {"method": request.method, "endpoint": request.url.path}
    )
    
    try:
        response = await call_next(request)
        
        # Calculate response time
        response_time = (time.time() - start_time) * 1000
        
        # Record metrics
        monitoring_service.record_timer(
            f"http.request.duration",
            response_time,
            {"method": request.method, "status": str(response.status_code)}
        )
        
        # Add headers
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Response-Time"] = f"{response_time:.2f}ms"
        
        return response
        
    except Exception as e:
        # Record error
        monitoring_service.increment_counter(
            "http.requests.errors",
            {"method": request.method, "error": type(e).__name__}
        )
        raise

# Health and monitoring endpoints
@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    try:
        health_data = monitoring_service.get_health_status()
        
        # Test database
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db.close()
        health_data["database"] = "healthy"
        
        # Test task queue
        queue_health = advanced_task_queue.get_health_status()
        health_data["task_queue"] = queue_health
        
        return health_data
        
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@app.get("/metrics")
async def get_metrics(tenant_id: str = Depends(get_current_tenant_id)):
    """Get tenant-specific metrics"""
    try:
        # Get metrics summary
        metrics = monitoring_service.get_metrics_summary(tenant_id, hours=24)
        
        # Get active alerts
        alerts = monitoring_service.get_active_alerts(tenant_id)
        
        return {
            "metrics": metrics,
            "alerts": [
                {
                    "id": alert.id,
                    "name": alert.name,
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "triggered_at": alert.triggered_at.isoformat()
                }
                for alert in alerts
            ],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get metrics: {str(e)}"
        )

# Tenant management endpoints
@app.post("/tenants/register")
async def register_tenant(
    tenant_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Register a new tenant with enterprise features"""
    try:
        # Validate required fields
        required_fields = ["name", "domain", "display_name", "admin_email", "admin_name"]
        for field in required_fields:
            if field not in tenant_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required field: {field}"
                )
        
        # Check if domain already exists
        existing_tenant = db.query(Tenant).filter(Tenant.domain == tenant_data["domain"]).first()
        if existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Domain already exists"
            )
        
        # Create tenant with enterprise defaults
        tenant = Tenant(
            name=tenant_data["name"],
            domain=tenant_data["domain"],
            display_name=tenant_data["display_name"],
            subscription_plan=tenant_data.get("subscription_plan", "trial"),
            subscription_tier=tenant_data.get("subscription_tier", "standard"),
            max_users=tenant_data.get("max_users", 10),
            max_datasets=tenant_data.get("max_datasets", 50),
            max_storage_gb=tenant_data.get("max_storage_gb", 10.0),
            max_api_calls_per_hour=tenant_data.get("max_api_calls_per_hour", 5000),
            company_size=tenant_data.get("company_size"),
            industry=tenant_data.get("industry"),
            contact_email=tenant_data["admin_email"],
            data_residency_region=tenant_data.get("data_residency_region", "us-east-1"),
            trial_expires_at=datetime.now(timezone.utc) + timedelta(days=30)
        )
        
        db.add(tenant)
        db.commit()
        db.refresh(tenant)
        
        # Create admin user
        admin_user = TenantUser(
            tenant_id=tenant.id,
            email=tenant_data["admin_email"],
            name=tenant_data["admin_name"],
            role="admin",
            is_active=True,
            oauth_provider="email"
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # Generate API key
        api_key = TenantApiKey(
            tenant_id=tenant.id,
            user_id=admin_user.id,
            name="Default Admin Key",
            key_hash=tenant_data.get("api_key", uuid.uuid4().hex),
            permissions={"admin": True, "read": True, "write": True}
        )
        
        db.add(api_key)
        db.commit()
        db.refresh(api_key)
        
        # Log audit event
        AuditLog.log_action(
            tenant_id=tenant.id,
            action=AuditAction.CREATE,
            resource_type="tenant",
            resource_id=tenant.id,
            user_id=admin_user.id,
            new_values={"name": tenant.name, "domain": tenant.domain}
        )
        
        # Record metrics
        monitoring_service.increment_counter("tenants.registered")
        
        # Background tasks
        background_tasks.add_task(
            setup_tenant_resources,
            tenant.id,
            admin_user.id
        )
        
        return {
            "status": "success",
            "tenant_id": tenant.id,
            "api_key": api_key.key_hash,
            "message": "Tenant registered successfully",
            "trial_expires_at": tenant.trial_expires_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Tenant registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

async def setup_tenant_resources(tenant_id: str, user_id: str):
    """Background task to setup tenant resources"""
    try:
        # Initialize vector store
        vector_store = vector_storage_service.get_vector_store(tenant_id, "faiss")
        
        # Setup monitoring for tenant
        monitoring_service.record_metric(
            "tenant.setup.completed",
            1,
            labels={"tenant_id": tenant_id}
        )
        
        logger.info(f"Tenant resources setup completed for {tenant_id}")

    except Exception as e:
        logger.error(f"Failed to setup tenant resources for {tenant_id}: {e}")

# RAG Query Endpoints
@app.post("/rag/query")
async def process_rag_query(
    query_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Process RAG query with enterprise features"""
    try:
        # Validate required fields
        if "query" not in query_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Query is required"
            )

        # Extract parameters
        query = query_data["query"]
        dataset_ids = query_data.get("dataset_ids", [])
        strategy = RAGStrategy(query_data.get("strategy", "ensemble_average"))
        llm_provider = LLMProvider(query_data.get("llm_provider", "openai"))
        trust_method = TrustScoringMethod(query_data.get("trust_method", "ensemble_average"))

        # Create RAG context
        rag_context = RAGContext(
            query=query,
            tenant_id=current_tenant_id,
            user_id=current_user_id,
            dataset_ids=dataset_ids,
            strategy=strategy,
            llm_provider=llm_provider,
            max_chunks=query_data.get("max_chunks", 10),
            similarity_threshold=query_data.get("similarity_threshold", 0.7),
            temperature=query_data.get("temperature", 0.7),
            max_tokens=query_data.get("max_tokens", 2048),
            include_sources=query_data.get("include_sources", True),
            use_cache=query_data.get("use_cache", True)
        )

        # Process RAG query
        rag_response = await enterprise_rag_service.process_query(rag_context)

        # Calculate trust score
        trust_score = await enterprise_trust_scoring_service.calculate_trust_score(
            query=query,
            response=rag_response.answer,
            sources=[asdict(source) for source in rag_response.sources],
            method=trust_method,
            tenant_id=current_tenant_id
        )

        # Store query in database
        tenant_query = TenantQuery(
            tenant_id=current_tenant_id,
            user_id=current_user_id,
            query_text=query,
            response_text=rag_response.answer,
            strategy_used=strategy.value,
            llm_provider=llm_provider.value,
            trust_score=trust_score.overall_score,
            confidence_level=trust_score.confidence_level.value,
            processing_time_ms=rag_response.processing_time_ms,
            tokens_used=rag_response.tokens_used,
            sources_count=len(rag_response.sources),
            cache_hit=rag_response.cache_hit
        )

        db.add(tenant_query)
        db.commit()
        db.refresh(tenant_query)

        # Log audit event
        AuditLog.log_action(
            tenant_id=current_tenant_id,
            action=AuditAction.CREATE,
            resource_type="query",
            resource_id=tenant_query.id,
            user_id=current_user_id,
            new_values={"query": query, "strategy": strategy.value}
        )

        # Record metrics
        monitoring_service.record_timer(
            "rag.query.duration",
            rag_response.processing_time_ms,
            {"strategy": strategy.value, "provider": llm_provider.value},
            current_tenant_id
        )

        monitoring_service.record_metric(
            "rag.trust_score",
            trust_score.overall_score,
            labels={"method": trust_method.value},
            tenant_id=current_tenant_id
        )

        # Prepare response
        response_data = {
            "query_id": tenant_query.id,
            "answer": rag_response.answer,
            "sources": [
                {
                    "chunk_id": source.chunk_id,
                    "content": source.content,
                    "score": source.score,
                    "source_document": source.source_document,
                    "chunk_index": source.chunk_index
                }
                for source in rag_response.sources
            ] if rag_response.include_sources else [],
            "trust_score": {
                "overall_score": trust_score.overall_score,
                "confidence_level": trust_score.confidence_level.value,
                "method_used": trust_score.method_used,
                "reasoning": trust_score.reasoning,
                "flags": trust_score.flags
            },
            "metadata": {
                "processing_time_ms": rag_response.processing_time_ms,
                "tokens_used": rag_response.tokens_used,
                "llm_provider": rag_response.llm_provider,
                "strategy_used": rag_response.strategy_used,
                "cache_hit": rag_response.cache_hit,
                "sources_analyzed": len(rag_response.sources)
            }
        }

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"RAG query processing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Query processing failed: {str(e)}"
        )

@app.get("/rag/strategies")
async def get_rag_strategies():
    """Get available RAG strategies"""
    return {
        "strategies": [
            {
                "value": strategy.value,
                "name": strategy.value.replace("_", " ").title(),
                "description": f"RAG strategy: {strategy.value}"
            }
            for strategy in RAGStrategy
        ]
    }

@app.get("/rag/providers")
async def get_llm_providers():
    """Get available LLM providers"""
    provider_status = enterprise_llm_service.get_health_status()

    return {
        "providers": [
            {
                "value": provider.value,
                "name": provider.value.title(),
                "available": provider.value in provider_status["provider_status"],
                "healthy": provider_status["provider_status"].get(provider.value, {}).get("healthy", False)
            }
            for provider in LLMProvider
        ]
    }

@app.get("/rag/trust-methods")
async def get_trust_methods():
    """Get available trust scoring methods"""
    return {
        "methods": [
            {
                "value": method.value,
                "name": method.value.replace("_", " ").title(),
                "description": f"Trust scoring method: {method.value}"
            }
            for method in TrustScoringMethod
        ]
    }

# Analytics & Insights Endpoints
@app.get("/analytics/dashboard")
async def get_analytics_dashboard(
    timeframe: str = "day",
    include_predictions: bool = True,
    include_insights: bool = True,
    current_tenant_id: str = Depends(get_current_tenant_id)
):
    """Get comprehensive analytics dashboard"""
    try:
        timeframe_enum = AnalyticsTimeframe(timeframe)

        dashboard = await enterprise_analytics_service.generate_analytics_dashboard(
            tenant_id=current_tenant_id,
            timeframe=timeframe_enum,
            include_predictions=include_predictions,
            include_insights=include_insights
        )

        return dashboard

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid timeframe: {timeframe}"
        )
    except Exception as e:
        logger.error(f"Analytics dashboard failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Dashboard generation failed: {str(e)}"
        )

@app.get("/insights/strategic")
async def get_strategic_insights(
    include_predictions: bool = True,
    include_recommendations: bool = True,
    include_alerts: bool = True,
    current_tenant_id: str = Depends(get_current_tenant_id)
):
    """Get strategic business insights"""
    try:
        insights = await enterprise_insights_service.generate_strategic_insights(
            tenant_id=current_tenant_id,
            include_predictions=include_predictions,
            include_recommendations=include_recommendations,
            include_alerts=include_alerts
        )

        return insights

    except Exception as e:
        logger.error(f"Strategic insights failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Insights generation failed: {str(e)}"
        )

@app.post("/reports/generate")
async def generate_report(
    report_config: Dict[str, Any],
    current_tenant_id: str = Depends(get_current_tenant_id)
):
    """Generate custom report"""
    try:
        # Validate and create report configuration
        config = ReportConfiguration(
            report_type=ReportType(report_config.get("report_type", "executive_summary")),
            title=report_config.get("title", "Custom Report"),
            description=report_config.get("description", ""),
            timeframe=report_config.get("timeframe", "month"),
            filters=report_config.get("filters", {}),
            visualizations=report_config.get("visualizations", []),
            export_format=ExportFormat(report_config.get("export_format", "json")),
            include_raw_data=report_config.get("include_raw_data", False)
        )

        # Generate report
        report = await enterprise_reporting_service.generate_report(
            tenant_id=current_tenant_id,
            config=config
        )

        # Prepare response
        response_data = {
            "report_id": report.report_id,
            "title": report.title,
            "generated_at": report.generated_at.isoformat(),
            "sections": [asdict(section) for section in report.sections],
            "metadata": report.metadata
        }

        # Include export data if available
        if report.export_data and config.export_format != ExportFormat.JSON:
            response_data["export_data"] = base64.b64encode(report.export_data).decode()
            response_data["export_format"] = config.export_format.value

        return response_data

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid report configuration: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Report generation failed: {str(e)}"
        )

@app.get("/analytics/metrics/{metric_type}")
async def get_specific_metric(
    metric_type: str,
    timeframe: str = "day",
    current_tenant_id: str = Depends(get_current_tenant_id)
):
    """Get specific metric data"""
    try:
        metric_enum = MetricType(metric_type)
        timeframe_enum = AnalyticsTimeframe(timeframe)

        # This would call a specific metric retrieval method
        # For now, return a placeholder response
        return {
            "metric_type": metric_type,
            "timeframe": timeframe,
            "tenant_id": current_tenant_id,
            "data": "Metric data would be returned here"
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid metric type or timeframe: {str(e)}"
        )

@app.get("/reports/templates")
async def get_report_templates():
    """Get available report templates"""
    return {
        "templates": [
            {
                "type": report_type.value,
                "name": report_type.value.replace("_", " ").title(),
                "description": f"Comprehensive {report_type.value.replace('_', ' ')} report"
            }
            for report_type in ReportType
        ]
    }

@app.get("/analytics/timeframes")
async def get_analytics_timeframes():
    """Get available analytics timeframes"""
    return {
        "timeframes": [
            {
                "value": timeframe.value,
                "name": timeframe.value.title(),
                "description": f"Analytics for the past {timeframe.value}"
            }
            for timeframe in AnalyticsTimeframe
        ]
    }

@app.get("/analytics/revolutionary")
async def get_revolutionary_insights(
    analysis_depth: str = "transcendent",
    quantum_dimensions: int = 11,
    consciousness_level: str = "advanced",
    current_tenant_id: str = Depends(get_current_tenant_id)
):
    """Get revolutionary quantum-inspired analytics insights"""
    try:
        insights = await revolutionary_analytics_engine.generate_revolutionary_insights(
            tenant_id=current_tenant_id,
            analysis_depth=analysis_depth,
            quantum_dimensions=quantum_dimensions,
            consciousness_level=consciousness_level
        )

        return insights

    except Exception as e:
        logger.error(f"Revolutionary analytics failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Revolutionary analytics failed: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main_enterprise:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
