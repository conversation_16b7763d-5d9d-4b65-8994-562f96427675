"""
Simple Trust Score Comparison API
Standalone implementation without complex dependencies
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import time
import asyncio

router = APIRouter()

class TrustComparisonRequest(BaseModel):
    query: str = Field(..., description="The question being asked")
    answer: str = Field(..., description="The AI-generated answer")
    sources: List[Dict[str, Any]] = Field(default=[], description="Source documents")
    selected_methods: List[str] = Field(..., description="Trust score methods to compare")
    user_id: Optional[str] = Field(default="demo_user", description="User ID")

class TrustComparisonResponse(BaseModel):
    results: List[Dict[str, Any]]
    analysis: Dict[str, Any]
    query: str
    answer: str
    timestamp: float

@router.get("/methods")
async def get_available_methods():
    """Get list of available trust score methods"""
    return {
        "methods": [
            {
                "id": "legacy_deterministic",
                "name": "Legacy Deterministic (Current Fallback)",
                "description": "Simple rule-based scoring with optimistic baseline"
            },
            {
                "id": "enhanced_v25",
                "name": "Enhanced V2.5 (Advanced System)",
                "description": "Multi-component analysis with weighted scoring"
            },
            {
                "id": "current_production",
                "name": "Current Production (In Use)",
                "description": "Production system with fallback logic"
            },
            {
                "id": "independent_rag",
                "name": "Independent RAG Evaluator",
                "description": "RAG evaluation without external dependencies"
            },
            {
                "id": "aithentiq_convergence",
                "name": "AIthentiq Convergence (Innovative)",
                "description": "Proprietary convergence-based trust scoring"
            }
        ]
    }

@router.post("/compare", response_model=TrustComparisonResponse)
async def compare_trust_scores(request: TrustComparisonRequest):
    """Compare multiple trust score algorithms"""
    try:
        # Simple implementation without complex dependencies
        results = []
        
        for method in request.selected_methods:
            start_time = time.time()
            
            try:
                if method == "legacy_deterministic":
                    result = _legacy_deterministic(request.query, request.answer, request.sources)
                elif method == "enhanced_v25":
                    result = _enhanced_v25(request.query, request.answer, request.sources)
                elif method == "current_production":
                    result = _current_production(request.query, request.answer, request.sources)
                elif method == "independent_rag":
                    result = _independent_rag(request.query, request.answer, request.sources)
                elif method == "aithentiq_convergence":
                    result = _aithentiq_convergence(request.query, request.answer, request.sources)
                else:
                    raise ValueError(f"Unknown method: {method}")
                
                processing_time = (time.time() - start_time) * 1000
                
                results.append({
                    "method_name": result["method_name"],
                    "score": result["score"],
                    "processing_time_ms": processing_time,
                    "components": result["components"],
                    "explanation": result["explanation"],
                    "score_percentage": f"{result['score']:.1%}",
                    "processing_time_formatted": f"{processing_time:.1f}ms"
                })
                
            except Exception as e:
                processing_time = (time.time() - start_time) * 1000
                results.append({
                    "method_name": _get_method_name(method),
                    "score": 0.0,
                    "processing_time_ms": processing_time,
                    "components": {},
                    "explanation": f"Error: {str(e)}",
                    "error": str(e),
                    "score_percentage": "0.0%",
                    "processing_time_formatted": f"{processing_time:.1f}ms"
                })
        
        # Generate analysis
        analysis = _analyze_results(results)
        
        return TrustComparisonResponse(
            results=results,
            analysis=analysis,
            query=request.query,
            answer=request.answer,
            timestamp=time.time()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Trust score comparison failed: {str(e)}"
        )

@router.post("/demo")
async def run_demo():
    """Run a demo comparison with sample data"""
    demo_request = TrustComparisonRequest(
        query="What is the average sales for Q1?",
        answer="Based on the data analysis, the average sales for Q1 is $45,000.",
        sources=[
            {"text": "Q1 sales data shows total revenue of $135,000 across 3 months"},
            {"text": "Sales performance report indicates consistent monthly averages"}
        ],
        selected_methods=["legacy_deterministic", "enhanced_v25", "aithentiq_convergence"]
    )
    
    return await compare_trust_scores(demo_request)

def _get_method_name(method_id: str) -> str:
    """Get display name for method"""
    names = {
        "legacy_deterministic": "Legacy Deterministic (Current Fallback)",
        "enhanced_v25": "Enhanced V2.5 (Advanced System)",
        "current_production": "Current Production (In Use)",
        "independent_rag": "Independent RAG Evaluator",
        "aithentiq_convergence": "AIthentiq Convergence (Innovative)"
    }
    return names.get(method_id, method_id)

def _legacy_deterministic(query: str, answer: str, sources: List[Dict]) -> Dict:
    """Legacy deterministic scoring"""
    score = 0.85  # Optimistic baseline
    
    # Bonuses
    if any(char.isdigit() for char in answer):
        score += 0.08
    if len(answer.strip()) >= 15:
        score += 0.05
    if any(term in answer.lower() for term in ['average', 'analysis', 'data']):
        score += 0.05
    
    score = min(1.0, score)
    
    return {
        "method_name": "Legacy Deterministic (Current Fallback)",
        "score": score,
        "components": {
            "base_score": 0.85,
            "digit_bonus": 0.08 if any(char.isdigit() for char in answer) else 0,
            "length_bonus": 0.05 if len(answer.strip()) >= 15 else 0,
            "data_terms_bonus": 0.05 if any(term in answer.lower() for term in ['average', 'analysis', 'data']) else 0
        },
        "explanation": f"Legacy deterministic score: {score:.1%}"
    }

def _enhanced_v25(query: str, answer: str, sources: List[Dict]) -> Dict:
    """Enhanced V2.5 scoring"""
    # Multi-component analysis
    model_confidence = 0.8 if len(answer) > 20 else 0.6
    source_quality = 0.7 if sources else 0.4
    citation_accuracy = 0.9 if 'based on' in answer.lower() else 0.6
    question_match = 0.8 if any(word in answer.lower() for word in query.lower().split()) else 0.5
    
    # Weighted combination
    score = (model_confidence * 0.4 + source_quality * 0.3 + 
             citation_accuracy * 0.2 + question_match * 0.1)
    
    return {
        "method_name": "Enhanced V2.5 (Advanced System)",
        "score": score,
        "components": {
            "model_confidence": model_confidence,
            "source_quality": source_quality,
            "citation_accuracy": citation_accuracy,
            "question_match": question_match
        },
        "explanation": f"Enhanced V2.5 multi-component: {score:.1%}"
    }

def _current_production(query: str, answer: str, sources: List[Dict]) -> Dict:
    """Current production scoring"""
    # Fallback to enhanced
    enhanced = _enhanced_v25(query, answer, sources)
    enhanced["method_name"] = "Current Production (In Use)"
    enhanced["explanation"] = f"Production system: {enhanced['score']:.1%}"
    return enhanced

def _independent_rag(query: str, answer: str, sources: List[Dict]) -> Dict:
    """Independent RAG evaluation"""
    # Simple RAG-inspired scoring
    relevance = 0.7 if sources else 0.4
    faithfulness = 0.8 if any(word in answer.lower() for word in ['based', 'according', 'data']) else 0.6
    context_precision = 0.75 if len(sources) >= 2 else 0.5
    answer_relevancy = 0.8 if any(word in answer.lower() for word in query.lower().split()) else 0.5
    
    score = (relevance + faithfulness + context_precision + answer_relevancy) / 4
    
    return {
        "method_name": "Independent RAG Evaluator",
        "score": score,
        "components": {
            "relevance": relevance,
            "faithfulness": faithfulness,
            "context_precision": context_precision,
            "answer_relevancy": answer_relevancy
        },
        "explanation": f"Independent RAG evaluation: {score:.1%}"
    }

def _aithentiq_convergence(query: str, answer: str, sources: List[Dict]) -> Dict:
    """AIthentiq Convergence algorithm"""
    # Proprietary convergence scoring
    semantic_stability = 0.85 if len(answer) > 30 else 0.7
    model_consensus = 0.8 if any(char.isdigit() for char in answer) else 0.6
    temporal_consistency = 0.9 if 'analysis' in answer.lower() else 0.7
    response_coherence = 0.85 if len(answer.split()) > 10 else 0.6
    
    # Convergence weighting
    score = (semantic_stability * 0.3 + model_consensus * 0.25 + 
             temporal_consistency * 0.25 + response_coherence * 0.2)
    
    return {
        "method_name": "AIthentiq Convergence (Innovative)",
        "score": score,
        "components": {
            "semantic_stability": semantic_stability,
            "model_consensus": model_consensus,
            "temporal_consistency": temporal_consistency,
            "response_coherence": response_coherence
        },
        "explanation": f"AIthentiq Convergence: {score:.1%}"
    }

def _analyze_results(results: List[Dict]) -> Dict[str, Any]:
    """Analyze comparison results"""
    successful_results = [r for r in results if not r.get('error')]
    failed_results = [r for r in results if r.get('error')]
    
    analysis = {
        "summary": {
            "methods_tested": len(results),
            "successful_methods": len(successful_results),
            "failed_methods": len(failed_results)
        },
        "recommendations": []
    }
    
    if successful_results:
        scores = [r['score'] for r in successful_results]
        times = [r['processing_time_ms'] for r in successful_results]
        
        analysis["score_analysis"] = {
            "highest_score": max(scores),
            "lowest_score": min(scores),
            "average_score": sum(scores) / len(scores),
            "best_method": {"name": max(successful_results, key=lambda x: x['score'])['method_name'], 
                           "score": max(scores)},
            "worst_method": {"name": min(successful_results, key=lambda x: x['score'])['method_name'], 
                            "score": min(scores)}
        }
        
        analysis["performance_analysis"] = {
            "fastest_time_ms": min(times),
            "slowest_time_ms": max(times),
            "average_time_ms": sum(times) / len(times),
            "fastest_method": {"name": min(successful_results, key=lambda x: x['processing_time_ms'])['method_name'], 
                              "time_ms": min(times)},
            "slowest_method": {"name": max(successful_results, key=lambda x: x['processing_time_ms'])['method_name'], 
                              "time_ms": max(times)}
        }
        
        # Generate recommendations
        if max(scores) - min(scores) > 0.3:
            analysis["recommendations"].append("High variance in trust scores detected - consider investigating why methods disagree")
        if max(times) > 1000:
            analysis["recommendations"].append("Some methods are slow - consider optimizing for production use")
        if max(times) > min(times) * 10:
            analysis["recommendations"].append("Large performance differences between methods - consider speed vs accuracy trade-offs")
    
    if failed_results:
        analysis["recommendations"].append(f"{len(failed_results)} method(s) failed - check system configuration")
    
    if not analysis["recommendations"]:
        analysis["recommendations"].append("All methods performed well - consider using fastest method for production")
    
    return analysis
