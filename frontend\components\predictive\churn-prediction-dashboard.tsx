'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import {
  UserX,
  TrendingDown,
  Target,
  Lightbulb,
  RefreshCw,
  AlertTriangle,
  Calendar,
  Activity,
  Info
} from 'lucide-react';

// Tooltip component for information icons
const InfoTooltip = ({ content, title }: { content: string; title: string }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="relative inline-block">
      <button
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="ml-1 text-gray-400 hover:text-gray-600"
      >
        <Info className="h-4 w-4" />
      </button>
      {showTooltip && (
        <div className="absolute z-10 w-64 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg -top-2 left-6">
          <div className="font-semibold mb-1">{title}</div>
          <div>{content}</div>
          <div className="absolute top-3 -left-1 w-2 h-2 bg-gray-900 rotate-45"></div>
        </div>
      )}
    </div>
  );
};

interface ChurnPrediction {
  prediction_id: number;
  user_id: string;
  churn_probability: number;
  churn_risk_level: string;
  days_to_churn: number;
  churn_factors: string[];
  retention_recommendations: string[];
  confidence_score: number;
  features_used: Record<string, any>;
  model_version: string;
  predicted_at: string;
  valid_until: string;
}

export default function ChurnPredictionDashboard() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [churnPrediction, setChurnPrediction] = useState<ChurnPrediction | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);

  // Create simple axios instance for predictive analytics (no API key needed)
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    timeout: 30000,
  });

  useEffect(() => {
    if (session?.user?.id) {
      setSelectedUserId(session.user.id);
      fetchChurnData(session.user.id);
    }
  }, [session]);

  const fetchChurnData = async (userId: string) => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);

    try {
      const response = await api.get(`/predictive/churn-prediction/${userId}`);
      setChurnPrediction(response.data);
    } catch (err: any) {
      console.error('Error fetching churn data:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching churn data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    if (!selectedUserId) return;
    
    setRefreshing(true);
    await fetchChurnData(selectedUserId);
    setRefreshing(false);
  };

  const getChurnRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'very_low': return 'text-green-700 bg-green-100';
      case 'low': return 'text-green-600 bg-green-100';
      case 'moderate': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getChurnProbabilityColor = (probability: number) => {
    if (probability >= 0.7) return 'text-red-600';
    if (probability >= 0.5) return 'text-yellow-600';
    if (probability >= 0.3) return 'text-yellow-500';
    return 'text-green-600';
  };

  const getDaysToChurnColor = (days: number) => {
    if (days <= 7) return 'text-red-600';
    if (days <= 30) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
          <p className="text-lg font-medium">Error Loading Churn Data</p>
          <p className="text-sm mt-2">{error}</p>
          <button
            onClick={refreshData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <UserX className="h-8 w-8 text-red-600" />
          <h2 className="text-2xl font-bold text-gray-900">Churn Prediction Analysis</h2>
          <InfoTooltip
            title="Churn Prediction Analysis"
            content="AI-powered analysis that predicts the likelihood of users discontinuing service or reducing engagement. Uses behavioral patterns, usage statistics, and engagement metrics to forecast churn risk."
          />
        </div>
        <button
          onClick={refreshData}
          disabled={refreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Churn Prediction Overview */}
      {churnPrediction && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Churn Probability Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center">
                  <p className="text-sm font-medium text-gray-600">Churn Probability</p>
                  <InfoTooltip
                    title="Churn Probability"
                    content="The likelihood (0-100%) that a user will stop using the service or significantly reduce engagement within the predicted timeframe. Higher percentages indicate greater churn risk."
                  />
                </div>
                <p className={`text-3xl font-bold ${getChurnProbabilityColor(churnPrediction.churn_probability)}`}>
                  {(churnPrediction.churn_probability * 100).toFixed(1)}%
                </p>
              </div>
              <div className={`p-3 rounded-full ${getChurnRiskColor(churnPrediction.churn_risk_level)}`}>
                <TrendingDown className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-4">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getChurnRiskColor(churnPrediction.churn_risk_level)}`}>
                {churnPrediction.churn_risk_level.replace('_', ' ').toUpperCase()} RISK
              </span>
            </div>
          </div>

          {/* Days to Churn Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center">
                  <p className="text-sm font-medium text-gray-600">Estimated Days to Churn</p>
                  <InfoTooltip
                    title="Estimated Days to Churn"
                    content="Predicted number of days until the user is likely to churn based on current behavior patterns. Shorter timeframes indicate more urgent retention needs."
                  />
                </div>
                <p className={`text-3xl font-bold ${getDaysToChurnColor(churnPrediction.days_to_churn)}`}>
                  {churnPrediction.days_to_churn}
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <Calendar className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-gray-500">
                Based on current behavior patterns
              </p>
            </div>
          </div>

          {/* Confidence Score Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Prediction Confidence</p>
                <p className="text-3xl font-bold text-blue-600">
                  {(churnPrediction.confidence_score * 100).toFixed(1)}%
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <Target className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-gray-500">
                Model: {churnPrediction.model_version}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Churn Factors */}
      {churnPrediction && churnPrediction.churn_factors.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            Churn Risk Factors
          </h3>
          <div className="space-y-3">
            {churnPrediction.churn_factors.map((factor, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-red-400 rounded-full mt-2"></div>
                <p className="text-sm text-gray-700">{factor}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Retention Recommendations */}
      {churnPrediction && churnPrediction.retention_recommendations.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Lightbulb className="h-5 w-5 text-yellow-500 mr-2" />
            Retention Recommendations
          </h3>
          <div className="space-y-3">
            {churnPrediction.retention_recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <p className="text-sm text-gray-700">{recommendation}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* User Engagement Metrics */}
      {churnPrediction && churnPrediction.features_used && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Activity className="h-5 w-5 text-blue-500 mr-2" />
            Current Engagement Metrics
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {churnPrediction.features_used.queries_last_30_days !== undefined && (
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {churnPrediction.features_used.queries_last_30_days}
                </p>
                <p className="text-sm text-gray-600">Queries (30 days)</p>
              </div>
            )}
            {churnPrediction.features_used.unique_features_used !== undefined && (
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {churnPrediction.features_used.unique_features_used}
                </p>
                <p className="text-sm text-gray-600">Features Used</p>
              </div>
            )}
            {churnPrediction.features_used.avg_session_duration !== undefined && (
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {Math.round(churnPrediction.features_used.avg_session_duration / 60)}m
                </p>
                <p className="text-sm text-gray-600">Avg Session</p>
              </div>
            )}
            {churnPrediction.features_used.days_since_last_query !== undefined && (
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {churnPrediction.features_used.days_since_last_query}
                </p>
                <p className="text-sm text-gray-600">Days Since Last Query</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Prediction Details */}
      {churnPrediction && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Prediction Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Predicted At:</p>
              <p className="font-medium">{new Date(churnPrediction.predicted_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-600">Valid Until:</p>
              <p className="font-medium">{new Date(churnPrediction.valid_until).toLocaleString()}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
