services:
  # Backend API Service
  - type: web
    name: aithentiq-backend
    env: python
    plan: starter
    buildCommand: cd backend && pip install -r requirements.txt
    startCommand: cd backend && uvicorn main:app --host 0.0.0.0 --port $PORT
    healthCheckPath: /health
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: aithentiq-db
          property: connectionString
      - key: OPENAI_API_KEY
        sync: false
      - key: GEMINI_API_KEY
        sync: false
      - key: CLAUDE_API_KEY
        sync: false
      - key: ENVIRONMENT
        value: production
      - key: CORS_ORIGINS
        value: "*"

  # Frontend Service
  - type: web
    name: aithentiq-frontend
    env: node
    plan: starter
    buildCommand: cd frontend && npm install && npm run build
    startCommand: cd frontend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_API_URL
        value: https://aithentiq-backend.onrender.com

databases:
  - name: aithentiq-db
    databaseName: aithentiq
    user: aithentiq
    plan: starter  # Upgraded from free to starter for better database performance


