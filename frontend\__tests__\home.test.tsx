import { render, screen } from '@testing-library/react';
import Home from '../app/page';
import '@testing-library/jest-dom';

// Mock the components that use client-side features
jest.mock('@/components/ui/navbar', () => {
  return function MockNavbar() {
    return <div data-testid="navbar">Navbar</div>;
  };
});

jest.mock('@/components/ui/footer', () => {
  return function MockFooter() {
    return <div data-testid="footer">Footer</div>;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href }: { children: React.ReactNode; href: string }) {
    return <a href={href}>{children}</a>;
  };
});

describe('Home Page', () => {
  it('renders the hero section', () => {
    render(<Home />);
    
    // Check if the main heading is rendered
    expect(screen.getByText(/Ask questions about your data in plain English/i)).toBeInTheDocument();
    
    // Check if the CTA buttons are rendered
    expect(screen.getByText(/Get Started Free/i)).toBeInTheDocument();
    expect(screen.getByText(/View Pricing/i)).toBeInTheDocument();
  });
  
  it('renders the features section', () => {
    render(<Home />);
    
    // Check if the section heading is rendered
    expect(screen.getByText(/How AskData.ai Works/i)).toBeInTheDocument();
    
    // Check if the feature steps are rendered
    expect(screen.getByText(/1. Upload Your Data/i)).toBeInTheDocument();
    expect(screen.getByText(/2. Ask Questions/i)).toBeInTheDocument();
    expect(screen.getByText(/3. Get Insights/i)).toBeInTheDocument();
  });
  
  it('renders the testimonials section', () => {
    render(<Home />);
    
    // Check if the section heading is rendered
    expect(screen.getByText(/What Our Users Say/i)).toBeInTheDocument();
    
    // Check if at least one testimonial is rendered
    expect(screen.getByText(/John Doe/i)).toBeInTheDocument();
  });
  
  it('renders the CTA section', () => {
    render(<Home />);
    
    // Check if the CTA heading is rendered
    expect(screen.getByText(/Ready to get insights from your data\?/i)).toBeInTheDocument();
    
    // Check if the CTA button is rendered
    expect(screen.getByText(/Get Started Now/i)).toBeInTheDocument();
  });
});
