#!/usr/bin/env python3
"""
Simple Tesseract OCR installer and tester for Windows
"""

import os
import sys
import subprocess
import requests
import zipfile
from pathlib import Path

def download_tesseract_windows():
    """Download and install Tesseract for Windows"""
    print("🔍 Downloading Tesseract OCR for Windows...")
    
    # Tesseract Windows installer URL
    tesseract_url = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3.20231005/tesseract-ocr-w64-setup-5.3.3.20231005.exe"
    
    try:
        print("📥 Downloading Tesseract installer...")
        response = requests.get(tesseract_url, stream=True)
        response.raise_for_status()
        
        installer_path = "tesseract_installer.exe"
        with open(installer_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print("✅ Download completed!")
        print(f"📁 Installer saved as: {installer_path}")
        print("\n🚀 To install Tesseract:")
        print(f"1. Run: {installer_path}")
        print("2. Follow the installation wizard")
        print("3. Make sure to add Tesseract to your PATH")
        print("4. Restart your terminal/IDE after installation")
        
        return True
        
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return False

def check_tesseract_installation():
    """Check if Tesseract is properly installed"""
    print("🔍 Checking Tesseract installation...")
    
    try:
        # Try to run tesseract command
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Tesseract is installed and working!")
            print(f"Version info: {result.stdout.split()[1] if result.stdout else 'Unknown'}")
            return True
        else:
            print("❌ Tesseract command failed")
            return False
            
    except FileNotFoundError:
        print("❌ Tesseract not found in PATH")
        return False
    except subprocess.TimeoutExpired:
        print("❌ Tesseract command timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking Tesseract: {e}")
        return False

def test_ocr_functionality():
    """Test OCR functionality with a simple image"""
    print("\n🧪 Testing OCR functionality...")
    
    try:
        import pytesseract
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple test image
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        draw.text((10, 30), "Hello OCR Test!", fill='black', font=font)
        
        # Save test image
        test_image_path = "test_ocr_image.png"
        img.save(test_image_path)
        
        # Perform OCR
        extracted_text = pytesseract.image_to_string(img)
        
        print(f"✅ OCR Test Results:")
        print(f"   Created test image: {test_image_path}")
        print(f"   Extracted text: '{extracted_text.strip()}'")
        
        # Clean up
        try:
            os.remove(test_image_path)
        except:
            pass
        
        if "Hello" in extracted_text or "OCR" in extracted_text:
            print("🎉 OCR is working correctly!")
            return True
        else:
            print("⚠️  OCR extracted text but results may be inaccurate")
            return False
            
    except ImportError as e:
        print(f"❌ Python OCR libraries not available: {e}")
        print("💡 Install with: pip install pytesseract pillow")
        return False
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        return False

def install_python_dependencies():
    """Install required Python packages"""
    print("📦 Installing Python OCR dependencies...")
    
    packages = ['pytesseract', 'pillow']
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def main():
    """Main installation and testing function"""
    print("🚀 AIthentiq OCR Setup Assistant")
    print("=" * 50)
    
    # Check current status
    tesseract_installed = check_tesseract_installation()
    
    if not tesseract_installed:
        print("\n📋 Tesseract OCR is not installed or not in PATH")
        print("\n🔧 Installation Options:")
        print("1. Download installer (recommended)")
        print("2. Manual installation")
        print("3. Skip for now")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == '1':
            if download_tesseract_windows():
                print("\n⏳ Please install Tesseract using the downloaded installer")
                print("   Then restart this script to test the installation")
                return
        elif choice == '2':
            print("\n📖 Manual Installation Instructions:")
            print("1. Go to: https://github.com/UB-Mannheim/tesseract/wiki")
            print("2. Download the Windows installer")
            print("3. Run the installer and follow the wizard")
            print("4. Add Tesseract to your system PATH")
            print("5. Restart your terminal/IDE")
            return
        else:
            print("⏭️  Skipping Tesseract installation")
            return
    
    # Install Python dependencies
    print("\n📦 Checking Python dependencies...")
    if not install_python_dependencies():
        print("❌ Failed to install Python dependencies")
        return
    
    # Test OCR functionality
    if tesseract_installed:
        test_ocr_functionality()
    
    print("\n" + "=" * 50)
    print("🎉 OCR Setup Complete!")
    print("\n📋 Next Steps:")
    print("1. Restart your AIthentiq backend")
    print("2. Upload an image file")
    print("3. Ask questions about the image content")
    print("4. Enjoy OCR-powered document analysis!")

if __name__ == "__main__":
    main()
