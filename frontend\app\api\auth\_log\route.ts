import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  // NextAuth internal logging endpoint
  // Return 200 to prevent 404 errors in logs
  return NextResponse.json({ status: 'ok' });
}

export async function GET(request: NextRequest) {
  // NextAuth internal logging endpoint
  // Return 200 to prevent 404 errors in logs
  return NextResponse.json({ status: 'ok' });
}
