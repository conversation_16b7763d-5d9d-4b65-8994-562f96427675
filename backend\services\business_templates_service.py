"""
Business Templates Service for AIthentiq
Provides predefined analysis templates for common business use cases
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.utils import PlotlyJSONEncoder
import json
import logging
from datetime import datetime, timedelta
from services.time_series_service import TimeSeriesService
from services.statistical_analysis_service import StatisticalAnalysisService

logger = logging.getLogger(__name__)

class BusinessTemplatesService:
    """
    Service for business-focused analytics templates
    """
    
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.ts_service = TimeSeriesService(df)
        self.stats_service = StatisticalAnalysisService(df)
    
    def revenue_trend_analysis(self, date_column: str, value_column: str, 
                              forecast_periods: int = 30, frequency: Optional[str] = None) -> Dict[str, Any]:
        """
        Revenue Trend Analysis Template
        """
        try:
            # Prepare time series
            ts_df = self.ts_service.prepare_time_series(value_column, date_column, frequency)
            
            # Calculate key metrics
            total_revenue = ts_df[value_column].sum()
            avg_revenue = ts_df[value_column].mean()
            revenue_growth = self._calculate_growth_rate(ts_df[value_column])
            
            # Trend analysis
            trend_results = self.ts_service.analyze_trend(value_column, date_column, frequency)
            
            # Forecast
            forecast_results = self.ts_service.forecast(
                value_column, date_column, forecast_periods, frequency, "auto"
            )
            
            # Create comprehensive visualization
            fig = go.Figure()
            
            # Historical data
            fig.add_trace(go.Scatter(
                x=ts_df.index,
                y=ts_df[value_column],
                mode='lines+markers',
                name='Historical Revenue',
                line=dict(color='blue', width=2)
            ))
            
            # Trend line
            if 'trend_line' in trend_results:
                fig.add_trace(go.Scatter(
                    x=trend_results['dates'],
                    y=trend_results['trend_line'],
                    mode='lines',
                    name='Trend Line',
                    line=dict(color='red', dash='dash')
                ))
            
            # Forecast
            if 'forecast_dates' in forecast_results and 'forecast_values' in forecast_results:
                fig.add_trace(go.Scatter(
                    x=pd.to_datetime(forecast_results['forecast_dates']),
                    y=forecast_results['forecast_values'],
                    mode='lines+markers',
                    name='Revenue Forecast',
                    line=dict(color='green', dash='dot')
                ))
                
                # Confidence intervals if available
                if 'lower_bound' in forecast_results and 'upper_bound' in forecast_results:
                    fig.add_trace(go.Scatter(
                        x=pd.to_datetime(forecast_results['forecast_dates']),
                        y=forecast_results['lower_bound'],
                        mode='lines',
                        line=dict(color='rgba(0,100,80,0)'),
                        showlegend=False
                    ))
                    fig.add_trace(go.Scatter(
                        x=pd.to_datetime(forecast_results['forecast_dates']),
                        y=forecast_results['upper_bound'],
                        mode='lines',
                        fill='tonexty',
                        fillcolor='rgba(0,100,80,0.2)',
                        line=dict(color='rgba(0,100,80,0)'),
                        name='Confidence Interval'
                    ))
            
            fig.update_layout(
                title='Revenue Trend Analysis & Forecast',
                xaxis_title='Date',
                yaxis_title='Revenue',
                hovermode='x unified',
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
            )
            
            chart_data = {
                "plotly_json": json.loads(fig.to_json()),
                "image_base64": self._get_image_base64(fig)
            }
            
            # Key insights
            insights = [
                f"Total revenue: ${total_revenue:,.2f}",
                f"Average revenue: ${avg_revenue:,.2f}",
                f"Growth rate: {revenue_growth:.2f}%",
                f"Trend direction: {trend_results.get('trend_direction', 'Unknown')}",
                f"Forecasted revenue for next {forecast_periods} periods: ${sum(forecast_results.get('forecast_values', [0])):,.2f}"
            ]
            
            return {
                "template_type": "revenue_trend",
                "key_metrics": {
                    "total_revenue": float(total_revenue),
                    "average_revenue": float(avg_revenue),
                    "growth_rate": float(revenue_growth),
                    "trend_direction": trend_results.get('trend_direction', 'Unknown'),
                    "forecasted_total": float(sum(forecast_results.get('forecast_values', [0])))
                },
                "insights": insights,
                "chart_data": chart_data,
                "trend_analysis": trend_results,
                "forecast_analysis": forecast_results
            }
            
        except Exception as e:
            logger.error(f"Error in revenue trend analysis: {e}")
            raise ValueError(f"Revenue trend analysis failed: {str(e)}")
    
    def sales_forecasting_template(self, date_column: str, value_column: str, 
                                  forecast_periods: int = 30, frequency: Optional[str] = None) -> Dict[str, Any]:
        """
        Weekly Sales Forecasting Template
        """
        try:
            # Prepare time series
            ts_df = self.ts_service.prepare_time_series(value_column, date_column, frequency)
            
            # Seasonality analysis
            seasonality_results = self.ts_service.analyze_seasonality(value_column, date_column, frequency)
            
            # Forecast with multiple methods for comparison
            methods = ['arima', 'exponential_smoothing']
            forecasts = {}
            
            for method in methods:
                try:
                    forecast = self.ts_service.forecast(
                        value_column, date_column, forecast_periods, frequency, method
                    )
                    forecasts[method] = forecast
                except Exception as e:
                    logger.warning(f"Forecast method {method} failed: {e}")
            
            # Create visualization
            fig = go.Figure()
            
            # Historical data
            fig.add_trace(go.Scatter(
                x=ts_df.index,
                y=ts_df[value_column],
                mode='lines+markers',
                name='Historical Sales',
                line=dict(color='blue')
            ))
            
            # Multiple forecasts
            colors = ['red', 'green', 'orange', 'purple']
            for i, (method, forecast) in enumerate(forecasts.items()):
                if 'forecast_dates' in forecast and 'forecast_values' in forecast:
                    fig.add_trace(go.Scatter(
                        x=pd.to_datetime(forecast['forecast_dates']),
                        y=forecast['forecast_values'],
                        mode='lines+markers',
                        name=f'{method.title()} Forecast',
                        line=dict(color=colors[i % len(colors)], dash='dash')
                    ))
            
            fig.update_layout(
                title='Sales Forecasting Analysis',
                xaxis_title='Date',
                yaxis_title='Sales',
                hovermode='x unified'
            )
            
            chart_data = {
                "plotly_json": json.loads(fig.to_json()),
                "image_base64": self._get_image_base64(fig)
            }
            
            # Calculate forecast accuracy metrics
            recent_data = ts_df[value_column].tail(min(30, len(ts_df) // 4))
            forecast_accuracy = {
                "mean_absolute_error": float(recent_data.std()),
                "coefficient_of_variation": float(recent_data.std() / recent_data.mean() * 100)
            }
            
            return {
                "template_type": "sales_forecast",
                "forecasts": forecasts,
                "seasonality_analysis": seasonality_results,
                "forecast_accuracy": forecast_accuracy,
                "chart_data": chart_data,
                "recommendations": self._generate_sales_recommendations(forecasts, seasonality_results)
            }
            
        except Exception as e:
            logger.error(f"Error in sales forecasting: {e}")
            raise ValueError(f"Sales forecasting failed: {str(e)}")
    
    def demand_planning_template(self, date_column: str, value_column: str, 
                                forecast_periods: int = 30, frequency: Optional[str] = None) -> Dict[str, Any]:
        """
        Demand Planning for Inventory Template
        """
        try:
            # Prepare time series
            ts_df = self.ts_service.prepare_time_series(value_column, date_column, frequency)
            
            # Calculate demand metrics
            avg_demand = ts_df[value_column].mean()
            demand_volatility = ts_df[value_column].std()
            max_demand = ts_df[value_column].max()
            min_demand = ts_df[value_column].min()
            
            # Forecast demand
            forecast_results = self.ts_service.forecast(
                value_column, date_column, forecast_periods, frequency, "auto"
            )
            
            # Calculate safety stock recommendations
            service_level = 0.95  # 95% service level
            lead_time = 7  # Assume 7-day lead time
            safety_stock = self._calculate_safety_stock(ts_df[value_column], service_level, lead_time)
            
            # Reorder point calculation
            reorder_point = avg_demand * lead_time + safety_stock
            
            # Create demand visualization
            fig = go.Figure()
            
            # Historical demand
            fig.add_trace(go.Scatter(
                x=ts_df.index,
                y=ts_df[value_column],
                mode='lines+markers',
                name='Historical Demand',
                line=dict(color='blue')
            ))
            
            # Forecast
            if 'forecast_dates' in forecast_results and 'forecast_values' in forecast_results:
                fig.add_trace(go.Scatter(
                    x=pd.to_datetime(forecast_results['forecast_dates']),
                    y=forecast_results['forecast_values'],
                    mode='lines+markers',
                    name='Demand Forecast',
                    line=dict(color='red', dash='dash')
                ))
            
            # Add safety stock line
            fig.add_hline(
                y=safety_stock,
                line_dash="dot",
                line_color="orange",
                annotation_text="Safety Stock Level"
            )
            
            # Add reorder point line
            fig.add_hline(
                y=reorder_point,
                line_dash="dot",
                line_color="red",
                annotation_text="Reorder Point"
            )
            
            fig.update_layout(
                title='Demand Planning Analysis',
                xaxis_title='Date',
                yaxis_title='Demand',
                hovermode='x unified'
            )
            
            chart_data = {
                "plotly_json": json.loads(fig.to_json()),
                "image_base64": self._get_image_base64(fig)
            }
            
            return {
                "template_type": "demand_planning",
                "demand_metrics": {
                    "average_demand": float(avg_demand),
                    "demand_volatility": float(demand_volatility),
                    "max_demand": float(max_demand),
                    "min_demand": float(min_demand),
                    "safety_stock": float(safety_stock),
                    "reorder_point": float(reorder_point)
                },
                "forecast_results": forecast_results,
                "chart_data": chart_data,
                "inventory_recommendations": self._generate_inventory_recommendations(
                    avg_demand, safety_stock, reorder_point, forecast_results
                )
            }
            
        except Exception as e:
            logger.error(f"Error in demand planning: {e}")
            raise ValueError(f"Demand planning failed: {str(e)}")
    
    def _calculate_growth_rate(self, series: pd.Series) -> float:
        """Calculate growth rate between first and last values"""
        if len(series) < 2:
            return 0.0
        first_val = series.iloc[0]
        last_val = series.iloc[-1]
        if first_val == 0:
            return 0.0
        return ((last_val - first_val) / first_val) * 100
    
    def _calculate_safety_stock(self, demand_series: pd.Series, service_level: float, lead_time: int) -> float:
        """Calculate safety stock based on demand variability"""
        from scipy import stats
        z_score = stats.norm.ppf(service_level)
        demand_std = demand_series.std()
        return z_score * demand_std * np.sqrt(lead_time)
    
    def _generate_sales_recommendations(self, forecasts: Dict, seasonality: Dict) -> List[str]:
        """Generate sales recommendations based on analysis"""
        recommendations = []
        
        if seasonality.get('is_stationary', False):
            recommendations.append("Sales data shows stable patterns - good for reliable forecasting")
        else:
            recommendations.append("Sales data shows trends - consider seasonal adjustments")
        
        if len(forecasts) > 1:
            recommendations.append("Multiple forecasting methods available - consider ensemble approach")
        
        recommendations.append("Monitor forecast accuracy and adjust models monthly")
        
        return recommendations
    
    def _generate_inventory_recommendations(self, avg_demand: float, safety_stock: float, 
                                          reorder_point: float, forecast: Dict) -> List[str]:
        """Generate inventory management recommendations"""
        recommendations = []
        
        recommendations.append(f"Maintain safety stock of {safety_stock:.0f} units")
        recommendations.append(f"Reorder when inventory reaches {reorder_point:.0f} units")
        
        if forecast.get('forecast_values'):
            max_forecast = max(forecast['forecast_values'])
            if max_forecast > avg_demand * 1.5:
                recommendations.append("Prepare for demand spike - consider increasing safety stock")
        
        recommendations.append("Review demand patterns monthly and adjust safety stock accordingly")
        
        return recommendations
    
    def _get_image_base64(self, fig) -> str:
        """Convert plotly figure to base64 image"""
        try:
            import base64
            img_bytes = fig.to_image(format="png", width=800, height=600)
            img_base64 = base64.b64encode(img_bytes).decode()
            return img_base64
        except Exception as e:
            logger.warning(f"Failed to generate image: {e}")
            return ""
