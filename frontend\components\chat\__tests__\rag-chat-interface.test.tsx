import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import RAGChatInterface from '../rag-chat-interface';

// Mock dependencies
jest.mock('../../lib/api', () => ({
  createApiInstance: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn()
  }))
}));

jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: { id: 'test-user', email: '<EMAIL>' }
    }
  }),
  SessionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const mockDataset = {
  id: 1,
  name: 'Test Dataset',
  file_type: 'csv',
  content_type: 'tabular',
  row_count: 100,
  total_chunks: 50
};

const defaultProps = {
  selectedDataset: mockDataset,
  onDatasetChange: jest.fn(),
  availableDatasets: [mockDataset]
};

describe('RAGChatInterface', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });
  });

  it('renders without crashing', () => {
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    expect(screen.getByText(/AI Assistant/i)).toBeInTheDocument();
  });

  it('shows welcome message when no dataset selected', () => {
    render(
      <SessionProvider session={null}>
        <RAGChatInterface 
          {...defaultProps} 
          selectedDataset={null}
          availableDatasets={[]}
        />
      </SessionProvider>
    );
    
    expect(screen.getByText(/Welcome to RAG Chat/i)).toBeInTheDocument();
  });

  it('validates input correctly', async () => {
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    const input = screen.getByPlaceholderText(/Ask me anything/i);
    const sendButton = screen.getByLabelText(/Send message/i);
    
    // Test empty input
    fireEvent.click(sendButton);
    // Should not send message with empty input
    
    // Test XSS attempt
    fireEvent.change(input, { target: { value: '<script>alert("xss")</script>' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Input contains potentially harmful content/i)).toBeInTheDocument();
    });
  });

  it('handles rate limiting', async () => {
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    const input = screen.getByPlaceholderText(/Ask me anything/i);
    const sendButton = screen.getByLabelText(/Send message/i);
    
    // Simulate rapid requests
    for (let i = 0; i < 35; i++) {
      fireEvent.change(input, { target: { value: `Test message ${i}` } });
      fireEvent.click(sendButton);
    }
    
    await waitFor(() => {
      expect(screen.getByText(/Rate limit exceeded/i)).toBeInTheDocument();
    });
  });

  it('shows offline indicator when offline', () => {
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false,
    });
    
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    const input = screen.getByPlaceholderText(/Ask me anything/i);
    const sendButton = screen.getByLabelText(/Send message/i);
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    expect(screen.getByText(/You appear to be offline/i)).toBeInTheDocument();
  });

  it('handles keyboard shortcuts', () => {
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    const input = screen.getByPlaceholderText(/Ask me anything/i);
    
    // Test Ctrl+K to focus input
    fireEvent.keyDown(window, { key: 'k', ctrlKey: true });
    expect(input).toHaveFocus();
  });

  it('manages memory correctly', async () => {
    const { rerender } = render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    // Simulate many messages to trigger memory management
    const manyMessages = Array.from({ length: 150 }, (_, i) => ({
      id: `msg-${i}`,
      type: 'user' as const,
      content: `Message ${i}`,
      timestamp: new Date()
    }));
    
    // This would be tested with a custom hook or by exposing the state
    // In a real implementation, you'd test the memory management logic
    expect(true).toBe(true); // Placeholder
  });

  it('persists conversation to localStorage', async () => {
    const setItemSpy = jest.spyOn(Storage.prototype, 'setItem');
    
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    const input = screen.getByPlaceholderText(/Ask me anything/i);
    fireEvent.change(input, { target: { value: 'Test message' } });
    
    // Wait for persistence
    await waitFor(() => {
      expect(setItemSpy).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  it('shows error boundary on component crash', () => {
    // Mock console.error to avoid noise in tests
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    const ThrowError = () => {
      throw new Error('Test error');
    };
    
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps}>
          <ThrowError />
        </RAGChatInterface>
      </SessionProvider>
    );
    
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });

  it('handles search method changes', () => {
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    const searchMethodSelect = screen.getByLabelText(/Select search method/i);
    
    fireEvent.change(searchMethodSelect, { target: { value: 'semantic' } });
    expect(searchMethodSelect).toHaveValue('semantic');
    
    fireEvent.change(searchMethodSelect, { target: { value: 'hybrid' } });
    expect(searchMethodSelect).toHaveValue('hybrid');
  });

  it('shows advanced settings panel', () => {
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    const advancedButton = screen.getByTitle(/Advanced search settings/i);
    fireEvent.click(advancedButton);
    
    expect(screen.getByText(/Advanced Search Configuration/i)).toBeInTheDocument();
  });

  it('exports conversation correctly', () => {
    // Mock URL.createObjectURL
    global.URL.createObjectURL = jest.fn(() => 'mock-url');
    global.URL.revokeObjectURL = jest.fn();
    
    const createElementSpy = jest.spyOn(document, 'createElement');
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const removeChildSpy = jest.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(
      <SessionProvider session={null}>
        <RAGChatInterface {...defaultProps} />
      </SessionProvider>
    );
    
    // Add a message first
    const input = screen.getByPlaceholderText(/Ask me anything/i);
    fireEvent.change(input, { target: { value: 'Test message' } });
    
    const exportButton = screen.getByTitle(/Export conversation/i);
    fireEvent.click(exportButton);
    
    expect(createElementSpy).toHaveBeenCalledWith('a');
    
    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
    removeChildSpy.mockRestore();
  });
});
