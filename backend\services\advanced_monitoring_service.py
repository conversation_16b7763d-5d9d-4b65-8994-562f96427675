"""
Advanced Enterprise Monitoring and Observability Service for AIthentiq
Features: Metrics Collection, Distributed Tracing, Alerting, Performance Analytics
"""

import os
import json
import logging
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import redis
import uuid
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class MetricType(Enum):
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"

class AlertSeverity(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class Metric:
    name: str
    value: float
    metric_type: MetricType
    labels: Dict[str, str]
    timestamp: datetime
    tenant_id: Optional[str] = None

@dataclass
class Alert:
    id: str
    name: str
    severity: AlertSeverity
    message: str
    tenant_id: Optional[str]
    triggered_at: datetime
    resolved_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None

class AdvancedMonitoringService:
    """Enterprise-grade monitoring and observability"""
    
    def __init__(self):
        self.metrics_buffer = deque(maxlen=10000)
        self.alerts_buffer = deque(maxlen=1000)
        self.alert_rules = {}
        self.redis_client = self._init_redis()
        self.monitoring_active = True
        self.start_time = time.time()
        
        # Performance thresholds
        self.thresholds = {
            "cpu_warning": 80,
            "cpu_critical": 95,
            "memory_warning": 80,
            "memory_critical": 95,
            "disk_warning": 85,
            "disk_critical": 95,
            "response_time_warning": 2000,  # 2 seconds
            "response_time_critical": 5000  # 5 seconds
        }
        
        # Setup default alert rules
        self._setup_default_alerts()
        
        # Start background monitoring
        self._start_system_monitoring()
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis for metrics storage"""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/2")
            client = redis.from_url(redis_url, decode_responses=True)
            client.ping()
            logger.info("Redis monitoring storage initialized")
            return client
        except Exception as e:
            logger.warning(f"Redis monitoring storage unavailable: {e}")
            return None
    
    def record_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType = MetricType.GAUGE,
        labels: Dict[str, str] = None,
        tenant_id: str = None
    ):
        """Record a metric with automatic alerting"""
        metric = Metric(
            name=name,
            value=value,
            metric_type=metric_type,
            labels=labels or {},
            timestamp=datetime.now(timezone.utc),
            tenant_id=tenant_id
        )
        
        self.metrics_buffer.append(metric)
        
        # Store in Redis for persistence and analytics
        if self.redis_client:
            try:
                metric_key = f"aithentiq:metrics:{name}:{int(time.time())}"
                metric_data = asdict(metric)
                metric_data['timestamp'] = metric_data['timestamp'].isoformat()
                self.redis_client.setex(metric_key, 86400, json.dumps(metric_data))
                
                # Store in time series for analytics
                ts_key = f"aithentiq:timeseries:{name}"
                self.redis_client.zadd(ts_key, {json.dumps(metric_data): time.time()})
                
            except Exception as e:
                logger.error(f"Failed to store metric in Redis: {e}")
        
        # Check alert rules
        self._check_alert_rules(metric)
    
    def increment_counter(self, name: str, labels: Dict[str, str] = None, tenant_id: str = None):
        """Increment a counter metric"""
        self.record_metric(name, 1, MetricType.COUNTER, labels, tenant_id)
    
    def record_timer(self, name: str, duration_ms: float, labels: Dict[str, str] = None, tenant_id: str = None):
        """Record a timer metric with automatic SLA checking"""
        self.record_metric(name, duration_ms, MetricType.TIMER, labels, tenant_id)
        
        # Check response time SLAs
        if duration_ms > self.thresholds["response_time_critical"]:
            self._trigger_custom_alert(
                f"critical_response_time_{name}",
                AlertSeverity.CRITICAL,
                f"Critical response time for {name}: {duration_ms}ms",
                tenant_id,
                {"duration_ms": duration_ms, "operation": name}
            )
        elif duration_ms > self.thresholds["response_time_warning"]:
            self._trigger_custom_alert(
                f"slow_response_time_{name}",
                AlertSeverity.WARNING,
                f"Slow response time for {name}: {duration_ms}ms",
                tenant_id,
                {"duration_ms": duration_ms, "operation": name}
            )
    
    def _setup_default_alerts(self):
        """Setup default system alert rules"""
        self.add_alert_rule(
            "high_cpu_usage",
            "system.cpu.usage_percent",
            lambda x: x > self.thresholds["cpu_warning"],
            AlertSeverity.WARNING,
            "High CPU usage detected: {value}%"
        )
        
        self.add_alert_rule(
            "critical_cpu_usage",
            "system.cpu.usage_percent",
            lambda x: x > self.thresholds["cpu_critical"],
            AlertSeverity.CRITICAL,
            "Critical CPU usage detected: {value}%"
        )
        
        self.add_alert_rule(
            "high_memory_usage",
            "system.memory.usage_percent",
            lambda x: x > self.thresholds["memory_warning"],
            AlertSeverity.WARNING,
            "High memory usage detected: {value}%"
        )
        
        self.add_alert_rule(
            "critical_memory_usage",
            "system.memory.usage_percent",
            lambda x: x > self.thresholds["memory_critical"],
            AlertSeverity.CRITICAL,
            "Critical memory usage detected: {value}%"
        )
        
        self.add_alert_rule(
            "low_disk_space",
            "system.disk.usage_percent",
            lambda x: x > self.thresholds["disk_warning"],
            AlertSeverity.WARNING,
            "Low disk space: {value}% used"
        )
        
        self.add_alert_rule(
            "critical_disk_space",
            "system.disk.usage_percent",
            lambda x: x > self.thresholds["disk_critical"],
            AlertSeverity.CRITICAL,
            "Critical disk space: {value}% used"
        )
    
    def add_alert_rule(
        self,
        name: str,
        metric_name: str,
        condition: Callable[[float], bool],
        severity: AlertSeverity = AlertSeverity.WARNING,
        message_template: str = "Alert triggered for {metric_name}",
        cooldown_seconds: int = 300
    ):
        """Add a custom alert rule"""
        self.alert_rules[name] = {
            "metric_name": metric_name,
            "condition": condition,
            "severity": severity,
            "message_template": message_template,
            "last_triggered": None,
            "cooldown_seconds": cooldown_seconds
        }
    
    def _check_alert_rules(self, metric: Metric):
        """Check if metric triggers any alerts"""
        for rule_name, rule in self.alert_rules.items():
            if rule["metric_name"] == metric.name:
                try:
                    if rule["condition"](metric.value):
                        # Check cooldown
                        now = datetime.now(timezone.utc)
                        last_triggered = rule.get("last_triggered")
                        
                        if (not last_triggered or 
                            (now - last_triggered).total_seconds() > rule["cooldown_seconds"]):
                            
                            self._trigger_alert(rule_name, rule, metric)
                            rule["last_triggered"] = now
                            
                except Exception as e:
                    logger.error(f"Error checking alert rule {rule_name}: {e}")
    
    def _trigger_alert(self, rule_name: str, rule: Dict[str, Any], metric: Metric):
        """Trigger an alert based on rule"""
        alert = Alert(
            id=str(uuid.uuid4()),
            name=rule_name,
            severity=rule["severity"],
            message=rule["message_template"].format(
                metric_name=metric.name,
                value=metric.value,
                tenant_id=metric.tenant_id or "system"
            ),
            tenant_id=metric.tenant_id,
            triggered_at=datetime.now(timezone.utc),
            metadata={
                "metric_value": metric.value,
                "metric_labels": metric.labels,
                "rule_name": rule_name,
                "threshold_breached": True
            }
        )
        
        self._store_alert(alert)
    
    def _trigger_custom_alert(
        self,
        name: str,
        severity: AlertSeverity,
        message: str,
        tenant_id: str = None,
        metadata: Dict[str, Any] = None
    ):
        """Trigger a custom alert"""
        alert = Alert(
            id=str(uuid.uuid4()),
            name=name,
            severity=severity,
            message=message,
            tenant_id=tenant_id,
            triggered_at=datetime.now(timezone.utc),
            metadata=metadata or {}
        )
        
        self._store_alert(alert)
    
    def _store_alert(self, alert: Alert):
        """Store alert in buffer and Redis"""
        self.alerts_buffer.append(alert)
        
        # Store in Redis
        if self.redis_client:
            try:
                alert_key = f"aithentiq:alerts:{alert.id}"
                alert_data = asdict(alert)
                alert_data['triggered_at'] = alert_data['triggered_at'].isoformat()
                if alert_data['resolved_at']:
                    alert_data['resolved_at'] = alert_data['resolved_at'].isoformat()
                
                self.redis_client.setex(alert_key, 604800, json.dumps(alert_data))  # 7 days
                
                # Add to active alerts set
                if not alert.resolved_at:
                    self.redis_client.sadd("aithentiq:alerts:active", alert.id)
                
            except Exception as e:
                logger.error(f"Failed to store alert in Redis: {e}")
        
        # Log alert
        log_level = logging.CRITICAL if alert.severity == AlertSeverity.CRITICAL else logging.WARNING
        logger.log(log_level, f"Alert triggered: {alert.name} - {alert.message}")
    
    def _start_system_monitoring(self):
        """Start comprehensive system monitoring"""
        def monitor_system():
            while self.monitoring_active:
                try:
                    # CPU metrics
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.record_metric("system.cpu.usage_percent", cpu_percent)
                    
                    # Memory metrics
                    memory = psutil.virtual_memory()
                    self.record_metric("system.memory.usage_percent", memory.percent)
                    self.record_metric("system.memory.available_gb", memory.available / (1024**3))
                    self.record_metric("system.memory.used_gb", memory.used / (1024**3))
                    
                    # Disk metrics
                    disk = psutil.disk_usage('/')
                    disk_usage_percent = (disk.used / disk.total) * 100
                    self.record_metric("system.disk.usage_percent", disk_usage_percent)
                    self.record_metric("system.disk.free_gb", disk.free / (1024**3))
                    self.record_metric("system.disk.used_gb", disk.used / (1024**3))
                    
                    # Network metrics
                    try:
                        network = psutil.net_io_counters()
                        self.record_metric("system.network.bytes_sent", network.bytes_sent, MetricType.COUNTER)
                        self.record_metric("system.network.bytes_recv", network.bytes_recv, MetricType.COUNTER)
                    except:
                        pass
                    
                    # Process metrics
                    process_count = len(psutil.pids())
                    self.record_metric("system.processes.count", process_count)
                    
                    time.sleep(30)  # Monitor every 30 seconds
                    
                except Exception as e:
                    logger.error(f"System monitoring error: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
        logger.info("System monitoring started")

# Global advanced monitoring instance
monitoring_service = AdvancedMonitoringService()
