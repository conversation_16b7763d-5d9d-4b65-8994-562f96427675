'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import {
  Brain,
  TrendingUp,
  Shield,
  Target,
  Lightbulb,
  RefreshCw,
  AlertTriangle,
  BarChart3,
  Activity,
  Zap,
  Eye,
  Clock,
  Info,
  HelpCircle
} from 'lucide-react';

interface AdvancedInsights {
  user_id: string;
  generated_at: string;
  ensemble_prediction: {
    overall_score: number;
    confidence_level: number;
    prediction_horizon_days: number;
    model_consensus: {
      consensus_score: number;
      consensus_level: string;
    };
  };
  confidence_intervals: {
    overall_confidence: number;
    confidence_level: string;
    prediction_bounds: {
      lower_bound: number;
      upper_bound: number;
    };
  };
  behavioral_patterns: {
    pattern_type: string;
    total_patterns_detected: number;
    patterns: Array<{
      type: string;
      description: string;
      confidence: number;
      impact: string;
    }>;
    behavioral_score: number;
    pattern_stability: number;
  };
  trend_predictions: {
    trend_type: string;
    total_predictions: number;
    predictions: Array<{
      metric: string;
      current_value: number;
      predicted_value: number;
      trend_direction: string;
      confidence: number;
      time_horizon: string;
    }>;
    overall_trend: string;
    trend_reliability: number;
  };
  smart_recommendations: Array<{
    type: string;
    priority: string;
    title: string;
    description: string;
    actions: string[];
    expected_impact: string;
    implementation_effort: string;
  }>;
  advanced_metrics: {
    prediction_stability: number;
    feature_importance: Record<string, number>;
    uncertainty_quantification: {
      overall_uncertainty: number;
      uncertainty_level: string;
      prediction_reliability: number;
    };
  };
}

// Tooltip component for information icons
const InfoTooltip = ({ content, title }: { content: string; title: string }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="relative inline-block">
      <button
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="ml-1 text-gray-400 hover:text-gray-600"
      >
        <Info className="h-4 w-4" />
      </button>
      {showTooltip && (
        <div className="absolute z-10 w-64 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg -top-2 left-6">
          <div className="font-semibold mb-1">{title}</div>
          <div>{content}</div>
          <div className="absolute top-3 -left-1 w-2 h-2 bg-gray-900 rotate-45"></div>
        </div>
      )}
    </div>
  );
};

interface AdvancedInsightsDashboardProps {
  datasetId?: number | null;
}

export default function AdvancedInsightsDashboard({ datasetId }: AdvancedInsightsDashboardProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [insights, setInsights] = useState<AdvancedInsights | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Create simple axios instance for predictive analytics (no API key needed)
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    timeout: 30000,
  });

  useEffect(() => {
    if (session?.user?.id) {
      fetchAdvancedInsights(session.user.id);
    }
  }, [session?.user?.id]);

  const fetchAdvancedInsights = async (userId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Include dataset ID in the request if available
      const url = datasetId
        ? `/predictive/advanced-insights/${userId}?dataset_id=${datasetId}`
        : `/predictive/advanced-insights/${userId}`;

      const response = await api.get(url);
      setInsights(response.data);

    } catch (err: any) {
      console.error('Error fetching advanced insights:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching advanced insights');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    if (!session?.user?.id) return;
    
    setRefreshing(true);
    await fetchAdvancedInsights(session.user.id);
    setRefreshing(false);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getTrendColor = (direction: string) => {
    switch (direction) {
      case 'increasing': return 'text-green-600';
      case 'decreasing': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
          <p className="text-lg font-medium">Error Loading Advanced Insights</p>
          <p className="text-sm mt-2">{error}</p>
          <button
            onClick={refreshData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!insights) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-gray-600">
          <Brain className="h-12 w-12 mx-auto mb-4" />
          <p className="text-lg font-medium">No Advanced Insights Available</p>
          <p className="text-sm mt-2">Unable to generate comprehensive insights at this time.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="h-8 w-8 text-purple-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Advanced AI Insights</h2>
            <p className="text-sm text-gray-600">Cutting-edge predictive analytics with ensemble modeling</p>
          </div>
        </div>
        <button
          onClick={refreshData}
          disabled={refreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Ensemble Prediction Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center">
                <p className="text-sm font-medium text-gray-600">Ensemble Score</p>
                <InfoTooltip
                  title="Ensemble Score"
                  content="Combined prediction from multiple AI models (Risk, Churn, Anomaly) weighted by their performance. Higher scores indicate higher overall risk or concern level."
                />
              </div>
              <p className="text-3xl font-bold text-purple-600">
                {(insights.ensemble_prediction.overall_score * 100).toFixed(1)}%
              </p>
            </div>
            <Target className="h-8 w-8 text-purple-600" />
          </div>
          <p className="text-xs text-gray-500 mt-2">
            {insights.ensemble_prediction.prediction_horizon_days}-day prediction
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center">
                <p className="text-sm font-medium text-gray-600">Model Consensus</p>
                <InfoTooltip
                  title="Model Consensus"
                  content="Measures how much the different AI models agree with each other. High consensus (>80%) means all models predict similar outcomes, indicating higher reliability."
                />
              </div>
              <p className="text-3xl font-bold text-blue-600">
                {(insights.ensemble_prediction.model_consensus.consensus_score * 100).toFixed(1)}%
              </p>
            </div>
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConfidenceColor(insights.ensemble_prediction.model_consensus.consensus_score)}`}>
            {insights.ensemble_prediction.model_consensus.consensus_level.toUpperCase()}
          </span>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center">
                <p className="text-sm font-medium text-gray-600">Confidence Level</p>
                <InfoTooltip
                  title="Confidence Level"
                  content="Measures how certain the AI models are about their predictions. High confidence (>80%) means the models are very sure about the results, while low confidence suggests more uncertainty."
                />
              </div>
              <p className="text-3xl font-bold text-green-600">
                {(insights.confidence_intervals.overall_confidence * 100).toFixed(1)}%
              </p>
            </div>
            <BarChart3 className="h-8 w-8 text-green-600" />
          </div>
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConfidenceColor(insights.confidence_intervals.overall_confidence)}`}>
            {insights.confidence_intervals.confidence_level.toUpperCase()}
          </span>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center">
                <p className="text-sm font-medium text-gray-600">Uncertainty</p>
                <InfoTooltip
                  title="Uncertainty"
                  content="Quantifies how much doubt exists in the predictions. Lower uncertainty means more reliable predictions. High uncertainty suggests the models need more data or the situation is inherently unpredictable."
                />
              </div>
              <p className="text-3xl font-bold text-orange-600">
                {(insights.advanced_metrics.uncertainty_quantification.overall_uncertainty * 100).toFixed(1)}%
              </p>
            </div>
            <Eye className="h-8 w-8 text-orange-600" />
          </div>
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConfidenceColor(1 - insights.advanced_metrics.uncertainty_quantification.overall_uncertainty)}`}>
            {insights.advanced_metrics.uncertainty_quantification.uncertainty_level.toUpperCase()}
          </span>
        </div>
      </div>

      {/* Behavioral Patterns */}
      {insights.behavioral_patterns.patterns.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Activity className="h-5 w-5 mr-2 text-blue-600" />
            Behavioral Patterns Detected
            <InfoTooltip
              title="Behavioral Patterns"
              content="AI-detected patterns in user behavior including temporal usage patterns, feature preferences, and engagement levels. These help predict future behavior and optimize user experience."
            />
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {insights.behavioral_patterns.patterns.map((pattern, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-900 capitalize">
                    {pattern.type.replace('_', ' ')}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getConfidenceColor(pattern.confidence)}`}>
                    {(pattern.confidence * 100).toFixed(0)}%
                  </span>
                </div>
                <p className="text-sm text-gray-600">{pattern.description}</p>
                <div className="mt-2">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    pattern.impact === 'high' ? 'bg-red-100 text-red-800' :
                    pattern.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {pattern.impact.toUpperCase()} IMPACT
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Trend Predictions */}
      {insights.trend_predictions.predictions.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
            Future Trend Predictions
            <InfoTooltip
              title="Future Trend Predictions"
              content="AI forecasts of how user metrics will change over the next 7-30 days based on historical patterns and current behavior. Includes confidence levels for each prediction."
            />
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {insights.trend_predictions.predictions.map((prediction, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-900 capitalize">
                    {prediction.metric.replace('_', ' ')}
                  </span>
                  <span className={`text-sm font-semibold ${getTrendColor(prediction.trend_direction)}`}>
                    {prediction.trend_direction === 'increasing' ? '↗' : 
                     prediction.trend_direction === 'decreasing' ? '↘' : '→'}
                  </span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Current:</span>
                    <span className="font-medium">{prediction.current_value.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Predicted:</span>
                    <span className="font-medium">{prediction.predicted_value.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Horizon:</span>
                    <span className="font-medium">{prediction.time_horizon}</span>
                  </div>
                </div>
                <div className="mt-2">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getConfidenceColor(prediction.confidence)}`}>
                    {(prediction.confidence * 100).toFixed(0)}% CONFIDENCE
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Smart Recommendations */}
      {insights.smart_recommendations.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Lightbulb className="h-5 w-5 mr-2 text-yellow-600" />
            AI-Powered Recommendations
            <InfoTooltip
              title="AI-Powered Recommendations"
              content="Intelligent suggestions generated by analyzing patterns, trends, and predictions. These actionable recommendations help optimize performance, reduce risks, and improve user experience based on AI insights."
            />
          </h3>
          <div className="space-y-4">
            {insights.smart_recommendations.map((rec, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-sm font-semibold text-gray-900">{rec.title}</h4>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full border ${getPriorityColor(rec.priority)}`}>
                        {rec.priority.toUpperCase()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <h5 className="text-xs font-semibold text-gray-700 uppercase tracking-wide">Recommended Actions:</h5>
                  <ul className="space-y-1">
                    {rec.actions.map((action, actionIndex) => (
                      <li key={actionIndex} className="text-sm text-gray-600 flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex items-center justify-between mt-3 pt-3 border-t">
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>Impact: <span className="font-medium">{rec.expected_impact}</span></span>
                    <span>Effort: <span className="font-medium">{rec.implementation_effort}</span></span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Advanced Metrics */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Zap className="h-5 w-5 mr-2 text-purple-600" />
          Advanced Analytics Metrics
          <InfoTooltip
            title="Advanced Analytics Metrics"
            content="Sophisticated performance indicators including feature importance weights, prediction stability scores, and model reliability metrics. These provide deep insights into how the AI models are performing and which factors are most influential."
          />
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Feature Importance */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 mb-3">Feature Importance</h4>
            <div className="space-y-2">
              {Object.entries(insights.advanced_metrics.feature_importance)
                .sort(([,a], [,b]) => b - a)
                .map(([feature, importance]) => (
                <div key={feature} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">
                    {feature.replace('_', ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full" 
                        style={{ width: `${importance * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {(importance * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Prediction Stability */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 mb-3">Model Performance</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Prediction Stability</span>
                <span className="text-sm font-medium text-gray-900">
                  {(insights.advanced_metrics.prediction_stability * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Reliability Score</span>
                <span className="text-sm font-medium text-gray-900">
                  {(insights.advanced_metrics.uncertainty_quantification.prediction_reliability * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Overall Trend</span>
                <span className={`text-sm font-medium capitalize ${getTrendColor(insights.trend_predictions.overall_trend)}`}>
                  {insights.trend_predictions.overall_trend}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Generation Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>Generated: {new Date(insights.generated_at).toLocaleString()}</span>
          </div>
          <div className="flex items-center space-x-4">
            <span>Patterns: {insights.behavioral_patterns.total_patterns_detected}</span>
            <span>Predictions: {insights.trend_predictions.total_predictions}</span>
            <span>Recommendations: {insights.smart_recommendations.length}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
