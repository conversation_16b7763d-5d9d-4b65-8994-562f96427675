"""
Database models for Predictive Analytics features
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base


class UserBehaviorLog(Base):
    """Track user behavior patterns for predictive analytics"""
    __tablename__ = "user_behavior_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Action tracking
    action_type = Column(String)  # login, query, upload, download, etc.
    action_details = Column(JSON, nullable=True)  # Additional action metadata
    
    # Session tracking
    session_id = Column(String, nullable=True)
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    
    # Feature usage
    feature_used = Column(String, nullable=True)  # rag_chat, analytics, predictive, etc.
    feature_duration = Column(Integer, nullable=True)  # Time spent in seconds
    
    # Performance metrics
    response_time_ms = Column(Float, nullable=True)
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")


class RiskPrediction(Base):
    """Store user risk level predictions"""
    __tablename__ = "risk_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Risk metrics
    risk_score = Column(Float)  # 0.0 to 1.0
    risk_level = Column(String)  # low, moderate, high
    risk_factors = Column(JSON)  # List of contributing factors
    
    # Prediction details
    model_version = Column(String)
    confidence_score = Column(Float)
    prediction_horizon_days = Column(Integer, default=30)
    
    # Features used for prediction
    features_used = Column(JSON)
    feature_importance = Column(JSON, nullable=True)
    
    # Validation
    actual_outcome = Column(Boolean, nullable=True)  # For model validation
    feedback_provided = Column(Boolean, default=False)
    
    # Timestamps
    predicted_at = Column(DateTime(timezone=True), server_default=func.now())
    valid_until = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User")


class ChurnPrediction(Base):
    """Store user churn probability predictions"""
    __tablename__ = "churn_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Churn metrics
    churn_probability = Column(Float)  # 0.0 to 1.0
    churn_risk_level = Column(String)  # low, moderate, high
    days_to_churn = Column(Integer, nullable=True)  # Estimated days until churn
    
    # Contributing factors
    churn_factors = Column(JSON)  # List of factors contributing to churn risk
    retention_recommendations = Column(JSON, nullable=True)  # Suggested actions
    
    # Prediction details
    model_version = Column(String)
    confidence_score = Column(Float)
    prediction_horizon_days = Column(Integer, default=90)
    
    # User engagement metrics at prediction time
    last_login_days_ago = Column(Integer)
    queries_last_30_days = Column(Integer)
    features_used_count = Column(Integer)
    avg_session_duration = Column(Float)
    
    # Validation
    actual_churned = Column(Boolean, nullable=True)
    churn_date = Column(DateTime(timezone=True), nullable=True)
    feedback_provided = Column(Boolean, default=False)
    
    # Timestamps
    predicted_at = Column(DateTime(timezone=True), server_default=func.now())
    valid_until = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User")


class AnomalyDetection(Base):
    """Store detected user behavior anomalies"""
    __tablename__ = "anomaly_detections"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Anomaly details
    anomaly_type = Column(String)  # usage_spike, unusual_hours, suspicious_activity, etc.
    anomaly_score = Column(Float)  # Severity score
    severity_level = Column(String)  # low, medium, high, critical
    
    # Anomaly description
    description = Column(Text)
    affected_features = Column(JSON)  # List of features involved
    anomaly_patterns = Column(JSON)  # Detected patterns
    
    # Context
    baseline_behavior = Column(JSON)  # Normal behavior patterns
    anomalous_behavior = Column(JSON)  # Detected anomalous patterns
    
    # Detection details
    detection_method = Column(String)  # isolation_forest, zscore, etc.
    model_version = Column(String)
    confidence_score = Column(Float)
    
    # Status
    status = Column(String, default="detected")  # detected, investigating, resolved, false_positive
    investigated_by = Column(String, nullable=True)
    resolution_notes = Column(Text, nullable=True)
    
    # Timestamps
    detected_at = Column(DateTime(timezone=True), server_default=func.now())
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User")


class FeatureAdoptionPrediction(Base):
    """Store feature adoption likelihood predictions"""
    __tablename__ = "feature_adoption_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    
    # Feature details
    feature_name = Column(String)  # predictive_analytics, advanced_analytics, etc.
    adoption_probability = Column(Float)  # 0.0 to 1.0
    adoption_likelihood = Column(String)  # very_low, low, moderate, high, very_high
    
    # Prediction factors
    adoption_factors = Column(JSON)  # Factors that influence adoption
    barriers = Column(JSON, nullable=True)  # Potential barriers to adoption
    recommendations = Column(JSON, nullable=True)  # How to encourage adoption
    
    # User context
    current_features_used = Column(JSON)  # Features currently used by user
    user_segment = Column(String, nullable=True)  # power_user, casual_user, etc.
    experience_level = Column(String, nullable=True)  # beginner, intermediate, advanced
    
    # Prediction details
    model_version = Column(String)
    confidence_score = Column(Float)
    prediction_horizon_days = Column(Integer, default=60)
    
    # Validation
    actual_adopted = Column(Boolean, nullable=True)
    adoption_date = Column(DateTime(timezone=True), nullable=True)
    feedback_provided = Column(Boolean, default=False)
    
    # Timestamps
    predicted_at = Column(DateTime(timezone=True), server_default=func.now())
    valid_until = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User")


class UsageTrendPrediction(Base):
    """Store system and module usage trend predictions"""
    __tablename__ = "usage_trend_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Trend scope
    scope_type = Column(String)  # system, feature, user_segment, department
    scope_identifier = Column(String)  # specific feature name, department, etc.
    
    # Trend metrics
    current_usage = Column(Float)  # Current usage level
    predicted_usage = Column(Float)  # Predicted usage level
    trend_direction = Column(String)  # increasing, decreasing, stable
    trend_strength = Column(Float)  # How strong the trend is
    
    # Prediction details
    prediction_horizon_days = Column(Integer, default=30)
    confidence_interval_lower = Column(Float)
    confidence_interval_upper = Column(Float)
    
    # Contributing factors
    trend_factors = Column(JSON)  # Factors driving the trend
    seasonal_patterns = Column(JSON, nullable=True)  # Detected seasonal patterns
    external_factors = Column(JSON, nullable=True)  # External influences
    
    # Model details
    model_type = Column(String)  # arima, prophet, linear_regression, etc.
    model_version = Column(String)
    accuracy_metrics = Column(JSON, nullable=True)
    
    # Validation
    actual_usage = Column(Float, nullable=True)
    prediction_error = Column(Float, nullable=True)
    
    # Timestamps
    predicted_at = Column(DateTime(timezone=True), server_default=func.now())
    prediction_date = Column(DateTime(timezone=True))  # Date this prediction is for
    
    
class PredictionFeedback(Base):
    """Store admin feedback on predictions for model improvement"""
    __tablename__ = "prediction_feedback"
    
    id = Column(Integer, primary_key=True, index=True)
    admin_user_id = Column(String, ForeignKey("users.id"))
    
    # Prediction reference
    prediction_type = Column(String)  # risk, churn, anomaly, feature_adoption, usage_trend
    prediction_id = Column(Integer)  # ID of the specific prediction
    target_user_id = Column(String, nullable=True)  # User the prediction was about
    
    # Feedback details
    feedback_type = Column(String)  # accurate, inaccurate, partially_correct
    accuracy_rating = Column(Integer)  # 1-5 scale
    feedback_notes = Column(Text, nullable=True)
    
    # Corrections
    correct_outcome = Column(JSON, nullable=True)  # What actually happened
    suggested_improvements = Column(Text, nullable=True)
    
    # Impact
    feedback_weight = Column(Float, default=1.0)  # Weight for model training
    used_for_retraining = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    admin_user = relationship("User")
