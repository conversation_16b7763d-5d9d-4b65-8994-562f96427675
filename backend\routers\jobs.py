from fastapi import APIRouter, HTTPException, status, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from services.job_queue import job_queue

router = APIRouter(
    prefix="/jobs",
    tags=["jobs"],
    responses={404: {"description": "Not found"}},
)

class JobResponse(BaseModel):
    id: str
    type: str
    status: str
    progress: int
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: Optional[str] = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    status_message: Optional[str] = None
    stages: List[str] = []
    current_stage: Optional[str] = None

class JobListResponse(BaseModel):
    jobs: List[JobResponse]

@router.get("/{job_id}", response_model=JobResponse)
async def get_job(job_id: str):
    """
    Get a job by ID
    """
    try:
        job = job_queue.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job with ID {job_id} not found"
            )

        # Convert job to dictionary
        job_dict = job.to_dict()

        # Add additional information for better client handling
        if job.status == "failed" and job.error:
            # Split the error message if it contains technical details
            if "\n\nTechnical details:" in job.error:
                user_msg, tech_details = job.error.split("\n\nTechnical details:", 1)
                job_dict["user_error"] = user_msg.strip()
                job_dict["technical_error"] = tech_details.strip()

        return job_dict
    except Exception as e:
        # Handle unexpected errors
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving job: {str(e)}"
        )

@router.get("/", response_model=JobListResponse)
async def list_jobs(status: Optional[str] = None):
    """
    List all jobs, optionally filtered by status
    """
    jobs = job_queue.get_jobs(status)
    return {"jobs": [job.to_dict() for job in jobs]}

@router.delete("/{job_id}", response_model=Dict[str, Any])
async def cancel_job(job_id: str):
    """
    Cancel a job
    """
    success = job_queue.cancel_job(job_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not cancel job with ID {job_id}"
        )

    return {"message": f"Job {job_id} cancelled successfully"}
