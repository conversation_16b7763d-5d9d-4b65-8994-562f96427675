from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, UploadFile, File, Form, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List
import pandas as pd
import json
import os
import tempfile
import glob
from dotenv import load_dotenv
from io import String<PERSON>
from datetime import datetime, timedelta
from passlib.context import CryptContext
import uuid

# Import numpy with fallback
try:
    import numpy as np
except ImportError:
    # Fallback for numpy operations
    class MockNumpy:
        def mean(self, data):
            return sum(data) / len(data) if data else 0
    np = MockNumpy()

# Import local modules
from database import get_db, engine
import models
import schemas
try:
    from services.llm_service import llm_service
    from services import chart_service
    SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Some services not available: {e}")
    SERVICES_AVAILABLE = False
    llm_service = None
    chart_service = None
from services.document_service import document_processor
from services.email_service import email_service
# from services import stripe_service  # Keep disabled - requires Stripe configuration
from routers import api_v1, conversations, document_intelligence, jobs, feedback, saved_queries, source_attribution, monitoring
# Import predictive separately to handle any import issues
try:
    from routers import predictive
    predictive_available = True
except Exception as e:
    print(f"⚠️ Predictive router not available: {e}")
    predictive_available = False
# from routers import trust  # Temporarily disabled due to circular import
# from routers import analytics, hybrid_search  # Keep disabled due to dependency issues
# from routers import predictive  # Temporarily disabled due to statsmodels import issue



# Load environment variables
load_dotenv()

# Create database tables
models.Base.metadata.create_all(bind=engine)

# AUTO-RUN SCHEMA FIXES ON STARTUP
async def auto_fix_schema():
    """Automatically fix database schema on startup"""
    try:
        from sqlalchemy import text
        db = next(get_db())

        # Auto-add missing columns
        fixes = [
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS response_time_ms INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS token_count INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS performance_grade VARCHAR",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS hallucination_risk DECIMAL(3,2)",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS completeness_score DECIMAL(3,2)",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS source_count INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS query_name VARCHAR",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS is_bookmarked BOOLEAN DEFAULT FALSE",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS tags TEXT",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS processing_time INTEGER",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS file_type VARCHAR DEFAULT 'csv'",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS content_type VARCHAR DEFAULT 'tabular'",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS processing_status VARCHAR DEFAULT 'completed'",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS name VARCHAR",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR"
        ]

        for sql in fixes:
            try:
                db.execute(text(sql))
                db.commit()
            except:
                pass  # Ignore if column exists

        print("✅ Auto-schema fix completed")
    except Exception as e:
        print(f"⚠️ Auto-schema fix failed: {e}")

# Run auto-fix on startup
import asyncio
try:
    asyncio.create_task(auto_fix_schema())
except:
    pass

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def check_account_lockout(user: models.User) -> bool:
    """Check if account is locked due to failed login attempts"""
    if user.locked_until and user.locked_until > datetime.now():
        return True
    return False

def increment_failed_attempts(db: Session, user: models.User):
    """Increment failed login attempts and lock account if necessary"""
    user.failed_login_attempts += 1

    # Lock account after 5 failed attempts for 30 minutes
    if user.failed_login_attempts >= 5:
        user.locked_until = datetime.now() + timedelta(minutes=30)

    db.commit()

def reset_failed_attempts(db: Session, user: models.User):
    """Reset failed login attempts on successful login"""
    user.failed_login_attempts = 0
    user.locked_until = None
    db.commit()

# RBAC Dependencies
def require_admin_role(user_id: str = None, db: Session = Depends(get_db)):
    """Require admin role for access"""
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User ID required for admin access"
        )

    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    if user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    return user




app = FastAPI(title="AIthentiq API", description="API for AIthentiq - Natural language interface for data insights")

# Configure CORS - Use environment variable for frontend URL
FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://localhost:3000')
CORS_ORIGINS = os.getenv('CORS_ORIGINS', '*')

# More permissive CORS for production
if CORS_ORIGINS == '*':
    allowed_origins = ["*"]
else:
    allowed_origins = [
        FRONTEND_URL,
        "http://localhost:3000",
        "https://aithentiq-6m1a.onrender.com",
        "https://aithentiq.com",
        "https://www.aithentiq.com"
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Add OPTIONS handler for preflight requests
@app.options("/{path:path}")
async def options_handler(path: str, response: Response):
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "*"
    return {"message": "OK"}

# Add middleware to ensure CORS headers on all responses including errors
@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    try:
        response = await call_next(request)
    except Exception as e:
        # Create error response with CORS headers
        from fastapi.responses import JSONResponse
        response = JSONResponse(
            status_code=500,
            content={"detail": "Internal server error", "error": str(e)}
        )

    # Set comprehensive CORS headers
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    response.headers["Access-Control-Allow-Headers"] = "*"
    response.headers["Access-Control-Expose-Headers"] = "*"
    response.headers["Access-Control-Max-Age"] = "86400"

    return response

# Include routers
from routers import analytics  # Re-enable analytics router
app.include_router(analytics.router)
app.include_router(api_v1.router)
app.include_router(feedback.router)
app.include_router(document_intelligence.router)
# app.include_router(hybrid_search.router)  # Keep disabled due to sentence_transformers dependency
app.include_router(conversations.router)
app.include_router(saved_queries.router)
app.include_router(jobs.router)
app.include_router(source_attribution.router)
app.include_router(monitoring.router)
# app.include_router(trust.router)  # Temporarily disabled due to circular import
if predictive_available:
    app.include_router(predictive.router)
    print("✅ Predictive router mounted")
else:
    print("⚠️ Predictive router not mounted due to import issues")
# app.include_router(jobs.router)  # Temporarily disabled

# Include working connector APIs
try:
    from routers.github_connector_simple import router as github_connector_router
    app.include_router(github_connector_router)
    print("✅ GitHub connector API mounted")
except Exception as e:
    print(f"⚠️ GitHub connector API not available: {e}")

try:
    from routers.data_connectors_simple import router as data_connectors_router
    app.include_router(data_connectors_router)
    print("✅ Data connectors API mounted")
except Exception as e:
    print(f"⚠️ Data connectors API not available: {e}")

# Use advanced monitoring API (working)
try:
    from routers.monitoring import router as monitoring_router
    app.include_router(monitoring_router)
    print("✅ Advanced monitoring API mounted")
except Exception as e:
    print(f"⚠️ Advanced monitoring API not available: {e}")

# Use advanced feedback API (working)
try:
    from routers.feedback import router as feedback_router
    app.include_router(feedback_router)
    print("✅ Advanced feedback API mounted")
except Exception as e:
    print(f"⚠️ Advanced feedback API not available: {e}")

# Use simple admin API for now (advanced has middleware issues)
try:
    from routers.admin_api import router as admin_router
    app.include_router(admin_router)
    print("✅ Admin API mounted")
except Exception as e:
    print(f"⚠️ Admin API not available: {e}")

try:
    from routers.client_configuration_api import router as client_config_router
    app.include_router(client_config_router)
    print("✅ Client configuration API mounted")
except Exception as e:
    print(f"⚠️ Client configuration API not available: {e}")

# Include trust comparison endpoints
try:
    import trust_comparison_server
    from trust_comparison_server import compare_trust_scores, get_available_methods

    @app.post("/api/v1/trust-comparison/compare")
    async def trust_compare_endpoint(request: dict):
        from trust_comparison_server import TrustComparisonRequest
        trust_request = TrustComparisonRequest(**request)
        return await compare_trust_scores(trust_request)

    @app.get("/api/v1/trust-comparison/methods")
    async def trust_methods_endpoint():
        return await get_available_methods()

    print("✅ Trust comparison endpoints mounted directly")
except Exception as e:
    print(f"⚠️ Trust comparison endpoints not available: {e}")

# Health check endpoint
@app.get("/health")
async def health_check():
    try:
        # Test database connection
        from database import test_db_connection
        db_status = test_db_connection()

        return {
            "status": "healthy" if db_status else "degraded",
            "service": "backend",
            "version": "1.0",
            "database": "connected" if db_status else "disconnected",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "backend",
            "version": "1.0",
            "database": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/migrate-database")
async def migrate_database():
    """Run database migration to add missing columns"""
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Migrate users table for authentication
        user_columns = [
            ("name", "VARCHAR"),
            ("password_hash", "VARCHAR"),
            ("failed_login_attempts", "INTEGER DEFAULT 0"),
            ("locked_until", "DATETIME")
        ]

        # Create password_resets table if it doesn't exist (PostgreSQL syntax)
        try:
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS password_resets (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    reset_token VARCHAR UNIQUE NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))
            db.commit()
            results.append("✅ Created password_resets table")
        except Exception as e:
            if "already exists" in str(e).lower():
                results.append("⚠️ password_resets table already exists")
            else:
                results.append(f"❌ Error creating password_resets table: {str(e)}")

        # Create api_keys table if it doesn't exist (PostgreSQL syntax)
        try:
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS api_keys (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    key VARCHAR UNIQUE NOT NULL,
                    name VARCHAR,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_used TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))
            db.commit()
            results.append("✅ Created api_keys table")
        except Exception as e:
            if "already exists" in str(e).lower():
                results.append("⚠️ api_keys table already exists")
            else:
                results.append(f"❌ Error creating api_keys table: {str(e)}")

        for column_name, column_def in user_columns:
            try:
                # Check if column exists first (PostgreSQL way)
                check_sql = f"""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name='users' AND column_name='{column_name}'
                """
                result = db.execute(text(check_sql)).fetchone()

                if not result:
                    # Fix DATETIME to TIMESTAMP for PostgreSQL
                    if "DATETIME" in column_def:
                        column_def = column_def.replace("DATETIME", "TIMESTAMP")

                    sql = f"ALTER TABLE users ADD COLUMN {column_name} {column_def}"
                    db.execute(text(sql))
                    db.commit()
                    results.append(f"✅ Added users.{column_name}")
                else:
                    results.append(f"⚠️ Column users.{column_name} already exists")
            except Exception as e:
                results.append(f"❌ Error adding users.{column_name}: {str(e)}")

        # Migrate datasets table
        dataset_columns = [
            ("file_type", "VARCHAR DEFAULT 'csv'"),
            ("content_type", "VARCHAR DEFAULT 'tabular'"),
            ("document_metadata", "TEXT"),
            ("embeddings_data", "TEXT"),
            ("processing_status", "VARCHAR DEFAULT 'completed'"),
            ("word_count", "INTEGER DEFAULT 0"),
            ("character_count", "INTEGER DEFAULT 0"),
            ("has_image", "BOOLEAN DEFAULT FALSE")
        ]

        for column_name, column_def in dataset_columns:
            try:
                # Check if column exists first (PostgreSQL way)
                check_sql = f"""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name='datasets' AND column_name='{column_name}'
                """
                result = db.execute(text(check_sql)).fetchone()

                if not result:
                    sql = f"ALTER TABLE datasets ADD COLUMN {column_name} {column_def}"
                    db.execute(text(sql))
                    db.commit()
                    results.append(f"✅ Added datasets.{column_name}")
                else:
                    results.append(f"⚠️ Column datasets.{column_name} already exists")
            except Exception as e:
                results.append(f"❌ Error adding datasets.{column_name}: {str(e)}")

        return {"status": "completed", "results": results}

    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/fix-postgresql-schema")
async def fix_postgresql_schema():
    """Fix PostgreSQL schema issues directly"""
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Add missing columns to users table one by one
        user_columns = [
            ("name", "VARCHAR"),
            ("password_hash", "VARCHAR"),
            ("failed_login_attempts", "INTEGER DEFAULT 0"),
            ("locked_until", "TIMESTAMP")
        ]

        for column_name, column_def in user_columns:
            try:
                # Check if column exists
                check_sql = f"""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name='users' AND column_name='{column_name}'
                """
                result = db.execute(text(check_sql)).fetchone()

                if not result:
                    sql = f"ALTER TABLE users ADD COLUMN {column_name} {column_def}"
                    db.execute(text(sql))
                    db.commit()
                    results.append(f"✅ Added users.{column_name}")
                else:
                    results.append(f"⚠️ Column users.{column_name} already exists")
            except Exception as e:
                results.append(f"❌ Error adding users.{column_name}: {str(e)}")

        # Create password_resets table with correct PostgreSQL syntax
        try:
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS password_resets (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    reset_token VARCHAR UNIQUE NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))
            db.commit()
            results.append("✅ Created password_resets table")
        except Exception as e:
            if "already exists" in str(e).lower():
                results.append("⚠️ password_resets table already exists")
            else:
                results.append(f"❌ Error creating password_resets table: {str(e)}")

        # Create api_keys table with correct PostgreSQL syntax
        try:
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS api_keys (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    key VARCHAR UNIQUE NOT NULL,
                    name VARCHAR,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_used TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))
            db.commit()
            results.append("✅ Created api_keys table")
        except Exception as e:
            if "already exists" in str(e).lower():
                results.append("⚠️ api_keys table already exists")
            else:
                results.append(f"❌ Error creating api_keys table: {str(e)}")

        return {"status": "completed", "results": results}

    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.post("/fix-database-schema")
async def fix_database_schema():
    """
    EMERGENCY FIX: Add all missing columns to fix delete and query issues
    """
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Fix queries table - add ALL missing columns
        query_fixes = [
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS query_name VARCHAR",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS is_bookmarked BOOLEAN DEFAULT FALSE",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS tags TEXT",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS processing_time INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS response_time_ms INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS token_count INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS performance_grade VARCHAR",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS hallucination_risk DECIMAL(3,2)",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS completeness_score DECIMAL(3,2)",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS source_count INTEGER"
        ]

        for sql in query_fixes:
            try:
                db.execute(text(sql))
                db.commit()
                results.append(f"✅ {sql}")
            except Exception as e:
                results.append(f"❌ {sql} - {str(e)}")

        # Fix datasets table - add missing columns
        dataset_fixes = [
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS file_type VARCHAR DEFAULT 'csv'",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS content_type VARCHAR DEFAULT 'tabular'",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS document_metadata TEXT",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS embeddings_data TEXT",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS processing_status VARCHAR DEFAULT 'completed'",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS character_count INTEGER DEFAULT 0",
            "ALTER TABLE datasets ADD COLUMN IF NOT EXISTS has_image BOOLEAN DEFAULT FALSE"
        ]

        for sql in dataset_fixes:
            try:
                db.execute(text(sql))
                db.commit()
                results.append(f"✅ {sql}")
            except Exception as e:
                results.append(f"❌ {sql} - {str(e)}")

        # Fix users table - make password_hash nullable for OAuth users
        user_fixes = [
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS name VARCHAR",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP",
            # Make password_hash nullable for OAuth users
            "ALTER TABLE users ALTER COLUMN password_hash DROP NOT NULL",
            "ALTER TABLE users ALTER COLUMN name DROP NOT NULL"
        ]

        for sql in user_fixes:
            try:
                db.execute(text(sql))
                db.commit()
                results.append(f"✅ {sql}")
            except Exception as e:
                results.append(f"❌ {sql} - {str(e)}")

        return {
            "status": "success",
            "message": "Database schema fixed! Delete and queries should work now.",
            "results": results
        }

    except Exception as e:
        try:
            db.rollback()
        except:
            pass
        return {
            "status": "error",
            "message": str(e),
            "results": [f"❌ Fatal Error: {str(e)}"]
        }

@app.post("/setup-predictive-database")
async def setup_predictive_database():
    """
    Setup database tables for predictive analytics
    Creates all necessary tables with proper schema
    """
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # SQL for creating all predictive analytics tables
        sql_commands = [
            # Enable UUID extension
            "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",

            # Create predictive_models table
            """
            CREATE TABLE IF NOT EXISTS predictive_models (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id VARCHAR(255) NOT NULL,
                dataset_id UUID NOT NULL,
                name VARCHAR(255) NOT NULL,
                model_type VARCHAR(100) NOT NULL,
                target_column VARCHAR(255) NOT NULL,
                feature_columns TEXT[],
                hyperparameters JSONB DEFAULT '{}',
                metrics JSONB DEFAULT '{}',
                model_data BYTEA,
                status VARCHAR(50) DEFAULT 'training',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
            """,

            # Create predictive_predictions table
            """
            CREATE TABLE IF NOT EXISTS predictive_predictions (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                model_id UUID NOT NULL,
                user_id VARCHAR(255) NOT NULL,
                input_data JSONB NOT NULL,
                prediction_result JSONB NOT NULL,
                confidence_score DECIMAL(5,4),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
            """,

            # Create time_series_forecasts table
            """
            CREATE TABLE IF NOT EXISTS time_series_forecasts (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id VARCHAR(255) NOT NULL,
                dataset_id UUID NOT NULL,
                target_column VARCHAR(255) NOT NULL,
                date_column VARCHAR(255) NOT NULL,
                forecast_horizon INTEGER NOT NULL,
                method VARCHAR(100) NOT NULL,
                forecast_data JSONB NOT NULL,
                accuracy_metrics JSONB DEFAULT '{}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
            """,

            # Create anomaly_detections table
            """
            CREATE TABLE IF NOT EXISTS anomaly_detections (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id VARCHAR(255) NOT NULL,
                dataset_id UUID NOT NULL,
                method VARCHAR(100) NOT NULL,
                parameters JSONB DEFAULT '{}',
                anomalies JSONB NOT NULL,
                summary_stats JSONB DEFAULT '{}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
            """,

            # Create background_jobs table
            """
            CREATE TABLE IF NOT EXISTS background_jobs (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                user_id VARCHAR(255) NOT NULL,
                job_type VARCHAR(100) NOT NULL,
                status VARCHAR(50) DEFAULT 'pending',
                parameters JSONB DEFAULT '{}',
                result JSONB,
                error_message TEXT,
                progress INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP WITH TIME ZONE
            );
            """
        ]

        # Execute each SQL command
        for sql in sql_commands:
            if sql.strip():  # Skip empty commands
                try:
                    db.execute(text(sql))
                    results.append(f"✅ Executed SQL command successfully")
                except Exception as e:
                    results.append(f"❌ SQL Error: {str(e)}")

        db.commit()

        # Create indexes for better performance
        index_commands = [
            "CREATE INDEX IF NOT EXISTS idx_predictive_models_user_id ON predictive_models(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_predictive_predictions_model_id ON predictive_predictions(model_id);",
            "CREATE INDEX IF NOT EXISTS idx_time_series_forecasts_user_id ON time_series_forecasts(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_anomaly_detections_user_id ON anomaly_detections(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_background_jobs_user_id ON background_jobs(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_background_jobs_status ON background_jobs(status);"
        ]

        for sql in index_commands:
            if sql.strip():
                try:
                    db.execute(text(sql))
                    results.append(f"✅ Created index successfully")
                except Exception as e:
                    results.append(f"❌ Index Error: {str(e)}")

        db.commit()

        return {
            "message": "Database setup completed successfully!",
            "tables_created": [
                "predictive_models",
                "predictive_predictions",
                "time_series_forecasts",
                "anomaly_detections",
                "background_jobs"
            ],
            "indexes_created": 6,
            "status": "success",
            "results": results
        }

    except Exception as e:
        try:
            db.rollback()
        except:
            pass
        return {
            "status": "error",
            "message": str(e),
            "results": [f"❌ Fatal Error: {str(e)}"]
        }

@app.get("/emergency-schema-fix")
async def emergency_schema_fix():
    """Emergency fix for PostgreSQL schema - run this first!"""
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Step 1: Add missing columns to users table one by one
        columns_to_add = [
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS name VARCHAR",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP"
        ]

        for sql in columns_to_add:
            try:
                db.execute(text(sql))
                db.commit()
                results.append(f"✅ Executed: {sql}")
            except Exception as e:
                results.append(f"❌ Failed: {sql} - {str(e)}")

        # Step 2: Create missing tables
        tables_to_create = [
            """
            CREATE TABLE IF NOT EXISTS password_resets (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR NOT NULL,
                reset_token VARCHAR UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS api_keys (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR NOT NULL,
                key VARCHAR UNIQUE NOT NULL,
                name VARCHAR,
                is_active BOOLEAN DEFAULT TRUE,
                last_used TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        ]

        for sql in tables_to_create:
            try:
                db.execute(text(sql))
                db.commit()
                results.append("✅ Created table successfully")
            except Exception as e:
                results.append(f"❌ Table creation failed: {str(e)}")

        return {"status": "completed", "results": results}

    except Exception as e:
        return {"status": "error", "message": str(e)}



@app.get("/fix-api-key/{user_id}")
async def fix_api_key(user_id: str, db: Session = Depends(get_db)):
    """Fix missing API key for a user"""
    try:
        # Check if user exists
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user:
            # Create user if doesn't exist
            user = models.User(
                id=user_id,
                email=f"{user_id}@oauth.local",
                name="OAuth User",
                role="user",
                subscription_status="free"
            )
            db.add(user)
            db.commit()
            db.refresh(user)

        # Check if API key exists
        api_key = db.query(models.ApiKey).filter(models.ApiKey.user_id == user_id).first()
        if not api_key:
            # Create API key
            api_key = models.ApiKey(
                user_id=user_id,
                name="User API Key",
                is_active=True
            )
            db.add(api_key)
            db.commit()
            db.refresh(api_key)

        return {
            "status": "success",
            "user_id": user_id,
            "api_key": api_key.key,
            "message": "API key created/found successfully"
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.post("/fix-foreign-keys")
async def fix_foreign_keys():
    """
    Fix foreign key constraints to allow proper cascading deletes
    """
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Drop and recreate foreign key constraints with CASCADE
        fixes = [
            # Drop existing constraints
            "ALTER TABLE feedback DROP CONSTRAINT IF EXISTS feedback_query_id_fkey",
            "ALTER TABLE saved_queries DROP CONSTRAINT IF EXISTS saved_queries_query_id_fkey",
            "ALTER TABLE query_cache DROP CONSTRAINT IF EXISTS query_cache_query_id_fkey",

            # Recreate with CASCADE DELETE
            """ALTER TABLE feedback ADD CONSTRAINT feedback_query_id_fkey
               FOREIGN KEY (query_id) REFERENCES queries (id) ON DELETE CASCADE""",
            """ALTER TABLE saved_queries ADD CONSTRAINT saved_queries_query_id_fkey
               FOREIGN KEY (query_id) REFERENCES queries (id) ON DELETE CASCADE""",
        ]

        for sql in fixes:
            try:
                db.execute(text(sql))
                db.commit()
                results.append(f"✅ {sql}")
            except Exception as e:
                results.append(f"❌ {sql} - {str(e)}")

        return {
            "status": "success",
            "message": "Foreign key constraints fixed! Delete should work now.",
            "results": results
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.delete("/datasets/{dataset_id}/force")
async def force_delete_dataset(dataset_id: int, user_id: str, db: Session = Depends(get_db)):
    """
    Force delete dataset with proper cascade handling
    """
    try:
        from sqlalchemy import text

        # Delete in correct order to respect foreign keys
        delete_queries = [
            f"DELETE FROM feedback WHERE query_id IN (SELECT id FROM queries WHERE dataset_id = {dataset_id})",
            f"DELETE FROM saved_queries WHERE query_id IN (SELECT id FROM queries WHERE dataset_id = {dataset_id})",
            f"DELETE FROM query_cache WHERE dataset_id = {dataset_id}",
            f"DELETE FROM queries WHERE dataset_id = {dataset_id}",
            f"DELETE FROM conversations WHERE dataset_id = {dataset_id}",
            f"DELETE FROM datasets WHERE id = {dataset_id} AND user_id = '{user_id}'"
        ]

        results = []
        for sql in delete_queries:
            try:
                result = db.execute(text(sql))
                db.commit()
                results.append(f"✅ Deleted {result.rowcount} records from {sql.split()[2]}")
            except Exception as e:
                results.append(f"❌ Error: {str(e)}")

        return {
            "status": "success",
            "message": f"Dataset {dataset_id} force deleted successfully",
            "results": results
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error force deleting dataset: {str(e)}"
        )

@app.get("/emergency-cleanup")
async def emergency_cleanup():
    """
    Emergency endpoint to delete ALL data (datasets, queries, conversations, cache)
    """
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Delete all data in correct order (respecting foreign keys)
        cleanup_queries = [
            "DELETE FROM feedback",
            "DELETE FROM saved_queries",
            "DELETE FROM query_cache",
            "DELETE FROM queries",
            "DELETE FROM conversations",
            "DELETE FROM datasets"
        ]

        for sql in cleanup_queries:
            try:
                result = db.execute(text(sql))
                db.commit()
                results.append(f"✅ {sql} - Deleted {result.rowcount} records")
            except Exception as e:
                results.append(f"❌ {sql} - Error: {str(e)}")

        return {"status": "completed", "results": results}

    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/migrate-user-queries")
async def migrate_user_queries():
    """
    Run database migration for user-specific queries and saved queries
    """
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Create saved_queries table
        try:
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS saved_queries (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    query_id INTEGER NOT NULL,
                    name VARCHAR,
                    description TEXT,
                    tags TEXT,
                    is_favorite BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                    FOREIGN KEY (query_id) REFERENCES queries (id) ON DELETE CASCADE,
                    UNIQUE(user_id, query_id)
                )
            """))
            db.commit()
            results.append("✅ Created saved_queries table")
        except Exception as e:
            if "already exists" in str(e).lower():
                results.append("⚠️ saved_queries table already exists")
            else:
                results.append(f"❌ Error creating saved_queries table: {str(e)}")

        # Add new columns to queries table
        query_columns = [
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS query_name VARCHAR",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS is_bookmarked BOOLEAN DEFAULT FALSE",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS tags TEXT",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS processing_time INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS response_time_ms INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS token_count INTEGER",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS performance_grade VARCHAR",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS hallucination_risk DECIMAL(3,2)",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS completeness_score DECIMAL(3,2)",
            "ALTER TABLE queries ADD COLUMN IF NOT EXISTS source_count INTEGER"
        ]

        for sql in query_columns:
            try:
                db.execute(text(sql))
                db.commit()
                results.append(f"✅ {sql}")
            except Exception as e:
                results.append(f"❌ {sql} - {str(e)}")

        # Add user_id to query_cache table
        try:
            db.execute(text("ALTER TABLE query_cache ADD COLUMN IF NOT EXISTS user_id VARCHAR"))
            db.commit()
            results.append("✅ Added user_id to query_cache table")
        except Exception as e:
            results.append(f"❌ Error adding user_id to query_cache: {str(e)}")

        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_saved_queries_user_id ON saved_queries(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_saved_queries_query_id ON saved_queries(query_id)",
            "CREATE INDEX IF NOT EXISTS idx_saved_queries_is_favorite ON saved_queries(is_favorite)",
            "CREATE INDEX IF NOT EXISTS idx_queries_user_id_created_at ON queries(user_id, created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_queries_is_bookmarked ON queries(is_bookmarked)",
            "CREATE INDEX IF NOT EXISTS idx_query_cache_user_id ON query_cache(user_id)"
        ]

        for sql in indexes:
            try:
                db.execute(text(sql))
                db.commit()
                results.append(f"✅ {sql}")
            except Exception as e:
                results.append(f"❌ {sql} - {str(e)}")

        return {"status": "completed", "results": results}

    except Exception as e:
        return {"status": "error", "message": str(e)}

# Test endpoint for frontend
@app.get("/test")
async def test_endpoint():
    return {"message": "Backend is working!", "cors": "enabled"}

# Test API v1 endpoints
@app.get("/api/v1/test")
async def test_api_v1():
    return {"message": "API v1 is working!", "endpoint": "/api/v1/test"}

@app.post("/api/v1/test-ask")
async def test_ask_endpoint():
    return {
        "message": "Ask endpoint is accessible",
        "endpoint": "/api/v1/ask",
        "status": "working"
    }

# CORS test endpoint
@app.get("/cors-test")
async def cors_test():
    return {
        "message": "CORS is working correctly",
        "timestamp": datetime.now().isoformat(),
        "cors_origins": os.getenv('CORS_ORIGINS', '*'),
        "frontend_url": os.getenv('FRONTEND_URL', 'http://localhost:3000')
    }

# Auth test endpoint
@app.get("/auth/test")
async def auth_test():
    return {
        "message": "Auth endpoints are accessible",
        "endpoints": ["/auth/sync-user", "/auth/session"],
        "status": "working"
    }

# Fix datasets endpoint
@app.get("/datasets")
async def get_datasets(user_id: str, db: Session = Depends(get_db)):
    """
    Get all datasets for a user - FIXED VERSION
    """
    try:
        # Get datasets from database
        datasets = db.query(models.Dataset).filter(
            models.Dataset.user_id == user_id
        ).order_by(models.Dataset.created_at.desc()).all()

        # Format response
        result = []
        for dataset in datasets:
            result.append({
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "file_type": getattr(dataset, 'file_type', 'csv'),
                "content_type": getattr(dataset, 'content_type', 'tabular'),
                "processing_status": getattr(dataset, 'processing_status', 'completed'),
                "word_count": getattr(dataset, 'word_count', 0),
                "character_count": getattr(dataset, 'character_count', 0),
                "has_image": getattr(dataset, 'has_image', False),
                "created_at": dataset.created_at,
                "updated_at": dataset.updated_at
            })

        print(f"✅ Found {len(result)} datasets for user {user_id}")
        return result

    except Exception as e:
        print(f"❌ Error fetching datasets: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching datasets: {str(e)}"
        )

@app.post("/fix-rag-system")
async def fix_rag_system():
    """
    Fix RAG system to properly find and process document content
    """
    try:
        from sqlalchemy import text

        db = next(get_db())
        results = []

        # Check if datasets have proper content
        datasets = db.execute(text("SELECT id, name, content_type, parsed_data FROM datasets LIMIT 5")).fetchall()

        for dataset in datasets:
            dataset_id, name, content_type, parsed_data = dataset

            if not parsed_data:
                results.append(f"❌ Dataset {name} (ID: {dataset_id}) has no parsed_data")
            elif len(str(parsed_data)) < 100:
                results.append(f"⚠️ Dataset {name} (ID: {dataset_id}) has minimal data ({len(str(parsed_data))} chars)")
            else:
                results.append(f"✅ Dataset {name} (ID: {dataset_id}) has data ({len(str(parsed_data))} chars)")

        # Check document chunks
        chunk_count = db.execute(text("SELECT COUNT(*) FROM document_chunks")).fetchone()[0]
        results.append(f"📄 Document chunks in database: {chunk_count}")

        return {
            "status": "success",
            "message": "RAG system diagnosis completed",
            "results": results
        }

    except Exception as e:
        return {"status": "error", "message": str(e)}

# Test ask endpoint with processing time
@app.post("/test-ask")
async def test_ask_endpoint():
    import time
    start_time = time.time()

    # Simulate some processing
    time.sleep(0.1)

    end_time = time.time()
    processing_time = int((end_time - start_time) * 1000)

    return {
        "id": 999,
        "answer": "This is a test response to verify processing time display.",
        "processing_time": processing_time,
        "trust_score": {
            "overall_score": 0.85,
            "factors": ["test"],
            "explanation": "Test trust score"
        }
    }

# User endpoints
@app.post("/users", response_model=schemas.UserResponse)
async def create_user(user: schemas.UserCreate, db: Session = Depends(get_db)):
    """
    Create a new user with email and password
    """
    # Check if email already exists
    db_user_email = db.query(models.User).filter(models.User.email == user.email).first()
    if db_user_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Generate user ID if not provided
    if not user.id:
        user.id = str(uuid.uuid4())

    # Generate referral code if not provided
    if not user.referral_code:
        import random
        import string
        user.referral_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

    # Hash the password
    hashed_password = hash_password(user.password)

    # Create new user
    db_user = models.User(
        id=user.id,
        email=user.email,
        name=user.name,
        password_hash=hashed_password,
        role=user.role,
        subscription_status=user.subscription_status,
        referral_code=user.referral_code
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return db_user

@app.post("/auth/login")
async def login_user(login_data: schemas.UserLogin, db: Session = Depends(get_db)):
    """
    Authenticate user with email and password
    """
    # Find user by email
    user = db.query(models.User).filter(models.User.email == login_data.email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )

    # Check if account is locked
    if check_account_lockout(user):
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="Account is temporarily locked due to too many failed login attempts"
        )

    # Verify password
    if not verify_password(login_data.password, user.password_hash):
        # Increment failed attempts
        increment_failed_attempts(db, user)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )

    # Reset failed attempts on successful login
    reset_failed_attempts(db, user)

    # Return user data (without password)
    return {
        "id": user.id,
        "email": user.email,
        "name": user.name,
        "role": user.role,
        "subscription_status": user.subscription_status
    }

@app.post("/auth/forgot-password")
async def forgot_password(forgot_data: schemas.ForgotPassword, db: Session = Depends(get_db)):
    """
    Send password reset email
    """
    # Find user by email
    user = db.query(models.User).filter(models.User.email == forgot_data.email).first()
    if not user:
        # Don't reveal if email exists or not for security
        return {"message": "If an account with that email exists, we've sent a password reset link."}

    # Generate reset token
    reset_token = email_service.generate_reset_token()
    expires_at = datetime.now() + timedelta(hours=1)  # Token expires in 1 hour

    # Save reset token to database
    password_reset = models.PasswordReset(
        user_id=user.id,
        reset_token=reset_token,
        expires_at=expires_at
    )

    db.add(password_reset)
    db.commit()

    # Send email
    email_sent = email_service.send_password_reset_email(
        to_email=user.email,
        reset_token=reset_token,
        user_name=user.name
    )

    if email_sent:
        return {"message": "If an account with that email exists, we've sent a password reset link."}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send reset email. Please try again later."
        )

@app.post("/auth/reset-password")
async def reset_password(reset_data: schemas.ResetPassword, db: Session = Depends(get_db)):
    """
    Reset password using reset token
    """
    # Find valid reset token
    password_reset = db.query(models.PasswordReset).filter(
        models.PasswordReset.reset_token == reset_data.reset_code,
        models.PasswordReset.used == False,
        models.PasswordReset.expires_at > datetime.now()
    ).first()

    if not password_reset:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )

    # Find user
    user = db.query(models.User).filter(models.User.id == password_reset.user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Update password
    user.password_hash = hash_password(reset_data.new_password)
    user.failed_login_attempts = 0  # Reset failed attempts
    user.locked_until = None  # Unlock account

    # Mark reset token as used
    password_reset.used = True

    db.commit()

    return {"message": "Password reset successfully"}

# Add specific OPTIONS handler for auth endpoints
@app.options("/auth/{path:path}")
async def auth_options_handler(path: str, response: Response):
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "*"
    return {"message": "OK"}

@app.post("/auth/sync-user")
async def sync_user(user_data: dict, db: Session = Depends(get_db)):
    """
    Sync user from NextAuth to backend database
    Called when user signs in via OAuth or creates account
    """
    try:
        user_id = user_data.get("id")
        email = user_data.get("email")
        name = user_data.get("name")

        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User ID is required"
            )

        # Check if user already exists
        existing_user = db.query(models.User).filter(models.User.id == user_id).first()

        if not existing_user:
            # Create new user
            new_user = models.User(
                id=user_id,
                email=email or f"user-{user_id}@oauth.local",
                name=name or "OAuth User",
                role="user",
                subscription_status="free",
                referral_code=f"REF{user_id[:8]}"
            )
            db.add(new_user)
            db.commit()
            db.refresh(new_user)

            # Create API key for the new user
            api_key = models.ApiKey(
                user_id=new_user.id,
                name="User API Key",
                is_active=True
            )
            db.add(api_key)
            db.commit()
            db.refresh(api_key)

            return {
                "message": "User created successfully",
                "user": {
                    "id": new_user.id,
                    "email": new_user.email,
                    "name": new_user.name
                },
                "api_key": api_key.key
            }
        else:
            # Update existing user info if needed
            if email and existing_user.email != email:
                existing_user.email = email
            if name and existing_user.name != name:
                existing_user.name = name

            db.commit()

            # Get or create API key for existing user
            api_key = db.query(models.ApiKey).filter(models.ApiKey.user_id == existing_user.id).first()
            if not api_key:
                api_key = models.ApiKey(
                    user_id=existing_user.id,
                    name="User API Key",
                    is_active=True
                )
                db.add(api_key)
                db.commit()
                db.refresh(api_key)

            return {
                "message": "User updated successfully",
                "user": {
                    "id": existing_user.id,
                    "email": existing_user.email,
                    "name": existing_user.name
                },
                "api_key": api_key.key
            }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error syncing user: {str(e)}"
        )

@app.post("/auth/session")
async def create_session(login_data: schemas.UserLogin, db: Session = Depends(get_db)):
    """
    Create a user session (alternative to NextAuth for API access)
    """
    # Find user by email
    user = db.query(models.User).filter(models.User.email == login_data.email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )

    # Check if account is locked
    if check_account_lockout(user):
        raise HTTPException(
            status_code=status.HTTP_423_LOCKED,
            detail="Account is temporarily locked due to too many failed login attempts"
        )

    # Verify password
    if not verify_password(login_data.password, user.password_hash):
        # Increment failed attempts
        increment_failed_attempts(db, user)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )

    # Reset failed attempts on successful login
    reset_failed_attempts(db, user)

    # Create or get API key for this user
    api_key = db.query(models.ApiKey).filter(models.ApiKey.user_id == user.id).first()
    if not api_key:
        api_key = models.ApiKey(
            user_id=user.id,
            name="User Session Key",
            is_active=True
        )
        db.add(api_key)
        db.commit()
        db.refresh(api_key)

    # Return user data with API key for frontend use
    return {
        "user": {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "role": user.role,
            "subscription_status": user.subscription_status
        },
        "api_key": api_key.key,
        "message": "Session created successfully"
    }



@app.get("/users/{user_id}", response_model=schemas.UserResponse)
async def get_user(user_id: str, db: Session = Depends(get_db)):
    """
    Get user by ID
    """
    print(f"🔍 Backend Debug - Looking for user_id: '{user_id}'")

    # Try to find user by ID first, then by email
    db_user = db.query(models.User).filter(models.User.id == user_id).first()
    if not db_user:
        # Try finding by email if ID lookup failed
        db_user = db.query(models.User).filter(models.User.email == user_id).first()
        print(f"🔍 Backend Debug - User not found by ID, tried email lookup: {db_user is not None}")

    if not db_user:
        print(f"🔍 Backend Debug - User not found. Available users:")
        all_users = db.query(models.User).all()
        for user in all_users:
            print(f"  - ID: '{user.id}', Email: '{user.email}', Role: '{user.role}'")

        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    print(f"🔍 Backend Debug - Found user: ID='{db_user.id}', Email='{db_user.email}', Role='{db_user.role}'")
    return db_user

@app.get("/admin/users")
async def get_all_users_admin(
    user_id: str,
    admin_user: models.User = Depends(require_admin_role),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Get all users with statistics
    """
    try:
        users = db.query(models.User).order_by(models.User.created_at.desc()).all()

        result = []
        for user in users:
            # Get user statistics
            dataset_count = db.query(models.Dataset).filter(models.Dataset.user_id == user.id).count()

            # Use raw SQL to avoid issues with missing columns
            from sqlalchemy import text
            query_count_result = db.execute(text("SELECT COUNT(*) FROM queries WHERE user_id = :user_id"), {"user_id": user.id})
            query_count = query_count_result.scalar()

            result.append({
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "role": user.role,
                "subscription_status": user.subscription_status,
                "created_at": user.created_at,
                "dataset_count": dataset_count,
                "query_count": query_count
            })

        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching users: {str(e)}"
        )

@app.get("/admin/users/stats")
async def get_user_stats_admin(
    user_id: str,
    admin_user: models.User = Depends(require_admin_role),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Get user statistics
    """
    try:
        total_users = db.query(models.User).count()
        active_users = db.query(models.User).filter(models.User.role == 'user').count()
        admin_users = db.query(models.User).filter(models.User.role == 'admin').count()

        # Users with datasets
        users_with_datasets = db.query(models.User.id).join(models.Dataset).distinct().count()

        # Users with queries - use raw SQL to avoid column issues
        from sqlalchemy import text
        users_with_queries_result = db.execute(text("SELECT COUNT(DISTINCT user_id) FROM queries"))
        users_with_queries = users_with_queries_result.scalar()

        return {
            "total_users": total_users,
            "active_users": active_users,
            "admin_users": admin_users,
            "users_with_datasets": users_with_datasets,
            "users_with_queries": users_with_queries,
            "users_with_datasets_percentage": round((users_with_datasets / total_users * 100) if total_users > 0 else 0, 1),
            "users_with_queries_percentage": round((users_with_queries / total_users * 100) if total_users > 0 else 0, 1)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching user stats: {str(e)}"
        )

# Dataset endpoints
@app.post("/upload", response_model=schemas.DatasetResponse)
async def upload_dataset(
    file: UploadFile = File(...),
    name: str = Form(...),
    user_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    Upload a CSV dataset
    """
    # Ensure user exists in database (auto-create if needed)
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        # Auto-create user for OAuth users
        user = models.User(
            id=user_id,
            email=f"user-{user_id}@oauth.local",  # Placeholder email
            name="OAuth User",
            role="user",
            subscription_status="free",
            referral_code=f"REF{user_id[:8]}"
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        print(f"✅ Auto-created user: {user_id}")
    # Validate file type
    file_extension = file.filename.split('.')[-1].lower()
    supported_formats = ['csv', 'xlsx', 'xls', 'pdf', 'docx', 'txt', 'md', 'jpg', 'jpeg', 'png']
    if file_extension not in supported_formats:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Supported formats: {', '.join(supported_formats)}"
        )

    # Debug logging for file type detection
    print(f"🔍 File upload debug - Filename: {file.filename}, Extension: {file_extension}, Content-Type: {file.content_type}")

    # Read file content
    contents = await file.read()

    try:
        # Determine content type and processing approach
        if file_extension in ['csv', 'xlsx', 'xls']:
            # Process tabular data
            if file_extension == 'csv':
                df = pd.read_csv(pd.io.common.BytesIO(contents))
            else:  # Excel files
                try:
                    # Try with openpyxl engine first (for .xlsx)
                    if file_extension == 'xlsx':
                        df = pd.read_excel(pd.io.common.BytesIO(contents), engine='openpyxl')
                    else:  # .xls files
                        df = pd.read_excel(pd.io.common.BytesIO(contents), engine='xlrd')
                except ImportError as ie:
                    # Missing engine dependency
                    if 'openpyxl' in str(ie):
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Excel support not available. Missing openpyxl dependency for .xlsx files."
                        )
                    elif 'xlrd' in str(ie):
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Excel support not available. Missing xlrd dependency for .xls files."
                        )
                    else:
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail=f"Excel processing error: {str(ie)}"
                        )
                except Exception as excel_error:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to read Excel file: {str(excel_error)}. Please ensure the file is a valid Excel file."
                    )

            # Basic validation
            if df.empty:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="The file is empty"
                )

            # Save tabular dataset
            print(f"📊 Creating tabular dataset - File: {file.filename}, Extension: {file_extension}, Content-Type: tabular")
            dataset = models.Dataset(
                name=name,
                user_id=user_id,
                columns=json.dumps(df.columns.tolist()),
                row_count=len(df),
                parsed_data=df.to_json(orient="records"),
                file_type=file_extension,
                content_type='tabular',
                processing_status='completed'
            )

        else:
            # Process document files
            doc_result = document_processor.process_document(contents, file.filename, file_extension)

            if 'error' in doc_result:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Document processing failed: {doc_result['error']}"
                )

            # Save document dataset
            parsed_data_entry = {
                'text': doc_result.get('text', ''),
                'metadata': doc_result.get('metadata', {}),
                'filename': file.filename
            }

            # For image files, include the original image data
            if file_extension in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
                parsed_data_entry['original_image_data'] = doc_result.get('original_image_data')

            print(f"📄 Creating document dataset - File: {file.filename}, Extension: {file_extension}, Content-Type: document")
            dataset = models.Dataset(
                name=name,
                user_id=user_id,
                columns=json.dumps(['text', 'metadata']),  # Standard document columns
                row_count=1,  # Documents are treated as single entities
                parsed_data=json.dumps([parsed_data_entry]),
                file_type=file_extension,
                content_type='document',
                document_metadata=json.dumps(doc_result.get('metadata', {})),
                embeddings_data=json.dumps(doc_result.get('embeddings', [])),
                processing_status=doc_result.get('processing_status', 'completed'),
                word_count=doc_result.get('word_count', 0),
                character_count=doc_result.get('character_count', 0)
            )

        db.add(dataset)
        db.commit()
        db.refresh(dataset)

        return {
            "id": dataset.id,
            "name": dataset.name,
            "columns": json.loads(dataset.columns),
            "row_count": dataset.row_count,
            "created_at": dataset.created_at,
            "file_type": dataset.file_type,
            "content_type": dataset.content_type,
            "processing_status": dataset.processing_status,
            "word_count": dataset.word_count,
            "character_count": dataset.character_count
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Log the full error for debugging
        print(f"❌ File processing error: {str(e)}")
        print(f"📁 File: {file.filename}, Extension: {file_extension}, Size: {len(contents)} bytes")

        # Provide user-friendly error messages
        if "openpyxl" in str(e).lower():
            detail = "Excel (.xlsx) files are not supported. Please convert to CSV or contact support."
        elif "xlrd" in str(e).lower():
            detail = "Legacy Excel (.xls) files are not supported. Please convert to CSV or .xlsx format."
        elif "empty" in str(e).lower():
            detail = "The uploaded file appears to be empty or corrupted."
        elif "encoding" in str(e).lower():
            detail = "File encoding issue. Please ensure the file is saved in UTF-8 format."
        elif file_extension in ['csv']:
            detail = f"CSV processing failed: {str(e)}. Please check the file format and try again."
        elif file_extension in ['xlsx', 'xls']:
            detail = f"Excel processing failed: {str(e)}. Please ensure the file is a valid Excel file."
        else:
            detail = f"File processing failed: {str(e)}"

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )

@app.get("/datasets/{user_id}")
async def get_user_datasets(user_id: str, db: Session = Depends(get_db)):
    """
    Get all datasets for a user
    """
    # Query all necessary columns including file_type
    datasets = db.query(models.Dataset).filter(models.Dataset.user_id == user_id).order_by(models.Dataset.created_at.desc()).all()

    try:
        result = []
        for dataset in datasets:
            try:
                # Debug: Print what's actually in the database
                print(f"🔍 Dataset {dataset.id}: name={dataset.name}, file_type='{dataset.file_type}', content_type='{dataset.content_type}'")

                result.append({
                    "id": dataset.id,
                    "name": dataset.name or "Untitled",
                    "columns": json.loads(dataset.columns) if dataset.columns else [],
                    "row_count": dataset.row_count or 0,
                    "created_at": dataset.created_at.isoformat() if dataset.created_at else None,
                    "file_type": dataset.file_type,  # Don't default to 'csv' - show actual file type
                    "content_type": dataset.content_type or 'tabular',
                    "processing_status": dataset.processing_status or 'completed',
                    "word_count": dataset.word_count or 0,
                    "character_count": dataset.character_count or 0
                })
            except Exception as item_error:
                print(f"Error processing dataset {dataset.id}: {item_error}")
                continue
        return result
    except Exception as e:
        print(f"Error in get_user_datasets: {e}")
        return []


@app.get("/datasets/{dataset_id}/content")
async def get_dataset_content(
    dataset_id: int,
    user_id: str,  # Add user_id parameter for security
    db: Session = Depends(get_db)
):
    """Get the content of a specific dataset (user-specific)"""
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == dataset_id,
        models.Dataset.user_id == user_id  # Ensure user owns the dataset
    ).first()

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found or access denied"
        )

    try:
        # Parse the stored data
        parsed_data = json.loads(dataset.parsed_data)

        if dataset.content_type == 'document':
            # For documents, return the first (and usually only) entry
            if parsed_data and len(parsed_data) > 0:
                result = {
                    "text": parsed_data[0].get("text", ""),
                    "metadata": parsed_data[0].get("metadata", {}),
                    "filename": parsed_data[0].get("filename", dataset.name)
                }
                # Include original_image_data if available (for debugging)
                if parsed_data[0].get("original_image_data"):
                    result["has_original_image"] = True
                else:
                    result["has_original_image"] = False
                return result
            else:
                return {
                    "text": "No content available",
                    "metadata": {},
                    "filename": dataset.name
                }
        else:
            # For tabular data, return a summary
            return {
                "text": f"Tabular data with {dataset.row_count} rows and {len(json.loads(dataset.columns))} columns",
                "metadata": {
                    "columns": json.loads(dataset.columns),
                    "row_count": dataset.row_count,
                    "file_type": dataset.file_type
                },
                "filename": dataset.name
            }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve dataset content: {str(e)}"
        )


@app.get("/datasets/{dataset_id}/image")
async def get_dataset_image(
    dataset_id: int,
    user_id: str,  # Add user_id parameter for security
    db: Session = Depends(get_db)
):
    """Get the original image file for image datasets (user-specific)"""
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == dataset_id,
        models.Dataset.user_id == user_id  # Ensure user owns the dataset
    ).first()

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found or access denied"
        )

    # Check if this is an image dataset
    if dataset.file_type not in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This dataset is not an image file"
        )

    try:
        # Get the original file data from parsed_data
        parsed_data = json.loads(dataset.parsed_data)

        # For now, we'll need to store the original image data
        # This is a temporary solution - in production, you'd want to store files separately
        if parsed_data and len(parsed_data) > 0:
            # Check if we have stored the original image data
            image_data = parsed_data[0].get("original_image_data")
            if image_data:
                # Decode base64 image data and serve it
                import base64
                image_bytes = base64.b64decode(image_data)

                # Create a temporary file to serve the image
                with tempfile.NamedTemporaryFile(delete=False, suffix=f".{dataset.file_type}") as temp_file:
                    temp_file.write(image_bytes)
                    temp_file_path = temp_file.name

                # Determine media type
                media_type_map = {
                    'jpg': 'image/jpeg',
                    'jpeg': 'image/jpeg',
                    'png': 'image/png',
                    'gif': 'image/gif',
                    'bmp': 'image/bmp',
                    'webp': 'image/webp'
                }

                media_type = media_type_map.get(dataset.file_type, 'image/jpeg')

                return FileResponse(
                    temp_file_path,
                    media_type=media_type,
                    filename=dataset.name,
                    background=None  # Don't delete the temp file immediately
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Original image data not found"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No image data available"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve image: {str(e)}"
        )


# 🚀 Revolutionary Hybrid Query Processing Endpoint
@app.post("/ask-hybrid", response_model=schemas.QueryResponse)
async def ask_question_hybrid(query: schemas.QueryRequest, db: Session = Depends(get_db)):
    """
    🚀 Revolutionary Hybrid Query Processing
    Combines lightning-fast structured operations with AI semantic intelligence
    """
    # Ensure user exists
    user = db.query(models.User).filter(models.User.id == query.user_id).first()
    if not user:
        user = models.User(
            id=query.user_id,
            email=f"user-{query.user_id}@oauth.local",
            name="OAuth User",
            role="user",
            subscription_status="free",
            referral_code=f"REF{query.user_id[:8]}"
        )
        db.add(user)
        db.commit()
        db.refresh(user)

    # Get dataset
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == query.dataset_id,
        models.Dataset.user_id == query.user_id
    ).first()

    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")

    try:
        # Load dataset with better error handling
        print(f"📊 Loading dataset: {dataset.name} (Type: {dataset.file_type}, Content: {dataset.content_type})")

        try:
            if dataset.content_type == 'document':
                df = pd.read_json(StringIO(dataset.parsed_data))
            else:
                df = pd.read_json(StringIO(dataset.parsed_data))

            print(f"✅ Dataset loaded successfully: {len(df)} rows, {len(df.columns)} columns")

        except Exception as load_error:
            print(f"❌ Dataset loading failed: {str(load_error)}")
            print(f"📄 Dataset info: ID={dataset.id}, Type={dataset.file_type}, Size={len(dataset.parsed_data) if dataset.parsed_data else 0} chars")

            # Try to provide helpful error message
            if "JSON" in str(load_error):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Dataset appears to be corrupted. Please re-upload the file. Error: {str(load_error)}"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to load dataset: {str(load_error)}"
                )

        # 🚀 Use World-Class ML-Enhanced Query Router
        from services.ml_query_router import ml_router
        from services.intelligent_query_router import intelligent_router
        from services.hybrid_data_engine import DataFormat

        dataset_info = {
            'rows': len(df),
            'columns': df.columns.tolist(),
            'columns_count': len(df.columns),
            'size_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
            'numeric_columns': df.select_dtypes(include=['number']).columns.tolist(),
            'text_columns': df.select_dtypes(include=['object']).columns.tolist(),
            'format': dataset.file_type or 'csv'
        }

        # 🧠 Use ML-Enhanced Router if available, fallback to intelligent router
        if ml_router:
            routing_decision = await ml_router.route_query_ml(query.question, dataset_info)
            print(f"🚀 ML Router - Path: {routing_decision.primary_path.value}")
        else:
            routing_decision = intelligent_router.route_query(query.question, dataset_info)
            print(f"🎯 Intelligent Router - Path: {routing_decision.primary_path.value}")

        print(f"🔍 Confidence: {routing_decision.confidence:.2f}")
        print(f"💭 Reasoning: {', '.join(routing_decision.reasoning[:2])}")

        # Execute routing decision
        result = await intelligent_router.execute_routing_decision(
            routing_decision, query.question, df, DataFormat.CSV
        )

        # Generate response
        if result.semantic_result:
            answer = result.semantic_result
        else:
            answer = intelligent_router.hybrid_engine.generate_natural_language_response(result, query.question)

        # Add metadata
        metadata = []
        if result.processing_time_ms:
            metadata.append(f"⚡ {result.processing_time_ms:.1f}ms")
        if routing_decision.confidence:
            metadata.append(f"🎯 {routing_decision.confidence:.1%}")
        if result.data_summary:
            s = result.data_summary
            metadata.append(f"📊 {s['rows']:,} rows, {s['columns']} cols")

        if metadata:
            answer += f"\n\n{' | '.join(metadata)}"

        if result.insights:
            answer += f"\n\n💡 **Insights:**\n" + "\n".join(f"   {i}" for i in result.insights)

        # 📈 Learn from performance (ML Enhancement)
        if ml_router and result.processing_time_ms:
            from services.ml_query_router import PerformanceMetrics

            actual_metrics = PerformanceMetrics(
                processing_time_ms=result.processing_time_ms,
                accuracy_score=result.confidence_score or 0.8,
                user_satisfaction=0.8,  # Default, will be updated with user feedback
                resource_usage=0.5,  # Estimated
                error_rate=0.0  # No errors if we got here
            )

            ml_router.learn_from_performance(
                query.question,
                routing_decision.primary_path,
                actual_metrics,
                dataset_info
            )

        # Save to database
        db_query = models.Query(
            user_id=query.user_id,
            dataset_id=query.dataset_id,
            question=query.question,
            answer=answer,
            processing_time=int(result.processing_time_ms) if result.processing_time_ms else None
        )
        db.add(db_query)
        db.commit()
        db.refresh(db_query)

        return {
            "id": db_query.id,
            "answer": answer,
            "created_at": db_query.created_at,
            "processing_time": db_query.processing_time
        }

    except Exception as e:
        print(f"Hybrid processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@app.post("/query-feedback")
async def submit_query_feedback(
    feedback_data: dict,
    db: Session = Depends(get_db)
):
    """
    🎯 Submit user feedback for ML learning and continuous improvement
    """
    try:
        from services.ml_query_router import ml_router, UserFeedback, ProcessingPath

        if not ml_router:
            return {"message": "ML router not available"}

        # Create feedback object
        feedback = UserFeedback(
            query_id=feedback_data.get('query_id'),
            chosen_path=ProcessingPath(feedback_data.get('chosen_path', 'HYBRID_STRUCTURED_FIRST')),
            user_rating=feedback_data.get('user_rating', 3.0),
            speed_rating=feedback_data.get('speed_rating', 3.0),
            accuracy_rating=feedback_data.get('accuracy_rating', 3.0),
            usefulness_rating=feedback_data.get('usefulness_rating', 3.0)
        )

        # Learn from feedback
        ml_router.learn_from_user_feedback(feedback)

        # Save model periodically
        if len(ml_router.user_feedback_history) % 10 == 0:
            ml_router.save_model()

        return {
            "message": "Feedback received and processed",
            "total_feedback_count": len(ml_router.user_feedback_history),
            "learning_status": "active"
        }

    except Exception as e:
        print(f"Feedback processing error: {str(e)}")
        return {"message": f"Error processing feedback: {str(e)}"}

@app.get("/ml-router-stats")
async def get_ml_router_stats():
    """
    📊 Get ML router performance statistics and learning progress
    """
    try:
        from services.ml_query_router import ml_router

        if not ml_router:
            return {"message": "ML router not available"}

        stats = {
            "ml_available": True,
            "performance_history_count": len(ml_router.performance_history),
            "user_feedback_count": len(ml_router.user_feedback_history),
            "current_decision_criteria": ml_router.decision_criteria,
            "performance_estimates": {
                path.value: estimate for path, estimate in ml_router.performance_estimates.items()
            },
            "learning_status": "active" if len(ml_router.performance_history) > 0 else "initializing"
        }

        # Recent performance trends
        if ml_router.performance_history:
            recent_performance = ml_router.performance_history[-10:]
            stats["recent_avg_processing_time"] = np.mean([p.processing_time_ms for p in recent_performance])
            stats["recent_avg_accuracy"] = np.mean([p.accuracy_score for p in recent_performance])

        # User satisfaction trends
        if ml_router.user_feedback_history:
            recent_feedback = ml_router.user_feedback_history[-20:]
            stats["recent_avg_user_rating"] = np.mean([f.user_rating for f in recent_feedback])
            stats["recent_avg_speed_rating"] = np.mean([f.speed_rating for f in recent_feedback])
            stats["recent_avg_accuracy_rating"] = np.mean([f.accuracy_rating for f in recent_feedback])

        return stats

    except Exception as e:
        return {"error": f"Error getting ML stats: {str(e)}"}

@app.post("/retrain-ml-router")
async def retrain_ml_router():
    """
    🔄 Manually trigger ML router retraining (admin endpoint)
    """
    try:
        from services.ml_query_router import ml_router

        if not ml_router:
            return {"message": "ML router not available"}

        # Trigger retraining
        ml_router._retrain_performance_model()
        ml_router.save_model()

        return {
            "message": "ML router retrained successfully",
            "performance_history_count": len(ml_router.performance_history),
            "feedback_count": len(ml_router.user_feedback_history)
        }

    except Exception as e:
        return {"error": f"Retraining failed: {str(e)}"}

# Query endpoints
@app.post("/ask", response_model=schemas.QueryResponse)
async def ask_question(query: schemas.QueryRequest, db: Session = Depends(get_db)):
    """
    Ask a natural language question about a dataset
    """
    # Ensure user exists in database (auto-create if needed)
    user = db.query(models.User).filter(models.User.id == query.user_id).first()
    if not user:
        # Auto-create user for OAuth users
        user = models.User(
            id=query.user_id,
            email=f"user-{query.user_id}@oauth.local",  # Placeholder email
            name="OAuth User",
            role="user",
            subscription_status="free",
            referral_code=f"REF{query.user_id[:8]}"
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        print(f"✅ Auto-created user: {query.user_id}")

    # Get dataset from database - ensure user owns the dataset
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == query.dataset_id,
        models.Dataset.user_id == query.user_id  # Security: verify ownership
    ).first()

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found or access denied"
        )

    try:
        # Start timing
        import time
        start_time = time.time()

        # Check if this is a document dataset
        if dataset.content_type == 'document':
            # Use document-specific processing with vector search
            try:
                result = llm_service.process_document_query(
                    question=query.question,
                    dataset_id=dataset.id,
                    db_session=db,
                    include_trust_score=query.include_trust_score
                )
            except Exception as doc_error:
                print(f"⚠️ Document processing failed, falling back to standard processing: {doc_error}")
                # Fallback to standard processing if document processing fails
                df = pd.read_json(StringIO(dataset.parsed_data))
                result = llm_service.process_query(
                    question=query.question,
                    df=df,
                    include_trust_score=query.include_trust_score
                )
        else:
            # Parse dataset for tabular data
            df = pd.read_json(StringIO(dataset.parsed_data))

            # 🚀 USE PHASE 1 PRODUCTION QUERY ENGINE
            print(f"🎯 Processing with Phase 1 Production Engine: {query.question}")

            try:
                from services.production_query_engine import production_engine

                # Use the bulletproof Phase 1 engine
                production_result = await production_engine.process_query(df, query.question)

                # Convert to expected format
                result = {
                    "answer": production_result.answer,
                    "chart_type": None,  # Phase 1 focuses on text answers
                    "chart_data": None,
                    "processing_time": 0,  # Will be calculated below
                    "confidence": production_result.confidence,
                    "operation": production_result.operation,
                    "target_column": production_result.target_column,
                    "filter_column": production_result.filter_column,
                    "filter_value": production_result.filter_value,
                    "rows_matched": production_result.rows_matched,
                    "total_rows": production_result.total_rows
                }

                print(f"✅ Phase 1 Result: {production_result.answer}")
                print(f"🎯 Operation: {production_result.operation}, Confidence: {production_result.confidence}")

                # Add trust score if requested
                if query.include_trust_score:
                    try:
                        trust_score = llm_service.calculate_simple_trust_score(
                            question=query.question,
                            answer=result.get("answer", ""),
                            df=df
                        )
                        result["trust_score"] = trust_score
                    except Exception as trust_error:
                        print(f"Trust score calculation failed: {trust_error}")
                        result["trust_score"] = {
                            "overall_score": production_result.confidence,
                            "explanation": f"Based on Phase 1 engine confidence: {production_result.confidence}"
                        }

            except Exception as phase1_error:
                print(f"⚠️ Phase 1 engine failed, falling back to LLM: {phase1_error}")

                # Fallback to LLM processing
                if query.include_cot:
                    result = llm_service.process_query_with_cot(
                        question=query.question,
                        df=df,
                        include_trust_score=False
                    )

                    if query.include_trust_score:
                        try:
                            trust_score = llm_service.calculate_simple_trust_score(
                                question=query.question,
                                answer=result.get("answer", ""),
                                df=df
                            )
                            result["trust_score"] = trust_score
                        except Exception as trust_error:
                            print(f"Error calculating trust score separately: {str(trust_error)}")
                            result["trust_score"] = {
                                "overall_score": 0.5,
                                "factors": ["Error in trust calculation"],
                                "explanation": f"Could not calculate trust score: {str(trust_error)}"
                            }
                else:
                    result = llm_service.process_query(
                        question=query.question,
                        df=df,
                        include_trust_score=query.include_trust_score
                    )

        # Generate chart if needed
        chart_data = None
        chart_type = result.get("chart_type")

        # Handle case when chart_type is "null" as a string or other invalid values
        if chart_type and chart_type.lower() != "null":
            try:
                # Handle list values in chart_data
                chart_data_dict = result.get("chart_data", {})

                # Convert any list values to strings for chart generation
                if isinstance(chart_data_dict, dict):
                    for key, value in chart_data_dict.items():
                        if isinstance(value, list):
                            # If the list contains column names, we need to check if they exist
                            if all(isinstance(item, str) for item in value):
                                missing_cols = [col for col in value if col not in df.columns]
                                if missing_cols:
                                    print(f"Warning: Columns {missing_cols} not found in dataset")
                                    # Use only columns that exist in the dataset
                                    chart_data_dict[key] = [col for col in value if col in df.columns]
                                    if not chart_data_dict[key]:  # If no valid columns remain
                                        if key == "x":
                                            chart_data_dict[key] = df.columns[0]  # Use first column as fallback
                                        elif key == "y":
                                            # Use first numeric column as fallback
                                            numeric_cols = df.select_dtypes(include=['number']).columns
                                            chart_data_dict[key] = numeric_cols[0] if len(numeric_cols) > 0 else df.columns[0]

                chart_data = chart_service.generate_chart(
                    df,
                    chart_type,
                    chart_data_dict
                )
            except Exception as chart_error:
                print(f"Error generating chart: {str(chart_error)}")
                # Don't fail the whole request if chart generation fails
                chart_data = None
                # Set chart_type to None so frontend doesn't try to render it
                result["chart_type"] = None

        # Calculate processing time
        end_time = time.time()
        processing_time = int((end_time - start_time) * 1000)  # Convert to milliseconds

        # Ensure answer is a string
        answer_value = result.get("answer")
        if not isinstance(answer_value, str):
            answer_value = str(answer_value) if answer_value is not None else "No answer generated"

        # Save query to database
        db_query = models.Query(
            user_id=query.user_id,
            dataset_id=query.dataset_id,
            question=query.question,
            answer=answer_value,
            chart_type=result.get("chart_type"),
            chart_data=json.dumps(chart_data) if chart_data else None,
            trust_score=json.dumps(result.get("trust_score")) if result.get("trust_score") else None,
            reasoning_steps=json.dumps(result.get("reasoning_steps")) if result.get("reasoning_steps") else None,
            formula_results=json.dumps(result.get("formula_results")) if result.get("formula_results") else None,
            processing_time=processing_time
        )

        db.add(db_query)
        db.commit()
        db.refresh(db_query)

        # Prepare response
        response_data = {
            "id": db_query.id,
            "answer": db_query.answer,
            "chart_type": db_query.chart_type,
            "chart_data": json.loads(db_query.chart_data) if db_query.chart_data else None,
            "created_at": db_query.created_at,
            "processing_time": db_query.processing_time
        }

        # Add trust score if available
        if db_query.trust_score:
            response_data["trust_score"] = json.loads(db_query.trust_score)

        # Add reasoning steps if available
        if db_query.reasoning_steps:
            response_data["reasoning_steps"] = json.loads(db_query.reasoning_steps)

        # Add formula results if available
        if db_query.formula_results:
            response_data["formula_results"] = json.loads(db_query.formula_results)

        return response_data

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing query: {str(e)}"
        )

# Query management endpoints
@app.get("/queries/{user_id}", response_model=List[schemas.QueryHistoryResponse])
async def get_user_queries(user_id: str, db: Session = Depends(get_db)):
    """
    Get all queries for a user with dataset information
    """
    # Use raw SQL for better performance and to include dataset names
    from sqlalchemy import text

    query_sql = text("""
        SELECT
            q.id,
            q.user_id,
            q.dataset_id,
            d.name as dataset_name,
            q.question,
            q.answer,
            q.chart_type,
            q.chart_data,
            q.trust_score,
            q.reasoning_steps,
            q.query_name,
            q.is_bookmarked,
            q.tags,
            q.created_at,
            CASE WHEN sq.id IS NOT NULL THEN TRUE ELSE FALSE END as is_saved
        FROM queries q
        LEFT JOIN datasets d ON q.dataset_id = d.id
        LEFT JOIN saved_queries sq ON q.id = sq.query_id AND q.user_id = sq.user_id
        WHERE q.user_id = :user_id
        ORDER BY q.created_at DESC
    """)

    result = db.execute(query_sql, {"user_id": user_id}).fetchall()

    return [
        {
            "id": row.id,
            "user_id": row.user_id,
            "dataset_id": row.dataset_id,
            "dataset_name": row.dataset_name,
            "question": row.question,
            "answer": row.answer,
            "chart_type": row.chart_type,
            "chart_data": json.loads(row.chart_data) if row.chart_data else None,
            "trust_score": json.loads(row.trust_score) if row.trust_score else None,
            "reasoning_steps": json.loads(row.reasoning_steps) if row.reasoning_steps else None,
            "query_name": row.query_name,
            "is_bookmarked": row.is_bookmarked or False,
            "tags": json.loads(row.tags) if row.tags else None,
            "created_at": row.created_at,
            "is_saved": row.is_saved or False
        }
        for row in result
    ]

@app.get("/saved-queries/{user_id}", response_model=List[schemas.SavedQueryResponse])
async def get_user_saved_queries(user_id: str, db: Session = Depends(get_db)):
    """
    Get all saved queries for a user
    """
    saved_queries = db.query(models.SavedQuery).filter(
        models.SavedQuery.user_id == user_id
    ).order_by(models.SavedQuery.created_at.desc()).all()

    return [
        {
            "id": sq.id,
            "user_id": sq.user_id,
            "query_id": sq.query_id,
            "name": sq.name,
            "description": sq.description,
            "tags": json.loads(sq.tags) if sq.tags else None,
            "is_favorite": sq.is_favorite,
            "created_at": sq.created_at,
            "updated_at": sq.updated_at
        }
        for sq in saved_queries
    ]

@app.post("/saved-queries", response_model=schemas.SavedQueryResponse)
async def save_query(saved_query: schemas.SavedQueryCreate, user_id: str, db: Session = Depends(get_db)):
    """
    Save a query for a user
    """
    # Verify the query exists and belongs to the user
    query = db.query(models.Query).filter(
        models.Query.id == saved_query.query_id,
        models.Query.user_id == user_id
    ).first()

    if not query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Query not found or access denied"
        )

    # Check if already saved
    existing = db.query(models.SavedQuery).filter(
        models.SavedQuery.user_id == user_id,
        models.SavedQuery.query_id == saved_query.query_id
    ).first()

    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Query is already saved"
        )

    # Create saved query
    db_saved_query = models.SavedQuery(
        user_id=user_id,
        query_id=saved_query.query_id,
        name=saved_query.name,
        description=saved_query.description,
        tags=json.dumps(saved_query.tags) if saved_query.tags else None,
        is_favorite=saved_query.is_favorite
    )

    db.add(db_saved_query)
    db.commit()
    db.refresh(db_saved_query)

    return {
        "id": db_saved_query.id,
        "user_id": db_saved_query.user_id,
        "query_id": db_saved_query.query_id,
        "name": db_saved_query.name,
        "description": db_saved_query.description,
        "tags": json.loads(db_saved_query.tags) if db_saved_query.tags else None,
        "is_favorite": db_saved_query.is_favorite,
        "created_at": db_saved_query.created_at,
        "updated_at": db_saved_query.updated_at
    }

@app.delete("/saved-queries/{saved_query_id}")
async def delete_saved_query(saved_query_id: int, user_id: str, db: Session = Depends(get_db)):
    """
    Delete a saved query
    """
    saved_query = db.query(models.SavedQuery).filter(
        models.SavedQuery.id == saved_query_id,
        models.SavedQuery.user_id == user_id
    ).first()

    if not saved_query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Saved query not found or access denied"
        )

    db.delete(saved_query)
    db.commit()

    return {"message": "Saved query deleted successfully"}

@app.delete("/queries/{query_id}")
async def delete_query(
    query_id: int,
    user_id: str,  # Add user_id parameter for security
    db: Session = Depends(get_db)
):
    """
    Delete a specific query (user-specific)
    """
    query = db.query(models.Query).filter(
        models.Query.id == query_id,
        models.Query.user_id == user_id  # Security: verify ownership
    ).first()

    if not query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Query not found or access denied"
        )

    db.delete(query)
    db.commit()

    return {"message": "Query deleted successfully"}

@app.delete("/datasets/{dataset_id}")
async def delete_dataset(
    dataset_id: int,
    user_id: str,  # Add user_id parameter for security
    db: Session = Depends(get_db)
):
    """
    Delete a dataset and all its associated queries (user-specific)
    """
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == dataset_id,
        models.Dataset.user_id == user_id  # Security: verify ownership
    ).first()

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found or access denied"
        )

    # Delete all associated queries first
    db.query(models.Query).filter(models.Query.dataset_id == dataset_id).delete()

    # Delete the dataset
    db.delete(dataset)
    db.commit()

    return {"message": "Dataset and associated queries deleted successfully"}

# Stripe endpoints
@app.post("/checkout", response_model=schemas.CheckoutResponse)
async def create_checkout_session(checkout: schemas.CheckoutRequest):
    """
    Create a Stripe checkout session
    """
    try:
        # Check if stripe service is available
        try:
            from services import stripe_service
            session = stripe_service.create_checkout_session(
                user_id=checkout.user_id,
                price_id=checkout.price_id,
                success_url=checkout.success_url,
                cancel_url=checkout.cancel_url
            )
            return {"checkout_url": session.url}
        except ImportError:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Stripe service is not available"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating checkout session: {str(e)}"
        )

@app.post("/webhook", status_code=status.HTTP_200_OK)
async def stripe_webhook(request: schemas.WebhookRequest, db: Session = Depends(get_db)):
    """
    Handle Stripe webhook events
    """
    try:
        # Check if stripe service is available
        try:
            from services import stripe_service
            event = stripe_service.construct_event(request.payload, request.signature)
        except ImportError:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Stripe service is not available"
            )

        # Handle the event
        if event['type'] == 'checkout.session.completed':
            session = event['data']['object']

            # Update user subscription status
            user_id = session.get('client_reference_id')
            if user_id:
                user = db.query(models.User).filter(models.User.id == user_id).first()

                if user:
                    user.subscription_status = "active"
                    user.stripe_customer_id = session.get('customer')
                    db.commit()

        return {"status": "success"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Webhook error: {str(e)}"
        )

@app.post("/fix-file-types")
async def fix_file_types(db: Session = Depends(get_db)):
    """
    Fix file types for existing datasets based on their names
    """
    try:
        datasets = db.query(models.Dataset).all()
        fixed_count = 0

        for dataset in datasets:
            if dataset.name and '.' in dataset.name:
                # Extract file extension from name
                file_extension = dataset.name.split('.')[-1].lower()

                # Only update if it's a known file type and currently wrong
                if file_extension in ['pdf', 'docx', 'txt', 'md', 'jpg', 'jpeg', 'png', 'xlsx', 'xls'] and dataset.file_type != file_extension:
                    print(f"🔧 Fixing dataset {dataset.id}: {dataset.name} - changing file_type from '{dataset.file_type}' to '{file_extension}'")
                    dataset.file_type = file_extension

                    # Also fix content_type based on file extension
                    if file_extension in ['csv', 'xlsx', 'xls']:
                        dataset.content_type = 'tabular'
                    else:
                        dataset.content_type = 'document'

                    fixed_count += 1

        db.commit()
        return {"message": f"Fixed {fixed_count} datasets", "fixed_count": fixed_count}

    except Exception as e:
        print(f"Error fixing file types: {e}")
        return {"error": str(e)}

@app.post("/create-user/{user_id}")
async def create_user(user_id: str, db: Session = Depends(get_db)):
    """Create a user - for initial setup"""
    try:
        # Check if user already exists
        existing_user = db.query(models.User).filter(models.User.id == user_id).first()
        if existing_user:
            return {
                "status": "success",
                "message": f"User {existing_user.email} already exists",
                "user": {
                    "id": existing_user.id,
                    "email": existing_user.email,
                    "role": existing_user.role
                }
            }

        # Create new user
        user = models.User(
            id=user_id,
            email=user_id,  # Use email as ID
            name=user_id.split('@')[0],  # Use part before @ as name
            role="user",
            subscription_status="free",
            referral_code=f"REF{user_id[:8]}"
        )
        db.add(user)
        db.commit()
        db.refresh(user)

        return {
            "status": "success",
            "message": f"User {user.email} created successfully",
            "user": {
                "id": user.id,
                "email": user.email,
                "role": user.role
            }
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.post("/make-admin/{user_id}")
async def make_user_admin(user_id: str, db: Session = Depends(get_db)):
    """Make a user admin - for initial setup"""
    try:
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user:
            # Auto-create user if they don't exist
            user = models.User(
                id=user_id,
                email=user_id,  # Use email as ID
                name=user_id.split('@')[0],  # Use part before @ as name
                role="admin",  # Create as admin directly
                subscription_status="free",
                referral_code=f"REF{user_id[:8]}"
            )
            db.add(user)
            db.commit()
            db.refresh(user)

            return {
                "status": "success",
                "message": f"User {user.email} created and made admin",
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "role": user.role
                }
            }

        user.role = "admin"
        db.commit()

        return {
            "status": "success",
            "message": f"User {user.email} is now an admin",
            "user": {
                "id": user.id,
                "email": user.email,
                "role": user.role
            }
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/debug/users")
async def debug_users(db: Session = Depends(get_db)):
    """Debug endpoint to see all users"""
    try:
        users = db.query(models.User).all()
        return {
            "total_users": len(users),
            "users": [
                {
                    "id": user.id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.role
                }
                for user in users
            ]
        }
    except Exception as e:
        return {"error": str(e)}

@app.post("/make-admin-by-email/{email}")
async def make_user_admin_by_email(email: str, db: Session = Depends(get_db)):
    """Make a user admin by email - for initial setup"""
    try:
        # Find user by email (either in id field or email field)
        user = db.query(models.User).filter(
            (models.User.email == email) | (models.User.id == email)
        ).first()

        if not user:
            return {
                "status": "error",
                "message": f"No user found with email {email}. Available users:",
                "available_users": [
                    {"id": u.id, "email": u.email, "name": u.name}
                    for u in db.query(models.User).all()
                ]
            }

        user.role = "admin"
        db.commit()

        return {
            "status": "success",
            "message": f"User {user.email} is now an admin",
            "user": {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "role": user.role
            }
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/debug/queries")
async def debug_queries(db: Session = Depends(get_db)):
    """Debug endpoint to see all queries"""
    try:
        queries = db.query(models.Query).order_by(models.Query.created_at.desc()).limit(10).all()
        return {
            "total_queries": db.query(models.Query).count(),
            "recent_queries": [
                {
                    "id": query.id,
                    "user_id": query.user_id,
                    "question": query.question[:100] + "..." if len(query.question) > 100 else query.question,
                    "created_at": query.created_at.isoformat() if query.created_at else None
                }
                for query in queries
            ]
        }
    except Exception as e:
        return {"error": str(e)}

@app.post("/debug/create-test-feedback")
async def create_test_feedback(db: Session = Depends(get_db)):
    """Create multiple test feedback entries for debugging"""
    try:
        # Get queries and users
        queries = db.query(models.Query).limit(5).all()
        users = db.query(models.User).all()

        if not queries or not users:
            return {"error": "No queries or users found. Upload a dataset and ask a question first."}

        created_feedback = []

        # Create multiple test feedback entries
        test_feedback_data = [
            {"rating": "up", "comment": "Great answer! Very helpful and accurate."},
            {"rating": "down", "comment": "The answer could be more detailed and specific."},
            {"rating": "up", "comment": "Perfect! Exactly what I was looking for."},
            {"rating": "down", "comment": "The chart doesn't match the data properly."},
            {"rating": "up", "comment": None},  # Positive feedback without comment
        ]

        for i, feedback_data in enumerate(test_feedback_data):
            if i < len(queries) and i < len(users):
                query = queries[i]
                user = users[i % len(users)]  # Cycle through users if fewer users than queries

                # Check if feedback already exists
                existing_feedback = db.query(models.Feedback).filter(
                    models.Feedback.user_id == user.id,
                    models.Feedback.query_id == query.id
                ).first()

                if not existing_feedback:
                    # Create test feedback
                    test_feedback = models.Feedback(
                        user_id=user.id,
                        query_id=query.id,
                        rating=feedback_data["rating"],
                        comment=feedback_data["comment"]
                    )

                    db.add(test_feedback)
                    created_feedback.append({
                        "query_id": query.id,
                        "user_id": user.id,
                        "rating": feedback_data["rating"],
                        "comment": feedback_data["comment"]
                    })

        db.commit()

        return {
            "message": f"Created {len(created_feedback)} test feedback entries",
            "created_feedback": created_feedback,
            "total_feedback_in_db": db.query(models.Feedback).count()
        }
    except Exception as e:
        return {"error": str(e)}

# Admin endpoints for Research Documents and Test Scripts

# Debug endpoint to test admin access
@app.get("/admin/debug-test")
async def debug_admin_test(
    user_id: str,
    admin_user: models.User = Depends(require_admin_role),
    db: Session = Depends(get_db)
):
    """
    Debug endpoint to test admin access
    """
    return {
        "status": "success",
        "message": "Admin access working",
        "user_id": user_id,
        "admin_user": admin_user.email if admin_user else None
    }

@app.get("/admin/research-documents")
async def get_research_documents(
    user_id: str,
    admin_user: models.User = Depends(require_admin_role),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Get all research documents (*.md files)
    """
    try:
        documents = []

        # Get all .md files from the ResearchDocuments directory
        md_files = glob.glob("ResearchDocuments/*.md")

        for file_path in md_files:
            try:
                file_stat = os.stat(file_path)
                file_name = os.path.basename(file_path)

                documents.append({
                    "name": file_name,
                    "path": file_path,
                    "size": file_stat.st_size,
                    "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                })
            except Exception as e:
                continue  # Skip files that can't be accessed

        # Sort by modification date (newest first)
        documents.sort(key=lambda x: x["modified"], reverse=True)

        return documents
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load research documents: {str(e)}"
        )

@app.get("/admin/research-documents/{document_name}")
async def get_research_document_content(
    document_name: str,
    user_id: str,
    admin_user: models.User = Depends(require_admin_role),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Get content of a specific research document
    """
    try:
        # Sanitize the document name to prevent path traversal
        safe_name = os.path.basename(document_name)
        file_path = f"ResearchDocuments/{safe_name}"

        if not os.path.exists(file_path) or not file_path.endswith('.md'):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )

        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        return {"content": content}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to read document: {str(e)}"
        )

@app.get("/admin/research-documents/{document_name}/download")
async def download_research_document(
    document_name: str,
    user_id: str = None,
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Download a research document
    """
    try:
        # Verify admin access
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User ID required for admin access"
            )

        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user or user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        # Sanitize the document name to prevent path traversal
        safe_name = os.path.basename(document_name)
        file_path = f"ResearchDocuments/{safe_name}"

        if not os.path.exists(file_path) or not file_path.endswith('.md'):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )

        return FileResponse(
            file_path,
            media_type='text/markdown',
            filename=safe_name
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download document: {str(e)}"
        )

@app.get("/admin/test-scripts")
async def get_test_scripts(
    user_id: str,
    admin_user: models.User = Depends(require_admin_role),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Get all test scripts
    """
    try:
        scripts = []

        # Define test script locations and patterns
        test_locations = [
            ("../Test", "*.py"),  # All Python files in Test folder
            ("../Test", "*.js"),  # All JavaScript files in Test folder
        ]

        for directory, pattern in test_locations:
            try:
                search_pattern = os.path.join(directory, pattern)
                files = glob.glob(search_pattern)

                for file_path in files:
                    try:
                        file_stat = os.stat(file_path)
                        file_name = os.path.basename(file_path)

                        # Determine script type
                        if file_name.endswith('.py'):
                            script_type = 'python'
                        elif file_name.endswith('.js'):
                            script_type = 'javascript'
                        else:
                            script_type = 'other'

                        # Get directory name for display
                        dir_name = os.path.basename(os.path.dirname(file_path)) if directory != "." else "root"

                        # Use relative path from backend directory for download
                        relative_path = file_path.replace("../", "")

                        scripts.append({
                            "name": file_name,
                            "path": relative_path,  # Use clean relative path
                            "directory": dir_name,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                            "type": script_type
                        })
                    except Exception as e:
                        continue  # Skip files that can't be accessed
            except Exception as e:
                continue  # Skip directories that can't be accessed

        # Remove duplicates based on file path
        unique_scripts = {}
        for script in scripts:
            unique_scripts[script["path"]] = script

        scripts = list(unique_scripts.values())

        # Sort by modification date (newest first)
        scripts.sort(key=lambda x: x["modified"], reverse=True)

        return scripts
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load test scripts: {str(e)}"
        )

@app.get("/admin/test-scripts/{script_path:path}")
async def get_test_script_content(
    script_path: str,
    user_id: str,
    admin_user: models.User = Depends(require_admin_role),
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Get content of a specific test script
    """
    try:
        # Decode the path and ensure it's safe
        import urllib.parse
        decoded_path = urllib.parse.unquote(script_path)

        # Basic security check - ensure the path doesn't go outside allowed directories
        if decoded_path.startswith("/") or ".." in decoded_path:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file path"
            )

        # Construct the full path from backend directory
        # Normalize path separators for cross-platform compatibility
        normalized_path = decoded_path.replace('\\', '/')
        full_path = f"../{normalized_path}"

        # Check if file exists and is a valid script
        if not os.path.exists(full_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Script not found"
            )

        # Ensure it's a script file
        if not (full_path.endswith('.py') or full_path.endswith('.js')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Not a valid script file"
            )

        with open(full_path, 'r', encoding='utf-8') as file:
            content = file.read()

        return {"content": content}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to read script: {str(e)}"
        )

@app.get("/admin/debug-script-path/{script_path:path}")
async def debug_script_path(script_path: str):
    """Debug endpoint to check script path resolution"""
    import urllib.parse
    decoded_path = urllib.parse.unquote(script_path)
    normalized_path = decoded_path.replace('\\', '/')
    full_path = f"../{normalized_path}"

    return {
        "raw_path": script_path,
        "decoded_path": decoded_path,
        "normalized_path": normalized_path,
        "full_path": full_path,
        "exists": os.path.exists(full_path),
        "is_file": os.path.isfile(full_path) if os.path.exists(full_path) else False,
        "current_dir": os.getcwd()
    }

@app.get("/admin/test-scripts/download/{script_name}")
async def download_test_script(
    script_name: str,
    user_id: str = None,
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Download a test script
    """
    try:
        # Verify admin access
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User ID required for admin access"
            )

        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user or user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )

        # Sanitize script name
        safe_name = os.path.basename(script_name)
        file_path = f"../Test/{safe_name}"

        if not os.path.exists(file_path) or not (file_path.endswith('.py') or file_path.endswith('.js')):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Script not found"
            )

        media_type = 'text/x-python' if file_path.endswith('.py') else 'text/javascript'

        return FileResponse(
            file_path,
            media_type=media_type,
            filename=safe_name
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download script: {str(e)}"
        )

@app.post("/admin/fix-database")
async def fix_database(
    request: dict,
    db: Session = Depends(get_db)
):
    """
    Admin endpoint: Fix database schema issues
    """
    try:
        user_id = request.get("user_id")
        action = request.get("action", "add_monitoring_fields")

        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User ID required for admin access"
            )

        # Verify admin access
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user or user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )

        if action == "add_monitoring_fields":
            # Add missing monitoring fields to queries table
            from sqlalchemy import text

            monitoring_fields = [
                "ALTER TABLE queries ADD COLUMN response_time_ms REAL;",
                "ALTER TABLE queries ADD COLUMN token_count INTEGER;",
                "ALTER TABLE queries ADD COLUMN performance_grade VARCHAR;",
                "ALTER TABLE queries ADD COLUMN hallucination_risk REAL;",
                "ALTER TABLE queries ADD COLUMN completeness_score REAL;",
                "ALTER TABLE queries ADD COLUMN source_count INTEGER;"
            ]

            results = []

            for field_sql in monitoring_fields:
                try:
                    db.execute(text(field_sql))
                    field_name = field_sql.split('ADD COLUMN')[1].split()[0]
                    results.append(f"✅ Added field: {field_name}")
                except Exception as e:
                    field_name = field_sql.split('ADD COLUMN')[1].split()[0]
                    if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                        results.append(f"⚠️ Field already exists: {field_name}")
                    else:
                        results.append(f"❌ Failed to add field {field_name}: {str(e)}")

            # Add indexes for performance
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_queries_performance_grade ON queries(performance_grade);",
                "CREATE INDEX IF NOT EXISTS idx_queries_response_time ON queries(response_time_ms);",
                "CREATE INDEX IF NOT EXISTS idx_queries_hallucination_risk ON queries(hallucination_risk);",
                "CREATE INDEX IF NOT EXISTS idx_queries_completeness ON queries(completeness_score);"
            ]

            for index_sql in indexes:
                try:
                    db.execute(text(index_sql))
                    index_name = index_sql.split('idx_')[1].split()[0]
                    results.append(f"✅ Created index: {index_name}")
                except Exception as e:
                    results.append(f"⚠️ Index may already exist: {str(e)}")

            db.commit()

            return {
                "status": "success",
                "message": "Database schema updated successfully",
                "action": action,
                "results": results,
                "admin_user": user.email
            }

        else:
            return {
                "status": "error",
                "message": f"Unknown action: {action}",
                "available_actions": ["add_monitoring_fields"]
            }

    except HTTPException:
        raise
    except Exception as e:
        return {
            "status": "error",
            "message": f"Database fix failed: {str(e)}"
        }

# Run the application
if __name__ == "__main__":
    import uvicorn
    import os
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=False)
