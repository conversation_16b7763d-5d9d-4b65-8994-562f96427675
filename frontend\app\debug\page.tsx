'use client';

import { useState } from 'react';
import api from '@/lib/api';

export default function DebugPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    setResult('Testing...\n');
    
    try {
      // Test 1: Basic connection
      setResult(prev => prev + '1. Testing basic connection...\n');
      const basicResponse = await api.get('/test');
      setResult(prev => prev + `✅ Basic: ${JSON.stringify(basicResponse.data)}\n\n`);
      
      // Test 2: Feedback stats
      setResult(prev => prev + '2. Testing feedback stats...\n');
      const statsResponse = await api.get('/feedback/admin/stats');
      setResult(prev => prev + `✅ Stats: ${JSON.stringify(statsResponse.data)}\n\n`);
      
      // Test 3: Feedback list
      setResult(prev => prev + '3. Testing feedback list...\n');
      const feedbackResponse = await api.get('/feedback/admin/all');
      setResult(prev => prev + `✅ Feedback: ${feedbackResponse.data.length} items\n`);
      setResult(prev => prev + `First item: ${JSON.stringify(feedbackResponse.data[0], null, 2)}\n\n`);
      
      setResult(prev => prev + '🎉 All tests passed!\n');
      
    } catch (error: any) {
      setResult(prev => prev + `❌ Error: ${error.message}\n`);
      setResult(prev => prev + `Details: ${JSON.stringify(error.response?.data || error, null, 2)}\n`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">API Debug Page</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <button
            onClick={testAPI}
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test API Connection'}
          </button>
        </div>
        
        {result && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap">
              {result}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
