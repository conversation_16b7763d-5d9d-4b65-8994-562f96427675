#!/usr/bin/env python3
"""
Security Check Script for AIthentiq
Scans for sensitive files and data before GitHub push
"""

import os
import re
import glob
from pathlib import Path

class SecurityChecker:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.sensitive_patterns = [
            r'sk-[a-zA-Z0-9]{48}',  # OpenAI API keys
            r'pk_test_[a-zA-Z0-9]+',  # Stripe publishable keys
            r'sk_test_[a-zA-Z0-9]+',  # Stripe secret keys
            r'whsec_[a-zA-Z0-9]+',   # Webhook secrets
            r'pk_live_[a-zA-Z0-9]+', # Stripe live keys
            r'sk_live_[a-zA-Z0-9]+', # Stripe live secret keys
        ]
        
        self.sensitive_files = [
            '.env',
            '.env.local',
            '.env.production',
            '.env.development',
            'backend/.env',
            'frontend/.env.local',
        ]
        
        self.large_folders = [
            'backend/venv',
            'backend/venv_new',
            'frontend/node_modules',
            'backend/__pycache__',
            'backend/datasets',
        ]
        
        self.database_files = [
            '*.db',
            '*.sqlite',
            '*.sqlite3',
        ]
    
    def scan_for_api_keys(self):
        """Scan all text files for API keys"""
        print("🔍 Scanning for API keys and secrets...")
        found_secrets = []
        
        # Get all text files
        text_files = []
        for ext in ['*.py', '*.js', '*.ts', '*.tsx', '*.json', '*.md', '*.txt', '*.env*']:
            text_files.extend(self.project_root.rglob(ext))
        
        for file_path in text_files:
            # Skip large folders
            if any(folder in str(file_path) for folder in ['node_modules', 'venv', '__pycache__']):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                for pattern in self.sensitive_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        found_secrets.append({
                            'file': str(file_path),
                            'pattern': pattern,
                            'matches': matches
                        })
            except Exception as e:
                continue
        
        return found_secrets
    
    def check_sensitive_files(self):
        """Check for sensitive files that shouldn't be committed"""
        print("📁 Checking for sensitive files...")
        found_files = []
        
        for file_pattern in self.sensitive_files:
            file_path = self.project_root / file_pattern
            if file_path.exists():
                found_files.append(str(file_path))
        
        return found_files
    
    def check_large_folders(self):
        """Check for large folders that shouldn't be committed"""
        print("📂 Checking for large folders...")
        found_folders = []
        
        for folder in self.large_folders:
            folder_path = self.project_root / folder
            if folder_path.exists():
                # Calculate folder size
                total_size = sum(f.stat().st_size for f in folder_path.rglob('*') if f.is_file())
                size_mb = total_size / (1024 * 1024)
                found_folders.append({
                    'path': str(folder_path),
                    'size_mb': round(size_mb, 2)
                })
        
        return found_folders
    
    def check_database_files(self):
        """Check for database files"""
        print("🗄️ Checking for database files...")
        found_dbs = []
        
        for pattern in self.database_files:
            for db_file in self.project_root.rglob(pattern):
                if db_file.is_file():
                    size_mb = db_file.stat().st_size / (1024 * 1024)
                    found_dbs.append({
                        'path': str(db_file),
                        'size_mb': round(size_mb, 2)
                    })
        
        return found_dbs
    
    def run_security_check(self):
        """Run complete security check"""
        print("🛡️ AIthentiq Security Check")
        print("=" * 50)
        
        # Check for API keys
        secrets = self.scan_for_api_keys()
        if secrets:
            print("❌ FOUND API KEYS/SECRETS:")
            for secret in secrets:
                print(f"   File: {secret['file']}")
                print(f"   Pattern: {secret['pattern']}")
                print(f"   Matches: {len(secret['matches'])}")
                print()
        else:
            print("✅ No API keys found in code")
        
        # Check sensitive files
        sensitive_files = self.check_sensitive_files()
        if sensitive_files:
            print("❌ FOUND SENSITIVE FILES:")
            for file in sensitive_files:
                print(f"   {file}")
            print()
        else:
            print("✅ No sensitive files found")
        
        # Check large folders
        large_folders = self.check_large_folders()
        if large_folders:
            print("⚠️ LARGE FOLDERS (should be in .gitignore):")
            for folder in large_folders:
                print(f"   {folder['path']} ({folder['size_mb']} MB)")
            print()
        else:
            print("✅ No large folders found")
        
        # Check database files
        db_files = self.check_database_files()
        if db_files:
            print("⚠️ DATABASE FILES (should be in .gitignore):")
            for db in db_files:
                print(f"   {db['path']} ({db['size_mb']} MB)")
            print()
        else:
            print("✅ No database files found")
        
        # Summary
        print("=" * 50)
        total_issues = len(secrets) + len(sensitive_files)
        if total_issues == 0:
            print("🎉 SECURITY CHECK PASSED!")
            print("✅ Safe to push to GitHub")
        else:
            print("🚨 SECURITY ISSUES FOUND!")
            print("❌ DO NOT push to GitHub until issues are resolved")
            print("\nRecommended actions:")
            if secrets:
                print("- Remove API keys from code files")
                print("- Use environment variables instead")
            if sensitive_files:
                print("- Delete or move sensitive files")
                print("- Ensure .gitignore is properly configured")
        
        print("=" * 50)

if __name__ == "__main__":
    checker = SecurityChecker()
    checker.run_security_check()
