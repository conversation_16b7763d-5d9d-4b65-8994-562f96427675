"""
Pydantic schemas for multi-tenant API endpoints
"""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

class SubscriptionPlan(str, Enum):
    FREE = "free"
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"

class TaskType(str, Enum):
    DOCUMENT_PROCESSING = "document_processing"
    EMBEDDING_GENERATION = "embedding_generation"
    CONNECTOR_SYNC = "connector_sync"
    TRUST_SCORE_CALCULATION = "trust_score_calculation"
    DATA_EXPORT = "data_export"
    CLEANUP = "cleanup"

class TaskPriority(str, Enum):
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"

# Tenant Schemas

class TenantCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    domain: str = Field(..., min_length=1, max_length=50)
    display_name: str = Field(..., min_length=1, max_length=100)
    admin_email: EmailStr
    admin_name: Optional[str] = None
    subscription_plan: SubscriptionPlan = SubscriptionPlan.FREE
    max_users: Optional[int] = 5
    max_datasets: Optional[int] = 10
    max_storage_gb: Optional[float] = 1.0

class TenantResponse(BaseModel):
    id: str
    name: str
    domain: str
    display_name: str
    subscription_plan: str
    features_enabled: Dict[str, Any]
    preferred_llm: str
    preferred_embedding: str
    preferred_vector_store: str
    is_active: bool
    is_trial: bool
    created_at: datetime

    class Config:
        from_attributes = True

class TenantConfigUpdate(BaseModel):
    preferred_llm: Optional[str] = None
    preferred_embedding: Optional[str] = None
    preferred_vector_store: Optional[str] = None
    features_enabled: Optional[Dict[str, Any]] = None
    require_2fa: Optional[bool] = None
    allowed_domains: Optional[List[str]] = None
    ip_whitelist: Optional[List[str]] = None

# User Schemas

class UserCreate(BaseModel):
    email: EmailStr
    name: Optional[str] = None
    role: UserRole = UserRole.USER
    permissions: Optional[Dict[str, Any]] = None

class UserResponse(BaseModel):
    id: str
    email: str
    name: Optional[str]
    role: str
    permissions: Dict[str, Any]
    is_active: bool
    is_verified: bool
    last_login: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True

class UserUpdate(BaseModel):
    name: Optional[str] = None
    role: Optional[UserRole] = None
    permissions: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class OAuthUserSync(BaseModel):
    id: str
    email: EmailStr
    name: Optional[str] = None
    provider: str = "oauth"

# API Key Schemas

class ApiKeyCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    scopes: List[str] = ["read", "write"]
    rate_limit_per_hour: int = 1000
    expires_at: Optional[datetime] = None

class ApiKeyResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    key: str
    scopes: List[str]
    rate_limit_per_hour: int
    is_active: bool
    expires_at: Optional[datetime]
    last_used: Optional[datetime]
    usage_count: int
    created_at: datetime

    class Config:
        from_attributes = True

# Dataset Schemas

class DatasetCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    source_type: str
    source_path: Optional[str] = None
    file_type: Optional[str] = None
    content_type: str = "tabular"

class DatasetResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    source_type: str
    file_type: Optional[str]
    content_type: str
    processing_status: str
    row_count: int
    word_count: int
    chunk_count: int
    created_at: datetime

    class Config:
        from_attributes = True

class DatasetUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

# Query Schemas

class QueryCreate(BaseModel):
    question: str = Field(..., min_length=1)
    dataset_id: Optional[str] = None
    query_type: str = "general"
    tags: Optional[List[str]] = None

class QueryResponse(BaseModel):
    id: str
    question: str
    answer: Optional[str]
    chart_type: Optional[str]
    chart_data: Optional[Dict[str, Any]]
    trust_score_data: Optional[Dict[str, Any]]
    query_type: str
    tags: Optional[List[str]]
    is_bookmarked: bool
    processing_time_ms: Optional[int]
    trust_score: Optional[float]
    created_at: datetime

    class Config:
        from_attributes = True

# Task Schemas

class TaskEnqueue(BaseModel):
    task_type: TaskType
    data: Dict[str, Any] = {}
    priority: TaskPriority = TaskPriority.NORMAL
    delay_seconds: int = 0

class TaskResponse(BaseModel):
    task_id: str
    task_type: str
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# Connector Schemas

class ConnectorCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    connector_type: str
    description: Optional[str] = None
    config: Dict[str, Any]
    auto_sync_enabled: bool = True
    sync_frequency: str = "daily"

class ConnectorResponse(BaseModel):
    id: str
    name: str
    connector_type: str
    description: Optional[str]
    status: str
    last_sync_at: Optional[datetime]
    next_sync_at: Optional[datetime]
    sync_count: int
    created_at: datetime

    class Config:
        from_attributes = True

class ConnectorUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    auto_sync_enabled: Optional[bool] = None
    sync_frequency: Optional[str] = None

# Response Schemas

class SuccessResponse(BaseModel):
    status: str = "success"
    message: str

class ErrorResponse(BaseModel):
    status: str = "error"
    detail: str

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str
    database: str
    task_queue: str
    timestamp: str

class TenantRegistrationResponse(BaseModel):
    status: str
    tenant: Dict[str, Any]
    admin_user: Dict[str, Any]
    api_key: str

class UserSyncResponse(BaseModel):
    status: str
    api_key: str
    tenant_id: str
    user_id: str
    role: str
    tenant_name: str

class UserListResponse(BaseModel):
    users: List[UserResponse]

class DatasetListResponse(BaseModel):
    datasets: List[DatasetResponse]

class QueueStatsResponse(BaseModel):
    backend: str
    pending_tasks: Optional[int] = None
    active_tasks: Optional[int] = None
    completed_tasks: Optional[int] = None
    failed_tasks: Optional[int] = None
    workers: Optional[List[str]] = None
