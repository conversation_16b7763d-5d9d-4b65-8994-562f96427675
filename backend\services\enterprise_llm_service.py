"""
Enterprise LLM Service for AIthentiq
Advanced LLM management with load balancing, fallbacks, and optimization
"""

import os
import json
import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import redis
from concurrent.futures import ThreadPoolExecutor
import numpy as np

logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"

class LLMModel(Enum):
    GPT4 = "gpt-4"
    GPT4_TURBO = "gpt-4-turbo"
    GPT35_TURBO = "gpt-3.5-turbo"
    CLAUDE3_OPUS = "claude-3-opus-20240229"
    CLAUDE3_SONNET = "claude-3-sonnet-20240229"
    CLAUDE3_HAIKU = "claude-3-haiku-20240307"
    GEMINI_PRO = "gemini-pro"
    GEMINI_ULTRA = "gemini-ultra"

@dataclass
class LLMRequest:
    prompt: str
    model: LLMModel
    provider: LLMProvider
    temperature: float = 0.7
    max_tokens: int = 2048
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    system_prompt: Optional[str] = None
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None

@dataclass
class LLMResponse:
    content: str
    provider: str
    model: str
    tokens_used: int
    response_time_ms: float
    cost_estimate: float
    finish_reason: str
    request_id: str

@dataclass
class ProviderMetrics:
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    avg_cost_per_request: float
    last_error: Optional[str]
    health_score: float
    rate_limit_remaining: int

class EnterpriseLLMService:
    """Enterprise LLM service with advanced features"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.providers = self._init_providers()
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        
        # Provider management
        self.provider_metrics = {provider: ProviderMetrics(0, 0, 0, 0.0, 0.0, None, 1.0, 1000) 
                               for provider in LLMProvider}
        self.provider_weights = {provider: 1.0 for provider in LLMProvider}
        
        # Cost tracking
        self.cost_per_token = {
            LLMModel.GPT4: 0.00003,
            LLMModel.GPT4_TURBO: 0.00001,
            LLMModel.GPT35_TURBO: 0.000002,
            LLMModel.CLAUDE3_OPUS: 0.000015,
            LLMModel.CLAUDE3_SONNET: 0.000003,
            LLMModel.CLAUDE3_HAIKU: 0.00000025,
            LLMModel.GEMINI_PRO: 0.000001,
            LLMModel.GEMINI_ULTRA: 0.000001
        }
        
        # Circuit breaker settings
        self.circuit_breaker_threshold = 5
        self.circuit_breaker_timeout = 300  # 5 minutes
        self.circuit_breaker_state = {provider: "closed" for provider in LLMProvider}
        self.circuit_breaker_failures = {provider: 0 for provider in LLMProvider}
        self.circuit_breaker_last_failure = {provider: None for provider in LLMProvider}
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis for caching and metrics"""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/4")
            client = redis.from_url(redis_url, decode_responses=True)
            client.ping()
            logger.info("LLM Redis cache initialized")
            return client
        except Exception as e:
            logger.warning(f"LLM Redis cache unavailable: {e}")
            return None
    
    def _init_providers(self) -> Dict[LLMProvider, Any]:
        """Initialize LLM provider clients"""
        providers = {}
        
        # OpenAI
        if os.getenv("OPENAI_API_KEY"):
            try:
                import openai
                openai.api_key = os.getenv("OPENAI_API_KEY")
                providers[LLMProvider.OPENAI] = openai
                logger.info("OpenAI provider initialized")
            except ImportError:
                logger.warning("OpenAI not available")
        
        # Anthropic
        if os.getenv("ANTHROPIC_API_KEY"):
            try:
                import anthropic
                providers[LLMProvider.ANTHROPIC] = anthropic.Anthropic(
                    api_key=os.getenv("ANTHROPIC_API_KEY")
                )
                logger.info("Anthropic provider initialized")
            except ImportError:
                logger.warning("Anthropic not available")
        
        # Google
        if os.getenv("GOOGLE_API_KEY"):
            try:
                import google.generativeai as genai
                genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
                providers[LLMProvider.GOOGLE] = genai
                logger.info("Google provider initialized")
            except ImportError:
                logger.warning("Google provider not available")
        
        # Azure OpenAI
        if os.getenv("AZURE_OPENAI_KEY"):
            try:
                import openai
                providers[LLMProvider.AZURE] = openai
                logger.info("Azure OpenAI provider initialized")
            except ImportError:
                logger.warning("Azure OpenAI not available")
        
        # Cohere
        if os.getenv("COHERE_API_KEY"):
            try:
                import cohere
                providers[LLMProvider.COHERE] = cohere.Client(os.getenv("COHERE_API_KEY"))
                logger.info("Cohere provider initialized")
            except ImportError:
                logger.warning("Cohere not available")
        
        return providers
    
    async def generate_response(self, request: LLMRequest) -> LLMResponse:
        """Generate response with enterprise features"""
        start_time = time.time()
        request_id = hashlib.md5(f"{request.prompt}{time.time()}".encode()).hexdigest()
        
        try:
            # Check circuit breaker
            if not self._check_circuit_breaker(request.provider):
                # Try fallback provider
                fallback_provider = self._get_fallback_provider(request.provider)
                if fallback_provider:
                    request.provider = fallback_provider
                else:
                    raise Exception("All providers unavailable")
            
            # Route to appropriate provider
            if request.provider == LLMProvider.OPENAI:
                response = await self._call_openai(request)
            elif request.provider == LLMProvider.ANTHROPIC:
                response = await self._call_anthropic(request)
            elif request.provider == LLMProvider.GOOGLE:
                response = await self._call_google(request)
            elif request.provider == LLMProvider.AZURE:
                response = await self._call_azure(request)
            elif request.provider == LLMProvider.COHERE:
                response = await self._call_cohere(request)
            else:
                raise ValueError(f"Unsupported provider: {request.provider}")
            
            # Calculate metrics
            response_time = (time.time() - start_time) * 1000
            cost_estimate = self._calculate_cost(request.model, response.tokens_used)
            
            # Update provider metrics
            self._update_provider_metrics(request.provider, True, response_time, cost_estimate)
            
            # Create response
            llm_response = LLMResponse(
                content=response["content"],
                provider=request.provider.value,
                model=request.model.value,
                tokens_used=response["tokens_used"],
                response_time_ms=response_time,
                cost_estimate=cost_estimate,
                finish_reason=response.get("finish_reason", "completed"),
                request_id=request_id
            )
            
            # Cache response if appropriate
            await self._cache_response(request, llm_response)
            
            return llm_response
            
        except Exception as e:
            logger.error(f"LLM request failed: {e}")
            
            # Update provider metrics
            self._update_provider_metrics(request.provider, False, 0, 0, str(e))
            
            # Try fallback
            fallback_provider = self._get_fallback_provider(request.provider)
            if fallback_provider and fallback_provider != request.provider:
                request.provider = fallback_provider
                return await self.generate_response(request)
            
            # Return error response
            return LLMResponse(
                content=f"I encountered an error: {str(e)}",
                provider="error",
                model="error",
                tokens_used=0,
                response_time_ms=(time.time() - start_time) * 1000,
                cost_estimate=0.0,
                finish_reason="error",
                request_id=request_id
            )
    
    async def _call_openai(self, request: LLMRequest) -> Dict[str, Any]:
        """Call OpenAI API"""
        try:
            import openai
            
            messages = []
            if request.system_prompt:
                messages.append({"role": "system", "content": request.system_prompt})
            messages.append({"role": "user", "content": request.prompt})
            
            response = await openai.ChatCompletion.acreate(
                model=request.model.value,
                messages=messages,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                top_p=request.top_p,
                frequency_penalty=request.frequency_penalty,
                presence_penalty=request.presence_penalty
            )
            
            return {
                "content": response.choices[0].message.content,
                "tokens_used": response.usage.total_tokens,
                "finish_reason": response.choices[0].finish_reason
            }
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise
    
    async def _call_anthropic(self, request: LLMRequest) -> Dict[str, Any]:
        """Call Anthropic Claude API"""
        try:
            messages = [{"role": "user", "content": request.prompt}]
            
            if request.system_prompt:
                # Anthropic handles system prompts differently
                messages[0]["content"] = f"{request.system_prompt}\n\n{request.prompt}"
            
            response = await self.providers[LLMProvider.ANTHROPIC].messages.create(
                model=request.model.value,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                messages=messages
            )
            
            return {
                "content": response.content[0].text,
                "tokens_used": response.usage.input_tokens + response.usage.output_tokens,
                "finish_reason": response.stop_reason
            }
            
        except Exception as e:
            logger.error(f"Anthropic API call failed: {e}")
            raise
    
    async def _call_google(self, request: LLMRequest) -> Dict[str, Any]:
        """Call Google Gemini API"""
        try:
            model = self.providers[LLMProvider.GOOGLE].GenerativeModel(request.model.value)
            
            prompt_text = request.prompt
            if request.system_prompt:
                prompt_text = f"{request.system_prompt}\n\n{prompt_text}"
            
            response = await model.generate_content_async(
                prompt_text,
                generation_config={
                    "temperature": request.temperature,
                    "max_output_tokens": request.max_tokens,
                    "top_p": request.top_p
                }
            )
            
            return {
                "content": response.text,
                "tokens_used": response.usage_metadata.total_token_count if hasattr(response, 'usage_metadata') else 0,
                "finish_reason": "completed"
            }
            
        except Exception as e:
            logger.error(f"Google API call failed: {e}")
            raise
    
    def _check_circuit_breaker(self, provider: LLMProvider) -> bool:
        """Check if circuit breaker allows requests"""
        state = self.circuit_breaker_state[provider]
        
        if state == "closed":
            return True
        elif state == "open":
            # Check if timeout has passed
            last_failure = self.circuit_breaker_last_failure[provider]
            if last_failure and (time.time() - last_failure) > self.circuit_breaker_timeout:
                self.circuit_breaker_state[provider] = "half_open"
                return True
            return False
        elif state == "half_open":
            return True
        
        return False
    
    def _get_fallback_provider(self, failed_provider: LLMProvider) -> Optional[LLMProvider]:
        """Get best fallback provider"""
        available_providers = [p for p in self.providers.keys() 
                             if p != failed_provider and self._check_circuit_breaker(p)]
        
        if not available_providers:
            return None
        
        # Select provider with best health score
        best_provider = max(available_providers, 
                          key=lambda p: self.provider_metrics[p].health_score)
        
        return best_provider
    
    def _update_provider_metrics(self, provider: LLMProvider, success: bool, 
                               response_time: float, cost: float, error: str = None):
        """Update provider performance metrics"""
        metrics = self.provider_metrics[provider]
        
        metrics.total_requests += 1
        
        if success:
            metrics.successful_requests += 1
            metrics.avg_response_time = (metrics.avg_response_time + response_time) / 2
            metrics.avg_cost_per_request = (metrics.avg_cost_per_request + cost) / 2
            
            # Reset circuit breaker on success
            self.circuit_breaker_failures[provider] = 0
            if self.circuit_breaker_state[provider] == "half_open":
                self.circuit_breaker_state[provider] = "closed"
        else:
            metrics.failed_requests += 1
            metrics.last_error = error
            
            # Update circuit breaker
            self.circuit_breaker_failures[provider] += 1
            if self.circuit_breaker_failures[provider] >= self.circuit_breaker_threshold:
                self.circuit_breaker_state[provider] = "open"
                self.circuit_breaker_last_failure[provider] = time.time()
        
        # Calculate health score
        if metrics.total_requests > 0:
            success_rate = metrics.successful_requests / metrics.total_requests
            response_time_factor = max(0, 1 - (metrics.avg_response_time / 10000))  # Penalize slow responses
            metrics.health_score = (success_rate * 0.7 + response_time_factor * 0.3)
        
        # Store metrics in Redis
        if self.redis_client:
            try:
                metrics_key = f"llm_metrics:{provider.value}"
                self.redis_client.setex(metrics_key, 3600, json.dumps(asdict(metrics)))
            except Exception as e:
                logger.error(f"Failed to store metrics: {e}")
    
    def _calculate_cost(self, model: LLMModel, tokens: int) -> float:
        """Calculate estimated cost for request"""
        cost_per_token = self.cost_per_token.get(model, 0.00001)
        return tokens * cost_per_token
    
    async def _cache_response(self, request: LLMRequest, response: LLMResponse):
        """Cache response for future use"""
        if not self.redis_client:
            return
        
        try:
            # Only cache deterministic responses (low temperature)
            if request.temperature <= 0.3:
                cache_key = self._generate_cache_key(request)
                cache_data = asdict(response)
                
                self.redis_client.setex(
                    cache_key,
                    1800,  # 30 minutes
                    json.dumps(cache_data, default=str)
                )
        except Exception as e:
            logger.error(f"Response caching failed: {e}")
    
    def _generate_cache_key(self, request: LLMRequest) -> str:
        """Generate cache key for request"""
        key_data = f"{request.prompt}{request.model.value}{request.temperature}{request.system_prompt}"
        return f"llm_cache:{hashlib.md5(key_data.encode()).hexdigest()}"
    
    def get_provider_metrics(self) -> Dict[str, ProviderMetrics]:
        """Get current provider metrics"""
        return self.provider_metrics.copy()
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get service health status"""
        total_providers = len(self.providers)
        healthy_providers = sum(1 for p in self.providers.keys() 
                              if self.provider_metrics[p].health_score > 0.5)
        
        return {
            "healthy": healthy_providers > 0,
            "total_providers": total_providers,
            "healthy_providers": healthy_providers,
            "provider_status": {
                provider.value: {
                    "healthy": metrics.health_score > 0.5,
                    "health_score": metrics.health_score,
                    "circuit_breaker": self.circuit_breaker_state[provider]
                }
                for provider, metrics in self.provider_metrics.items()
                if provider in self.providers
            }
        }

# Global enterprise LLM service instance
enterprise_llm_service = EnterpriseLLMService()
