'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Send,
  Bot,
  User,
  Database,
  Clock,
  Star,
  Copy,
  ThumbsUp,
  ThumbsDown,
  MoreVertical,
  Sparkles,
  MessageSquare,
  RefreshCw,
  Heart,
  Bookmark,
  ChevronDown,
  ChevronUp,
  TrendingUp,
  Zap,
  Search,
  BarChart3
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { createApiInstance, getUserId } from '../../lib/api';
import ChatErrorBoundary from './chat-error-boundary';
import SourcePanel from '../source-attribution/source-panel';

import { useTrustSystem } from '../../hooks/useTrustSystem';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  datasetId?: number;
  datasetName?: string;
  sources?: Array<{
    chunk_id?: string;
    text: string;
    score?: number;
    rank?: number;
    method?: string;
    metadata?: any;
    document?: string;
    line_start?: number;
    line_end?: number;
    char_start?: number;
    char_end?: number;
    page_number?: number;
    section?: string;
    chunk_type?: string;
    chunk_db_id?: number;
    confidence_score?: number;
    relevance_score?: number;
    search_method?: string;
  }>;
  trustScore?: {
    overall_score: number;
    base_score?: number;
    component_scores?: {
      model_confidence: number;
      qat_regressor: number;
      refusal_detector: number;
      citation_precision: number;
    };
    factors: string[];
    explanation: string;
    confidence_interval?: [number, number];
    processing_time_ms?: number;
    version?: string;
  };
  bayesianInfo?: {
    user_posterior: number;
    topic_posterior: number;
    user_confidence: number;
    topic_confidence: number;
    user_interactions: number;
    topic_interactions: number;
  };
  searchAnalytics?: {
    query_analysis?: any;
    results_analysis?: any;
    search_config?: any;
    method_used?: string;
    total_results?: number;
  };
  qualityMetrics?: {
    hallucination_risk: number;
    completeness_score: number;
    confidence_level: string;
    uncertainty_indicators?: number;
    unsupported_claims?: number;
  };
  performanceMetrics?: {
    response_time_ms: number;
    token_count: number;
    source_count: number;
  };
  citations?: Array<{
    id: number;
    text: string;
    source: string;
    confidence: number;
    verified: boolean;
    context_before?: string;
    context_after?: string;
  }>;
  processingTime?: number;
  processingMethod?: string; // ML Router, Traditional RAG, etc.
  routingPath?: string; // structured_only, semantic_only, hybrid, etc.
  confidenceScore?: number; // ML routing confidence
  isLoading?: boolean;
  error?: string;
  isImportant?: boolean;
  queryId?: number; // Add queryId for feedback submission
  isLiked?: boolean;
  isDisliked?: boolean;
}



interface Dataset {
  id: number;
  name: string;
  columns: string[];
  row_count: number;
  created_at: string;
  file_type?: string;
  content_type?: string;
  processing_status?: string;
  word_count?: number;
  character_count?: number;
  total_chunks?: number;
  chunking_template?: string;
}

interface RAGChatProps {
  selectedDataset: Dataset | null;
  onDatasetChange: (dataset: Dataset | null) => void;
  availableDatasets: Dataset[];
}

export default function RAGChatInterface({
  selectedDataset,
  onDatasetChange,
  availableDatasets
}: RAGChatProps) {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true); // Enable smart auto-scroll
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [showNewMessageNotification, setShowNewMessageNotification] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const lastMessageCountRef = useRef(messages.length);

  // Initialize trust system hook
  const {
    computeTrustScore,
    submitTrustFeedback,
    getCurrentUserId,
    classifyTopic,
    classifyFeedback,
    loading: trustLoading,
    error: trustError
  } = useTrustSystem();

  // Simplified configuration - no hybrid search
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [searchPerformanceHistory, setSearchPerformanceHistory] = useState<any[]>([]);
  const [showHelp, setShowHelp] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchAbortController, setSearchAbortController] = useState<AbortController | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);
  const [expandedSources, setExpandedSources] = useState<{[key: number]: boolean}>({});
  const [isOnline, setIsOnline] = useState(true);
  const [requestQueue, setRequestQueue] = useState<Set<string>>(new Set());
  const [conversationPersisted, setConversationPersisted] = useState(false);

  // Constants for memory management
  const MAX_MESSAGES = 100;
  const MAX_PERFORMANCE_HISTORY = 50;

  // View Context functionality removed as requested

  // Rate limiting
  const RATE_LIMIT_WINDOW = 60000; // 1 minute
  const MAX_REQUESTS_PER_WINDOW = 30;
  const [requestTimestamps, setRequestTimestamps] = useState<number[]>([]);

  // CSRF protection
  const [csrfToken, setCsrfToken] = useState<string>('');

  // Rate limiting check
  const checkRateLimit = useCallback(() => {
    const now = Date.now();
    const windowStart = now - RATE_LIMIT_WINDOW;
    const recentRequests = requestTimestamps.filter(timestamp => timestamp > windowStart);

    if (recentRequests.length >= MAX_REQUESTS_PER_WINDOW) {
      return false;
    }

    setRequestTimestamps([...recentRequests, now]);
    return true;
  }, [requestTimestamps]);

  // Initialize CSRF token
  useEffect(() => {
    const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
    setCsrfToken(token);
  }, []);

  // Chat settings state - enabled by default, no UI controls
  const includeTrustScore = true;
  const useChainOfThought = false;

  // Trust Score Method Selection
  const [selectedTrustMethod, setSelectedTrustMethod] = useState('current_production');

  const trustMethods = [
    {
      id: 'multi_llm_convergence',
      name: 'Multi-LLM',
      shortName: 'Multi-LLM',
      icon: '🤖',
      description: 'Uses multiple AI models (OpenAI, Gemini, Claude) to cross-validate responses and calculate consensus-based trust scores',
      details: 'Highest accuracy but slower processing. Best for critical decisions.',
      speed: 'Slow',
      accuracy: 'Highest'
    },
    {
      id: 'faithfulness_context',
      name: 'Faithfulness',
      shortName: 'Faithfulness',
      icon: '🔍',
      description: 'Analyzes how well the response stays faithful to source documents and maintains contextual accuracy',
      details: 'Balanced speed and accuracy. Good for general use cases.',
      speed: 'Medium',
      accuracy: 'High'
    },
    {
      id: 'current_production',
      name: 'Production',
      shortName: 'Production',
      icon: '⚡',
      description: 'Fast, lightweight scoring based on source quality, citation accuracy, and response confidence',
      details: 'Fastest processing. Suitable for high-volume queries.',
      speed: 'Fast',
      accuracy: 'Good'
    },
    {
      id: 'average_top_three',
      name: 'Combined',
      shortName: 'Combined',
      icon: '🎯',
      description: 'Averages results from all three methods to provide the most comprehensive trust assessment',
      details: 'Most robust scoring but requires more processing time.',
      speed: 'Slow',
      accuracy: 'Highest'
    }
  ];

  // Memory management - limit message history
  const manageMemory = useCallback(() => {
    setMessages(prev => {
      if (prev.length > MAX_MESSAGES) {
        // Keep system messages and recent messages
        const systemMessages = prev.filter(m => m.type === 'system');
        const recentMessages = prev.slice(-MAX_MESSAGES + systemMessages.length);
        return [...systemMessages, ...recentMessages];
      }
      return prev;
    });

    setSearchPerformanceHistory(prev => {
      if (prev.length > MAX_PERFORMANCE_HISTORY) {
        return prev.slice(-MAX_PERFORMANCE_HISTORY);
      }
      return prev;
    });
  }, []);

  // Online/offline detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (searchAbortController) {
        searchAbortController.abort();
      }
      // Clear any pending timers
      const timers = (window as any).__ragChatTimers || [];
      timers.forEach((timer: number) => clearTimeout(timer));
    };
  }, [searchAbortController]);

  // Enhanced scroll position detection with progress tracking
  const checkScrollPosition = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

      // Calculate scroll progress (0-100%)
      const maxScroll = scrollHeight - clientHeight;
      const progress = maxScroll > 0 ? Math.min(100, Math.max(0, (scrollTop / maxScroll) * 100)) : 0;
      setScrollProgress(progress);

      // If user manually scrolled up, respect their intent
      if (!isNearBottom) {
        setUserHasScrolled(true);
        setShouldAutoScroll(false);
      } else {
        setUserHasScrolled(false);
        setShouldAutoScroll(true);
        setShowNewMessageNotification(false); // Hide notification when at bottom
      }
    }
  };

  // Keyboard shortcuts for scrolling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when not typing in input
      if (document.activeElement === inputRef.current) return;

      const container = messagesContainerRef.current;
      if (!container) return;

      switch (event.code) {
        case 'Space':
          event.preventDefault();
          // Scroll down by viewport height
          container.scrollBy({ top: container.clientHeight * 0.8, behavior: 'smooth' });
          setUserHasScrolled(true);
          setShouldAutoScroll(false);
          break;

        case 'PageDown':
          event.preventDefault();
          // Scroll down by viewport height
          container.scrollBy({ top: container.clientHeight, behavior: 'smooth' });
          setUserHasScrolled(true);
          setShouldAutoScroll(false);
          break;

        case 'PageUp':
          event.preventDefault();
          // Scroll up by viewport height
          container.scrollBy({ top: -container.clientHeight, behavior: 'smooth' });
          setUserHasScrolled(true);
          setShouldAutoScroll(false);
          break;

        case 'Home':
          if (event.ctrlKey) {
            event.preventDefault();
            // Scroll to top
            container.scrollTo({ top: 0, behavior: 'smooth' });
            setUserHasScrolled(true);
            setShouldAutoScroll(false);
          }
          break;

        case 'End':
          if (event.ctrlKey) {
            event.preventDefault();
            // Scroll to bottom
            container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
            setUserHasScrolled(false);
            setShouldAutoScroll(true);
            setShowNewMessageNotification(false);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Add scroll event listener to messages container
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollPosition);
      return () => container.removeEventListener('scroll', checkScrollPosition);
    }
  }, []);

  // Smart auto-scroll with new message notifications
  useEffect(() => {
    // Check if new message was added
    if (messages.length > lastMessageCountRef.current) {
      const lastMessage = messages[messages.length - 1];

      // If user has scrolled up and new assistant message arrives, show notification
      if (userHasScrolled && lastMessage?.type === 'assistant') {
        setShowNewMessageNotification(true);
        // Auto-hide notification after 5 seconds
        setTimeout(() => setShowNewMessageNotification(false), 5000);
      }

      // Auto-scroll if conditions are met
      if (shouldAutoScroll && !userHasScrolled) {
        // Auto-scroll for assistant messages (answers) or when user sends a message
        if (lastMessage?.type === 'assistant' || lastMessage?.type === 'user') {
          setTimeout(() => {
            messagesEndRef.current?.scrollIntoView({
              behavior: 'smooth',
              block: 'end'
            });
          }, 100);
        }
      }
    }

    // Update the message count reference
    lastMessageCountRef.current = messages.length;
  }, [messages.length, shouldAutoScroll, userHasScrolled]);

  // Restore conversation from localStorage
  const restoreConversation = useCallback(() => {
    if (typeof window !== 'undefined' && selectedDataset) {
      try {
        const stored = localStorage.getItem(`rag-conversation-${selectedDataset.id}`);
        if (stored) {
          const conversationData = JSON.parse(stored);
          if (conversationData.datasetId === selectedDataset.id &&
              Date.now() - conversationData.timestamp < 24 * 60 * 60 * 1000) { // 24 hours
            setMessages(conversationData.messages || []);
            setConversationId(conversationData.id);
            setConversationPersisted(true);
            return true;
          }
        }
      } catch (error) {
        console.warn('Failed to restore conversation:', error);
      }
    }
    return false;
  }, [selectedDataset]);

  // Initialize conversation when dataset is selected or changed
  useEffect(() => {
    if (selectedDataset) {
      // Disable auto-scroll when dataset changes to prevent jarring behavior
      setShouldAutoScroll(false);

      const restored = restoreConversation();
      if (!restored) {
        initializeConversation();
      }
    } else {
      setMessages([]);
      setConversationId(null);
      setSuggestions([]);
    }
  }, [selectedDataset, restoreConversation]);



  // Save current conversation to database permanently
  const saveConversation = async () => {
    if (!selectedDataset || messages.length < 2) return;

    try {
      const sessionApi = createApiInstance(session);

      // Find the last user question and AI answer
      const userMessages = messages.filter(m => m.type === 'user');
      const assistantMessages = messages.filter(m => m.type === 'assistant');

      if (userMessages.length === 0 || assistantMessages.length === 0) return;

      const lastUserMessage = userMessages[userMessages.length - 1];
      const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];

      // Save to saved queries table
      console.log('🔄 Saving query to database...');
      const response = await sessionApi.post('/api/v1/saved-queries/', {
        question: lastUserMessage.content,
        answer: lastAssistantMessage.content,
        dataset_id: selectedDataset.id,
        name: lastUserMessage.content.length > 50 ? lastUserMessage.content.substring(0, 50) + '...' : lastUserMessage.content,
        description: `Chat about ${selectedDataset.name}`,
        tags: `dataset:${selectedDataset.name}`,
        trust_score: lastAssistantMessage.trustScore?.overall_score,
        processing_time: lastAssistantMessage.processingTime
      });
      console.log('✅ Query saved successfully:', response.data);

      // Show success message briefly
      setMessages(prev => [...prev, {
        id: `system-${Date.now()}`,
        type: 'system',
        content: '✅ Conversation saved to database!',
        timestamp: new Date()
      }]);

      // Remove success message after 3 seconds
      setTimeout(() => {
        setMessages(prev => prev.filter(msg => !msg.content.includes('saved to database')));
      }, 3000);



      // Trigger conversation history refresh
      window.dispatchEvent(new CustomEvent('conversationSaved'));

    } catch (error) {
      console.error('Failed to save conversation:', error);

    }
  };

  // Export conversation
  const exportConversation = () => {
    if (!messages.length) return;

    const exportData = {
      dataset: selectedDataset?.name,
      timestamp: new Date().toISOString(),
      messages: messages.map(msg => ({
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp,
        sources: msg.sources?.length || 0,
        trustScore: msg.trustScore?.overall_score
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rag-chat-${selectedDataset?.name}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Input validation and sanitization
  const validateAndSanitizeInput = (input: string): { isValid: boolean; sanitized: string; error?: string } => {
    if (!input || typeof input !== 'string') {
      return { isValid: false, sanitized: '', error: 'Input is required' };
    }

    // Trim and check length
    const trimmed = input.trim();
    if (trimmed.length === 0) {
      return { isValid: false, sanitized: '', error: 'Input cannot be empty' };
    }

    if (trimmed.length > 2000) {
      return { isValid: false, sanitized: '', error: 'Input too long (max 2000 characters)' };
    }

    // Basic XSS protection - remove potentially dangerous characters
    const sanitized = trimmed
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /\b(eval|exec|system|shell)\s*\(/i,
      /\b(drop|delete|truncate|alter)\s+table\b/i,
      /\bunion\s+select\b/i
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(sanitized)) {
        return { isValid: false, sanitized: '', error: 'Input contains potentially harmful content' };
      }
    }

    return { isValid: true, sanitized };
  };



  const initializeConversation = () => {
    if (!selectedDataset) return;

    // Generate dynamic welcome message based on file type
    const getWelcomeMessage = () => {
      const fileType = selectedDataset.file_type?.toLowerCase() || '';
      const itemCount = selectedDataset.total_chunks || selectedDataset.row_count;

      let typeSpecificMessage = '';
      if (fileType.includes('pdf')) {
        typeSpecificMessage = 'I can help you explore this PDF document, extract key information, and answer questions about its content.';
      } else if (fileType.includes('doc')) {
        typeSpecificMessage = 'I can help you analyse this document, summarise sections, and find specific information.';
      } else if (fileType.includes('csv') || fileType.includes('xlsx')) {
        typeSpecificMessage = 'I can help you analyse this data, find patterns, and answer questions about the information.';
      } else if (fileType.includes('txt') || fileType.includes('md')) {
        typeSpecificMessage = 'I can help you explore this text, find specific information, and answer questions about the content.';
      } else {
        typeSpecificMessage = 'I can help you explore this document and answer questions about its content.';
      }

      return `${typeSpecificMessage}${itemCount ? ` The document contains ${itemCount} ${selectedDataset.total_chunks ? 'processed chunks' : 'items'}.` : ''}\n\nWhat would you like to know?`;
    };

    const welcomeMessage: ChatMessage = {
      id: `system-${Date.now()}`,
      type: 'system',
      content: getWelcomeMessage(),
      timestamp: new Date(),
      datasetId: selectedDataset.id,
      datasetName: selectedDataset.name
    };

    setMessages([welcomeMessage]);
    setConversationId(`conv-${selectedDataset.id}-${Date.now()}`);
    generateSuggestions();
  };

  const generateSuggestions = () => {
    if (!selectedDataset) return;

    const fileType = selectedDataset.file_type?.toLowerCase() || '';
    let suggestions: string[] = [];

    if (fileType.includes('pdf') || fileType.includes('doc')) {
      suggestions = [
        "What is the main topic of this document?",
        "Can you summarize the key points?",
        "What are the most important findings?"
      ];
    } else if (fileType.includes('csv') || fileType.includes('xlsx')) {
      suggestions = [
        "What data does this contain?",
        "Can you analyse the main trends?",
        "What are the key statistics?"
      ];
    } else {
      suggestions = [
        "What is this document about?",
        "Can you give me an overview?",
        "What should I know about this content?"
      ];
    }

    setSuggestions(suggestions);
  };

  // Request deduplication
  const generateRequestId = (content: string, datasetId: number) => {
    return `${datasetId}-${content.trim().toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`;
  };

  // Persist conversation state
  const persistConversation = useCallback(() => {
    if (typeof window !== 'undefined' && conversationId && messages.length > 0) {
      try {
        const conversationData = {
          id: conversationId,
          datasetId: selectedDataset?.id,
          messages: messages.slice(-20), // Keep last 20 messages
          timestamp: Date.now()
        };
        localStorage.setItem(`rag-conversation-${conversationId}`, JSON.stringify(conversationData));
        setConversationPersisted(true);
      } catch (error) {
        console.warn('Failed to persist conversation:', error);
      }
    }
  }, [conversationId, messages, selectedDataset?.id]);

  // Auto-persist conversation
  useEffect(() => {
    if (messages.length > 0) {
      const timer = setTimeout(persistConversation, 2000);
      (window as any).__ragChatTimers = [...((window as any).__ragChatTimers || []), timer];
      return () => clearTimeout(timer);
    }
  }, [messages, persistConversation]);

  const sendMessage = async (content: string, isRetry: boolean = false) => {
    if (!content.trim() || !selectedDataset || !isOnline) {
      if (!isOnline) {
        setMessages(prev => [...prev, {
          id: `system-${Date.now()}`,
          type: 'system',
          content: '🔌 You appear to be offline. Please check your connection.',
          timestamp: new Date()
        }]);
      }
      return;
    }

    // Rate limiting check
    if (!isRetry && !checkRateLimit()) {
      setMessages(prev => [...prev, {
        id: `system-${Date.now()}`,
        type: 'system',
        content: '⏱️ Rate limit exceeded. Please wait a moment before sending another message.',
        timestamp: new Date()
      }]);
      return;
    }

    // Request deduplication
    const requestId = generateRequestId(content, selectedDataset.id);
    if (requestQueue.has(requestId) && !isRetry) {
      return; // Duplicate request, ignore
    }

    setRequestQueue(prev => new Set([...prev, requestId]));

    // Validate and sanitize input
    const validation = validateAndSanitizeInput(content);
    if (!validation.isValid) {
      setMessages(prev => [...prev, {
        id: `system-${Date.now()}`,
        type: 'system',
        content: `❌ ${validation.error}`,
        timestamp: new Date()
      }]);
      setRequestQueue(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
      return;
    }

    const sanitizedContent = validation.sanitized;

    // Cancel any ongoing search
    if (searchAbortController) {
      searchAbortController.abort();
    }

    // Create new abort controller for this request
    const abortController = new AbortController();
    setSearchAbortController(abortController);
    setIsSearching(true);
    setLastError(null);

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: sanitizedContent,
      timestamp: new Date(),
      datasetId: selectedDataset.id,
      datasetName: selectedDataset.name
    };

    // Enable auto-scroll for new conversation flow
    setShouldAutoScroll(true);
    setUserHasScrolled(false);

    // Add user message and loading assistant message
    const loadingMessage: ChatMessage = {
      id: `assistant-${Date.now()}`,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isLoading: true,
      datasetId: selectedDataset.id,
      datasetName: selectedDataset.name
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Auto-scroll will be handled by useEffect

    try {
      let currentConversationId = conversationId;

      // Create conversation if it doesn't exist
      if (!currentConversationId) {
        try {
          console.log('🔄 Creating new conversation for dataset:', selectedDataset.id);

          // Check if session has API key
          const userWithApiKey = session?.user as any;
          if (!userWithApiKey?.apiKey) {
            console.error('❌ No API key in session for conversation creation');
            throw new Error('No API key in session');
          }

          const sessionApi = createApiInstance(session);
          const createResponse = await sessionApi.post('/api/v1/conversations/', {
            dataset_id: selectedDataset.id,
            title: `Chat about ${selectedDataset.name}`
          });

          if (createResponse.data) {
            currentConversationId = createResponse.data.conversation_id;
            setConversationId(currentConversationId);
            console.log('✅ Conversation created:', currentConversationId);

            // Trigger conversation history refresh
            window.dispatchEvent(new CustomEvent('conversationCreated'));
          } else {
            console.error('❌ No conversation ID returned from API');
          }
        } catch (convError: any) {
          console.error('❌ Failed to create conversation:', convError);
          console.error('❌ Error details:', convError.response?.data);

          // Show error to user
          setMessages(prev => [...prev, {
            id: Date.now().toString(),
            type: 'system',
            content: `⚠️ Conversation not saved: ${convError.response?.data?.detail || convError.message || 'Unknown error'}`,
            timestamp: new Date(),
            sources: []
          }]);

          // Continue without conversation ID - the ask endpoint will still work
        }
      }

      // Build contextual query with conversation history
      const conversationContext = messages
        .filter(m => m.type !== 'system')
        .map(m => `${m.type === 'user' ? 'Human' : 'Assistant'}: ${m.content}`)
        .join('\n');

      const contextualQuery = conversationContext
        ? `Previous conversation:\n${conversationContext}\n\nCurrent question: ${content}`
        : content;

      const sessionApi = createApiInstance(session);
      let response;
      let searchAnalytics = null;



      // Use standard ask API with proper authentication
      const apiInstance = createApiInstance(session);
      response = await apiInstance.post('/api/v1/ask', {
        user_id: getUserId(session),
        dataset_id: selectedDataset.id,
        question: contextualQuery,
        include_cot: useChainOfThought,
        include_trust_score: includeTrustScore,
        trust_score_method: selectedTrustMethod || 'current_production'  // Fallback to production
      });

      const data = response.data;
      console.log('API Response:', data); // Debug log
      console.log('Processing Time:', data.processing_time); // Debug log

      // Enhanced JSON response cleaning
      let cleanAnswer = data.answer;

      if (typeof cleanAnswer === 'string') {
        // Function to convert JSON object to readable text
        const jsonToReadableText = (obj: any, depth: number = 0): string => {
          if (typeof obj === 'string') return obj;
          if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);
          if (obj === null || obj === undefined) return '';

          const indent = '  '.repeat(depth);

          if (Array.isArray(obj)) {
            if (obj.length === 0) return '';
            return obj.map(item => jsonToReadableText(item, depth)).join(', ');
          }

          if (typeof obj === 'object') {
            const entries = Object.entries(obj);
            if (entries.length === 0) return '';

            return entries.map(([key, value]) => {
              const formattedKey = key.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
              const formattedValue = jsonToReadableText(value, depth + 1);

              if (Array.isArray(value)) {
                return `${formattedKey}: ${formattedValue}`;
              } else if (typeof value === 'object' && value !== null) {
                return `${formattedKey}:\n${indent}  ${formattedValue.split('\n').join(`\n${indent}  `)}`;
              } else {
                return `${formattedKey}: ${formattedValue}`;
              }
            }).join(`\n${indent}`);
          }

          return String(obj);
        };

        // Check for various JSON patterns
        if (cleanAnswer.includes('{') && cleanAnswer.includes('}')) {
          try {
            // Try to parse as complete JSON
            const parsed = JSON.parse(cleanAnswer);
            cleanAnswer = jsonToReadableText(parsed);
          } catch (e) {
            // Try to extract and parse JSON parts
            const jsonMatch = cleanAnswer.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              try {
                const parsed = JSON.parse(jsonMatch[0]);
                const readableJson = jsonToReadableText(parsed);
                cleanAnswer = cleanAnswer.replace(jsonMatch[0], readableJson);
              } catch (e2) {
                // If all JSON parsing fails, clean up manually
                cleanAnswer = cleanAnswer
                  .replace(/[{}]/g, '') // Remove braces
                  .replace(/["']/g, '') // Remove quotes
                  .replace(/,\s*/g, ', ') // Clean up commas
                  .replace(/:\s*/g, ': ') // Clean up colons
                  .replace(/\[/g, '') // Remove array brackets
                  .replace(/\]/g, '') // Remove array brackets
                  .trim();
              }
            }
          }
        }

        // Final cleanup
        cleanAnswer = cleanAnswer
          .replace(/\\n/g, '\n') // Convert escaped newlines
          .replace(/\\"/g, '"') // Convert escaped quotes
          .replace(/\\\\/g, '\\') // Convert escaped backslashes
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim();
      }

      // Update the loading message with the response
      setMessages(prev => prev.map(msg =>
        msg.id === loadingMessage.id
          ? {
              ...msg,
              content: cleanAnswer,
              isLoading: false,
              sources: (data.sources || []).map((source: any, index: number) => ({
                ...source,
                chunk_db_id: source.chunk_db_id || source.chunk_id || null, // Use actual chunk_db_id from backend
                confidence_score: source.confidence_score || source.score, // Use actual confidence from backend
                relevance_score: source.relevance_score || source.score, // Use actual relevance from backend
                search_method: source.method || 'standard',
                document: source.source || (source.row_number && source.column_name ? `Row ${source.row_number}, Column ${source.column_name}` : source.document || source.filename || `Document ${index + 1}`),
                text: source.text || source.content || 'Source content available'
              })),
              trustScore: (() => {
                console.log('🔍 Trust Score Debug - Frontend:', {
                  received: data.trust_score,
                  type: typeof data.trust_score,
                  overall_score: data.trust_score?.overall_score,
                  factors: data.trust_score?.factors
                });

                // Enhanced trust score with V3 features
                if (data.trust_score && data.trust_score.version === '3.0') {
                  return {
                    overall_score: data.trust_score.overall_score,
                    base_score: data.trust_score.base_score,
                    component_scores: data.trust_score.component_scores,
                    factors: data.trust_score.factors,
                    explanation: data.trust_score.explanation,
                    confidence_interval: data.trust_score.confidence_interval,
                    processing_time_ms: data.trust_score.processing_time_ms,
                    version: data.trust_score.version
                  };
                }

                // Fallback to legacy trust score format
                return data.trust_score;
              })(), // Use actual trust score from backend
              bayesianInfo: data.trust_score?.version === '3.0' ? {
                user_posterior: data.bayesian_info?.user_posterior || 0.5,
                topic_posterior: data.bayesian_info?.topic_posterior || 0.5,
                user_confidence: data.bayesian_info?.user_confidence || 0.0,
                topic_confidence: data.bayesian_info?.topic_confidence || 0.0,
                user_interactions: data.bayesian_info?.user_interactions || 0,
                topic_interactions: data.bayesian_info?.topic_interactions || 0
              } : undefined,
              searchAnalytics: data.search_analytics || searchAnalytics,
              processingTime: data.processing_time,
              processingMethod: data.processing_method, // ML Router, Traditional RAG, etc.
              routingPath: data.routing_path, // structured_only, semantic_only, etc.
              confidenceScore: data.confidence_score, // ML routing confidence
              queryId: data.id, // Store the query ID for feedback submission
              qualityMetrics: data.quality_metrics || {
                hallucination_risk: 0.0,
                completeness_score: 0.8,
                confidence_level: 'medium',
                uncertainty_indicators: 0,
                unsupported_claims: 0
              },
              performanceMetrics: {
                response_time_ms: data.processing_time || 0,
                token_count: data.token_count || 0,
                source_count: (data.sources || []).length
              },
              citations: (data.sources || []).map((source: any, index: number) => ({
                id: source.id || index + 1,
                text: source.text || 'No content available',
                source: source.source || (source.row_number && source.column_name ? `Row ${source.row_number}, Column ${source.column_name}` : source.document || `Source ${index + 1}`),
                confidence: source.confidence || source.confidence_score || source.score || 0.8,
                verified: source.verified !== false,
                context_before: source.context_before || (source.patient_info ? `Patient: ${source.patient_info.name}` : undefined),
                context_after: source.context_after || (source.column_name ? `Column: ${source.column_name}` : undefined),
                row_number: source.row_number,
                column_name: source.column_name,
                patient_info: source.patient_info
              }))
            }
          : msg
      ));

      // Load existing feedback for this query
      if (data.id) {
        try {
          const feedbackResponse = await sessionApi.get(`/feedback/query/${data.id}`);
          const userFeedback = feedbackResponse.data.find((fb: any) => fb.user_id === getUserId(session));

          if (userFeedback) {
            setMessages(prev => prev.map(msg =>
              msg.id === loadingMessage.id
                ? {
                    ...msg,
                    isLiked: userFeedback.rating === 'up',
                    isDisliked: userFeedback.rating === 'down'
                  }
                : msg
            ));
          }
        } catch (feedbackError) {
          console.log('No existing feedback found or error loading feedback:', feedbackError);
        }
      }

      // Auto-scroll will be handled by useEffect when message is added

      // Generate new suggestions based on the response
      generateFollowUpSuggestions(content, cleanAnswer);

      // Save conversation to history immediately
      try {
        const conversationData = {
          id: Date.now().toString(),
          title: content.length > 50 ? content.substring(0, 50) + '...' : content,
          datasetId: selectedDataset.id,
          datasetName: selectedDataset.name,
          lastMessage: cleanAnswer.length > 100 ? cleanAnswer.substring(0, 100) + '...' : cleanAnswer,
          messageCount: messages.length + 2, // +2 for current question and answer
          createdAt: new Date(),
          updatedAt: new Date(),
          isImportant: false,
          messages: [
            { role: 'user', content: content, timestamp: new Date() },
            { role: 'assistant', content: cleanAnswer, timestamp: new Date() }
          ]
        };

        // Save to localStorage for immediate display
        const existingConversations = JSON.parse(localStorage.getItem('rag_conversations') || '[]');
        const updatedConversations = [conversationData, ...existingConversations.slice(0, 19)];
        localStorage.setItem('rag_conversations', JSON.stringify(updatedConversations));

        // Trigger conversation history refresh
        window.dispatchEvent(new CustomEvent('conversationCreated'));
      } catch (saveError) {
        console.error('Failed to save conversation:', saveError);
      }

      // Track search performance
      if (data.search_analytics || searchAnalytics) {
        const performanceData = {
          timestamp: new Date(),
          query: content,
          responseTime: data.processing_time,
          trustScore: data.trust_score?.overall_score,
          sourcesCount: data.sources?.length || 0,
          searchTime: (searchAnalytics as any)?.search_time || 0
        };

        setSearchPerformanceHistory(prev => [...prev.slice(-19), performanceData]);
      }

    } catch (error: any) {
      console.error('Error sending message:', error);
      setLastError(error.message || 'Unknown error');

      let errorMessage = 'Sorry, I encountered an error while processing your question.';
      let showRetry = false;

      // Handle different types of errors
      if (error.name === 'AbortError') {
        errorMessage = 'Request was cancelled.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please sign out and sign back in.';
      } else if (error.response?.status === 429) {
        errorMessage = 'Too many requests. Please wait a moment before trying again.';
        showRetry = true;
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again in a few moments.';
        showRetry = true;
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your connection and try again.';
        showRetry = true;
      } else {
        errorMessage = `Error: ${error.message || 'Unknown error occurred'}`;
        showRetry = retryCount < 3;
      }

      // Update loading message with error
      setMessages(prev => prev.map(msg =>
        msg.id === loadingMessage.id
          ? {
              ...msg,
              content: errorMessage,
              isLoading: false,
              error: error.message || 'Failed to process query'
            }
          : msg
      ));

      // Add retry button if applicable
      if (showRetry && !isRetry) {
        setMessages(prev => [...prev, {
          id: `system-${Date.now()}`,
          type: 'system',
          content: `🔄 Would you like to retry? (Attempt ${retryCount + 1}/3)`,
          timestamp: new Date()
        }]);
      }

      // Increment retry count
      if (!isRetry) {
        setRetryCount(prev => prev + 1);
      }
    } finally {
      setIsTyping(false);
      setIsSearching(false);
      setSearchAbortController(null);

      // Clean up request queue
      setRequestQueue(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });

      // Trigger memory management
      manageMemory();

      // Reset retry count on successful completion
      if (!lastError) {
        setRetryCount(0);
      }
    }
  };

  const generateFollowUpSuggestions = (question: string, answer: string) => {
    // Simple follow-up generation - in production, this could use AI
    const followUps = [
      "Can you elaborate on that?",
      "What are the implications?",
      "Are there any examples?",
      "How does this relate to other parts?",
      "What should I know next?"
    ];

    setSuggestions(followUps.slice(0, 3));
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
    inputRef.current?.focus();
  };

  // Enhanced trust feedback handler
  const handleTrustFeedback = async (messageId: string, feedbackType: 'up' | 'down' | 'neutral', comment?: string) => {
    try {
      const message = messages.find(m => m.id === messageId);
      if (!message || !message.trustScore) return;

      const userMessage = messages
        .slice()
        .reverse()
        .find(m => m.type === 'user' && messages.indexOf(m) < messages.indexOf(message));

      if (!userMessage) return;

      // Submit feedback to new trust system
      const feedbackSuccess = await submitTrustFeedback({
        query: userMessage.content,
        predicted_trust: message.trustScore.overall_score,
        actual_correctness: classifyFeedback(feedbackType),
        user_id: getCurrentUserId(),
        topic: classifyTopic(userMessage.content),
        feedback_weight: 1.0
      });

      if (feedbackSuccess) {
        // Update message with feedback state
        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? {
                ...msg,
                isLiked: feedbackType === 'up',
                isDisliked: feedbackType === 'down'
              }
            : msg
        ));

        // Show success message
        setMessages(prev => [...prev, {
          id: `system-${Date.now()}`,
          type: 'system',
          content: '✅ Thank you for your feedback! This helps improve our trust scoring.',
          timestamp: new Date()
        }]);

        // Remove success message after 3 seconds
        setTimeout(() => {
          setMessages(prev => prev.filter(msg => !msg.content.includes('Thank you for your feedback')));
        }, 3000);
      }

      // Also submit to legacy feedback system if query ID exists
      if (message.queryId) {
        try {
          const sessionApi = createApiInstance(session);
          await sessionApi.post('/feedback/', {
            query_id: message.queryId,
            user_id: getCurrentUserId(),
            rating: feedbackType,
            comment: comment || '',
            feedback_type: 'trust_score'
          });
        } catch (legacyError) {
          console.warn('Legacy feedback submission failed:', legacyError);
        }
      }

    } catch (error) {
      console.error('Trust feedback error:', error);
      setMessages(prev => [...prev, {
        id: `system-${Date.now()}`,
        type: 'system',
        content: '❌ Failed to submit feedback. Please try again.',
        timestamp: new Date()
      }]);
    }
  };



  // Retry last failed message
  const retryLastMessage = () => {
    const lastUserMessage = messages
      .slice()
      .reverse()
      .find(m => m.type === 'user');

    if (lastUserMessage) {
      sendMessage(lastUserMessage.content, true);
    }
  };

  // Debounced input handler for better performance
  const [debouncedInput, setDebouncedInput] = useState('');
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedInput(inputMessage);
    }, 300);

    return () => clearTimeout(timer);
  }, [inputMessage]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputMessage);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K: Focus search input
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        inputRef.current?.focus();
      }

      // Ctrl/Cmd + S: Save conversation
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        saveConversation();
      }

      // Ctrl/Cmd + E: Export conversation
      if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
        e.preventDefault();
        exportConversation();
      }

      // Ctrl/Cmd + R: Clear conversation
      if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        clearConversation();
      }


    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [conversationId, selectedDataset]);

  const clearConversation = () => {
    setMessages([]);
    setConversationId(null);
    setSuggestions([]);
    if (selectedDataset) {
      initializeConversation();
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const toggleMessageImportant = (messageId: string) => {
    setUserHasScrolled(true); // User interaction - respect their scroll position
    setShouldAutoScroll(false);
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, isImportant: !msg.isImportant }
        : msg
    ));
  };



  const toggleMessageLike = async (messageId: string) => {
    setUserHasScrolled(true); // User interaction - respect their scroll position
    setShouldAutoScroll(false);

    const message = messages.find(m => m.id === messageId);
    if (!message || message.type !== 'assistant') return;

    // Find the corresponding user message (question)
    const messageIndex = messages.findIndex(m => m.id === messageId);
    const userMessage = messageIndex > 0 ? messages[messageIndex - 1] : null;
    if (!userMessage || userMessage.type !== 'user') return;

    try {
      // Submit feedback to database
      const sessionApi = createApiInstance(session);
      await sessionApi.post('/feedback', {
        user_id: getUserId(session),
        query_id: message.queryId || Date.now(), // Use queryId if available, fallback to timestamp
        rating: message.isLiked ? 'neutral' : 'up', // Toggle: if already liked, remove like (neutral), else like
        comment: null
      });

      // Update local UI state
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isLiked: !msg.isLiked, isDisliked: false }
          : msg
      ));

      console.log('Feedback submitted successfully: thumbs up');
    } catch (error) {
      console.error('Error submitting feedback:', error);
      // Still update UI even if API call fails
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isLiked: !msg.isLiked, isDisliked: false }
          : msg
      ));
    }
  };

  const toggleMessageDislike = async (messageId: string) => {
    setUserHasScrolled(true); // User interaction - respect their scroll position
    setShouldAutoScroll(false);

    const message = messages.find(m => m.id === messageId);
    if (!message || message.type !== 'assistant') return;

    // Find the corresponding user message (question)
    const messageIndex = messages.findIndex(m => m.id === messageId);
    const userMessage = messageIndex > 0 ? messages[messageIndex - 1] : null;
    if (!userMessage || userMessage.type !== 'user') return;

    try {
      // Submit feedback to database
      const sessionApi = createApiInstance(session);
      await sessionApi.post('/feedback', {
        user_id: getUserId(session),
        query_id: message.queryId || Date.now(), // Use queryId if available, fallback to timestamp
        rating: message.isDisliked ? 'neutral' : 'down', // Toggle: if already disliked, remove dislike (neutral), else dislike
        comment: null
      });

      // Update local UI state
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isDisliked: !msg.isDisliked, isLiked: false }
          : msg
      ));

      console.log('Feedback submitted successfully: thumbs down');
    } catch (error) {
      console.error('Error submitting feedback:', error);
      // Still update UI even if API call fails
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isDisliked: !msg.isDisliked, isLiked: false }
          : msg
      ));
    }
  };

  // Save query from message
  const saveQueryFromMessage = async (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (!message || message.type !== 'assistant') return;

    // Find the corresponding user message
    const messageIndex = messages.findIndex(m => m.id === messageId);
    const userMessage = messageIndex > 0 ? messages[messageIndex - 1] : null;

    if (!userMessage || userMessage.type !== 'user') return;

    try {
      const sessionApi = createApiInstance(session);
      await sessionApi.post('/api/v1/saved-queries/', {
        question: userMessage.content,
        answer: message.content,
        dataset_id: selectedDataset?.id,
        search_method: message.searchAnalytics?.method_used || 'standard',
        trust_score: message.trustScore?.overall_score,
        sources_count: message.sources?.length || 0
      });

      setMessages(prev => [...prev, {
        id: `system-${Date.now()}`,
        type: 'system',
        content: '💾 Query saved to your collection!',
        timestamp: new Date()
      }]);
    } catch (error) {
      console.error('Failed to save query:', error);
    }
  };

  const getTrustScoreColor = (score: number) => {
    if (score >= 0.7) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 0.5) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getTrustScoreLabel = (score: number) => {
    if (score >= 0.7) return 'High reliability, can be trusted';
    if (score >= 0.5) return 'Moderately reliable, verify key points';
    return 'Low reliability, use with caution';
  };



  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const truncateDatasetName = (name: string, maxLength: number = 18) => {
    if (name.length <= maxLength) return name;
    return name.substring(0, maxLength) + '...';
  };



  return (
    <ChatErrorBoundary>
      <style jsx>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

        .ai-font-primary {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
          font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
          letter-spacing: -0.01em;
        }

        .ai-font-display {
          font-family: 'Space Grotesk', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          font-feature-settings: 'ss01', 'ss02';
          letter-spacing: -0.02em;
        }

        .ai-font-mono {
          font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
          font-feature-settings: 'liga', 'calt';
        }

        .ai-text-glow {
          text-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
        }

        .custom-scrollbar::-webkit-scrollbar {
          width: 12px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, #3b82f6, #8b5cf6);
          border-radius: 6px;
          border: 2px solid #f1f5f9;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, #2563eb, #7c3aed);
        }
        .custom-scrollbar::-webkit-scrollbar-corner {
          background: #f1f5f9;
        }
      `}</style>
      <div className="flex flex-col h-full bg-gradient-to-br from-slate-50 to-blue-50/30 ai-font-primary">
      {/* Chat Header with Dropdown - AI Professional Design */}
      <div className="flex items-center justify-between p-6 border-b border-slate-200/60 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-3 rounded-xl shadow-lg">
              <Zap className="h-6 w-6 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
          </div>
          <div>
            <h3 className="text-lg font-semibold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
              RAG Chat
            </h3>

          </div>

          {/* Performance Dashboard */}
          {selectedDataset && (
            <div className="flex items-center space-x-3">
              {/* Response Time Indicator */}
              {searchPerformanceHistory.length > 0 && (
                <div className="flex items-center space-x-2 px-2 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200/60">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-xs font-medium text-blue-700">
                      {(searchPerformanceHistory.slice(-1)[0]?.responseTime / 1000 || 0).toFixed(1)}s
                    </span>
                  </div>
                  <div className="w-px h-3 bg-blue-300"></div>
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-blue-600">Avg:</span>
                    <span className="text-xs font-medium text-blue-700">
                      {(searchPerformanceHistory.reduce((acc, p) => acc + (p.responseTime || 0), 0) / searchPerformanceHistory.length / 1000).toFixed(1)}s
                    </span>
                  </div>
                </div>
              )}

              {/* Trust Score Indicator */}
              {searchPerformanceHistory.length > 0 && (
                <div className="flex items-center space-x-2 px-2 py-1 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200/60">
                  <div className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${
                      (searchPerformanceHistory.slice(-1)[0]?.trustScore || 0) > 0.7 ? 'bg-green-500' :
                      (searchPerformanceHistory.slice(-1)[0]?.trustScore || 0) > 0.5 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}></div>
                    <span className="text-xs font-medium text-green-700">
                      {((searchPerformanceHistory.slice(-1)[0]?.trustScore || 0) * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="w-px h-3 bg-green-300"></div>
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-green-600">Avg:</span>
                    <span className="text-xs font-medium text-green-700">
                      {(searchPerformanceHistory.reduce((acc, p) => acc + (p.trustScore || 0), 0) / searchPerformanceHistory.length * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              )}



              {/* Live Status */}
              {isSearching && (
                <div className="flex items-center space-x-2 px-2 py-1 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg border border-yellow-200/60">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium text-yellow-700">Processing...</span>
                </div>
              )}

              {/* AI Assistant Badge */}
              {!isSearching && (
                <div className="flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200/60">
                  <span className="text-xs">🧠</span>
                  <span className="text-xs font-medium text-blue-700">AI Assistant</span>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {/* Action Buttons */}
          <div className="flex items-center space-x-2">

            {/* Save Conversation */}
            {messages.length > 1 && (
              <button
                onClick={saveConversation}
                className="p-2 text-slate-400 hover:text-green-600 rounded-lg hover:bg-green-50 transition-all duration-200"
                title="Save conversation permanently to database"
              >
                <Bookmark className="h-4 w-4" />
              </button>
            )}

            {/* Export Conversation */}
            {messages.length > 1 && (
              <button
                onClick={exportConversation}
                className="p-2 text-slate-400 hover:text-slate-600 rounded-lg hover:bg-slate-100/80 transition-all duration-200"
                title="Export conversation"
              >
                <Copy className="h-4 w-4" />
              </button>
            )}

            {/* Help */}
            <button
              onClick={() => setShowHelp(!showHelp)}
              className={`p-2 rounded-lg transition-all duration-200 ${
                showHelp
                  ? 'bg-amber-100 text-amber-600'
                  : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100/80'
              }`}
              title="Keyboard shortcuts & help"
            >
              <MessageSquare className="h-4 w-4" />
            </button>

            {/* Clear Conversation */}
            {messages.length > 1 && (
              <button
                onClick={clearConversation}
                className="p-2 text-slate-400 hover:text-slate-600 rounded-lg hover:bg-slate-100/80 transition-all duration-200 group"
                title="Clear conversation"
              >
                <RefreshCw className="h-4 w-4 group-hover:rotate-180 transition-transform duration-300" />
              </button>
            )}
          </div>

          {/* Dataset Dropdown - AI Professional Style */}
          <div className="relative">
            <select
              value={selectedDataset?.id || ''}
              onChange={(e) => {
                const datasetId = parseInt(e.target.value);
                const dataset = availableDatasets.find(d => d.id === datasetId);
                onDatasetChange(dataset || null);
              }}
              className="min-w-[220px] px-4 py-2.5 border border-slate-200 rounded-xl text-xs font-medium bg-white/90 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 shadow-sm hover:shadow-md transition-all duration-200 appearance-none cursor-pointer"
              title={selectedDataset ? selectedDataset.name : 'Select a dataset'}
            >
              {availableDatasets.length === 0 ? (
                <option value="" className="text-xs">⚠️ No datasets available</option>
              ) : (
                <>
                  <option value="" className="text-xs">🔍 Select a dataset</option>
                  {availableDatasets.map(dataset => (
                    <option key={dataset.id} value={dataset.id} className="text-xs" title={dataset.name}>
                      📊 {truncateDatasetName(dataset.name)}
                    </option>
                  ))}
                </>
              )}
            </select>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <Database className="h-4 w-4 text-slate-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Trust Score Method Selector - Horizontal Cards */}
      {selectedDataset && (
        <div className="border-b border-slate-200/60 bg-white/90 backdrop-blur-sm p-4">
          <div className="mb-3">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-slate-700">Trust Score Method:</span>
              <span className="text-xs text-slate-500">Select your preferred scoring algorithm</span>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-3">
            {trustMethods.map((method) => (
              <div
                key={method.id}
                className="group relative"
              >
                <button
                  onClick={() => setSelectedTrustMethod(method.id)}
                  className={`w-full p-1.5 rounded-md border-2 transition-all duration-300 hover:shadow-md hover:scale-[1.01] relative overflow-hidden ${
                    selectedTrustMethod === method.id
                      ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md ring-1 ring-blue-200'
                      : 'border-slate-200 bg-white hover:border-blue-300 hover:bg-gradient-to-br hover:from-blue-50/30 hover:to-indigo-50/30'
                  }`}
                  style={{ minHeight: '32px' }}
                >
                  {/* Icon and name only */}
                  <div className="flex items-center justify-center space-x-1.5">
                    <div className="text-base">{method.icon}</div>
                    <div className="font-bold text-xs text-slate-800 ai-font-display">{method.shortName}</div>
                  </div>

                  {/* Selection indicator - Subtle ring */}
                  {selectedTrustMethod === method.id && (
                    <div className="absolute inset-0 bg-blue-500/5 rounded-xl"></div>
                  )}
                </button>

                {/* Enhanced Tooltip with Speed/Accuracy */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 w-72 p-4 bg-slate-900 text-white text-sm rounded-xl shadow-2xl opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 border border-slate-700">
                  <div className="font-bold mb-2 text-blue-300 ai-font-display">{method.name}</div>

                  {/* Speed and Accuracy badges in tooltip */}
                  <div className="flex items-center space-x-2 mb-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      method.speed === 'Fast' ? 'bg-green-100 text-green-700' :
                      method.speed === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-red-100 text-red-700'
                    }`}>
                      Speed: {method.speed}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      method.accuracy === 'Highest' ? 'bg-purple-100 text-purple-700' :
                      method.accuracy === 'High' ? 'bg-blue-100 text-blue-700' :
                      'bg-slate-100 text-slate-700'
                    }`}>
                      Accuracy: {method.accuracy}
                    </span>
                  </div>

                  <div className="text-slate-200 mb-3 leading-relaxed">{method.description}</div>
                  <div className="text-slate-400 text-xs leading-relaxed">{method.details}</div>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-900"></div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-3 text-xs text-slate-500 text-center">
            Selected method will be used for all new queries. Hover over cards for detailed information.
          </div>
        </div>
      )}




      {/* Help Panel */}
      {showHelp && (
        <div className="border-b border-slate-200/60 bg-gradient-to-r from-amber-50/50 to-orange-50/30 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-semibold text-slate-700 flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                Keyboard Shortcuts & Tips
              </h4>
              <button
                onClick={() => setShowHelp(false)}
                className="p-1 text-slate-400 hover:text-slate-600 rounded"
              >
                <ChevronUp className="h-4 w-4" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 className="text-sm font-medium text-slate-700 mb-2">Keyboard Shortcuts</h5>
                <div className="space-y-1 text-xs text-slate-600">
                  <div className="flex justify-between">
                    <span>Focus input</span>
                    <kbd className="px-1 py-0.5 bg-slate-200 rounded text-xs">Ctrl+K</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Scroll down</span>
                    <kbd className="px-1 py-0.5 bg-slate-200 rounded text-xs">Space</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Page down</span>
                    <kbd className="px-1 py-0.5 bg-slate-200 rounded text-xs">Page Down</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Page up</span>
                    <kbd className="px-1 py-0.5 bg-slate-200 rounded text-xs">Page Up</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Scroll to top</span>
                    <kbd className="px-1 py-0.5 bg-slate-200 rounded text-xs">Ctrl+Home</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Scroll to bottom</span>
                    <kbd className="px-1 py-0.5 bg-slate-200 rounded text-xs">Ctrl+End</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Clear conversation</span>
                    <kbd className="px-1 py-0.5 bg-slate-200 rounded text-xs">Ctrl+R</kbd>
                  </div>
                </div>
              </div>

              <div>
                <h5 className="text-sm font-medium text-slate-700 mb-2">Chat Tips</h5>
                <div className="space-y-1 text-xs text-slate-600">
                  <div>• Ask <strong>questions</strong> in natural language</div>
                  <div>• Be <strong>specific</strong> about what you need</div>
                  <div>• Use <strong>context</strong> from previous messages</div>
                  <div>• Try <strong>follow-up questions</strong> for deeper insights</div>
                  <div>• Mark <strong>important</strong> responses with the heart icon</div>
                </div>

                <h5 className="text-sm font-medium text-slate-700 mb-2 mt-4">Reliability Levels</h5>
                <div className="space-y-1 text-xs text-slate-600">
                  <div>• <strong className="text-green-600">High reliability</strong> (70-100%)</div>
                  <div>• <strong className="text-yellow-600">Moderate reliability</strong> (50-69%)</div>
                  <div>• <strong className="text-red-600">Low reliability</strong> (0-49%)</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Messages Area with Custom Scrollbar and Progress Indicator */}
      <div className="flex-1 relative overflow-hidden">
        {/* Scroll Progress Indicator - Inside Chat Window */}
        {messages.length > 3 && scrollProgress < 95 && (
          <div className="absolute top-0 left-0 right-0 h-1 bg-slate-200/50 z-10">
            <div
              className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 ease-out"
              style={{ width: `${scrollProgress}%` }}
            />
          </div>
        )}

        <div
          ref={messagesContainerRef}
          className="h-full overflow-y-auto p-6 space-y-6 bg-gradient-to-b from-transparent to-slate-50/30 custom-scrollbar"
          onScroll={checkScrollPosition}
          style={{
            scrollbarWidth: 'auto',
            scrollbarColor: '#3b82f6 #f1f5f9'
          }}
        >
        {!selectedDataset && (
          <div className="text-center py-16">
            <div className="relative mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mx-auto flex items-center justify-center shadow-xl">
                <Database className="h-10 w-10 text-white" />
              </div>
            </div>
            <h3 className="text-lg font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2 ai-font-display ai-text-glow">
              Welcome to RAG Chat
            </h3>
            <p className="text-sm text-slate-600 font-medium max-w-md mx-auto leading-relaxed">
              {availableDatasets.length === 0
                ? "🚀 Upload a dataset to start chatting with your documents using advanced AI retrieval"
                : "🎯 Select a dataset from the dropdown above to begin an intelligent conversation"
              }
            </p>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 group relative ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : message.type === 'system'
                  ? 'bg-gray-100 text-gray-800 border border-gray-200'
                  : message.isImportant
                  ? 'bg-red-50 text-gray-800 border border-red-200'
                  : 'bg-gray-50 text-gray-800 border border-gray-200'
              }`}
            >
              {/* Important Indicator */}
              {message.isImportant && (
                <div className="absolute -top-1 -right-1">
                  <Heart className="h-3 w-3 text-red-500 fill-current" />
                </div>
              )}
              {/* Message Header */}
              <div className="flex items-center justify-between mb-1.5">
                <div className="flex items-center space-x-1.5">
                  {message.type === 'user' ? (
                    <User className="h-3 w-3" />
                  ) : message.type === 'system' ? (
                    <Database className="h-3 w-3" />
                  ) : (
                    <Bot className="h-3 w-3" />
                  )}
                  {message.type === 'system' && selectedDataset ? (
                    <>
                      <span className="text-xs opacity-75 font-medium">Connected to</span>
                      <span className="text-xs opacity-75 font-medium">{selectedDataset.name}</span>
                      <span className="text-xs opacity-50 font-medium">{formatTimestamp(message.timestamp)}</span>
                    </>
                  ) : (
                    <span className="text-xs opacity-75 font-medium">
                      {formatTimestamp(message.timestamp)}
                    </span>
                  )}
                </div>

                {message.type !== 'user' && !message.isLoading && message.type !== 'system' && (
                  <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100">
                    <button
                      onClick={() => toggleMessageLike(message.id)}
                      className="p-1 hover:bg-gray-200 rounded"
                      title={message.isLiked ? "Remove like" : "Like message"}
                    >
                      <ThumbsUp className={`h-3 w-3 ${message.isLiked ? 'fill-current text-green-500' : 'text-gray-400'}`} />
                    </button>

                    <button
                      onClick={() => toggleMessageDislike(message.id)}
                      className="p-1 hover:bg-gray-200 rounded"
                      title={message.isDisliked ? "Remove dislike" : "Dislike message"}
                    >
                      <ThumbsDown className={`h-3 w-3 ${message.isDisliked ? 'fill-current text-red-500' : 'text-gray-400'}`} />
                    </button>

                    <button
                      onClick={() => toggleMessageImportant(message.id)}
                      className="p-1 hover:bg-gray-200 rounded"
                      title={message.isImportant ? "Remove from important" : "Mark as important"}
                    >
                      <Heart className={`h-3 w-3 ${message.isImportant ? 'fill-current text-red-500' : 'text-gray-400'}`} />
                    </button>



                    <button
                      onClick={() => saveQueryFromMessage(message.id)}
                      className="p-1 hover:bg-gray-200 rounded"
                      title="Save this Q&A"
                    >
                      <Star className="h-3 w-3 text-gray-400" />
                    </button>

                    <button
                      onClick={() => copyMessage(message.content)}
                      className="p-1 hover:bg-gray-200 rounded"
                      title="Copy message"
                    >
                      <Copy className="h-3 w-3 text-gray-400" />
                    </button>
                  </div>
                )}
              </div>

              {/* Message Content */}
              {message.isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                  <span className="text-xs font-medium">Thinking...</span>
                </div>
              ) : (
                <div className="prose prose-sm max-w-none">
                  <p className="whitespace-pre-wrap text-xs leading-relaxed font-medium">{message.content}</p>
                </div>
              )}



              {/* Quality Metrics - Trust Score and Response Time */}
              {(message.trustScore || message.processingTime || message.qualityMetrics) && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex flex-wrap items-start gap-2">

                    {/* Trust Score Display */}
                    {message.trustScore && (
                      <div className="inline-flex items-center space-x-2 px-3 py-2 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 text-xs text-yellow-700 shadow-sm">
                        <div className="flex items-center justify-center w-5 h-5 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg shadow-sm">
                          <svg className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                          </svg>
                        </div>
                        <div className="flex flex-col">
                          <span className="font-bold text-sm ai-font-display">Trust Score: {(message.trustScore.overall_score * 100).toFixed(0)}%</span>
                          <span className="text-xs opacity-80 leading-tight font-medium ai-font-primary">
                            {message.trustScore.overall_score >= 0.7 ? 'High reliability' :
                             message.trustScore.overall_score >= 0.5 ? 'Moderate reliability' :
                             'Low reliability'}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Enhanced Processing Time with ML Router Info */}
                    {message.type !== 'user' && message.processingTime && (
                      <div className="inline-flex items-center space-x-2 px-3 py-2 rounded-lg bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-xs text-green-700 shadow-sm">
                        <div className="flex items-center justify-center w-5 h-5 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-sm">
                          {message.processingMethod?.includes('ML') ? (
                            <svg className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                          ) : (
                            <svg className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          )}
                        </div>
                        <div className="flex flex-col">
                          <span className="font-bold text-sm ai-font-display">
                            {message.processingTime < 1000 ? `${message.processingTime}ms` : `${(message.processingTime / 1000).toFixed(2)}s`}
                            {message.processingMethod?.includes('ML') && <span className="ml-1 text-blue-600">⚡</span>}
                          </span>
                          <span className="text-xs opacity-80 leading-tight font-medium ai-font-primary">
                            {message.processingMethod || 'Processing complete'}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Enhanced Sources & Citations Header - Same Size as Trust Score */}
                    {message.sources && message.sources.length > 0 && (
                      <button
                        onClick={() => {
                          const messageIndex = messages.findIndex(m => m.id === message.id);
                          setExpandedSources(prev => ({
                            ...prev,
                            [messageIndex]: !prev[messageIndex]
                          }));
                        }}
                        className="inline-flex items-center space-x-2 px-3 py-2 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 text-xs text-blue-700 shadow-sm hover:from-blue-100 hover:to-purple-100 hover:border-blue-300 transition-all duration-200"
                      >
                        <div className="flex items-center justify-center w-5 h-5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm">
                          <svg className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <div className="flex flex-col">
                          <span className="font-bold text-sm ai-font-display">Sources ({message.sources.length})</span>
                          <span className="text-xs opacity-80 leading-tight font-medium ai-font-primary">Click to expand</span>
                        </div>
                        <svg
                          className={`h-3 w-3 transition-transform ${expandedSources[messages.findIndex(m => m.id === message.id)] ? 'rotate-180' : ''}`}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                    )}




                  </div>

                  {/* Enhanced Search Analytics */}
                  {message.searchAnalytics && (
                    <div className="mt-2 p-3 bg-gradient-to-r from-slate-50 to-blue-50/30 rounded-lg border border-slate-200">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Search className="h-3 w-3 text-blue-600" />
                          <span className="text-xs font-bold text-slate-700 ai-font-display">Search Analytics</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <BarChart3 className="h-3 w-3 text-slate-500" />
                          <span className="text-xs text-slate-500">Enhanced Retrieval</span>
                        </div>
                      </div>

                      <div className="text-xs text-slate-600">
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="h-3 w-3" />
                          <span>{message.searchAnalytics.total_results} sources retrieved</span>
                        </div>
                      </div>

                      {/* Query Analysis */}
                      {message.searchAnalytics.query_analysis && (
                        <div className="mt-2 pt-2 border-t border-slate-200">
                          <div className="text-xs text-slate-600">
                            <span className="font-medium">Query Type:</span> {
                              message.searchAnalytics.query_analysis.is_question ? 'Question' :
                              message.searchAnalytics.query_analysis.has_entities ? 'Entity Search' :
                              'General Query'
                            }
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Professional Source Attribution - Collapsible */}
                  {message.type === 'assistant' && !message.isLoading && expandedSources[messages.findIndex(m => m.id === message.id)] && (
                    <div className="mt-4 border-t border-gradient-to-r from-blue-100 to-purple-100 pt-4">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="flex items-center justify-center w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm">
                          <svg className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h4 className="text-sm font-bold text-slate-800 ai-font-display ai-text-glow">
                          Sources & Citations ({(message.sources || []).length})
                        </h4>
                      </div>

                      <div className="space-y-3">
                        {(message.sources || []).slice(0, 3).map((source, index) => (
                          <div key={index} className="group relative bg-white border border-slate-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 hover:border-blue-300">
                            {/* Source Header */}
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-2">
                                <div className="flex items-center justify-center w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm">
                                  <span className="text-xs font-bold text-white">{index + 1}</span>
                                </div>
                                <span className="font-bold text-slate-800 text-sm ai-font-display">
                                  {source.document || source.source || (source.row_number ? `Row ${source.row_number}` : `Document ${index + 1}`)}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <div className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                                  Verified
                                </div>
                              </div>
                            </div>

                            {/* Source Content */}
                            <div className="mb-3">
                              <p className="text-slate-700 text-sm leading-relaxed line-clamp-3">
                                {source.text}
                              </p>
                            </div>

                            {/* Source Metadata */}
                            <div className="flex items-center justify-between text-xs">
                              <div className="flex items-center space-x-3 text-blue-600">
                                {source.page_number && (
                                  <span className="flex items-center space-x-1">
                                    <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span>Page {source.page_number}</span>
                                  </span>
                                )}
                                {source.line_start && (
                                  <span className="flex items-center space-x-1">
                                    <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                    </svg>
                                    <span>Lines {source.line_start}-{source.line_end || source.line_start}</span>
                                  </span>
                                )}
                              </div>
                              <div className="text-slate-500">
                                <span>Source {index + 1}</span>
                              </div>
                            </div>
                          </div>
                        ))}

                        {(message.sources || []).length > 3 && (
                          <div className="text-xs text-gray-500 text-center py-2 bg-gray-50 rounded-lg">
                            +{(message.sources || []).length - 3} more sources available
                          </div>
                        )}

                        {/* Show message when no sources available */}
                        {(!message.sources || message.sources.length === 0) && (
                          <div className="text-xs text-gray-500 text-center py-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div className="flex items-center justify-center space-x-2">
                              <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <span>Response generated from general knowledge</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* New Message Notification - Positioned relative to chat window */}
      {showNewMessageNotification && (
        <div className="absolute top-16 left-1/2 transform -translate-x-1/2 z-20">
          <button
            onClick={() => {
              setShouldAutoScroll(true);
              setUserHasScrolled(false);
              setShowNewMessageNotification(false);
              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }}
            className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full shadow-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 hover:scale-105 animate-pulse"
          >
            <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
            <span className="text-sm font-bold ai-font-display">New message</span>
            <ChevronDown className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* Scroll to bottom button - only show when user has scrolled up */}
      {!shouldAutoScroll && messages.length > 3 && !showNewMessageNotification && (
        <button
          onClick={() => {
            setShouldAutoScroll(true);
            setUserHasScrolled(false);
            messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
          }}
          className="absolute bottom-20 right-6 bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 hover:scale-105 z-10"
          aria-label="Scroll to bottom"
        >
          <ChevronDown className="h-4 w-4" />
        </button>
      )}



      {/* Suggestions - AI Professional Design */}
      {suggestions.length > 0 && selectedDataset && (
        <div className="px-6 py-4 border-t border-slate-200/60 bg-gradient-to-r from-blue-50/50 to-purple-50/30">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Sparkles className="h-2.5 w-2.5 text-white" />
            </div>
            <span className="text-xs font-bold text-slate-700 tracking-wide ai-font-display ai-text-glow">AI Suggestions</span>
            <div className="flex-1 h-px bg-gradient-to-r from-slate-200 to-transparent"></div>
          </div>
          <div className="flex flex-wrap gap-2 min-h-[28px]">
            {suggestions.map((suggestion, index) => (
              <button
                key={`suggestion-${index}-${suggestion}`}
                onClick={() => handleSuggestionClick(suggestion)}
                className="text-xs bg-gradient-to-r from-blue-50 to-purple-50 text-slate-700 font-medium px-3 py-1.5 rounded-xl border border-slate-200/60 hover:from-blue-100 hover:to-purple-100 hover:border-blue-300/60 hover:shadow-md transition-all duration-200 hover:scale-105"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Performance Analytics */}
      {searchPerformanceHistory.length > 0 && (
        <div className="px-6 py-3 border-t border-slate-200/60 bg-slate-50/80">
          <div className="flex items-center justify-between text-xs text-slate-600">
            <div className="flex items-center space-x-4">
              <span className="font-bold ai-font-display">Session Analytics:</span>
              <span>{searchPerformanceHistory.length} queries</span>
              <span>Avg Response: {(searchPerformanceHistory.reduce((acc, p) => acc + (p.responseTime || 0), 0) / searchPerformanceHistory.length / 1000).toFixed(2)}s</span>
              <span>Avg Trust: {(searchPerformanceHistory.reduce((acc, p) => acc + (p.trustScore || 0), 0) / searchPerformanceHistory.length * 100).toFixed(1)}%</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-700">
                🚀 Smart RAG + ML Routing
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Error Display & Retry */}
      {lastError && retryCount < 3 && (
        <div className="px-6 py-3 border-t border-red-200 bg-red-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-red-700">
              <span className="text-sm">⚠️ {lastError}</span>
            </div>
            <button
              onClick={retryLastMessage}
              disabled={isSearching}
              className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
              aria-label="Retry last message"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Input Area - AI Professional Design */}
      <div className="p-6 border-t border-slate-200/60 bg-white/90 backdrop-blur-sm">
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={selectedDataset ? "✨ Ask me anything about your data..." : "🔍 Select a dataset to begin"}
              disabled={!selectedDataset || isTyping || isSearching}
              className="w-full resize-none border border-slate-200 rounded-2xl px-4 py-3 pr-10 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 disabled:opacity-50 disabled:cursor-not-allowed bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 font-medium text-slate-700 placeholder-slate-400 text-sm ai-font-primary"
              rows={1}
              style={{ minHeight: '48px', maxHeight: '102px' }}
              aria-label="Type your question here"
              maxLength={2000}
            />

            {/* Character count */}
            <div className="absolute top-1/2 right-3 transform -translate-y-1/2 text-xs text-slate-400">
              {inputMessage.length}/2000
            </div>

          </div>
          <button
            onClick={() => sendMessage(inputMessage)}
            disabled={!inputMessage.trim() || !selectedDataset || isTyping || isSearching}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 rounded-2xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group min-w-[48px]"
            aria-label="Send message"
          >
            {isSearching ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Send className="h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-200" />
            )}
          </button>

          {/* Cancel button when searching */}
          {isSearching && (
            <button
              onClick={() => {
                if (searchAbortController) {
                  searchAbortController.abort();
                }
              }}
              className="bg-red-600 text-white p-3 rounded-2xl hover:bg-red-700 transition-colors shadow-lg"
              aria-label="Cancel search"
            >
              ✕
            </button>
          )}
        </div>
      </div>
      </div>


    </ChatErrorBoundary>
  );
}
