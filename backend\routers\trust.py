"""
Trust Score API Router
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
import json
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
import models
from aithentiq.trust import TrustScoreComputer, BayesianTrustUpdater, TrustAuditor, OfflineEvaluator
from aithentiq.config import trust_config
from middleware.api_auth import get_current_user_from_api_key


# Pydantic models
class TrustComputeRequest(BaseModel):
    query: str = Field(..., description="User question")
    answer: str = Field(..., description="AI-generated answer")
    user_id: str = Field(..., description="User identifier")
    topic: Optional[str] = Field(default="general", description="Topic/domain")
    sources: Optional[List[Dict[str, Any]]] = Field(default=[], description="Source documents")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="Additional context")


class TrustFeedbackRequest(BaseModel):
    query: str = Field(..., description="Original query")
    predicted_trust: float = Field(..., ge=0.0, le=1.0, description="Predicted trust score")
    actual_correctness: bool = Field(..., description="Whether answer was actually correct")
    user_id: str = Field(..., description="User identifier")
    topic: Optional[str] = Field(default="general", description="Topic/domain")
    feedback_weight: Optional[float] = Field(default=1.0, ge=0.0, le=1.0, description="Feedback weight")


class AuditRequest(BaseModel):
    start_date: Optional[datetime] = Field(default=None, description="Start date for audit period")
    end_date: Optional[datetime] = Field(default=None, description="End date for audit period")
    user_id: Optional[str] = Field(default=None, description="Filter by specific user")
    limit: Optional[int] = Field(default=1000, ge=1, le=10000, description="Maximum responses to audit")


# Initialize trust system components
trust_computer = TrustScoreComputer()
bayesian_updater = BayesianTrustUpdater()
trust_auditor = TrustAuditor()
offline_evaluator = OfflineEvaluator(trust_computer)

# Create router
router = APIRouter(
    prefix="/trust",
    tags=["trust"]
)


@router.post("/compute")
async def compute_trust_score(
    request: TrustComputeRequest,
    current_user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Compute trust score for a query-answer pair with Bayesian updates
    """
    try:
        # Prepare context
        context = {
            "sources": request.sources,
            "metadata": request.metadata,
            "user_id": request.user_id,
            "topic": request.topic
        }
        
        # Compute base trust score
        trust_result = trust_computer.compute(
            query=request.query,
            answer=request.answer,
            context=context
        )
        
        # Apply Bayesian updates
        posterior_result = bayesian_updater.get_posterior_trust(
            user_id=request.user_id,
            topic=request.topic,
            base_trust=trust_result.overall_score
        )
        
        # Combine results
        response = {
            "trust_score": {
                "overall_score": posterior_result["posterior_trust"],
                "base_score": trust_result.overall_score,
                "components": trust_result.components.to_dict(),
                "explanation": trust_result.explanation,
                "factors": trust_result.factors,
                "confidence_interval": trust_result.confidence_interval,
                "processing_time_ms": trust_result.processing_time_ms
            },
            "bayesian_info": {
                "user_posterior": posterior_result["user_posterior"],
                "topic_posterior": posterior_result["topic_posterior"],
                "user_confidence": posterior_result["user_confidence"],
                "topic_confidence": posterior_result["topic_confidence"],
                "user_interactions": posterior_result["user_interactions"],
                "topic_interactions": posterior_result["topic_interactions"]
            },
            "computed_at": datetime.utcnow().isoformat()
        }
        
        return response
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Trust score computation failed: {str(e)}")


@router.post("/feedback")
async def submit_trust_feedback(
    request: TrustFeedbackRequest,
    current_user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Submit feedback to update Bayesian priors
    """
    try:
        # Update Bayesian posteriors
        bayesian_updater.update_posterior(
            user_id=request.user_id,
            topic=request.topic,
            predicted_trust=request.predicted_trust,
            actual_correctness=request.actual_correctness,
            feedback_weight=request.feedback_weight
        )
        
        return {
            "status": "success",
            "message": f"Feedback processed for user {request.user_id}, topic {request.topic}",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Feedback processing failed: {str(e)}")


@router.get("/user/{user_id}/profile")
async def get_user_trust_profile(
    user_id: str,
    current_user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get comprehensive trust profile for a user
    """
    try:
        profile = bayesian_updater.get_user_trust_profile(user_id)
        return profile
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user profile: {str(e)}")


@router.get("/topic/{topic}/profile")
async def get_topic_trust_profile(
    topic: str,
    current_user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get comprehensive trust profile for a topic
    """
    try:
        profile = bayesian_updater.get_topic_trust_profile(topic)
        return profile
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get topic profile: {str(e)}")


@router.post("/audit")
async def run_trust_audit(
    request: AuditRequest,
    current_user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Run trust score audit on recent responses
    """
    try:
        # Query recent responses from database
        query = db.query(models.Query)
        
        if request.start_date:
            query = query.filter(models.Query.created_at >= request.start_date)
        if request.end_date:
            query = query.filter(models.Query.created_at <= request.end_date)
        if request.user_id:
            query = query.filter(models.Query.user_id == request.user_id)
        
        responses = query.limit(request.limit).all()
        
        # Convert to audit format
        audit_data = []
        for response in responses:
            try:
                trust_score = json.loads(response.trust_score) if response.trust_score else {}
                sources = json.loads(response.chart_data) if response.chart_data else {}
                
                audit_data.append({
                    "question": response.question,
                    "answer": response.answer,
                    "trust_score": trust_score,
                    "sources": sources.get("sources", []),
                    "user_id": response.user_id,
                    "created_at": response.created_at.isoformat()
                })
            except Exception as e:
                continue  # Skip malformed records
        
        # Run audit
        audit_report = trust_auditor.compute_audit_metrics(audit_data)
        
        return {
            "audit_report": audit_report.to_dict(),
            "sample_size": len(audit_data),
            "query_period": {
                "start": request.start_date.isoformat() if request.start_date else None,
                "end": request.end_date.isoformat() if request.end_date else None
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Audit failed: {str(e)}")


@router.post("/evaluate")
async def run_offline_evaluation(
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Trigger offline evaluation (runs in background)
    """
    try:
        # Run evaluation in background
        background_tasks.add_task(offline_evaluator.nightly_evaluation, db)
        
        return {
            "status": "started",
            "message": "Offline evaluation started in background",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start evaluation: {str(e)}")


@router.get("/evaluation/summary")
async def get_evaluation_summary(
    days: int = 30,
    current_user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get evaluation summary for the last N days
    """
    try:
        summary = offline_evaluator.get_evaluation_summary(days=days)
        return summary
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get evaluation summary: {str(e)}")


@router.get("/config")
async def get_trust_config(
    current_user: models.User = Depends(get_current_user_from_api_key)
) -> Dict[str, Any]:
    """
    Get current trust score configuration
    """
    try:
        return {
            "component_weights": {
                "model_confidence": trust_config.model_confidence_weight,
                "qat_regressor": trust_config.qat_regressor_weight,
                "refusal_detector": trust_config.refusal_detector_weight,
                "citation_precision": trust_config.citation_precision_weight
            },
            "bayesian_priors": {
                "user_alpha": trust_config.user_prior_alpha,
                "user_beta": trust_config.user_prior_beta,
                "topic_alpha": trust_config.topic_prior_alpha,
                "topic_beta": trust_config.topic_prior_beta
            },
            "thresholds": {
                "high_trust": trust_config.high_trust_threshold,
                "moderate_trust": trust_config.moderate_trust_threshold,
                "low_trust": trust_config.low_trust_threshold
            },
            "evaluation": {
                "sample_size": trust_config.evaluation_sample_size,
                "frequency_hours": trust_config.evaluation_frequency_hours,
                "roc_auc_threshold": trust_config.roc_auc_threshold,
                "calibration_threshold": trust_config.calibration_error_threshold
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get config: {str(e)}")


@router.get("/health")
async def trust_system_health() -> Dict[str, Any]:
    """
    Get trust system health status
    """
    try:
        # Basic health checks
        health_status = {
            "trust_computer": "healthy",
            "bayesian_updater": "healthy", 
            "trust_auditor": "healthy",
            "offline_evaluator": "healthy",
            "config_loaded": trust_config is not None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Check component counts
        health_status["user_priors_count"] = len(bayesian_updater.user_priors)
        health_status["topic_priors_count"] = len(bayesian_updater.topic_priors)
        health_status["evaluation_history_count"] = len(offline_evaluator.evaluation_history)
        
        return health_status
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
