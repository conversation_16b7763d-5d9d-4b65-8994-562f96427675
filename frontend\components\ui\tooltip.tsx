'use client';

import React, { useState, useRef, useEffect, ReactNode } from 'react';

interface TooltipProps {
  children: ReactNode;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
}

export function Tooltip({ 
  children, 
  content, 
  position = 'top', 
  delay = 300 
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [coords, setCoords] = useState({ x: 0, y: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);
  const childRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const showTooltip = () => {
    timerRef.current = setTimeout(() => {
      setIsVisible(true);
      
      if (childRef.current) {
        const rect = childRef.current.getBoundingClientRect();
        
        let x = 0;
        let y = 0;
        
        switch (position) {
          case 'top':
            x = rect.left + rect.width / 2;
            y = rect.top;
            break;
          case 'bottom':
            x = rect.left + rect.width / 2;
            y = rect.bottom;
            break;
          case 'left':
            x = rect.left;
            y = rect.top + rect.height / 2;
            break;
          case 'right':
            x = rect.right;
            y = rect.top + rect.height / 2;
            break;
        }
        
        setCoords({ x, y });
      }
    }, delay);
  };

  const hideTooltip = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // Calculate tooltip position
  const getTooltipStyle = () => {
    if (!tooltipRef.current) return {};
    
    const tooltipWidth = tooltipRef.current.offsetWidth;
    const tooltipHeight = tooltipRef.current.offsetHeight;
    
    let left = 0;
    let top = 0;
    
    switch (position) {
      case 'top':
        left = coords.x - tooltipWidth / 2;
        top = coords.y - tooltipHeight - 8;
        break;
      case 'bottom':
        left = coords.x - tooltipWidth / 2;
        top = coords.y + 8;
        break;
      case 'left':
        left = coords.x - tooltipWidth - 8;
        top = coords.y - tooltipHeight / 2;
        break;
      case 'right':
        left = coords.x + 8;
        top = coords.y - tooltipHeight / 2;
        break;
    }
    
    // Adjust if tooltip would go off screen
    const rightEdge = left + tooltipWidth;
    const bottomEdge = top + tooltipHeight;
    
    if (rightEdge > window.innerWidth) {
      left = window.innerWidth - tooltipWidth - 8;
    }
    
    if (left < 8) {
      left = 8;
    }
    
    if (bottomEdge > window.innerHeight) {
      top = window.innerHeight - tooltipHeight - 8;
    }
    
    if (top < 8) {
      top = 8;
    }
    
    return {
      left: `${left}px`,
      top: `${top}px`
    };
  };

  return (
    <>
      <div 
        ref={childRef}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
        className="inline-block"
      >
        {children}
      </div>
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className="fixed z-50 px-3 py-2 text-xs font-medium text-white bg-gray-900 rounded-md shadow-sm max-w-xs pointer-events-none opacity-90"
          style={getTooltipStyle()}
        >
          {content}
          <div 
            className={`absolute w-2 h-2 bg-gray-900 transform rotate-45 ${
              position === 'top' ? 'bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2' :
              position === 'bottom' ? 'top-0 left-1/2 -translate-x-1/2 -translate-y-1/2' :
              position === 'left' ? 'right-0 top-1/2 translate-x-1/2 -translate-y-1/2' :
              'left-0 top-1/2 -translate-x-1/2 -translate-y-1/2'
            }`}
          />
        </div>
      )}
    </>
  );
}
