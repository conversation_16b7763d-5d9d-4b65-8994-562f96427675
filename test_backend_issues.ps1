# AIthentiq Backend Issue Testing Script (PowerShell)
# Tests for: 1) Missing test files visibility, 2) User Management database error
# Run this from the AIthentiq project root directory

$BaseURL = "http://localhost:8000"
$AdminUserID = "<EMAIL>"
$ProjectRoot = Get-Location

Write-Host "🔧 AIthentiq Backend Issue Testing Script" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Project Root: $ProjectRoot"
Write-Host "Base URL: $BaseURL"
Write-Host "Admin User: $AdminUserID"
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "backend\main.py") -or -not (Test-Path "Test")) {
    Write-Host "❌ Error: Please run this script from the AIthentiq project root directory" -ForegroundColor Red
    Write-Host "Expected files: backend\main.py, Test folder" -ForegroundColor Red
    exit 1
}

# Function to print colored output
function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO" { Write-Host "ℹ️  $Message" -ForegroundColor Blue }
    }
}

# Test 1: Basic Backend Health
Write-Host "1. Testing Backend Health" -ForegroundColor White
Write-Host "------------------------" -ForegroundColor White
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/test" -Method GET -TimeoutSec 10
    Write-Status "SUCCESS" "Backend is running"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Status "ERROR" "Backend not responding: $($_.Exception.Message)"
    exit 1
}
Write-Host ""

# Test 2: Database Migration Check
Write-Host "2. Testing Database Schema" -ForegroundColor White
Write-Host "-------------------------" -ForegroundColor White
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/debug/queries" -Method GET -TimeoutSec 10
    Write-Status "SUCCESS" "Database queries endpoint working"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Status "ERROR" "Database queries endpoint failed: $($_.Exception.Message)"
}
Write-Host ""

# Test 3: Create Admin User (if needed)
Write-Host "3. Creating/Verifying Admin User" -ForegroundColor White
Write-Host "--------------------------------" -ForegroundColor White
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/make-admin/$AdminUserID" -Method POST -TimeoutSec 10
    Write-Status "SUCCESS" "Admin user created/verified"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Status "ERROR" "Failed to create admin user: $($_.Exception.Message)"
}
Write-Host ""

# Test 4: User Management Endpoint (The failing one)
Write-Host "4. Testing User Management (The Problem)" -ForegroundColor White
Write-Host "---------------------------------------" -ForegroundColor White
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/admin/users" -Method GET -Body @{user_id=$AdminUserID} -TimeoutSec 10
    Write-Status "SUCCESS" "User management endpoint working"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Status "ERROR" "User management endpoint failed: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response content: $responseBody" -ForegroundColor Red
    }
}
Write-Host ""

# Test 5: Test Scripts Endpoint
Write-Host "5. Testing Test Scripts Visibility" -ForegroundColor White
Write-Host "----------------------------------" -ForegroundColor White
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/admin/test-scripts" -Method GET -Body @{user_id=$AdminUserID} -TimeoutSec 10
    Write-Status "SUCCESS" "Test scripts endpoint working"
    $scriptsCount = if ($response.scripts) { $response.scripts.Count } else { 0 }
    Write-Status "INFO" "Found $scriptsCount test scripts"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Status "ERROR" "Test scripts endpoint failed: $($_.Exception.Message)"
}
Write-Host ""

# Test 6: Debug Users Endpoint
Write-Host "6. Testing Debug Users Endpoint" -ForegroundColor White
Write-Host "-------------------------------" -ForegroundColor White
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/debug/users" -Method GET -TimeoutSec 10
    Write-Status "SUCCESS" "Debug users endpoint working"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Status "ERROR" "Debug users endpoint failed: $($_.Exception.Message)"
}
Write-Host ""

# Test 7: Database Migration Status
Write-Host "7. Testing Database Migration Status" -ForegroundColor White
Write-Host "------------------------------------" -ForegroundColor White
Write-Host "Attempting to run database migration..."

if (Test-Path "backend\migrations\add_monitoring_fields.py") {
    Write-Status "INFO" "Found migration script, attempting to run..."
    try {
        Push-Location backend
        Write-Host "Running migration from: $(Get-Location)" -ForegroundColor Yellow
        $migrationOutput = python migrations\add_monitoring_fields.py 2>&1
        Pop-Location

        Write-Status "SUCCESS" "Migration completed"
        Write-Host "Migration output:" -ForegroundColor Yellow
        Write-Host $migrationOutput
    } catch {
        Write-Status "ERROR" "Migration failed: $($_.Exception.Message)"
        Pop-Location
    }
} else {
    Write-Status "WARNING" "Migration script not found at backend\migrations\add_monitoring_fields.py"
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    Write-Host "Checking if backend directory exists: $(Test-Path 'backend')" -ForegroundColor Yellow
}
Write-Host ""

# Test 8: Re-test User Management After Migration
Write-Host "8. Re-testing User Management After Migration" -ForegroundColor White
Write-Host "---------------------------------------------" -ForegroundColor White
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/admin/users" -Method GET -Body @{user_id=$AdminUserID} -TimeoutSec 10
    Write-Status "SUCCESS" "User management now working!"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Status "ERROR" "User management still failing: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response content: $responseBody" -ForegroundColor Red
    }
}
Write-Host ""

# Test 9: Check Test Folder Directly
Write-Host "9. Checking Test Folder Directly" -ForegroundColor White
Write-Host "--------------------------------" -ForegroundColor White
$testFiles = Get-ChildItem -Path "Test" -Filter "*.py" | Select-Object Name, Length, LastWriteTime
Write-Status "INFO" "Found $($testFiles.Count) Python test files in Test folder"
$testFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Gray }
Write-Host ""

# Summary
Write-Host "🎯 SUMMARY" -ForegroundColor Cyan
Write-Host "==========" -ForegroundColor Cyan
Write-Host "Issues tested:"
Write-Host "1. Test files visibility - Check test scripts endpoint result above"
Write-Host "2. User Management database error - Check user management endpoint results above"
Write-Host ""
Write-Host "Test files found locally: $($testFiles.Count) files in Test folder"
Write-Host ""
Write-Host "If user management is still failing, the response_time_ms column is missing from the database."
Write-Host "If test scripts show 0 results but files exist locally, there's a path resolution issue in the backend."
Write-Host ""
Write-Host "Next steps:"
Write-Host "- If migration failed, manually add the missing column to the database"
Write-Host "- If test scripts not visible, check the backend's path resolution for Test folder"
Write-Host "- Backend should look for Test files at: ../Test/ (relative to backend directory)"
