"""
Tenant Isolation Middleware for AIthentiq
Ensures all database queries are automatically scoped to the current tenant
"""

from fastapi import Request, HTTPException, status, Depends
from sqlalchemy.orm import Session
from sqlalchemy import event
from sqlalchemy.engine import Engine
from typing import Optional, Dict, Any
import logging
from contextlib import contextmanager

from database import get_db
from models_multitenant import Tenant, TenantUser, TenantApiKey

logger = logging.getLogger(__name__)

class TenantContext:
    """
    Thread-local storage for tenant context
    """
    def __init__(self):
        self._tenant_id: Optional[str] = None
        self._user_id: Optional[str] = None
        self._tenant: Optional[Tenant] = None
        self._user: Optional[TenantUser] = None

    @property
    def tenant_id(self) -> Optional[str]:
        return self._tenant_id

    @property
    def user_id(self) -> Optional[str]:
        return self._user_id

    @property
    def tenant(self) -> Optional[Tenant]:
        return self._tenant

    @property
    def user(self) -> Optional[TenantUser]:
        return self._user

    def set_context(self, tenant_id: str, user_id: str, tenant: Tenant = None, user: TenantUser = None):
        """Set the current tenant and user context"""
        self._tenant_id = tenant_id
        self._user_id = user_id
        self._tenant = tenant
        self._user = user

    def clear_context(self):
        """Clear the current context"""
        self._tenant_id = None
        self._user_id = None
        self._tenant = None
        self._user = None

    def is_set(self) -> bool:
        """Check if context is set"""
        return self._tenant_id is not None and self._user_id is not None

# Global tenant context instance
tenant_context = TenantContext()

class TenantIsolationMiddleware:
    """
    Middleware to enforce tenant isolation
    """
    
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)
            
            # Skip tenant isolation for certain paths
            if self._should_skip_isolation(request.url.path):
                await self.app(scope, receive, send)
                return

            try:
                # Extract tenant context from request
                await self._extract_tenant_context(request)
                
                # Process the request with tenant context
                await self.app(scope, receive, send)
                
            except HTTPException as e:
                # Handle authentication/authorization errors
                response = JSONResponse(
                    status_code=e.status_code,
                    content={"detail": e.detail}
                )
                await response(scope, receive, send)
            finally:
                # Always clear context after request
                tenant_context.clear_context()
        else:
            await self.app(scope, receive, send)

    def _should_skip_isolation(self, path: str) -> bool:
        """
        Determine if tenant isolation should be skipped for this path
        """
        skip_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/auth/login",
            "/auth/register",
            "/auth/oauth",
            "/tenants/register",  # Tenant registration endpoint
            "/static/",
            "/favicon.ico"
        ]
        
        return any(path.startswith(skip_path) for skip_path in skip_paths)

    async def _extract_tenant_context(self, request: Request):
        """
        Extract tenant and user context from the request
        """
        db = next(get_db())
        
        try:
            # Method 1: Extract from API key
            api_key = request.headers.get("X-API-Key")
            if api_key:
                await self._authenticate_via_api_key(api_key, db)
                return

            # Method 2: Extract from JWT token (NextAuth)
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                await self._authenticate_via_jwt(token, db)
                return

            # Method 3: Extract from session/cookie
            session_data = request.cookies.get("session")
            if session_data:
                await self._authenticate_via_session(session_data, db)
                return

            # Method 4: Extract from subdomain (e.g., acme.aithentiq.com)
            host = request.headers.get("host", "")
            if "." in host:
                subdomain = host.split(".")[0]
                tenant = db.query(Tenant).filter(Tenant.domain == subdomain).first()
                if tenant:
                    # For subdomain-based access, we still need user authentication
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="User authentication required"
                    )

            # No valid authentication found
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )

        finally:
            db.close()

    async def _authenticate_via_api_key(self, api_key: str, db: Session):
        """
        Authenticate using API key and set tenant context
        """
        # Handle temporary API keys (from frontend fallback)
        if api_key.startswith("temp-key-"):
            user_id = api_key.replace("temp-key-", "")
            await self._handle_temporary_key(user_id, db)
            return

        # Look up API key
        db_api_key = db.query(TenantApiKey).filter(
            TenantApiKey.key == api_key,
            TenantApiKey.is_active == True
        ).first()

        if not db_api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )

        # Check if API key is expired
        if db_api_key.expires_at and db_api_key.expires_at < func.now():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key expired"
            )

        # Get tenant and user
        tenant = db.query(Tenant).filter(Tenant.id == db_api_key.tenant_id).first()
        user = db.query(TenantUser).filter(TenantUser.id == db_api_key.user_id).first()

        if not tenant or not tenant.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Tenant not active"
            )

        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User not active"
            )

        # Set tenant context
        tenant_context.set_context(
            tenant_id=tenant.id,
            user_id=user.id,
            tenant=tenant,
            user=user
        )

        # Update API key usage
        db_api_key.usage_count += 1
        db_api_key.last_used = func.now()
        db.commit()

    async def _handle_temporary_key(self, user_id: str, db: Session):
        """
        Handle temporary API keys from frontend OAuth
        """
        # This is a simplified approach for development
        # In production, you'd want more robust user/tenant association
        
        # For now, create a default tenant if none exists
        default_tenant = db.query(Tenant).filter(Tenant.domain == "default").first()
        if not default_tenant:
            default_tenant = Tenant(
                domain="default",
                name="Default Tenant",
                display_name="Default Organization"
            )
            db.add(default_tenant)
            db.commit()
            db.refresh(default_tenant)

        # Check if user exists
        user = db.query(TenantUser).filter(
            TenantUser.id == user_id,
            TenantUser.tenant_id == default_tenant.id
        ).first()

        if not user:
            # Create user in default tenant
            user = TenantUser(
                id=user_id,
                tenant_id=default_tenant.id,
                email=f"{user_id}@oauth.local",
                name="OAuth User",
                oauth_provider="auto"
            )
            db.add(user)
            db.commit()
            db.refresh(user)

        # Set tenant context
        tenant_context.set_context(
            tenant_id=default_tenant.id,
            user_id=user.id,
            tenant=default_tenant,
            user=user
        )

    async def _authenticate_via_jwt(self, token: str, db: Session):
        """
        Authenticate using JWT token (NextAuth)
        """
        # TODO: Implement JWT token validation
        # This would decode the JWT, validate it, and extract user information
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="JWT authentication not yet implemented"
        )

    async def _authenticate_via_session(self, session_data: str, db: Session):
        """
        Authenticate using session cookie
        """
        # TODO: Implement session-based authentication
        # This would validate the session and extract user information
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Session authentication not yet implemented"
        )

# Database event listeners for automatic tenant filtering
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """
    Set SQLite pragmas for better performance and foreign key support
    """
    if 'sqlite' in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.close()

def get_current_tenant() -> Tenant:
    """
    Get the current tenant from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No tenant context available"
        )
    return tenant_context.tenant

def get_current_user() -> TenantUser:
    """
    Get the current user from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No user context available"
        )
    return tenant_context.user

def get_current_tenant_id() -> str:
    """
    Get the current tenant ID from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No tenant context available"
        )
    return tenant_context.tenant_id

def get_current_user_id() -> str:
    """
    Get the current user ID from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No user context available"
        )
    return tenant_context.user_id

@contextmanager
def tenant_scope(tenant_id: str, user_id: str):
    """
    Context manager for setting tenant scope in background tasks
    """
    old_tenant_id = tenant_context.tenant_id
    old_user_id = tenant_context.user_id
    
    try:
        tenant_context.set_context(tenant_id, user_id)
        yield
    finally:
        if old_tenant_id and old_user_id:
            tenant_context.set_context(old_tenant_id, old_user_id)
        else:
            tenant_context.clear_context()
