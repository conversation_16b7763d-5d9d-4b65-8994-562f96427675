"""
Advanced Enterprise Tenant Isolation Middleware for AIthentiq
Thread-safe, distributed, with comprehensive security and monitoring
"""

from fastapi import Request, HTTPException, status, Depends
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import event, text, func
from sqlalchemy.engine import Engine
from typing import Optional, Dict, Any, Tuple
import os
import logging
import threading
import time
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta, timezone
from contextlib import contextmanager, asynccontextmanager
import asyncio
from functools import wraps
import redis
import json
from dataclasses import dataclass, asdict
from enum import Enum

from database import get_db
from models_multitenant import Tenant, TenantUser, TenantApiKey

logger = logging.getLogger(__name__)

class SecurityLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class TenantContextData:
    """Thread-safe tenant context data structure"""
    tenant_id: str
    user_id: str
    session_id: str
    security_level: SecurityLevel
    permissions: Dict[str, Any]
    rate_limit_remaining: int
    expires_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    tenant_name: Optional[str] = None
    user_email: Optional[str] = None

class AdvancedTenantContext:
    """
    Thread-safe, distributed tenant context with Redis backing
    """
    def __init__(self):
        self._local = threading.local()
        self._redis_client = self._init_redis()
        self._session_timeout = 3600  # 1 hour

    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis client for distributed context"""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/1")
            client = redis.from_url(redis_url, decode_responses=True)
            client.ping()  # Test connection
            logger.info("Redis context store initialized")
            return client
        except Exception as e:
            logger.warning(f"Redis unavailable, using local context only: {e}")
            return None

    def _get_session_key(self, session_id: str) -> str:
        """Generate Redis key for session"""
        return f"aithentiq:session:{session_id}"

    def _get_rate_limit_key(self, tenant_id: str, user_id: str) -> str:
        """Generate Redis key for rate limiting"""
        return f"aithentiq:ratelimit:{tenant_id}:{user_id}"

    @property
    def context(self) -> Optional[TenantContextData]:
        """Get current thread's tenant context"""
        return getattr(self._local, 'context', None)

    async def set_context(
        self,
        tenant_id: str,
        user_id: str,
        security_level: SecurityLevel = SecurityLevel.MEDIUM,
        permissions: Dict[str, Any] = None,
        ip_address: str = None,
        user_agent: str = None,
        tenant_name: str = None,
        user_email: str = None
    ) -> str:
        """Set tenant context with distributed storage"""
        session_id = secrets.token_urlsafe(32)
        expires_at = datetime.now(timezone.utc) + timedelta(seconds=self._session_timeout)

        # Check rate limits
        rate_limit_remaining = await self._check_rate_limit(tenant_id, user_id)

        context_data = TenantContextData(
            tenant_id=tenant_id,
            user_id=user_id,
            session_id=session_id,
            security_level=security_level,
            permissions=permissions or {},
            rate_limit_remaining=rate_limit_remaining,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent,
            tenant_name=tenant_name,
            user_email=user_email
        )

        # Store in thread-local
        self._local.context = context_data

        # Store in Redis for distributed access
        if self._redis_client:
            try:
                session_key = self._get_session_key(session_id)
                context_json = json.dumps(asdict(context_data), default=str)
                self._redis_client.setex(session_key, self._session_timeout, context_json)
            except Exception as e:
                logger.error(f"Failed to store context in Redis: {e}")

        return session_id

    async def get_context_by_session(self, session_id: str) -> Optional[TenantContextData]:
        """Retrieve context by session ID from distributed storage"""
        if not self._redis_client:
            return None

        try:
            session_key = self._get_session_key(session_id)
            context_json = self._redis_client.get(session_key)

            if context_json:
                context_dict = json.loads(context_json)
                # Convert string back to datetime
                context_dict['expires_at'] = datetime.fromisoformat(context_dict['expires_at'])
                context_dict['security_level'] = SecurityLevel(context_dict['security_level'])
                return TenantContextData(**context_dict)

        except Exception as e:
            logger.error(f"Failed to retrieve context from Redis: {e}")

        return None

    async def _check_rate_limit(self, tenant_id: str, user_id: str) -> int:
        """Check and update rate limits"""
        if not self._redis_client:
            return 1000  # Default limit when Redis unavailable

        try:
            rate_key = self._get_rate_limit_key(tenant_id, user_id)
            current_count = self._redis_client.get(rate_key)

            if current_count is None:
                # First request in window
                self._redis_client.setex(rate_key, 3600, 1)  # 1 hour window
                return 999
            else:
                count = int(current_count)
                if count >= 1000:  # Rate limit exceeded
                    return 0
                else:
                    self._redis_client.incr(rate_key)
                    return 1000 - count - 1

        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return 1000  # Fail open

    def clear_context(self):
        """Clear current thread's context"""
        if hasattr(self._local, 'context'):
            context = self._local.context

            # Remove from Redis
            if self._redis_client and context:
                try:
                    session_key = self._get_session_key(context.session_id)
                    self._redis_client.delete(session_key)
                except Exception as e:
                    logger.error(f"Failed to clear context from Redis: {e}")

            delattr(self._local, 'context')

    def is_set(self) -> bool:
        """Check if context is set and valid"""
        context = self.context
        if not context:
            return False

        # Check expiration
        if context.expires_at < datetime.now(timezone.utc):
            self.clear_context()
            return False

        return True

    def require_permission(self, permission: str) -> bool:
        """Check if current context has required permission"""
        context = self.context
        if not context:
            return False

        return context.permissions.get(permission, False)

    def get_security_level(self) -> SecurityLevel:
        """Get current security level"""
        context = self.context
        return context.security_level if context else SecurityLevel.LOW

# Global advanced tenant context instance
tenant_context = AdvancedTenantContext()

class TenantIsolationMiddleware:
    """
    Middleware to enforce tenant isolation
    """
    
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)
            
            # Skip tenant isolation for certain paths
            if self._should_skip_isolation(request.url.path):
                await self.app(scope, receive, send)
                return

            try:
                # Extract tenant context from request
                await self._extract_tenant_context(request)
                
                # Process the request with tenant context
                await self.app(scope, receive, send)
                
            except HTTPException as e:
                # Handle authentication/authorization errors
                response = JSONResponse(
                    status_code=e.status_code,
                    content={"detail": e.detail}
                )
                await response(scope, receive, send)
            finally:
                # Always clear context after request
                tenant_context.clear_context()
        else:
            await self.app(scope, receive, send)

    def _should_skip_isolation(self, path: str) -> bool:
        """
        Determine if tenant isolation should be skipped for this path
        """
        skip_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/auth/login",
            "/auth/register",
            "/auth/oauth",
            "/tenants/register",  # Tenant registration endpoint
            "/static/",
            "/favicon.ico"
        ]
        
        return any(path.startswith(skip_path) for skip_path in skip_paths)

    async def _extract_tenant_context(self, request: Request):
        """
        Extract tenant and user context from the request
        """
        db = next(get_db())
        
        try:
            # Method 1: Extract from API key
            api_key = request.headers.get("X-API-Key")
            if api_key:
                await self._authenticate_via_api_key(api_key, db)
                return

            # Method 2: Extract from JWT token (NextAuth)
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                await self._authenticate_via_jwt(token, db)
                return

            # Method 3: Extract from session/cookie
            session_data = request.cookies.get("session")
            if session_data:
                await self._authenticate_via_session(session_data, db)
                return

            # Method 4: Extract from subdomain (e.g., acme.aithentiq.com)
            host = request.headers.get("host", "")
            if "." in host:
                subdomain = host.split(".")[0]
                tenant = db.query(Tenant).filter(Tenant.domain == subdomain).first()
                if tenant:
                    # For subdomain-based access, we still need user authentication
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="User authentication required"
                    )

            # No valid authentication found
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )

        finally:
            db.close()

    async def _authenticate_via_api_key(self, api_key: str, db: Session):
        """
        Authenticate using API key and set tenant context
        """
        # Handle temporary API keys (from frontend fallback)
        if api_key.startswith("temp-key-"):
            user_id = api_key.replace("temp-key-", "")
            await self._handle_temporary_key(user_id, db)
            return

        # Look up API key
        db_api_key = db.query(TenantApiKey).filter(
            TenantApiKey.key == api_key,
            TenantApiKey.is_active == True
        ).first()

        if not db_api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )

        # Check if API key is expired
        if db_api_key.expires_at and db_api_key.expires_at < func.now():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key expired"
            )

        # Get tenant and user
        tenant = db.query(Tenant).filter(Tenant.id == db_api_key.tenant_id).first()
        user = db.query(TenantUser).filter(TenantUser.id == db_api_key.user_id).first()

        if not tenant or not tenant.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Tenant not active"
            )

        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User not active"
            )

        # Set tenant context
        tenant_context.set_context(
            tenant_id=tenant.id,
            user_id=user.id,
            tenant=tenant,
            user=user
        )

        # Update API key usage
        db_api_key.usage_count += 1
        db_api_key.last_used = func.now()
        db.commit()

    async def _handle_temporary_key(self, user_id: str, db: Session):
        """
        Handle temporary API keys from frontend OAuth
        """
        # This is a simplified approach for development
        # In production, you'd want more robust user/tenant association
        
        # For now, create a default tenant if none exists
        default_tenant = db.query(Tenant).filter(Tenant.domain == "default").first()
        if not default_tenant:
            default_tenant = Tenant(
                domain="default",
                name="Default Tenant",
                display_name="Default Organization"
            )
            db.add(default_tenant)
            db.commit()
            db.refresh(default_tenant)

        # Check if user exists
        user = db.query(TenantUser).filter(
            TenantUser.id == user_id,
            TenantUser.tenant_id == default_tenant.id
        ).first()

        if not user:
            # Create user in default tenant
            user = TenantUser(
                id=user_id,
                tenant_id=default_tenant.id,
                email=f"{user_id}@oauth.local",
                name="OAuth User",
                oauth_provider="auto"
            )
            db.add(user)
            db.commit()
            db.refresh(user)

        # Set tenant context
        tenant_context.set_context(
            tenant_id=default_tenant.id,
            user_id=user.id,
            tenant=default_tenant,
            user=user
        )

    async def _authenticate_via_jwt(self, token: str, db: Session):
        """
        Authenticate using JWT token (NextAuth)
        """
        # TODO: Implement JWT token validation
        # This would decode the JWT, validate it, and extract user information
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="JWT authentication not yet implemented"
        )

    async def _authenticate_via_session(self, session_data: str, db: Session):
        """
        Authenticate using session cookie
        """
        # TODO: Implement session-based authentication
        # This would validate the session and extract user information
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Session authentication not yet implemented"
        )

# Database event listeners for automatic tenant filtering
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """
    Set SQLite pragmas for better performance and foreign key support
    """
    if 'sqlite' in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.close()

def get_current_tenant() -> Tenant:
    """
    Get the current tenant from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No tenant context available"
        )
    return tenant_context.tenant

def get_current_user() -> TenantUser:
    """
    Get the current user from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No user context available"
        )
    return tenant_context.user

def get_current_tenant_id() -> str:
    """
    Get the current tenant ID from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No tenant context available"
        )
    return tenant_context.tenant_id

def get_current_user_id() -> str:
    """
    Get the current user ID from context
    """
    if not tenant_context.is_set():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No user context available"
        )
    return tenant_context.user_id

@contextmanager
def tenant_scope(tenant_id: str, user_id: str):
    """
    Context manager for setting tenant scope in background tasks
    """
    old_tenant_id = tenant_context.tenant_id
    old_user_id = tenant_context.user_id
    
    try:
        tenant_context.set_context(tenant_id, user_id)
        yield
    finally:
        if old_tenant_id and old_user_id:
            tenant_context.set_context(old_tenant_id, old_user_id)
        else:
            tenant_context.clear_context()
