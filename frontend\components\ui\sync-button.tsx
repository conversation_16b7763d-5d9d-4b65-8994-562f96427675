"use client";

import React, { useState } from 'react';
import { RefreshCw, Sync, CheckCircle, XCircle } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { createApiInstance } from '@/lib/api';

interface SyncButtonProps {
  connectorId: string;
  connectorType?: string;
  repositoryIds?: number[];
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
  showText?: boolean;
  onSyncComplete?: (success: boolean, message: string) => void;
  className?: string;
  syncEndpoint?: string; // Custom sync endpoint
}

export default function SyncButton({
  connectorId,
  connectorType,
  repositoryIds,
  size = 'md',
  variant = 'primary',
  showText = true,
  onSyncComplete,
  className = '',
  syncEndpoint
}: SyncButtonProps) {
  const { data: session } = useSession();
  const [syncing, setSyncing] = useState(false);
  const [lastSyncResult, setLastSyncResult] = useState<'success' | 'error' | null>(null);

  const handleSync = async () => {
    if (!session || syncing) return;

    setSyncing(true);
    setLastSyncResult(null);

    try {
      const api = createApiInstance(session);

      // Determine sync endpoint and data based on connector type
      let endpoint: string;
      let syncData: any;

      if (syncEndpoint) {
        // Use custom endpoint if provided
        endpoint = syncEndpoint;
        syncData = {
          repository_ids: repositoryIds || [],
          force_full_sync: false
        };
      } else if (connectorType === 'github' || (!connectorType && repositoryIds)) {
        // GitHub-specific sync (legacy support)
        endpoint = `/connectors/github/connectors/${connectorId}/sync`;
        syncData = {
          repository_ids: repositoryIds || [],
          force_full_sync: false
        };
      } else {
        // Universal connector sync (default for all other types)
        endpoint = `/connectors/${connectorId}/sync`;
        syncData = {
          force_full_sync: false,
          sync_options: repositoryIds ? { repository_ids: repositoryIds } : {}
        };
      }

      const response = await api.post(endpoint, syncData);

      if (response.data.success) {
        setLastSyncResult('success');

        // Create appropriate success message based on connector type
        let message: string;
        if (response.data.repositories_scheduled) {
          message = `✅ Sync started for ${response.data.repositories_scheduled} repositories`;
        } else if (response.data.connector_type) {
          message = `✅ ${response.data.connector_type} sync started successfully`;
        } else {
          message = `✅ Sync started successfully`;
        }

        if (onSyncComplete) {
          onSyncComplete(true, message);
        }

        // Reset success indicator after 3 seconds
        setTimeout(() => setLastSyncResult(null), 3000);
      } else {
        throw new Error(response.data.error || 'Sync failed');
      }
    } catch (err) {
      setLastSyncResult('error');
      const errorMessage = err instanceof Error ? err.message : 'Sync failed';
      
      if (onSyncComplete) {
        onSyncComplete(false, `❌ ${errorMessage}`);
      }

      // Reset error indicator after 5 seconds
      setTimeout(() => setLastSyncResult(null), 5000);
    } finally {
      setSyncing(false);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'p-1.5 text-xs',
    md: 'p-2 text-sm',
    lg: 'p-3 text-base'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  // Variant classes
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 border-blue-600',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 border-gray-600',
    ghost: 'bg-transparent text-blue-600 hover:bg-blue-50 border-transparent'
  };

  // Get current icon based on state
  const getCurrentIcon = () => {
    if (syncing) {
      return <RefreshCw className={`${iconSizes[size]} animate-spin`} />;
    }
    
    if (lastSyncResult === 'success') {
      return <CheckCircle className={`${iconSizes[size]} text-green-500`} />;
    }
    
    if (lastSyncResult === 'error') {
      return <XCircle className={`${iconSizes[size]} text-red-500`} />;
    }
    
    return <Sync className={iconSizes[size]} />;
  };

  // Get button text based on state
  const getButtonText = () => {
    if (syncing) return 'Syncing...';
    if (lastSyncResult === 'success') return 'Synced!';
    if (lastSyncResult === 'error') return 'Failed';
    return repositoryIds && repositoryIds.length === 1 ? 'Sync Repo' : 'Sync';
  };

  // Get button title/tooltip
  const getButtonTitle = () => {
    if (syncing) return 'Sync in progress...';
    if (lastSyncResult === 'success') return 'Sync completed successfully';
    if (lastSyncResult === 'error') return 'Sync failed - click to retry';

    if (repositoryIds && repositoryIds.length > 0) {
      return `Sync ${repositoryIds.length} repository${repositoryIds.length > 1 ? 'ies' : ''}`;
    }

    // Universal connector sync tooltip
    const connectorName = connectorType ?
      connectorType.charAt(0).toUpperCase() + connectorType.slice(1) :
      'Data';

    return `Sync all ${connectorName.toLowerCase()} in this connector`;
  };

  const baseClasses = `
    inline-flex items-center justify-center
    border rounded-lg font-medium
    transition-all duration-200
    disabled:opacity-50 disabled:cursor-not-allowed
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
  `;

  const buttonClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${lastSyncResult ? '' : variantClasses[variant]}
    ${className}
  `;

  return (
    <button
      onClick={handleSync}
      disabled={syncing}
      className={buttonClasses}
      title={getButtonTitle()}
    >
      <span className="flex items-center space-x-2">
        {getCurrentIcon()}
        {showText && <span>{getButtonText()}</span>}
      </span>
    </button>
  );
}

// Quick sync button for use in lists/cards
export function QuickSyncButton({
  connectorId,
  repositoryIds,
  onSyncComplete,
  className = ''
}: Pick<SyncButtonProps, 'connectorId' | 'repositoryIds' | 'onSyncComplete' | 'className'>) {
  return (
    <SyncButton
      connectorId={connectorId}
      repositoryIds={repositoryIds}
      size="sm"
      variant="ghost"
      showText={false}
      onSyncComplete={onSyncComplete}
      className={className}
    />
  );
}

// Sync all button for connector-level operations
export function SyncAllButton({
  connectorId,
  onSyncComplete,
  className = ''
}: Pick<SyncButtonProps, 'connectorId' | 'onSyncComplete' | 'className'>) {
  return (
    <SyncButton
      connectorId={connectorId}
      size="md"
      variant="primary"
      showText={true}
      onSyncComplete={onSyncComplete}
      className={className}
    />
  );
}
