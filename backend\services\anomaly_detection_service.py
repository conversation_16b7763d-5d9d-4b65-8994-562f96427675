"""
Anomaly Detection Service
Detects unusual user behavior patterns using statistical and ML methods
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc
import json
import logging
from collections import defaultdict

# ML imports
try:
    from sklearn.ensemble import IsolationForest
    from sklearn.preprocessing import StandardScaler
    from scipy import stats
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    logging.warning("ML libraries not available. Anomaly detection will use statistical methods only.")

import models
from models_predictive_analytics import UserBehaviorLog, AnomalyDetection, PredictionFeedback


class AnomalyDetectionService:
    """Service for detecting unusual user behavior patterns"""
    
    def __init__(self):
        self.isolation_forest = None
        self.scaler = StandardScaler()
        self.model_version = "1.0"
        
    def extract_behavior_features(self, user_id: str, db: Session, days_back: int = 30) -> Dict[str, Any]:
        """Extract behavioral features for anomaly detection"""
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Get user behavior data
            queries = db.query(models.Query).filter(
                and_(
                    models.Query.user_id == user_id,
                    models.Query.created_at >= start_date
                )
            ).all()
            
            behavior_logs = db.query(UserBehaviorLog).filter(
                and_(
                    UserBehaviorLog.user_id == user_id,
                    UserBehaviorLog.created_at >= start_date
                )
            ).all()
            
            # Calculate daily patterns
            daily_stats = defaultdict(lambda: {
                'queries': 0,
                'sessions': set(),
                'features_used': set(),
                'total_duration': 0,
                'errors': 0,
                'unique_ips': set(),
                'response_times': []
            })
            
            # Process queries
            for query in queries:
                day = query.created_at.date()
                daily_stats[day]['queries'] += 1
                
                if query.response_time_ms:
                    daily_stats[day]['response_times'].append(query.response_time_ms)
                
                if query.answer and 'error' in query.answer.lower():
                    daily_stats[day]['errors'] += 1
            
            # Process behavior logs
            for log in behavior_logs:
                day = log.created_at.date()
                
                if log.session_id:
                    daily_stats[day]['sessions'].add(log.session_id)
                
                if log.feature_used:
                    daily_stats[day]['features_used'].add(log.feature_used)
                
                if log.feature_duration:
                    daily_stats[day]['total_duration'] += log.feature_duration
                
                if log.ip_address:
                    daily_stats[day]['unique_ips'].add(log.ip_address)
            
            # Convert to feature vectors
            features = {
                'daily_query_counts': [],
                'daily_session_counts': [],
                'daily_feature_counts': [],
                'daily_durations': [],
                'daily_error_rates': [],
                'daily_ip_counts': [],
                'daily_avg_response_times': [],
                'hourly_patterns': [0] * 24,  # Activity by hour
                'weekly_patterns': [0] * 7,   # Activity by day of week
            }
            
            # Calculate hourly and weekly patterns
            for query in queries:
                hour = query.created_at.hour
                weekday = query.created_at.weekday()
                features['hourly_patterns'][hour] += 1
                features['weekly_patterns'][weekday] += 1
            
            for log in behavior_logs:
                hour = log.created_at.hour
                weekday = log.created_at.weekday()
                features['hourly_patterns'][hour] += 1
                features['weekly_patterns'][weekday] += 1
            
            # Process daily statistics
            for day_stats in daily_stats.values():
                features['daily_query_counts'].append(day_stats['queries'])
                features['daily_session_counts'].append(len(day_stats['sessions']))
                features['daily_feature_counts'].append(len(day_stats['features_used']))
                features['daily_durations'].append(day_stats['total_duration'])
                features['daily_ip_counts'].append(len(day_stats['unique_ips']))
                
                # Error rate
                error_rate = day_stats['errors'] / max(day_stats['queries'], 1)
                features['daily_error_rates'].append(error_rate)
                
                # Average response time
                avg_response_time = np.mean(day_stats['response_times']) if day_stats['response_times'] else 0
                features['daily_avg_response_times'].append(avg_response_time)
            
            return features
            
        except Exception as e:
            logging.error(f"Error extracting behavior features: {e}")
            return {}
    
    def detect_statistical_anomalies(self, features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect anomalies using statistical methods (Z-score, IQR)"""
        anomalies = []
        
        try:
            # Define metrics to check for anomalies
            metrics = {
                'daily_query_counts': 'Query volume',
                'daily_session_counts': 'Session count',
                'daily_durations': 'Session duration',
                'daily_error_rates': 'Error rate',
                'daily_avg_response_times': 'Response time'
            }
            
            for metric_key, metric_name in metrics.items():
                if metric_key in features and features[metric_key]:
                    values = np.array(features[metric_key])
                    
                    if len(values) < 3:  # Need at least 3 data points
                        continue
                    
                    # Z-score method
                    z_scores = np.abs(stats.zscore(values))
                    z_threshold = 2.5
                    
                    # IQR method
                    q1, q3 = np.percentile(values, [25, 75])
                    iqr = q3 - q1
                    iqr_lower = q1 - 1.5 * iqr
                    iqr_upper = q3 + 1.5 * iqr
                    
                    # Find anomalies
                    z_anomalies = np.where(z_scores > z_threshold)[0]
                    iqr_anomalies = np.where((values < iqr_lower) | (values > iqr_upper))[0]
                    
                    # Combine anomalies
                    all_anomalies = set(z_anomalies) | set(iqr_anomalies)
                    
                    for idx in all_anomalies:
                        anomaly_value = values[idx]
                        baseline_mean = np.mean(values)
                        baseline_std = np.std(values)
                        
                        # Calculate severity
                        z_score = abs((anomaly_value - baseline_mean) / max(baseline_std, 0.001))
                        
                        if z_score > 3:
                            severity = "critical"
                        elif z_score > 2.5:
                            severity = "high"
                        elif z_score > 2:
                            severity = "medium"
                        else:
                            severity = "low"
                        
                        anomalies.append({
                            'type': f'{metric_name.lower().replace(" ", "_")}_anomaly',
                            'metric': metric_name,
                            'anomaly_value': float(anomaly_value),
                            'baseline_mean': float(baseline_mean),
                            'baseline_std': float(baseline_std),
                            'z_score': float(z_score),
                            'severity': severity,
                            'day_index': int(idx),
                            'description': f'Unusual {metric_name.lower()}: {anomaly_value:.2f} (baseline: {baseline_mean:.2f}±{baseline_std:.2f})'
                        })
            
            # Check temporal patterns
            temporal_anomalies = self.detect_temporal_anomalies(features)
            anomalies.extend(temporal_anomalies)
            
            return anomalies
            
        except Exception as e:
            logging.error(f"Error detecting statistical anomalies: {e}")
            return []
    
    def detect_temporal_anomalies(self, features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect temporal pattern anomalies"""
        anomalies = []
        
        try:
            # Check hourly patterns
            if 'hourly_patterns' in features:
                hourly_activity = np.array(features['hourly_patterns'])
                
                # Detect unusual hours (activity outside normal business hours)
                night_hours = list(range(0, 6)) + list(range(22, 24))  # 10 PM - 6 AM
                night_activity = sum(hourly_activity[hour] for hour in night_hours)
                total_activity = sum(hourly_activity)
                
                if total_activity > 0:
                    night_ratio = night_activity / total_activity
                    
                    if night_ratio > 0.5:  # More than 50% activity at night
                        anomalies.append({
                            'type': 'unusual_hours',
                            'metric': 'Activity timing',
                            'anomaly_value': float(night_ratio),
                            'severity': 'medium',
                            'description': f'High activity during unusual hours: {night_ratio:.1%} of activity between 10 PM - 6 AM'
                        })
            
            # Check weekly patterns
            if 'weekly_patterns' in features:
                weekly_activity = np.array(features['weekly_patterns'])
                
                # Detect weekend activity anomalies
                weekend_activity = weekly_activity[5] + weekly_activity[6]  # Saturday + Sunday
                weekday_activity = sum(weekly_activity[:5])  # Monday - Friday
                
                if weekday_activity > 0:
                    weekend_ratio = weekend_activity / (weekday_activity + weekend_activity)
                    
                    if weekend_ratio > 0.4:  # More than 40% activity on weekends
                        anomalies.append({
                            'type': 'unusual_weekend_activity',
                            'metric': 'Weekly pattern',
                            'anomaly_value': float(weekend_ratio),
                            'severity': 'low',
                            'description': f'High weekend activity: {weekend_ratio:.1%} of total activity'
                        })
            
            return anomalies
            
        except Exception as e:
            logging.error(f"Error detecting temporal anomalies: {e}")
            return []
    
    def detect_user_anomalies(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Detect anomalies for a specific user"""
        try:
            # Extract behavioral features
            features = self.extract_behavior_features(user_id, db)
            if not features:
                return {
                    "error": "Could not extract behavioral features",
                    "anomalies": []
                }
            
            # Detect anomalies using statistical methods
            anomalies = self.detect_statistical_anomalies(features)
            
            # Calculate overall anomaly score
            if anomalies:
                severity_weights = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
                total_score = sum(severity_weights.get(a['severity'], 1) for a in anomalies)
                max_possible_score = len(anomalies) * 4
                anomaly_score = total_score / max_possible_score if max_possible_score > 0 else 0
            else:
                anomaly_score = 0
            
            # Determine overall severity
            if anomaly_score >= 0.75:
                overall_severity = "critical"
            elif anomaly_score >= 0.5:
                overall_severity = "high"
            elif anomaly_score >= 0.25:
                overall_severity = "medium"
            else:
                overall_severity = "low"
            
            # Store significant anomalies in database
            if anomalies and anomaly_score > 0.3:  # Only store significant anomalies
                for anomaly in anomalies:
                    if anomaly['severity'] in ['high', 'critical']:
                        detection = AnomalyDetection(
                            user_id=user_id,
                            anomaly_type=anomaly['type'],
                            anomaly_score=anomaly_score,
                            severity_level=anomaly['severity'],
                            description=anomaly['description'],
                            affected_features=[anomaly['metric']],
                            anomaly_patterns=anomaly,
                            baseline_behavior=features,
                            anomalous_behavior=anomaly,
                            detection_method="statistical",
                            model_version=self.model_version,
                            confidence_score=0.8,
                            status="detected"
                        )
                        
                        db.add(detection)
                
                db.commit()
            
            return {
                "user_id": user_id,
                "anomaly_score": anomaly_score,
                "overall_severity": overall_severity,
                "total_anomalies": len(anomalies),
                "anomalies": anomalies,
                "features_analyzed": list(features.keys()),
                "detection_method": "statistical",
                "model_version": self.model_version,
                "detected_at": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error detecting user anomalies: {e}")
            return {
                "error": str(e),
                "anomalies": []
            }
    
    def get_anomaly_trends(self, user_id: str, db: Session, days_back: int = 90) -> Dict[str, Any]:
        """Get anomaly detection trends for a user"""
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Get historical anomaly detections
            detections = db.query(AnomalyDetection).filter(
                and_(
                    AnomalyDetection.user_id == user_id,
                    AnomalyDetection.detected_at >= start_date
                )
            ).order_by(AnomalyDetection.detected_at).all()
            
            if not detections:
                return {"message": "No anomaly detection history available"}
            
            # Analyze trends
            anomaly_types = {}
            severity_counts = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
            monthly_counts = defaultdict(int)
            
            for detection in detections:
                # Count by type
                anomaly_types[detection.anomaly_type] = anomaly_types.get(detection.anomaly_type, 0) + 1
                
                # Count by severity
                severity_counts[detection.severity_level] += 1
                
                # Count by month
                month_key = detection.detected_at.strftime('%Y-%m')
                monthly_counts[month_key] += 1
            
            return {
                "user_id": user_id,
                "analysis_period_days": days_back,
                "total_anomalies": len(detections),
                "anomaly_types": dict(anomaly_types),
                "severity_distribution": severity_counts,
                "monthly_trends": dict(monthly_counts),
                "recent_anomalies": [
                    {
                        "type": d.anomaly_type,
                        "severity": d.severity_level,
                        "description": d.description,
                        "detected_at": d.detected_at.isoformat(),
                        "status": d.status
                    }
                    for d in detections[-5:]  # Last 5 anomalies
                ]
            }
            
        except Exception as e:
            logging.error(f"Error getting anomaly trends: {e}")
            return {"error": str(e)}
