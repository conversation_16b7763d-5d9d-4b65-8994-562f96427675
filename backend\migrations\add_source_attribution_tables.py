"""
Professional Database Migration: Add Source Attribution Tables
Creates comprehensive tables for document chunks and source attribution tracking
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from database import DATABASE_URL, Base
from models import DocumentChunk, SourceAttribution, DocumentViewer
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_migration():
    """
    Run the source attribution migration
    """
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)
        
        logger.info("🚀 Starting source attribution migration...")
        
        # Create all new tables
        Base.metadata.create_all(bind=engine, tables=[
            DocumentChunk.__table__,
            SourceAttribution.__table__,
            DocumentViewer.__table__
        ])
        
        logger.info("✅ Created new tables:")
        logger.info("   - document_chunks")
        logger.info("   - source_attributions") 
        logger.info("   - document_viewers")
        
        # Add indexes for performance
        with engine.connect() as conn:
            # Indexes for document_chunks
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chunks_dataset_type 
                ON document_chunks(dataset_id, chunk_type);
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chunks_source_lines 
                ON document_chunks(source_document, line_start, line_end);
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chunks_text_hash 
                ON document_chunks(text_hash);
            """))
            
            # Indexes for source_attributions
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_attributions_query_rank 
                ON source_attributions(query_id, rank_position);
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_attributions_relevance 
                ON source_attributions(relevance_score DESC);
            """))
            
            # Indexes for document_viewers
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_viewers_user_dataset 
                ON document_viewers(user_id, dataset_id);
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_viewers_session 
                ON document_viewers(session_id);
            """))
            
            conn.commit()
            
        logger.info("✅ Created performance indexes")
        
        # Add triggers for automatic updates (SQLite compatible)
        with engine.connect() as conn:
            # Update chunk text_hash automatically
            conn.execute(text("""
                CREATE TRIGGER IF NOT EXISTS update_chunk_hash
                AFTER INSERT ON document_chunks
                BEGIN
                    UPDATE document_chunks 
                    SET text_hash = substr(
                        hex(randomblob(16)), 1, 32
                    ) || substr(
                        hex(NEW.text), 1, 8
                    )
                    WHERE id = NEW.id AND text_hash IS NULL;
                END;
            """))
            
            # Update document_viewers last_accessed
            conn.execute(text("""
                CREATE TRIGGER IF NOT EXISTS update_viewer_access
                AFTER UPDATE ON document_viewers
                BEGIN
                    UPDATE document_viewers 
                    SET last_accessed = CURRENT_TIMESTAMP
                    WHERE id = NEW.id;
                END;
            """))
            
            conn.commit()
            
        logger.info("✅ Created database triggers")
        
        logger.info("🎉 Source attribution migration completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {str(e)}")
        return False

def rollback_migration():
    """
    Rollback the migration (for development/testing)
    """
    try:
        engine = create_engine(DATABASE_URL)
        
        logger.info("🔄 Rolling back source attribution migration...")
        
        with engine.connect() as conn:
            # Drop triggers
            conn.execute(text("DROP TRIGGER IF EXISTS update_chunk_hash;"))
            conn.execute(text("DROP TRIGGER IF EXISTS update_viewer_access;"))
            
            # Drop tables in reverse order (due to foreign keys)
            conn.execute(text("DROP TABLE IF EXISTS document_viewers;"))
            conn.execute(text("DROP TABLE IF EXISTS source_attributions;"))
            conn.execute(text("DROP TABLE IF EXISTS document_chunks;"))
            
            conn.commit()
            
        logger.info("✅ Migration rolled back successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Rollback failed: {str(e)}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        run_migration()
