from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
import pandas as pd
import json
from io import StringIO

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database import get_db
import models
import schemas
from services.llm_service import llm_service
from services import chart_service
from services.document_service import document_processor
from services.query_cache_service import query_cache_service

from middleware.api_auth import get_current_user_from_api_key

router = APIRouter(
    prefix="/api/v1",
    tags=["api"]
)




@router.get("/datasets", response_model=List[schemas.DatasetResponse])
async def get_datasets(
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get all datasets for the authenticated user
    """
    datasets = db.query(models.Dataset).filter(models.Dataset.user_id == user.id).order_by(models.Dataset.created_at.desc()).all()

    return [
        {
            "id": dataset.id,
            "name": dataset.name,
            "columns": json.loads(dataset.columns),
            "row_count": dataset.row_count,
            "created_at": dataset.created_at,
            "file_type": dataset.file_type,
            "content_type": dataset.content_type or 'tabular',
            "processing_status": dataset.processing_status or 'completed',
            "word_count": dataset.word_count or 0,
            "character_count": dataset.character_count or 0
        }
        for dataset in datasets
    ]

@router.get("/datasets/{dataset_id}", response_model=schemas.DatasetResponse)
async def get_dataset(
    dataset_id: int,
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get a specific dataset
    """
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == dataset_id,
        models.Dataset.user_id == user.id
    ).first()

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    return {
        "id": dataset.id,
        "name": dataset.name,
        "columns": json.loads(dataset.columns),
        "row_count": dataset.row_count,
        "created_at": dataset.created_at,
        "file_type": dataset.file_type,
        "content_type": dataset.content_type or 'tabular',
        "processing_status": dataset.processing_status or 'completed',
        "word_count": dataset.word_count or 0,
        "character_count": dataset.character_count or 0
    }

@router.get("/datasets/{dataset_id}/content")
async def get_dataset_content(
    dataset_id: int,
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """Get the content of a specific dataset"""
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == dataset_id,
        models.Dataset.user_id == user.id  # Ensure user owns the dataset
    ).first()

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found or access denied"
        )

    try:
        # Parse the stored data
        parsed_data = json.loads(dataset.parsed_data)

        if dataset.content_type == 'document':
            # For documents, return the first (and usually only) entry
            if parsed_data and len(parsed_data) > 0:
                result = {
                    "text": parsed_data[0].get("text", ""),
                    "metadata": parsed_data[0].get("metadata", {}),
                    "filename": parsed_data[0].get("filename", dataset.name)
                }
                # Include original_image_data if available (for debugging)
                if parsed_data[0].get("original_image_data"):
                    result["has_original_image"] = True
                else:
                    result["has_original_image"] = False
                return result
            else:
                return {
                    "text": "No content available",
                    "metadata": {},
                    "filename": dataset.name
                }
        else:
            # For tabular data (including Excel), return a preview with actual data
            if parsed_data and len(parsed_data) > 0:
                # Show first 10 rows as preview
                preview_data = parsed_data[:10] if len(parsed_data) > 10 else parsed_data

                return {
                    "text": f"Tabular data with {dataset.row_count} rows and {len(json.loads(dataset.columns))} columns",
                    "metadata": {
                        "columns": json.loads(dataset.columns),
                        "row_count": dataset.row_count,
                        "file_type": dataset.file_type,
                        "preview_data": preview_data,
                        "total_rows": len(parsed_data)
                    },
                    "filename": dataset.name
                }
            else:
                return {
                    "text": "No data available",
                    "metadata": {
                        "columns": json.loads(dataset.columns) if dataset.columns else [],
                        "row_count": dataset.row_count or 0,
                        "file_type": dataset.file_type
                    },
                    "filename": dataset.name
                }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve dataset content: {str(e)}"
        )

@router.post("/datasets", response_model=schemas.DatasetResponse)
async def create_dataset(
    file: UploadFile = File(...),
    name: str = Form(...),
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Upload a new dataset
    """
    # Validate file type
    file_extension = file.filename.split('.')[-1].lower()
    supported_formats = ['csv', 'xlsx', 'xls', 'pdf', 'docx', 'txt', 'md', 'jpg', 'jpeg', 'png']
    if file_extension not in supported_formats:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Supported formats: {', '.join(supported_formats)}"
        )

    # Read file content
    contents = await file.read()

    try:
        # Determine content type and processing approach
        if file_extension in ['csv', 'xlsx', 'xls']:
            # Process tabular data
            if file_extension == 'csv':
                df = pd.read_csv(pd.io.common.BytesIO(contents))
            else:  # Excel files
                try:
                    # Try with openpyxl engine first (for .xlsx)
                    if file_extension == 'xlsx':
                        df = pd.read_excel(pd.io.common.BytesIO(contents), engine='openpyxl')
                    else:  # .xls files
                        df = pd.read_excel(pd.io.common.BytesIO(contents), engine='xlrd')
                except ImportError as ie:
                    # Missing engine dependency
                    if 'openpyxl' in str(ie):
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Excel support not available. Missing openpyxl dependency for .xlsx files."
                        )
                    elif 'xlrd' in str(ie):
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="Excel support not available. Missing xlrd dependency for .xls files."
                        )
                    else:
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail=f"Excel processing error: {str(ie)}"
                        )
                except Exception as excel_error:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to read Excel file: {str(excel_error)}. Please ensure the file is a valid Excel file."
                    )

            # Basic validation
            if df.empty:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="The file is empty"
                )

            # Save tabular dataset
            dataset = models.Dataset(
                name=name,
                user_id=user.id,
                columns=json.dumps(df.columns.tolist()),
                row_count=len(df),
                parsed_data=df.to_json(orient="records"),
                file_type=file_extension,
                content_type='tabular',
                processing_status='completed'
            )

        else:
            # Process document files
            doc_result = document_processor.process_document(contents, file.filename, file_extension)

            if 'error' in doc_result:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Document processing failed: {doc_result['error']}"
                )

            # Save document dataset
            dataset = models.Dataset(
                name=name,
                user_id=user.id,
                columns=json.dumps(['text', 'metadata']),  # Standard document columns
                row_count=1,  # Documents are treated as single entities
                parsed_data=json.dumps([{
                    'text': doc_result.get('text', ''),
                    'metadata': doc_result.get('metadata', {}),
                    'filename': file.filename
                }]),
                file_type=file_extension,
                content_type='document',
                document_metadata=json.dumps(doc_result.get('metadata', {})),
                embeddings_data=json.dumps(doc_result.get('embeddings', [])),
                processing_status=doc_result.get('processing_status', 'completed'),
                word_count=doc_result.get('word_count', 0),
                character_count=doc_result.get('character_count', 0)
            )

        db.add(dataset)
        db.commit()
        db.refresh(dataset)

        # Create professional chunks for document datasets
        if dataset.content_type == 'document' and 'chunks' in doc_result:
            try:
                chunk_result = document_processor.create_professional_chunks(
                    dataset_id=dataset.id,
                    chunks_data=doc_result['chunks'],
                    db_session=db
                )

                if chunk_result.get('success'):
                    print(f"✅ Created {chunk_result.get('chunks_created', 0)} professional chunks for dataset {dataset.id}")
                else:
                    print(f"⚠️ Failed to create professional chunks: {chunk_result.get('error', 'Unknown error')}")

            except Exception as chunk_error:
                print(f"⚠️ Error creating professional chunks: {chunk_error}")
                # Don't fail the entire upload if chunking fails

        return {
            "id": dataset.id,
            "name": dataset.name,
            "columns": json.loads(dataset.columns),
            "row_count": dataset.row_count,
            "created_at": dataset.created_at,
            "file_type": dataset.file_type,  # Don't default to 'csv' - show actual file type
            "content_type": dataset.content_type or 'tabular',
            "processing_status": dataset.processing_status or 'completed',
            "word_count": dataset.word_count or 0,
            "character_count": dataset.character_count or 0
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Log the full error for debugging
        print(f"❌ API v1 file processing error: {str(e)}")
        print(f"📁 File: {file.filename}, Extension: {file_extension}, Size: {len(contents)} bytes")

        # Provide user-friendly error messages
        if "openpyxl" in str(e).lower():
            detail = "Excel (.xlsx) files are not supported. Please convert to CSV or contact support."
        elif "xlrd" in str(e).lower():
            detail = "Legacy Excel (.xls) files are not supported. Please convert to CSV or .xlsx format."
        elif "empty" in str(e).lower():
            detail = "The uploaded file appears to be empty or corrupted."
        elif "encoding" in str(e).lower():
            detail = "File encoding issue. Please ensure the file is saved in UTF-8 format."
        elif file_extension in ['csv']:
            detail = f"CSV processing failed: {str(e)}. Please check the file format and try again."
        elif file_extension in ['xlsx', 'xls']:
            detail = f"Excel processing failed: {str(e)}. Please ensure the file is a valid Excel file."
        else:
            detail = f"File processing failed: {str(e)}"

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )

@router.delete("/datasets/{dataset_id}")
async def delete_dataset(
    dataset_id: int,
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Delete a dataset
    """
    try:
        dataset = db.query(models.Dataset).filter(
            models.Dataset.id == dataset_id,
            models.Dataset.user_id == user.id
        ).first()

        if not dataset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Dataset not found"
            )

        # Delete all related records first (in correct order)
        # 1. Delete saved queries that reference queries
        db.execute(text("""
            DELETE FROM saved_queries
            WHERE query_id IN (
                SELECT id FROM queries WHERE dataset_id = :dataset_id
            )
        """), {"dataset_id": dataset_id})

        # 2. Delete queries
        db.query(models.Query).filter(models.Query.dataset_id == dataset_id).delete()

        # 3. Delete query cache entries
        db.query(models.QueryCache).filter(models.QueryCache.dataset_id == dataset_id).delete()

        # 4. Delete conversations
        db.query(models.Conversation).filter(models.Conversation.dataset_id == dataset_id).delete()

        # 5. Finally delete the dataset
        db.delete(dataset)
        db.commit()

        return {"message": "Dataset deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting dataset: {str(e)}"
        )

@router.post("/ask")
async def ask_question(
    query: schemas.QueryRequest,
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Ask a natural language question about a dataset
    """
    # Validate user ID
    if query.user_id != user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User ID in request does not match authenticated user"
        )

    # Get dataset from database
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == query.dataset_id,
        models.Dataset.user_id == user.id
    ).first()

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    try:
        # Start timing
        import time
        start_time = time.time()

        # 🚀 SMART ROUTING: ML-Enhanced vs Traditional RAG
        from services.rag_ml_integration import rag_ml_integration

        try:
            # Use smart integration service
            result = await rag_ml_integration.process_query_smart(
                query=query.question,
                dataset_id=dataset.id,
                user_id=user.id,
                db_session=db,
                include_trust_score=query.include_trust_score
            )

            # Extract processing method for logging
            processing_method = result.get("processing_method", "Unknown")
            print(f"🎯 Processed via: {processing_method}")

        except Exception as smart_error:
            print(f"⚠️ Smart routing failed, using traditional fallback: {smart_error}")

            # FALLBACK: Original RAG logic (ZERO changes to existing functionality)
            if dataset.content_type == 'document':
                # Use document-specific processing with vector search
                try:
                    result = llm_service.process_document_query(
                        question=query.question,
                        dataset_id=dataset.id,
                        db_session=db,
                        include_trust_score=query.include_trust_score,
                        trust_score_method=query.trust_score_method
                    )
                except Exception as doc_error:
                    print(f"⚠️ Document processing failed, falling back to standard processing: {doc_error}")
                    # Fallback to standard processing if document processing fails
                    df = pd.read_json(StringIO(dataset.parsed_data))
                    result = llm_service.process_query(
                        question=query.question,
                        df=df,
                        include_trust_score=query.include_trust_score,
                        dataset_id=dataset.id,
                        trust_score_method=query.trust_score_method
                    )
            else:
                # Parse dataset for tabular data
                df = pd.read_json(StringIO(dataset.parsed_data))

                # 🚀 USE PHASE 1 PRODUCTION QUERY ENGINE
                print(f"🎯 API v1 - Processing with Phase 1 Production Engine: {query.question}")

                try:
                    from services.production_query_engine import production_engine

                    # Use the bulletproof Phase 1 engine
                    production_result = await production_engine.process_query(df, query.question)

                    # Convert to expected format
                    result = {
                        "answer": production_result.answer,
                        "chart_type": None,  # Phase 1 focuses on text answers
                        "chart_data": None,
                        "processing_time": 0,  # Will be calculated below
                        "confidence": production_result.confidence,
                        "operation": production_result.operation,
                        "target_column": production_result.target_column,
                        "filter_column": production_result.filter_column,
                        "filter_value": production_result.filter_value,
                        "rows_matched": production_result.rows_matched,
                        "total_rows": production_result.total_rows
                    }

                    print(f"✅ API v1 Phase 1 Result: {production_result.answer}")
                    print(f"🎯 Operation: {production_result.operation}, Confidence: {production_result.confidence}")

                    # Add trust score if requested
                    if query.include_trust_score:
                        try:
                            trust_score = llm_service.calculate_simple_trust_score(
                                question=query.question,
                                answer=result.get("answer", ""),
                                df=df
                            )
                            result["trust_score"] = trust_score
                        except Exception as trust_error:
                            print(f"Trust score calculation failed: {trust_error}")
                            result["trust_score"] = {
                                "overall_score": production_result.confidence,
                                "explanation": f"Based on Phase 1 engine confidence: {production_result.confidence}"
                            }

                except Exception as phase1_error:
                    print(f"⚠️ API v1 Phase 1 engine failed, falling back to LLM: {phase1_error}")

                    # Fallback to LLM processing
                    if query.include_cot:
                        result = llm_service.process_query_with_cot(
                            question=query.question,
                            df=df,
                            include_trust_score=query.include_trust_score,
                            dataset_id=dataset.id,
                            trust_score_method=query.trust_score_method
                        )
                    else:
                        result = llm_service.process_query(
                            question=query.question,
                            df=df,
                            include_trust_score=query.include_trust_score,
                            dataset_id=dataset.id,
                            trust_score_method=query.trust_score_method
                        )

        # Generate chart if needed (only for traditional RAG results)
        chart_data = result.get("chart_data")  # ML integration may already provide this
        chart_type = result.get("chart_type")

        # Only generate chart if not already provided and we have tabular data
        if not chart_data and chart_type and chart_type.lower() != "null":
            try:
                # Need to load dataframe for chart generation
                if dataset.content_type != 'document':
                    df = pd.read_json(StringIO(dataset.parsed_data))

                    # Handle list values in chart_data
                    chart_data_dict = result.get("chart_data", {})

                    # Convert any list values to strings for chart generation
                    if isinstance(chart_data_dict, dict):
                        for key, value in chart_data_dict.items():
                            if isinstance(value, list):
                                # If the list contains column names, we need to check if they exist
                                if all(isinstance(item, str) for item in value):
                                    missing_cols = [col for col in value if col not in df.columns]
                                    if missing_cols:
                                        print(f"Warning: Columns {missing_cols} not found in dataset")
                                        # Use only columns that exist in the dataset
                                        chart_data_dict[key] = [col for col in value if col in df.columns]
                                        if not chart_data_dict[key]:  # If no valid columns remain
                                            if key == "x":
                                                chart_data_dict[key] = df.columns[0]  # Use first column as fallback
                                            elif key == "y":
                                                # Use first numeric column as fallback
                                                numeric_cols = df.select_dtypes(include=['number']).columns
                                                chart_data_dict[key] = numeric_cols[0] if len(numeric_cols) > 0 else df.columns[0]

                    chart_data = chart_service.generate_chart(
                        df,
                        chart_type,
                        chart_data_dict
                    )
            except Exception as chart_error:
                print(f"Error generating chart: {str(chart_error)}")
                # Don't fail the whole request if chart generation fails
                chart_data = None
                # Set chart_type to None so frontend doesn't try to render it
                result["chart_type"] = None

        # Calculate processing time
        end_time = time.time()
        processing_time = int((end_time - start_time) * 1000)  # Convert to milliseconds

        # Save query to database with professional monitoring fields + ML routing info
        db_query = models.Query(
            user_id=query.user_id,
            dataset_id=query.dataset_id,
            question=query.question,
            answer=result.get("answer"),
            chart_type=result.get("chart_type"),
            chart_data=json.dumps(chart_data) if chart_data else None,
            trust_score=json.dumps(result.get("trust_score")) if result.get("trust_score") else None,
            reasoning_steps=json.dumps(result.get("reasoning_steps")) if result.get("reasoning_steps") else None,
            formula_results=json.dumps(result.get("formula_results")) if result.get("formula_results") else None,
            processing_time=result.get("processing_time_ms") or processing_time,
            # Professional monitoring fields
            response_time_ms=float(result.get("processing_time_ms") or processing_time) if processing_time else None,
            token_count=result.get("token_count"),
            hallucination_risk=result.get("hallucination_risk"),
            completeness_score=result.get("completeness_score"),
            source_count=len(result.get("sources", [])) if result.get("sources") else None
        )

        db.add(db_query)
        db.commit()
        db.refresh(db_query)

        # Prepare response with all professional data + ML routing info
        response_data = {
            "id": db_query.id,
            "answer": db_query.answer,
            "chart_type": db_query.chart_type,
            "chart_data": json.loads(db_query.chart_data) if db_query.chart_data else None,
            "created_at": db_query.created_at,
            "processing_time": db_query.processing_time,
            # ML routing metadata
            "processing_method": result.get("processing_method"),
            "routing_path": result.get("routing_path"),
            "confidence_score": result.get("confidence_score")
        }

        # Add trust score if available
        if db_query.trust_score:
            trust_score_data = json.loads(db_query.trust_score)
            # Ensure trust_score has the required structure
            if isinstance(trust_score_data, dict) and "overall_score" in trust_score_data:
                response_data["trust_score"] = trust_score_data
            else:
                # Skip invalid trust score data
                response_data["trust_score"] = None

        # Add reasoning steps if available
        if db_query.reasoning_steps:
            response_data["reasoning_steps"] = json.loads(db_query.reasoning_steps)

        # Add formula results if available
        if db_query.formula_results:
            response_data["formula_results"] = json.loads(db_query.formula_results)

        # Add professional quality metrics from LLM result
        if "sources" in result:
            response_data["sources"] = result["sources"]

        if "hallucination_risk" in result:
            response_data["hallucination_risk"] = result["hallucination_risk"]

        if "completeness_score" in result:
            response_data["completeness_score"] = result["completeness_score"]

        if "confidence_level" in result:
            response_data["confidence_level"] = result["confidence_level"]

        if "uncertainty_indicators" in result:
            response_data["uncertainty_indicators"] = result["uncertainty_indicators"]

        if "unsupported_claims" in result:
            response_data["unsupported_claims"] = result["unsupported_claims"]

        if "token_count" in result:
            response_data["token_count"] = result["token_count"]

        return response_data

    except Exception as e:
        # Rollback any database changes
        db.rollback()
        print(f"Error in ask endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing query: {str(e)}"
        )



@router.get("/queries", response_model=List[schemas.QueryResponse])
async def get_queries(
    dataset_id: Optional[int] = None,
    limit: int = 10,
    offset: int = 0,
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get queries for the authenticated user
    """
    query = db.query(models.Query).filter(models.Query.user_id == user.id)

    if dataset_id:
        query = query.filter(models.Query.dataset_id == dataset_id)

    queries = query.order_by(models.Query.created_at.desc()).offset(offset).limit(limit).all()

    return [
        {
            "id": q.id,
            "answer": q.answer,
            "chart_type": q.chart_type,
            "chart_data": json.loads(q.chart_data) if q.chart_data else None,
            "created_at": q.created_at,
            "trust_score": json.loads(q.trust_score) if q.trust_score else None,
            "reasoning_steps": json.loads(q.reasoning_steps) if q.reasoning_steps else None,
            "formula_results": json.loads(q.formula_results) if q.formula_results else None
        }
        for q in queries
    ]

@router.get("/cache/stats")
async def get_cache_stats(
    user: models.User = Depends(get_current_user_from_api_key)
):
    """
    Get cache statistics
    """
    return query_cache_service.get_cache_stats()

@router.delete("/cache/clear")
async def clear_cache(
    dataset_id: Optional[int] = None,
    user: models.User = Depends(get_current_user_from_api_key)
):
    """
    Clear cache entries (optionally for specific dataset)
    """
    success = query_cache_service.clear_cache(dataset_id)
    if success:
        return {"message": f"Cache cleared successfully{'for dataset ' + str(dataset_id) if dataset_id else ''}"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear cache"
        )
