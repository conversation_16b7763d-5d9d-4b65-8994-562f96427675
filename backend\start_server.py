#!/usr/bin/env python3

import sys
import os

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("🚀 Starting Trust Score Comparison Server...")
print("📊 Available at: http://localhost:8001")
print("📋 Methods endpoint: http://localhost:8001/api/v1/trust-comparison/methods")
print("🎮 Demo endpoint: http://localhost:8001/api/v1/trust-comparison/demo")
print("📊 Sample data endpoint: http://localhost:8001/api/v1/trust-comparison/sample-data")

# Import and run the server
try:
    import uvicorn
    from trust_comparison_server import app
    
    uvicorn.run(app, host="0.0.0.0", port=8001)
    
except Exception as e:
    print(f"❌ Failed to start server: {e}")
    import traceback
    traceback.print_exc()
