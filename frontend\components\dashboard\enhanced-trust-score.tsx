'use client';

import { useEffect, useRef, useState } from 'react';
import { Tooltip } from '../ui/tooltip';

// Import Plotly dynamically to avoid SSR issues
let Plotly: any;
if (typeof window !== 'undefined') {
  import('plotly.js-dist-min').then((module: any) => {
    Plotly = module.default || module;
  }).catch(err => {
    console.error('Failed to load Plotly:', err);
  });
}

interface TrustScore {
  overall_score: number;
  factors: string[];
  explanation: string;
  component_scores?: {
    coherence: number;
    factual_accuracy: number;
    reasoning_quality: number;
    data_alignment: number;
  };
}

interface EnhancedTrustScoreProps {
  trustScore: TrustScore;
}

export default function EnhancedTrustScore({ trustScore }: EnhancedTrustScoreProps) {
  const [showDetails, setShowDetails] = useState(false);
  const radarChartRef = useRef<HTMLDivElement>(null);
  const [activeComponent, setActiveComponent] = useState<string | null>(null);

  // Component descriptions for tooltips and detailed explanations
  const componentDescriptions = {
    coherence: {
      short: "How well the response flows logically from one point to the next",
      long: "Coherence measures how well the different parts of the response connect to each other. A high coherence score means the response is well-structured, with each sentence logically following from the previous one, without contradictions or abrupt topic changes."
    },
    factual_accuracy: {
      short: "How accurate the numerical claims and facts are",
      long: "Factual accuracy evaluates whether numerical claims and statements in the response are correct according to the dataset. A high score indicates that the facts presented are verifiable and align with the actual data values."
    },
    reasoning_quality: {
      short: "How well the response explains its conclusions",
      long: "Reasoning quality assesses the logical process used to reach conclusions. A high score means the response provides clear explanations, uses appropriate analytical methods, and acknowledges uncertainty when appropriate."
    },
    data_alignment: {
      short: "How well the response aligns with the dataset",
      long: "Data alignment measures how well the response utilizes the available data. A high score indicates that the response references relevant columns from the dataset, discusses appropriate data ranges, and mentions statistical distributions when applicable."
    }
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get background color based on score
  const getScoreBgColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-600';
    if (score >= 0.6) return 'bg-yellow-500';
    return 'bg-red-600';
  };

  // Get background color with opacity based on score
  const getScoreBgColorLight = (score: number) => {
    if (score >= 0.8) return 'bg-green-100';
    if (score >= 0.6) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  // Get border color based on score
  const getScoreBorderColor = (score: number) => {
    if (score >= 0.8) return 'border-green-600';
    if (score >= 0.6) return 'border-yellow-500';
    return 'border-red-600';
  };

  // Create radar chart for component scores
  useEffect(() => {
    if (showDetails && radarChartRef.current && Plotly && trustScore.component_scores) {
      const { coherence, factual_accuracy, reasoning_quality, data_alignment } = trustScore.component_scores;

      const data: any[] = [{
        type: 'scatterpolar',
        r: [coherence, factual_accuracy, reasoning_quality, data_alignment, coherence], // Repeat first point to close the shape
        theta: ['Coherence', 'Factual Accuracy', 'Reasoning Quality', 'Data Alignment', 'Coherence'], // Repeat first point to close the shape
        fill: 'toself',
        fillcolor: 'rgba(59, 130, 246, 0.2)',
        line: {
          color: 'rgb(59, 130, 246)'
        }
      }];

      const layout = {
        polar: {
          radialaxis: {
            visible: true,
            range: [0, 1],
            tickfont: {
              size: 10
            }
          },
          angularaxis: {
            tickfont: {
              size: 10
            }
          }
        },
        showlegend: false,
        margin: {
          l: 40,
          r: 40,
          t: 20,
          b: 20
        },
        height: 300,
        width: 300,
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)'
      };

      Plotly.newPlot(radarChartRef.current, data, layout, {displayModeBar: false});
    }
  }, [showDetails, trustScore]);

  return (
    <div className="p-4 border rounded-md bg-white shadow-sm">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-black">Trust Score Analysis</h3>
        <div className="flex items-center mt-2 md:mt-0">
          <div className="flex items-center">
            <div className="text-2xl font-bold mr-2 ml-2 md:ml-0 md:text-3xl">
              <span className={getScoreColor(trustScore.overall_score)}>
                {Math.round(trustScore.overall_score * 100)}%
              </span>
            </div>
            <div className={`px-3 py-1 rounded-full text-white text-xs font-medium ${getScoreBgColor(trustScore.overall_score)}`}>
              {trustScore.overall_score >= 0.8 ? 'High Trust' :
               trustScore.overall_score >= 0.6 ? 'Moderate Trust' : 'Low Trust'}
            </div>
          </div>
        </div>
      </div>

      <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
        <div
          className={`h-3 rounded-full ${getScoreBgColor(trustScore.overall_score)}`}
          style={{ width: `${trustScore.overall_score * 100}%` }}
        ></div>
      </div>

      <div className="text-sm text-gray-700 mb-4">
        <p>{trustScore.explanation}</p>
      </div>

      <div className="mb-4">
        <h4 className="font-medium text-gray-800 mb-2">Quality Factors</h4>
        <div className="space-y-2">
          {trustScore.factors.map((factor, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-700">{factor}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-700">
        <p className="font-medium mb-1">How to interpret:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li><span className="text-green-600 font-medium">80-100%</span>: High reliability, can be trusted</li>
          <li><span className="text-yellow-600 font-medium">60-79%</span>: Moderately reliable, verify key points</li>
          <li><span className="text-red-600 font-medium">Below 60%</span>: Low reliability, use with caution</li>
        </ul>
      </div>
    </div>
  );
}
