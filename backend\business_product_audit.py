"""
🔬 BUSINESS ANALYST & PRODUCT OWNER COMPREHENSIVE AUDIT
Testing all critical business scenarios and product requirements
"""

import pandas as pd
import numpy as np
from services.production_query_engine import production_engine
import asyncio
import time

async def business_analyst_audit():
    """Business Analyst perspective - Real-world business scenarios"""
    
    print("🔬 BUSINESS ANALYST AUDIT")
    print("=" * 50)
    
    # Load real healthcare data
    try:
        df = pd.read_excel(r"C:\Users\<USER>\Downloads\Healthcare_Data.xlsx")
        print(f"✅ Dataset loaded: {df.shape[0]} records, {df.shape[1]} fields")
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return
    
    # BUSINESS CRITICAL SCENARIOS
    business_scenarios = [
        # Financial Analysis
        ("What is the total revenue from COVID-19 treatments?", "total cost of COVID-19"),
        ("How much did we spend on Flu treatments?", "total cost of Flu"),
        ("What's the average treatment cost per COVID-19 patient?", "average cost of COVID-19"),
        
        # Operational Metrics
        ("How many COVID-19 patients did we treat?", "count COVID-19 patients"),
        ("How many Flu cases do we have?", "count Flu patients"),
        ("What's our total patient volume?", "count all patients"),
        
        # Performance Analysis
        ("What's the highest treatment cost for COVID-19?", "maximum cost of COVID-19"),
        ("What's the lowest Flu treatment cost?", "minimum cost of Flu"),
        ("What's our overall revenue?", "total cost"),
        
        # Comparative Analysis
        ("Which costs more on average - COVID-19 or Flu?", "compare COVID-19 and Flu average cost"),
        ("What percentage of patients have COVID-19?", "what percentage of patients have COVID-19"),

        # Trend Analysis
        ("Show me treatment costs by diagnosis", "cost breakdown by diagnosis"),
        ("What's the cost distribution?", "Need statistical analysis"),
    ]
    
    print("\n📊 BUSINESS SCENARIO TESTING:")
    print("-" * 40)
    
    passed = 0
    failed = 0
    missing_features = []
    
    for i, (business_question, test_query) in enumerate(business_scenarios, 1):
        print(f"\n{i}. Business Question: {business_question}")
        print(f"   Test Query: {test_query}")
        
        if "Need" in test_query:
            print(f"   Status: ⚠️ MISSING FEATURE")
            missing_features.append(business_question)
            continue
        
        try:
            start_time = time.time()
            result = await production_engine.process_query(df, test_query)
            response_time = (time.time() - start_time) * 1000
            
            if result.value is not None and result.confidence > 0.8:
                print(f"   Result: ✅ {result.answer}")
                print(f"   Response Time: {response_time:.1f}ms")
                passed += 1
            else:
                print(f"   Result: ❌ {result.answer}")
                print(f"   Confidence: {result.confidence}")
                failed += 1
                
        except Exception as e:
            print(f"   Result: ❌ Error: {str(e)}")
            failed += 1
    
    print(f"\n📊 BUSINESS AUDIT RESULTS:")
    print(f"   ✅ Working: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   ⚠️ Missing Features: {len(missing_features)}")
    
    if missing_features:
        print(f"\n🚨 CRITICAL MISSING FEATURES:")
        for feature in missing_features:
            print(f"   - {feature}")

async def product_owner_audit():
    """Product Owner perspective - User experience and feature completeness"""
    
    print("\n\n🎯 PRODUCT OWNER AUDIT")
    print("=" * 50)
    
    try:
        df = pd.read_excel(r"C:\Users\<USER>\Downloads\Healthcare_Data.xlsx")
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return
    
    # USER EXPERIENCE SCENARIOS
    ux_scenarios = [
        # Natural Language Variations
        ("total cost of COVID-19", "Standard phrasing"),
        ("how much did COVID-19 cost", "Conversational phrasing"),
        ("COVID-19 total expenses", "Business terminology"),
        ("sum all COVID-19 costs", "Excel-like phrasing"),
        ("what is the total cost for COVID-19 patients", "Question format"),
        
        # Different Operations
        ("count COVID-19", "Short form"),
        ("how many COVID-19 patients", "Long form"),
        ("COVID-19 patient count", "Noun form"),
        
        # Edge Cases
        ("total cost of xyz", "Non-existent condition"),
        ("", "Empty query"),
        ("cost", "Minimal query"),
        ("maximum", "Operation only"),
        
        # Complex Queries
        ("total cost of COVID-19 and Flu", "Multiple conditions"),
        ("average cost per patient", "Per-unit calculation"),
        ("cost breakdown by diagnosis", "Grouping request"),
    ]
    
    print("\n🎯 USER EXPERIENCE TESTING:")
    print("-" * 40)
    
    ux_passed = 0
    ux_failed = 0
    
    for i, (query, scenario_type) in enumerate(ux_scenarios, 1):
        print(f"\n{i}. Query: '{query}'")
        print(f"   Scenario: {scenario_type}")
        
        try:
            start_time = time.time()
            result = await production_engine.process_query(df, query)
            response_time = (time.time() - start_time) * 1000
            
            # Evaluate based on scenario type
            if scenario_type == "Non-existent condition":
                # Should return 0 or handle gracefully
                if result.value == 0 or "no result" in result.answer.lower():
                    print(f"   Result: ✅ Handled gracefully: {result.answer}")
                    ux_passed += 1
                else:
                    print(f"   Result: ❌ Should return 0 for non-existent: {result.answer}")
                    ux_failed += 1
            elif scenario_type == "Empty query":
                # Should handle gracefully
                if result.confidence < 0.5 or "error" in result.answer.lower():
                    print(f"   Result: ✅ Handled empty query: {result.answer}")
                    ux_passed += 1
                else:
                    print(f"   Result: ❌ Should handle empty query better: {result.answer}")
                    ux_failed += 1
            else:
                # Normal scenarios - should work
                if result.value is not None and result.confidence > 0.8:
                    print(f"   Result: ✅ {result.answer}")
                    print(f"   Response Time: {response_time:.1f}ms")
                    ux_passed += 1
                else:
                    print(f"   Result: ❌ {result.answer}")
                    print(f"   Confidence: {result.confidence}")
                    ux_failed += 1
                    
        except Exception as e:
            print(f"   Result: ❌ Error: {str(e)}")
            ux_failed += 1
    
    print(f"\n🎯 UX AUDIT RESULTS:")
    print(f"   ✅ Working: {ux_passed}")
    print(f"   ❌ Failed: {ux_failed}")

async def performance_audit():
    """Performance and scalability testing"""
    
    print("\n\n⚡ PERFORMANCE AUDIT")
    print("=" * 50)
    
    try:
        df = pd.read_excel(r"C:\Users\<USER>\Downloads\Healthcare_Data.xlsx")
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return
    
    # Performance test queries
    perf_queries = [
        "total cost of COVID-19",
        "count all patients", 
        "maximum treatment cost",
        "average cost of Flu",
        "minimum cost of Diabetes"
    ]
    
    print("\n⚡ PERFORMANCE TESTING:")
    print("-" * 40)
    
    response_times = []
    
    for query in perf_queries:
        times = []
        
        # Run each query 5 times
        for i in range(5):
            start_time = time.time()
            try:
                result = await production_engine.process_query(df, query)
                response_time = (time.time() - start_time) * 1000
                times.append(response_time)
            except Exception as e:
                print(f"❌ Error in performance test: {e}")
                times.append(999999)  # Mark as failed
        
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        response_times.append(avg_time)
        
        print(f"Query: {query}")
        print(f"   Avg: {avg_time:.1f}ms, Min: {min_time:.1f}ms, Max: {max_time:.1f}ms")
        
        if avg_time < 100:
            print(f"   Status: ✅ Excellent")
        elif avg_time < 500:
            print(f"   Status: ✅ Good")
        elif avg_time < 1000:
            print(f"   Status: ⚠️ Acceptable")
        else:
            print(f"   Status: ❌ Too slow")
        print()
    
    overall_avg = sum(response_times) / len(response_times)
    print(f"📊 OVERALL PERFORMANCE: {overall_avg:.1f}ms average")
    
    if overall_avg < 100:
        print("✅ EXCELLENT performance")
    elif overall_avg < 500:
        print("✅ GOOD performance")
    else:
        print("⚠️ Performance needs improvement")

async def feature_completeness_audit():
    """Check feature completeness against requirements"""
    
    print("\n\n📋 FEATURE COMPLETENESS AUDIT")
    print("=" * 50)
    
    required_features = {
        "Basic Aggregations": {
            "Sum/Total": "✅ Implemented",
            "Average/Mean": "✅ Implemented", 
            "Count": "✅ Implemented",
            "Maximum": "✅ Implemented",
            "Minimum": "✅ Implemented"
        },
        "Conditional Analysis": {
            "Filtered Aggregations": "✅ Implemented",
            "Comparison Queries": "✅ Implemented",
            "Percentage Calculations": "✅ Implemented",
            "Breakdown/Grouping": "✅ Implemented",
            "Non-existent Condition Handling": "✅ Implemented",
            "Multiple Conditions": "❌ Not implemented",
            "Complex Filters": "❌ Not implemented"
        },
        "Excel-like Functions": {
            "SUMIF equivalent": "✅ Implemented",
            "COUNTIF equivalent": "✅ Implemented",
            "AVERAGEIF equivalent": "✅ Implemented",
            "VLOOKUP": "❌ Not implemented",
            "Pivot Tables": "❌ Not implemented"
        },
        "Statistical Analysis": {
            "Descriptive Statistics": "❌ Not implemented",
            "Correlations": "❌ Not implemented",
            "Distributions": "❌ Not implemented"
        },
        "Data Visualization": {
            "Charts": "❌ Not implemented",
            "Graphs": "❌ Not implemented",
            "Dashboards": "❌ Not implemented"
        },
        "Advanced Analytics": {
            "Forecasting": "❌ Not implemented",
            "Trend Analysis": "❌ Not implemented",
            "Anomaly Detection": "❌ Not implemented"
        }
    }
    
    print("\n📋 FEATURE STATUS:")
    print("-" * 40)
    
    total_features = 0
    implemented_features = 0
    
    for category, features in required_features.items():
        print(f"\n{category}:")
        for feature, status in features.items():
            print(f"   {feature}: {status}")
            total_features += 1
            if "✅" in status:
                implemented_features += 1
    
    completion_rate = (implemented_features / total_features) * 100
    print(f"\n📊 COMPLETION RATE: {completion_rate:.1f}% ({implemented_features}/{total_features})")
    
    if completion_rate >= 80:
        print("✅ READY for production")
    elif completion_rate >= 60:
        print("⚠️ NEEDS more features before production")
    else:
        print("❌ NOT READY for production")

if __name__ == "__main__":
    asyncio.run(business_analyst_audit())
    asyncio.run(product_owner_audit())
    asyncio.run(performance_audit())
    asyncio.run(feature_completeness_audit())
