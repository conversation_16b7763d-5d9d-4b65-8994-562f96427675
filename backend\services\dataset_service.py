import pandas as pd
import json
import os
from typing import Optional, Dict, List, Any, Union
from io import StringIO

class DatasetService:
    """
    Service for handling dataset operations
    """
    
    def __init__(self):
        """
        Initialize the dataset service
        """
        # Define dataset storage directory if needed
        self.dataset_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "datasets")
        os.makedirs(self.dataset_dir, exist_ok=True)
    
    def load_dataset(self, file_path: Optional[str] = None, json_data: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        Load a dataset from a file or JSON string
        
        Args:
            file_path: Path to the dataset file (CSV, Excel)
            json_data: JSON string representation of the dataset
            
        Returns:
            Pandas DataFrame or None if loading fails
        """
        try:
            if file_path and os.path.exists(file_path):
                # Determine file type from extension
                file_extension = os.path.splitext(file_path)[1].lower()
                
                if file_extension == '.csv':
                    return pd.read_csv(file_path)
                elif file_extension in ['.xlsx', '.xls']:
                    return pd.read_excel(file_path)
                else:
                    print(f"Unsupported file type: {file_extension}")
                    return None
            elif json_data:
                # Parse JSON data
                return pd.read_json(StringIO(json_data))
            else:
                print("No valid data source provided")
                return None
        except Exception as e:
            print(f"Error loading dataset: {str(e)}")
            return None
    
    def save_dataset(self, df: pd.DataFrame, file_path: str) -> bool:
        """
        Save a dataset to a file
        
        Args:
            df: Pandas DataFrame to save
            file_path: Path where to save the dataset
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Determine file type from extension
            file_extension = os.path.splitext(file_path)[1].lower()
            
            if file_extension == '.csv':
                df.to_csv(file_path, index=False)
            elif file_extension in ['.xlsx', '.xls']:
                df.to_excel(file_path, index=False)
            else:
                print(f"Unsupported file type: {file_extension}")
                return False
            
            return True
        except Exception as e:
            print(f"Error saving dataset: {str(e)}")
            return False
    
    def get_dataset_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get basic information about a dataset
        
        Args:
            df: Pandas DataFrame
            
        Returns:
            Dictionary with dataset information
        """
        try:
            # Get basic info
            info = {
                "row_count": len(df),
                "column_count": len(df.columns),
                "columns": df.columns.tolist(),
                "dtypes": {col: str(df[col].dtype) for col in df.columns},
                "missing_values": df.isnull().sum().to_dict(),
                "sample": df.head(5).to_dict(orient="records")
            }
            
            # Get numeric column statistics
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                info["numeric_stats"] = {
                    col: {
                        "min": float(df[col].min()) if not pd.isna(df[col].min()) else None,
                        "max": float(df[col].max()) if not pd.isna(df[col].max()) else None,
                        "mean": float(df[col].mean()) if not pd.isna(df[col].mean()) else None,
                        "median": float(df[col].median()) if not pd.isna(df[col].median()) else None,
                        "std": float(df[col].std()) if not pd.isna(df[col].std()) else None
                    }
                    for col in numeric_cols
                }
            
            # Get categorical column statistics
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns
            if len(categorical_cols) > 0:
                info["categorical_stats"] = {
                    col: {
                        "unique_values": df[col].nunique(),
                        "top_values": df[col].value_counts().head(5).to_dict()
                    }
                    for col in categorical_cols
                }
            
            return info
        except Exception as e:
            print(f"Error getting dataset info: {str(e)}")
            return {"error": str(e)}
    
    def filter_dataset(self, df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
        """
        Filter a dataset based on column conditions
        
        Args:
            df: Pandas DataFrame
            filters: Dictionary of column filters
            
        Returns:
            Filtered DataFrame
        """
        try:
            filtered_df = df.copy()
            
            for column, condition in filters.items():
                if column not in filtered_df.columns:
                    continue
                
                if isinstance(condition, dict):
                    # Handle complex conditions
                    if "equals" in condition:
                        filtered_df = filtered_df[filtered_df[column] == condition["equals"]]
                    elif "not_equals" in condition:
                        filtered_df = filtered_df[filtered_df[column] != condition["not_equals"]]
                    elif "greater_than" in condition:
                        filtered_df = filtered_df[filtered_df[column] > condition["greater_than"]]
                    elif "less_than" in condition:
                        filtered_df = filtered_df[filtered_df[column] < condition["less_than"]]
                    elif "contains" in condition:
                        filtered_df = filtered_df[filtered_df[column].astype(str).str.contains(str(condition["contains"]), na=False)]
                    elif "in" in condition and isinstance(condition["in"], list):
                        filtered_df = filtered_df[filtered_df[column].isin(condition["in"])]
                else:
                    # Simple equality condition
                    filtered_df = filtered_df[filtered_df[column] == condition]
            
            return filtered_df
        except Exception as e:
            print(f"Error filtering dataset: {str(e)}")
            return df
    
    def transform_dataset(self, df: pd.DataFrame, transformations: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        Apply transformations to a dataset
        
        Args:
            df: Pandas DataFrame
            transformations: List of transformation operations
            
        Returns:
            Transformed DataFrame
        """
        try:
            transformed_df = df.copy()
            
            for transform in transformations:
                operation = transform.get("operation")
                
                if operation == "rename_column":
                    old_name = transform.get("column")
                    new_name = transform.get("new_name")
                    if old_name in transformed_df.columns and new_name:
                        transformed_df = transformed_df.rename(columns={old_name: new_name})
                
                elif operation == "drop_column":
                    column = transform.get("column")
                    if column in transformed_df.columns:
                        transformed_df = transformed_df.drop(columns=[column])
                
                elif operation == "fill_na":
                    column = transform.get("column")
                    value = transform.get("value")
                    if column in transformed_df.columns:
                        transformed_df[column] = transformed_df[column].fillna(value)
                
                elif operation == "convert_type":
                    column = transform.get("column")
                    dtype = transform.get("dtype")
                    if column in transformed_df.columns:
                        try:
                            transformed_df[column] = transformed_df[column].astype(dtype)
                        except:
                            print(f"Could not convert column {column} to {dtype}")
                
                elif operation == "create_column":
                    new_column = transform.get("new_column")
                    expression = transform.get("expression")
                    if new_column and expression:
                        try:
                            # Simple expressions using existing columns
                            if "+" in expression:
                                cols = expression.split("+")
                                transformed_df[new_column] = sum(transformed_df[col.strip()] for col in cols if col.strip() in transformed_df.columns)
                            elif "-" in expression:
                                cols = expression.split("-")
                                if cols[0].strip() in transformed_df.columns:
                                    transformed_df[new_column] = transformed_df[cols[0].strip()]
                                    for col in cols[1:]:
                                        if col.strip() in transformed_df.columns:
                                            transformed_df[new_column] -= transformed_df[col.strip()]
                            elif "*" in expression:
                                cols = expression.split("*")
                                result = 1
                                for col in cols:
                                    if col.strip() in transformed_df.columns:
                                        if result == 1:
                                            result = transformed_df[col.strip()].copy()
                                        else:
                                            result *= transformed_df[col.strip()]
                                transformed_df[new_column] = result
                            elif "/" in expression:
                                cols = expression.split("/")
                                if cols[0].strip() in transformed_df.columns:
                                    transformed_df[new_column] = transformed_df[cols[0].strip()]
                                    for col in cols[1:]:
                                        if col.strip() in transformed_df.columns:
                                            transformed_df[new_column] /= transformed_df[col.strip()]
                        except Exception as e:
                            print(f"Error creating column {new_column}: {str(e)}")
            
            return transformed_df
        except Exception as e:
            print(f"Error transforming dataset: {str(e)}")
            return df
    
    def get_column_sample(self, df: pd.DataFrame, column: str, n: int = 10) -> List[Any]:
        """
        Get a sample of values from a column
        
        Args:
            df: Pandas DataFrame
            column: Column name
            n: Number of samples to return
            
        Returns:
            List of sample values
        """
        if column not in df.columns:
            return []
        
        try:
            # Get unique values if there are fewer than n
            unique_values = df[column].unique()
            if len(unique_values) <= n:
                return unique_values.tolist()
            
            # Otherwise get a random sample
            return df[column].sample(n).tolist()
        except Exception as e:
            print(f"Error getting column sample: {str(e)}")
            return []
