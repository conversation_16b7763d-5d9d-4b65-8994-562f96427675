import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import json
import base64
from io import BytesIO
from typing import Dict, Any, Optional

def get_image_base64(fig):
    """Helper function to generate base64 image from a plotly figure"""
    try:
        img_bytes = BytesIO()
        fig.write_image(img_bytes, format="png", engine="auto")
        img_bytes.seek(0)
        return base64.b64encode(img_bytes.read()).decode("utf-8")
    except Exception as e:
        print(f"Error generating image: {str(e)}")
        return ""

def generate_chart(df: pd.DataFrame, chart_type: str, chart_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate a chart based on the specified type and data

    Args:
        df: The pandas DataFrame containing the data
        chart_type: The type of chart to generate (bar, line, pie, scatter, table, heatmap, box, radar, waterfall, treemap)
        chart_data: Dictionary containing chart configuration

    Returns:
        Dictionary containing chart data in Plotly JSON format and base64 image
    """
    # Extract chart parameters
    x_col = chart_data.get("x")
    y_col = chart_data.get("y")
    group_col = chart_data.get("group")

    # Validate columns exist in DataFrame
    for col_name, col_value in [("x", x_col), ("y", y_col), ("group", group_col)]:
        if col_value and not isinstance(col_value, list) and col_value not in df.columns:
            # For non-list columns, check if they exist in the DataFrame
            raise ValueError(f"Column '{col_value}' not found in dataset")
        elif isinstance(col_value, list):
            # For list columns, check if they are lists of column names or data points
            if col_name == "y" and all(isinstance(item, list) for item in col_value):
                # This is a list of data points, not column names
                continue
            # Check each column name in the list
            for subcol in col_value:
                if subcol and isinstance(subcol, str) and subcol not in df.columns:
                    raise ValueError(f"Column '{subcol}' not found in dataset")

    # Create figure based on chart type
    fig = None

    # Prepare data for visualization
    # Handle case when y_col is a list of columns
    if isinstance(y_col, list):
        # For multiple y columns, we need special handling
        multi_y_cols = y_col
    else:
        multi_y_cols = None

    # Determine if we should use a time series visualization
    is_time_series = False
    if x_col and x_col in df.columns:
        if pd.api.types.is_datetime64_any_dtype(df[x_col]) or (
            pd.api.types.is_object_dtype(df[x_col]) and
            any(term in x_col.lower() for term in ['date', 'time', 'year', 'month', 'day'])
        ):
            is_time_series = True
            # Try to convert to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(df[x_col]):
                try:
                    df[x_col] = pd.to_datetime(df[x_col])
                    is_time_series = True
                except:
                    is_time_series = False

    # Create appropriate chart based on type and data characteristics
    if chart_type == "bar":
        if multi_y_cols:
            # Create a bar chart with multiple y columns
            fig = px.bar(
                df,
                x=x_col,
                y=multi_y_cols,
                barmode="group" if len(multi_y_cols) > 1 else None,
                color_discrete_sequence=px.colors.qualitative.G10
            )
        elif group_col:
            # Create a grouped bar chart
            fig = px.bar(
                df,
                x=x_col,
                y=y_col,
                color=group_col,
                barmode="group",
                color_discrete_sequence=px.colors.qualitative.G10
            )
        else:
            # Create a simple bar chart
            fig = px.bar(
                df,
                x=x_col,
                y=y_col,
                color_discrete_sequence=['#3366CC']
            )

            # Add data labels on top of bars for better readability
            fig.update_traces(
                texttemplate='%{y:.1f}',
                textposition='outside'
            )

    elif chart_type == "line":
        if is_time_series:
            # For time series data, ensure proper formatting
            if multi_y_cols:
                fig = px.line(
                    df,
                    x=x_col,
                    y=multi_y_cols,
                    markers=True,
                    color_discrete_sequence=px.colors.qualitative.G10
                )
            elif group_col:
                fig = px.line(
                    df,
                    x=x_col,
                    y=y_col,
                    color=group_col,
                    markers=True,
                    color_discrete_sequence=px.colors.qualitative.G10
                )
            else:
                fig = px.line(
                    df,
                    x=x_col,
                    y=y_col,
                    markers=True,
                    color_discrete_sequence=['#3366CC']
                )
        else:
            # For non-time series data
            if multi_y_cols:
                fig = px.line(
                    df,
                    x=x_col,
                    y=multi_y_cols,
                    markers=True,
                    color_discrete_sequence=px.colors.qualitative.G10
                )
            elif group_col:
                fig = px.line(
                    df,
                    x=x_col,
                    y=y_col,
                    color=group_col,
                    markers=True,
                    color_discrete_sequence=px.colors.qualitative.G10
                )
            else:
                fig = px.line(
                    df,
                    x=x_col,
                    y=y_col,
                    markers=True,
                    color_discrete_sequence=['#3366CC']
                )

    elif chart_type == "pie":
        # For pie charts, ensure we have categorical x and numeric y
        if y_col and x_col and x_col in df.columns and y_col in df.columns:
            if pd.api.types.is_numeric_dtype(df[y_col]):
                # Limit to top 10 categories for readability if there are too many
                if df[x_col].nunique() > 10:
                    top_categories = df.groupby(x_col)[y_col].sum().nlargest(10).index.tolist()
                    filtered_df = df[df[x_col].isin(top_categories)].copy()

                    # Add an "Other" category for the rest
                    if len(df) > len(filtered_df):
                        other_sum = df[~df[x_col].isin(top_categories)][y_col].sum()
                        other_row = pd.DataFrame({x_col: ["Other"], y_col: [other_sum]})
                        filtered_df = pd.concat([filtered_df, other_row], ignore_index=True)

                    fig = px.pie(
                        filtered_df,
                        names=x_col,
                        values=y_col,
                        color_discrete_sequence=px.colors.qualitative.G10
                    )
                else:
                    fig = px.pie(
                        df,
                        names=x_col,
                        values=y_col,
                        color_discrete_sequence=px.colors.qualitative.G10
                    )

                # Add percentage and value in hover
                fig.update_traces(
                    textinfo='percent+label',
                    hovertemplate='%{label}<br>%{value:.1f} (%{percent})'
                )
            else:
                # If y is not numeric, count occurrences of each category
                value_counts = df[x_col].value_counts().reset_index()
                value_counts.columns = [x_col, 'count']

                # Limit to top 10 categories for readability if there are too many
                if len(value_counts) > 10:
                    value_counts = pd.concat([
                        value_counts.nlargest(9, 'count'),
                        pd.DataFrame({
                            x_col: ['Other'],
                            'count': [value_counts.nsmallest(len(value_counts) - 9, 'count')['count'].sum()]
                        })
                    ]).reset_index(drop=True)

                fig = px.pie(
                    value_counts,
                    names=x_col,
                    values='count',
                    color_discrete_sequence=px.colors.qualitative.G10
                )

                # Add percentage and value in hover
                fig.update_traces(
                    textinfo='percent+label',
                    hovertemplate='%{label}<br>%{value} (%{percent})'
                )
        else:
            # Fallback if columns are not properly specified
            fig = px.pie(
                df,
                names=x_col,
                values=y_col,
                color_discrete_sequence=px.colors.qualitative.G10
            )

    elif chart_type == "scatter":
        if multi_y_cols:
            # For multiple y columns, create a scatter plot with multiple traces
            fig = go.Figure()
            for col in multi_y_cols:
                fig.add_trace(
                    go.Scatter(
                        x=df[x_col],
                        y=df[col],
                        mode='markers',
                        name=col
                    )
                )
        elif group_col:
            # Create a scatter plot with groups
            fig = px.scatter(
                df,
                x=x_col,
                y=y_col,
                color=group_col,
                color_discrete_sequence=px.colors.qualitative.G10,
                opacity=0.7,
                size_max=15
            )

            # Add trendline if both x and y are numeric
            if (pd.api.types.is_numeric_dtype(df[x_col]) and
                pd.api.types.is_numeric_dtype(df[y_col])):
                fig.update_traces(
                    marker=dict(size=10),
                )

                # Add trendlines for each group
                for group in df[group_col].unique():
                    group_df = df[df[group_col] == group]
                    fig.add_trace(
                        go.Scatter(
                            x=group_df[x_col],
                            y=group_df[y_col],
                            mode='lines',
                            name=f'Trend: {group}',
                            line=dict(dash='dash'),
                            opacity=0.5
                        )
                    )
        else:
            # Create a simple scatter plot
            fig = px.scatter(
                df,
                x=x_col,
                y=y_col,
                color_discrete_sequence=['#3366CC'],
                opacity=0.7
            )

            # Add trendline if both x and y are numeric
            if (pd.api.types.is_numeric_dtype(df[x_col]) and
                pd.api.types.is_numeric_dtype(df[y_col])):
                fig.update_traces(
                    marker=dict(size=10),
                )

                # Add trendline
                fig.add_trace(
                    go.Scatter(
                        x=df[x_col],
                        y=df[y_col],
                        mode='lines',
                        name='Trend',
                        line=dict(color='red', dash='dash'),
                        opacity=0.5
                    )
                )

    elif chart_type == "table":
        # For tables, we'll create a more advanced table figure
        columns = []
        formatted_values = []

        # Check if y_col is a list of data points (not column names)
        if isinstance(y_col, list) and y_col and isinstance(y_col[0], list):
            # This is a list of data points, not column names
            # Use x_col as column headers
            if isinstance(x_col, list):
                columns = x_col
            else:
                columns = [x_col] if x_col else df.columns[:10].tolist()

            # Use y_col as the data rows
            formatted_values = []
            for col_idx in range(len(columns)):
                col_values = []
                for row_idx in range(len(y_col)):
                    if col_idx < len(y_col[row_idx]):
                        val = y_col[row_idx][col_idx]
                        if isinstance(val, (int, float)):
                            col_values.append(f"{val:,.2f}" if pd.notna(val) else "")
                        else:
                            col_values.append(str(val)[:50] if pd.notna(val) else "")
                    else:
                        col_values.append("")
                formatted_values.append(col_values)
        else:
            # Handle multi_y_cols for tables
            if multi_y_cols:
                columns = [x_col] + multi_y_cols if x_col else multi_y_cols
            else:
                # Add columns in a logical order
                if x_col:
                    columns.append(x_col)
                if y_col and not isinstance(y_col, list):
                    columns.append(y_col)
                if group_col:
                    columns.append(group_col)

            # Get unique columns without using set (to avoid unhashable type error)
            unique_columns = []
            for col in columns:
                if col and col not in unique_columns and col in df.columns:
                    unique_columns.append(col)
            columns = unique_columns

            # If no columns specified, use all columns (up to a reasonable limit)
            if not columns:
                columns = df.columns[:10].tolist()  # Limit to first 10 columns for readability

            # Limit rows for better performance (show first 50 rows)
            display_df = df.head(50)

            # Format values based on data types
            formatted_values = []
            for col in columns:
                if col in df.columns:
                    if pd.api.types.is_numeric_dtype(df[col]):
                        # Format numbers with commas and 2 decimal places
                        formatted_values.append([f"{val:,.2f}" if pd.notna(val) else "" for val in display_df[col]])
                    elif pd.api.types.is_datetime64_any_dtype(df[col]):
                        # Format dates as YYYY-MM-DD
                        formatted_values.append([val.strftime('%Y-%m-%d') if pd.notna(val) else "" for val in display_df[col]])
                    else:
                        # For other types, convert to string and limit length
                        formatted_values.append([str(val)[:50] if pd.notna(val) else "" for val in display_df[col]])
                else:
                    formatted_values.append([])

        # Create an enhanced table with better styling
        fig = go.Figure(data=[go.Table(
            header=dict(
                values=columns,
                fill_color='#3366CC',
                align='center',
                font=dict(color='white', size=12),
                height=40
            ),
            cells=dict(
                values=formatted_values,
                fill_color=[['#f5f7fa', '#e4e8f0'] * len(display_df)],  # Alternating row colors
                align=['left'] * len(columns),
                font=dict(color='black', size=11),
                height=30
            )
        )])

    elif chart_type == "heatmap":
        # Create a heatmap for correlation or 2D data visualization
        if isinstance(x_col, list) and isinstance(y_col, list):
            # Use provided x and y lists as labels
            x_labels = x_col
            y_labels = y_col

            # Get z values from chart_data if provided, otherwise create a correlation matrix
            z_values = chart_data.get("z")
            if z_values is None:
                # If no z values provided, try to create a correlation matrix
                numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
                if len(numeric_cols) >= 2:
                    corr_matrix = df[numeric_cols].corr()
                    x_labels = corr_matrix.columns.tolist()
                    y_labels = corr_matrix.index.tolist()
                    z_values = corr_matrix.values.tolist()
                else:
                    raise ValueError("Not enough numeric columns for correlation heatmap")
        else:
            # Try to create a heatmap from x and y columns
            if x_col and y_col and x_col in df.columns and y_col in df.columns:
                # If group_col is provided, use it for z values
                if group_col and group_col in df.columns and pd.api.types.is_numeric_dtype(df[group_col]):
                    # Create a pivot table
                    pivot_df = df.pivot_table(
                        values=group_col,
                        index=y_col,
                        columns=x_col,
                        aggfunc='mean'
                    )
                    x_labels = pivot_df.columns.tolist()
                    y_labels = pivot_df.index.tolist()
                    z_values = pivot_df.values.tolist()
                else:
                    # Create a cross-tabulation (frequency table)
                    crosstab = pd.crosstab(df[y_col], df[x_col])
                    x_labels = crosstab.columns.tolist()
                    y_labels = crosstab.index.tolist()
                    z_values = crosstab.values.tolist()
            else:
                # Create a correlation matrix of all numeric columns
                numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
                if len(numeric_cols) >= 2:
                    corr_matrix = df[numeric_cols].corr()
                    x_labels = corr_matrix.columns.tolist()
                    y_labels = corr_matrix.index.tolist()
                    z_values = corr_matrix.values.tolist()
                else:
                    raise ValueError("Not enough numeric columns for correlation heatmap")

        # Create the heatmap
        fig = go.Figure(data=go.Heatmap(
            z=z_values,
            x=x_labels,
            y=y_labels,
            colorscale='Viridis',
            hoverongaps=False,
            colorbar=dict(
                title="Value",
                titleside="right"
            )
        ))

        # Update layout for better readability
        fig.update_layout(
            title="Heatmap Visualization",
            xaxis=dict(title=chart_data.get('xaxis', 'X Axis')),
            yaxis=dict(title=chart_data.get('yaxis', 'Y Axis'))
        )

    elif chart_type == "box":
        # Create a box plot for statistical distribution
        if multi_y_cols:
            # Create a box plot with multiple columns
            fig = go.Figure()
            for col in multi_y_cols:
                if col in df.columns and pd.api.types.is_numeric_dtype(df[col]):
                    fig.add_trace(go.Box(
                        y=df[col],
                        name=col,
                        boxpoints='outliers',
                        jitter=0.3,
                        pointpos=-1.8
                    ))
        elif group_col and y_col:
            # Create a grouped box plot
            fig = px.box(
                df,
                x=group_col,
                y=y_col,
                color=group_col if x_col != group_col else None,
                points="outliers"
            )
        elif y_col:
            # Create a simple box plot
            fig = go.Figure()
            fig.add_trace(go.Box(
                y=df[y_col],
                name=y_col,
                boxpoints='outliers',
                jitter=0.3,
                pointpos=-1.8
            ))
        else:
            # Create box plots for all numeric columns
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()[:5]  # Limit to 5 columns
            fig = go.Figure()
            for col in numeric_cols:
                fig.add_trace(go.Box(
                    y=df[col],
                    name=col,
                    boxpoints='outliers',
                    jitter=0.3,
                    pointpos=-1.8
                ))

    elif chart_type == "radar":
        # Create a radar/spider chart for multi-dimensional comparison
        if isinstance(x_col, list) and isinstance(y_col, list):
            # Use provided x and y lists as categories and values
            categories = x_col
            values = y_col
        elif group_col and y_col and x_col:
            # Create a radar chart comparing different groups
            # Pivot the data to get values for each group
            pivot_df = df.pivot_table(
                values=y_col,
                index=group_col,
                columns=x_col,
                aggfunc='mean'
            )

            categories = pivot_df.columns.tolist()

            # Create a trace for each group
            fig = go.Figure()

            for group in pivot_df.index:
                values = pivot_df.loc[group].tolist()
                # Close the loop by repeating the first value
                values.append(values[0])
                categories_closed = categories + [categories[0]]

                fig.add_trace(go.Scatterpolar(
                    r=values,
                    theta=categories_closed,
                    fill='toself',
                    name=str(group)
                ))

            # Update layout
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, max(pivot_df.max()) * 1.1]
                    )
                ),
                showlegend=True
            )

            # Return early since we've already created the figure
            return {
                "plotly_json": json.loads(fig.to_json()),
                "image_base64": get_image_base64(fig)
            }
        else:
            # Create a radar chart for a single entity
            # Use column names as categories and values from the first row
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()[:8]  # Limit to 8 dimensions

            if len(numeric_cols) < 3:
                raise ValueError("Not enough numeric columns for radar chart (need at least 3)")

            categories = numeric_cols

            # Normalize values to a 0-1 scale for better comparison
            normalized_df = df[numeric_cols].copy()
            for col in numeric_cols:
                min_val = normalized_df[col].min()
                max_val = normalized_df[col].max()
                if max_val > min_val:
                    normalized_df[col] = (normalized_df[col] - min_val) / (max_val - min_val)
                else:
                    normalized_df[col] = 0.5  # Default value if all values are the same

            # Create a trace for each row (limit to first 5 rows)
            fig = go.Figure()

            for i, (idx, row) in enumerate(normalized_df.head(5).iterrows()):
                values = row.tolist()
                # Close the loop by repeating the first value
                values.append(values[0])
                categories_closed = categories + [categories[0]]

                # Use row index or another identifying column as the name
                name = f"Row {idx}" if x_col is None else f"{df.loc[idx, x_col]}"

                fig.add_trace(go.Scatterpolar(
                    r=values,
                    theta=categories_closed,
                    fill='toself',
                    name=name
                ))

            # Update layout
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 1.1]
                    )
                ),
                showlegend=True
            )

            # Return early since we've already created the figure
            return {
                "plotly_json": json.loads(fig.to_json()),
                "image_base64": get_image_base64(fig)
            }

    elif chart_type == "waterfall":
        # Create a waterfall chart for financial analysis
        if x_col and y_col and x_col in df.columns and y_col in df.columns:
            # Sort data by x_col if it's a date/time column
            if pd.api.types.is_datetime64_any_dtype(df[x_col]):
                sorted_df = df.sort_values(by=x_col)
            else:
                sorted_df = df

            # Get x and y values
            x_values = sorted_df[x_col].tolist()
            y_values = sorted_df[y_col].tolist()

            # Calculate cumulative sum for measure
            measure = ['relative'] * len(x_values)
            measure[0] = 'absolute'  # First value is absolute
            measure[-1] = 'total'    # Last value is total

            # Create the waterfall chart
            fig = go.Figure(go.Waterfall(
                name="Waterfall",
                orientation="v",
                measure=measure,
                x=x_values,
                y=y_values,
                connector={"line": {"color": "rgb(63, 63, 63)"}},
                increasing={"marker": {"color": "rgba(44, 160, 101, 0.7)"}},
                decreasing={"marker": {"color": "rgba(255, 65, 54, 0.7)"}},
                totals={"marker": {"color": "rgba(0, 123, 255, 0.7)"}}
            ))
        else:
            # Create a simple waterfall chart with dummy data
            raise ValueError("Waterfall chart requires both x and y columns")

    elif chart_type == "treemap":
        # Create a treemap for hierarchical data
        if group_col and x_col and y_col:
            # Create a treemap with three levels: group, x, and values from y
            fig = px.treemap(
                df,
                path=[group_col, x_col],
                values=y_col,
                color=y_col,
                color_continuous_scale='RdBu',
                color_continuous_midpoint=np.average(df[y_col], weights=df[y_col])
            )
        elif x_col and y_col:
            # Create a treemap with two levels: x and values from y
            fig = px.treemap(
                df,
                path=[x_col],
                values=y_col,
                color=y_col,
                color_continuous_scale='RdBu',
                color_continuous_midpoint=np.average(df[y_col], weights=df[y_col])
            )
        else:
            # Create a treemap with all columns as path
            # Use the first numeric column as values
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            if not numeric_cols:
                raise ValueError("No numeric columns found for treemap values")

            # Use categorical columns for path
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            if not categorical_cols:
                raise ValueError("No categorical columns found for treemap path")

            # Limit path to 3 columns for readability
            path_cols = categorical_cols[:3]
            value_col = numeric_cols[0]

            fig = px.treemap(
                df,
                path=path_cols,
                values=value_col,
                color=value_col,
                color_continuous_scale='RdBu',
                color_continuous_midpoint=np.average(df[value_col], weights=df[value_col])
            )

    else:
        raise ValueError(f"Unsupported chart type: {chart_type}")

    # Get chart title
    chart_title = chart_data.get('title', 'Data Visualization')

    # Determine appropriate axis titles based on data
    x_axis_title = x_col
    y_axis_title = y_col

    # If y_col is a list (multiple columns for y-axis), create a more descriptive title
    if isinstance(y_col, list):
        y_axis_title = "Values"
        if len(y_col) <= 3:  # If there are only a few columns, list them
            y_axis_title = ", ".join(y_col)

    # Check data types and format axes accordingly
    if x_col and x_col in df.columns:
        # For datetime x-axis
        if pd.api.types.is_datetime64_any_dtype(df[x_col]):
            fig.update_xaxes(
                title=x_axis_title,
                type='date',
                tickformat='%Y-%m-%d',
                tickangle=-45
            )
        # For categorical x-axis
        elif pd.api.types.is_categorical_dtype(df[x_col]) or pd.api.types.is_object_dtype(df[x_col]):
            fig.update_xaxes(
                title=x_axis_title,
                type='category',
                tickangle=-45
            )
        # For numeric x-axis
        elif pd.api.types.is_numeric_dtype(df[x_col]):
            fig.update_xaxes(
                title=x_axis_title
            )

    # Update y-axis based on data type
    if y_col and not isinstance(y_col, list) and y_col in df.columns:
        if pd.api.types.is_numeric_dtype(df[y_col]):
            # Format y-axis for currency if column name suggests it
            if any(term in y_col.lower() for term in ['price', 'cost', 'revenue', 'sales', 'income', 'expense', 'amount', 'dollar', '$']):
                fig.update_yaxes(
                    title=y_axis_title,
                    tickprefix='$',
                    tickformat=',.2f'
                )
            # Format y-axis for percentages if column name suggests it
            elif any(term in y_col.lower() for term in ['percent', 'rate', 'ratio', '%']):
                fig.update_yaxes(
                    title=y_axis_title,
                    ticksuffix='%',
                    tickformat='.1f'
                )
            else:
                fig.update_yaxes(title=y_axis_title)

    # Update layout with improved settings
    fig.update_layout(
        title=f"Chart for: {chart_title}",
        template="plotly_white",
        margin=dict(l=50, r=30, t=50, b=80),  # Better margins for readability
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        # Improve font settings
        font=dict(
            family="Arial, sans-serif",
            size=12,
            color="black"
        )
    )

    # Convert to JSON for frontend rendering
    chart_json = fig.to_json()

    # Generate image for download
    img_base64 = ""
    try:
        img_bytes = BytesIO()
        fig.write_image(img_bytes, format="png", engine="auto")
        img_bytes.seek(0)
        img_base64 = base64.b64encode(img_bytes.read()).decode("utf-8")
    except Exception as e:
        print(f"Error generating image: {str(e)}")
        # Return without image if Kaleido is not available

    return {
        "plotly_json": json.loads(chart_json),
        "image_base64": img_base64
    }
