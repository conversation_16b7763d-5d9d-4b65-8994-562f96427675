# AIthentIQ Folder Rename Script (PowerShell)
# Run as Administrator for best results

Write-Host "===========================================" -ForegroundColor Cyan
Write-Host "     AIthentIQ Folder Rename Script" -ForegroundColor Cyan  
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  WARNING: Not running as Administrator" -ForegroundColor Yellow
    Write-Host "   Some operations may fail. Consider running as Admin." -ForegroundColor Yellow
    Write-Host ""
}

# Current location
Write-Host "🔍 Current location:" -ForegroundColor Green
Write-Host "   $PWD" -ForegroundColor White
Write-Host ""

# Check if we're in the right place
$currentFolder = Split-Path -Leaf $PWD
Write-Host "📁 Current folder name: $currentFolder" -ForegroundColor Green

if ($currentFolder -eq "AskData - Copy (2)") {
    Write-Host "✅ Perfect! We're in the source folder." -ForegroundColor Green
    
    # Stop services first
    Write-Host ""
    Write-Host "🛑 Stopping services..." -ForegroundColor Yellow
    if (Test-Path "stop_services.bat") {
        & ".\stop_services.bat"
    }
    
    Write-Host ""
    Write-Host "📋 AUTOMATED RENAME PROCESS:" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    
    $parentDir = Split-Path -Parent $PWD
    $newPath = Join-Path $parentDir "AIthentiq"
    
    Write-Host "Source: $PWD" -ForegroundColor White
    Write-Host "Target: $newPath" -ForegroundColor White
    Write-Host ""
    
    $confirm = Read-Host "Proceed with rename? (y/N)"
    
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        try {
            # Change to parent directory
            Set-Location $parentDir
            
            # Rename the folder
            Rename-Item "AskData - Copy (2)" "AIthentiq" -ErrorAction Stop
            
            Write-Host ""
            Write-Host "✅ SUCCESS! Folder renamed to AIthentiq" -ForegroundColor Green
            Write-Host ""
            
            # Navigate to new folder
            Set-Location "AIthentiq"
            
            Write-Host "🎉 You are now in the AIthentiq folder!" -ForegroundColor Green
            Write-Host "📍 Current location: $PWD" -ForegroundColor White
            Write-Host ""
            Write-Host "🚀 Ready to test! Run one of these:" -ForegroundColor Cyan
            Write-Host "   - .\start_both.bat" -ForegroundColor White
            Write-Host "   - npm run dev:both" -ForegroundColor White
            Write-Host ""
            
        } catch {
            Write-Host ""
            Write-Host "❌ ERROR: Could not rename folder" -ForegroundColor Red
            Write-Host "   $($_.Exception.Message)" -ForegroundColor Red
            Write-Host ""
            Write-Host "💡 Try manual rename:" -ForegroundColor Yellow
            Write-Host "   1. Close all applications using this folder" -ForegroundColor White
            Write-Host "   2. Use File Explorer to rename manually" -ForegroundColor White
            Write-Host ""
        }
    } else {
        Write-Host ""
        Write-Host "❌ Rename cancelled by user" -ForegroundColor Yellow
    }
    
} elseif ($currentFolder -eq "AIthentiq") {
    Write-Host "✅ Already renamed! You're in the AIthentiq folder." -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Ready to use! Try:" -ForegroundColor Cyan
    Write-Host "   .\start_both.bat" -ForegroundColor White
    
} else {
    Write-Host "⚠️  Not in the expected folder." -ForegroundColor Yellow
    Write-Host "   Expected: 'AskData - Copy (2)' or 'AIthentiq'" -ForegroundColor White
    Write-Host "   Found: '$currentFolder'" -ForegroundColor White
    Write-Host ""
    Write-Host "📂 Navigate to the correct folder first:" -ForegroundColor Yellow
    Write-Host "   cd 'C:\Users\<USER>\Documents\augment-projects\AskData - Copy (2)'" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
