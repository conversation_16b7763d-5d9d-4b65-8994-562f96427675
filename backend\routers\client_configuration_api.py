"""
Client Configuration API Router
Handles per-tenant configuration for LLM, embedding, vector store, and feature settings
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
import logging
from datetime import datetime
import json

from database import get_db
from middleware.tenant_isolation import get_current_tenant_id
from middleware.auth import get_current_user_id
from middleware.rbac_complete import require_permission, Permission
from models_multitenant import TenantConfiguration

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/admin", tags=["Client Configuration"])

# Pydantic models for API

class LLMSettings(BaseModel):
    """LLM configuration settings"""
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=2048, ge=1, le=8192)
    top_p: float = Field(default=1.0, ge=0.0, le=1.0)
    frequency_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    presence_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)

class EmbeddingSettings(BaseModel):
    """Embedding configuration settings"""
    chunk_size: int = Field(default=1000, ge=100, le=2000)
    chunk_overlap: int = Field(default=200, ge=0, le=500)
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0)

class VectorStoreSettings(BaseModel):
    """Vector store configuration settings"""
    index_name: str = Field(..., description="Vector store index name")
    dimensions: int = Field(default=1536, ge=128, le=3072)
    metric: str = Field(default="cosine", description="Distance metric")

class ConnectorSettings(BaseModel):
    """Data connector configuration settings"""
    max_file_size: int = Field(default=10485760, ge=1048576, le=104857600)  # 1MB to 100MB
    max_files_per_sync: int = Field(default=1000, ge=10, le=10000)
    sync_frequency: str = Field(default="hourly", description="Sync frequency")
    auto_sync_enabled: bool = Field(default=True)

class SecuritySettings(BaseModel):
    """Security configuration settings"""
    require_2fa: bool = Field(default=False)
    allowed_domains: List[str] = Field(default=[])
    ip_whitelist: List[str] = Field(default=[])
    session_timeout: int = Field(default=3600, ge=300, le=86400)  # 5 minutes to 24 hours

class FeaturesEnabled(BaseModel):
    """Feature enablement settings"""
    trust_scoring: bool = Field(default=True)
    source_attribution: bool = Field(default=True)
    query_history: bool = Field(default=True)
    data_export: bool = Field(default=True)
    api_access: bool = Field(default=False)
    advanced_analytics: bool = Field(default=True)

class ClientConfigurationRequest(BaseModel):
    """Complete client configuration request"""
    tenant_name: str = Field(..., description="Tenant display name")
    preferred_llm: str = Field(default="openai", description="Preferred LLM provider")
    llm_settings: LLMSettings = Field(default_factory=LLMSettings)
    preferred_embedding: str = Field(default="openai", description="Preferred embedding provider")
    embedding_settings: EmbeddingSettings = Field(default_factory=EmbeddingSettings)
    preferred_vector_store: str = Field(default="pinecone", description="Preferred vector store")
    vector_store_settings: VectorStoreSettings
    allowed_connectors: List[str] = Field(default=[], description="Allowed data connector types")
    connector_settings: ConnectorSettings = Field(default_factory=ConnectorSettings)
    security_settings: SecuritySettings = Field(default_factory=SecuritySettings)
    features_enabled: FeaturesEnabled = Field(default_factory=FeaturesEnabled)

@router.get("/client-config")
async def get_client_configuration(
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_SYSTEM)),
    db: Session = Depends(get_db)
):
    """
    Get current client configuration for the tenant
    
    Admin-only endpoint to view tenant configuration
    """
    try:
        config = db.query(TenantConfiguration).filter(
            TenantConfiguration.tenant_id == tenant_id
        ).first()
        
        if not config:
            # Return default configuration
            return {
                "tenant_id": tenant_id,
                "tenant_name": "Default Organization",
                "preferred_llm": "openai",
                "llm_settings": {
                    "temperature": 0.7,
                    "max_tokens": 2048,
                    "top_p": 1.0,
                    "frequency_penalty": 0.0,
                    "presence_penalty": 0.0
                },
                "preferred_embedding": "openai",
                "embedding_settings": {
                    "chunk_size": 1000,
                    "chunk_overlap": 200,
                    "similarity_threshold": 0.7
                },
                "preferred_vector_store": "pinecone",
                "vector_store_settings": {
                    "index_name": f"aithentiq-{tenant_id}",
                    "dimensions": 1536,
                    "metric": "cosine"
                },
                "allowed_connectors": ["github", "sharepoint", "onedrive", "file_upload"],
                "connector_settings": {
                    "max_file_size": 10485760,
                    "max_files_per_sync": 1000,
                    "sync_frequency": "hourly",
                    "auto_sync_enabled": True
                },
                "security_settings": {
                    "require_2fa": False,
                    "allowed_domains": [],
                    "ip_whitelist": [],
                    "session_timeout": 3600
                },
                "features_enabled": {
                    "trust_scoring": True,
                    "source_attribution": True,
                    "query_history": True,
                    "data_export": True,
                    "api_access": False,
                    "advanced_analytics": True
                }
            }
        
        return {
            "tenant_id": config.tenant_id,
            "tenant_name": config.tenant_name,
            "preferred_llm": config.preferred_llm,
            "llm_settings": config.llm_settings,
            "preferred_embedding": config.preferred_embedding,
            "embedding_settings": config.embedding_settings,
            "preferred_vector_store": config.preferred_vector_store,
            "vector_store_settings": config.vector_store_settings,
            "allowed_connectors": config.allowed_connectors,
            "connector_settings": config.connector_settings,
            "security_settings": config.security_settings,
            "features_enabled": config.features_enabled,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat() if config.updated_at else None
        }
        
    except Exception as e:
        logger.error(f"Failed to get client configuration: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get client configuration: {str(e)}"
        )

@router.put("/client-config")
async def update_client_configuration(
    config_data: ClientConfigurationRequest,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_SYSTEM)),
    db: Session = Depends(get_db)
):
    """
    Update client configuration for the tenant
    
    Admin-only endpoint to modify tenant configuration
    """
    try:
        config = db.query(TenantConfiguration).filter(
            TenantConfiguration.tenant_id == tenant_id
        ).first()
        
        if not config:
            # Create new configuration
            config = TenantConfiguration(
                tenant_id=tenant_id,
                tenant_name=config_data.tenant_name,
                preferred_llm=config_data.preferred_llm,
                llm_settings=config_data.llm_settings.dict(),
                preferred_embedding=config_data.preferred_embedding,
                embedding_settings=config_data.embedding_settings.dict(),
                preferred_vector_store=config_data.preferred_vector_store,
                vector_store_settings=config_data.vector_store_settings.dict(),
                allowed_connectors=config_data.allowed_connectors,
                connector_settings=config_data.connector_settings.dict(),
                security_settings=config_data.security_settings.dict(),
                features_enabled=config_data.features_enabled.dict(),
                created_at=datetime.utcnow()
            )
            db.add(config)
        else:
            # Update existing configuration
            config.tenant_name = config_data.tenant_name
            config.preferred_llm = config_data.preferred_llm
            config.llm_settings = config_data.llm_settings.dict()
            config.preferred_embedding = config_data.preferred_embedding
            config.embedding_settings = config_data.embedding_settings.dict()
            config.preferred_vector_store = config_data.preferred_vector_store
            config.vector_store_settings = config_data.vector_store_settings.dict()
            config.allowed_connectors = config_data.allowed_connectors
            config.connector_settings = config_data.connector_settings.dict()
            config.security_settings = config_data.security_settings.dict()
            config.features_enabled = config_data.features_enabled.dict()
            config.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(config)
        
        logger.info(f"Updated client configuration for tenant {tenant_id}")
        
        return {
            "success": True,
            "message": "Client configuration updated successfully",
            "tenant_id": tenant_id
        }
        
    except Exception as e:
        logger.error(f"Failed to update client configuration: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update client configuration: {str(e)}"
        )

@router.get("/client-config/providers")
async def get_available_providers(
    _: None = Depends(require_permission(Permission.MANAGE_SYSTEM))
):
    """
    Get available AI providers and their capabilities
    """
    return {
        "llm_providers": [
            {
                "id": "openai",
                "name": "OpenAI GPT-4",
                "description": "Advanced language model with excellent reasoning",
                "models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
                "features": ["chat", "completion", "function_calling"],
                "pricing_tier": "premium"
            },
            {
                "id": "anthropic",
                "name": "Anthropic Claude",
                "description": "Safe and helpful AI assistant",
                "models": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
                "features": ["chat", "completion", "long_context"],
                "pricing_tier": "premium"
            },
            {
                "id": "google",
                "name": "Google Gemini",
                "description": "Multimodal AI with strong performance",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "features": ["chat", "completion", "multimodal"],
                "pricing_tier": "standard"
            },
            {
                "id": "azure",
                "name": "Azure OpenAI",
                "description": "Enterprise-grade OpenAI models",
                "models": ["gpt-4", "gpt-35-turbo"],
                "features": ["chat", "completion", "enterprise"],
                "pricing_tier": "enterprise"
            }
        ],
        "embedding_providers": [
            {
                "id": "openai",
                "name": "OpenAI Embeddings",
                "description": "High-quality text embeddings",
                "models": ["text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large"],
                "dimensions": [1536, 512, 3072],
                "pricing_tier": "standard"
            },
            {
                "id": "sentence-transformers",
                "name": "Sentence Transformers",
                "description": "Open-source embedding models",
                "models": ["all-MiniLM-L6-v2", "all-mpnet-base-v2"],
                "dimensions": [384, 768],
                "pricing_tier": "free"
            },
            {
                "id": "cohere",
                "name": "Cohere Embeddings",
                "description": "Multilingual embedding models",
                "models": ["embed-english-v3.0", "embed-multilingual-v3.0"],
                "dimensions": [1024],
                "pricing_tier": "standard"
            }
        ],
        "vector_stores": [
            {
                "id": "pinecone",
                "name": "Pinecone",
                "description": "Managed vector database",
                "features": ["managed", "scalable", "real_time"],
                "max_dimensions": 20000,
                "pricing_tier": "premium"
            },
            {
                "id": "weaviate",
                "name": "Weaviate",
                "description": "Open-source vector database",
                "features": ["open_source", "graphql", "hybrid_search"],
                "max_dimensions": 65536,
                "pricing_tier": "free"
            },
            {
                "id": "qdrant",
                "name": "Qdrant",
                "description": "High-performance vector search",
                "features": ["high_performance", "filtering", "clustering"],
                "max_dimensions": 65536,
                "pricing_tier": "standard"
            },
            {
                "id": "chroma",
                "name": "ChromaDB",
                "description": "AI-native open-source embedding database",
                "features": ["open_source", "simple", "local"],
                "max_dimensions": 2048,
                "pricing_tier": "free"
            }
        ],
        "data_connectors": [
            {
                "id": "github",
                "name": "GitHub",
                "description": "Git repositories and source code",
                "features": ["oauth", "webhooks", "incremental_sync"],
                "file_types": ["code", "markdown", "documentation"]
            },
            {
                "id": "sharepoint",
                "name": "SharePoint",
                "description": "SharePoint sites and document libraries",
                "features": ["oauth", "permissions", "metadata"],
                "file_types": ["documents", "spreadsheets", "presentations"]
            },
            {
                "id": "onedrive",
                "name": "OneDrive",
                "description": "OneDrive files and folders",
                "features": ["oauth", "file_watching", "sharing"],
                "file_types": ["documents", "images", "videos"]
            },
            {
                "id": "google_drive",
                "name": "Google Drive",
                "description": "Google Drive documents and files",
                "features": ["oauth", "real_time_updates", "collaboration"],
                "file_types": ["docs", "sheets", "slides", "pdfs"]
            },
            {
                "id": "dropbox",
                "name": "Dropbox",
                "description": "Dropbox files and shared folders",
                "features": ["oauth", "file_watching", "sharing"],
                "file_types": ["documents", "images", "archives"]
            },
            {
                "id": "database",
                "name": "Database",
                "description": "SQL databases and data warehouses",
                "features": ["scheduled_sync", "query_based", "schema_detection"],
                "file_types": ["structured_data", "tables", "views"]
            },
            {
                "id": "api",
                "name": "API",
                "description": "REST APIs and web services",
                "features": ["scheduled_sync", "custom_endpoints", "authentication"],
                "file_types": ["json", "xml", "api_responses"]
            },
            {
                "id": "file_upload",
                "name": "File Upload",
                "description": "Manually uploaded documents",
                "features": ["manual_upload", "batch_processing", "metadata"],
                "file_types": ["pdf", "docx", "txt", "csv"]
            }
        ]
    }

@router.post("/client-config/validate")
async def validate_configuration(
    config_data: ClientConfigurationRequest,
    _: None = Depends(require_permission(Permission.MANAGE_SYSTEM))
):
    """
    Validate client configuration without saving
    """
    try:
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": []
        }
        
        # Validate LLM settings
        if config_data.llm_settings.temperature < 0 or config_data.llm_settings.temperature > 2:
            validation_results["errors"].append("Temperature must be between 0 and 2")
            validation_results["valid"] = False
        
        # Validate embedding settings
        if config_data.embedding_settings.chunk_size < 100:
            validation_results["warnings"].append("Chunk size below 100 may result in poor embeddings")
        
        # Validate vector store settings
        if not config_data.vector_store_settings.index_name:
            validation_results["errors"].append("Vector store index name is required")
            validation_results["valid"] = False
        
        # Validate connectors
        if not config_data.allowed_connectors:
            validation_results["warnings"].append("No data connectors enabled - users won't be able to ingest data")
        
        # Validate security settings
        if config_data.security_settings.session_timeout < 300:
            validation_results["warnings"].append("Session timeout below 5 minutes may impact user experience")
        
        return validation_results
        
    except Exception as e:
        logger.error(f"Failed to validate configuration: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to validate configuration: {str(e)}"
        )
