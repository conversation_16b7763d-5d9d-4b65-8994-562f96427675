#!/usr/bin/env python3
"""
Database migration script to add missing columns to existing datasets table
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import ProgrammingError

def get_database_url():
    """Get database URL from environment"""
    return os.getenv('DATABASE_URL')

def migrate_database():
    """Add missing columns to datasets table"""
    database_url = get_database_url()
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        return False
    
    try:
        engine = create_engine(database_url)
        
        # List of columns to add with their definitions
        columns_to_add = [
            ("file_type", "VARCHAR DEFAULT 'csv'"),
            ("content_type", "VARCHAR DEFAULT 'tabular'"),
            ("document_metadata", "TEXT"),
            ("embeddings_data", "TEXT"),
            ("processing_status", "VARCHAR DEFAULT 'completed'"),
            ("word_count", "INTEGER DEFAULT 0"),
            ("character_count", "INTEGER DEFAULT 0"),
            ("has_image", "BOOLEAN DEFAULT FALSE")
        ]
        
        with engine.connect() as conn:
            # Start transaction
            trans = conn.begin()
            
            try:
                for column_name, column_def in columns_to_add:
                    try:
                        # Try to add the column
                        sql = f"ALTER TABLE datasets ADD COLUMN {column_name} {column_def}"
                        conn.execute(text(sql))
                        print(f"✅ Added column: {column_name}")
                    except ProgrammingError as e:
                        if "already exists" in str(e).lower():
                            print(f"⚠️  Column {column_name} already exists, skipping")
                        else:
                            print(f"❌ Error adding column {column_name}: {e}")
                            raise
                
                # Commit all changes
                trans.commit()
                print("✅ Database migration completed successfully!")
                return True
                
            except Exception as e:
                trans.rollback()
                print(f"❌ Migration failed, rolling back: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🔄 Starting database migration...")
    success = migrate_database()
    sys.exit(0 if success else 1)
