"""
Text Chunking Service for AIthentiq
Intelligent text splitting with overlap and metadata preservation
Uses langchain text splitters with custom enhancements
"""

import re
import logging
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)

class ChunkType(Enum):
    PARAGRAPH = "paragraph"
    SENTENCE = "sentence"
    FIXED_SIZE = "fixed_size"
    SEMANTIC = "semantic"
    DOCUMENT_SECTION = "document_section"

@dataclass
class TextChunk:
    """Represents a chunk of text with metadata"""
    text: str
    chunk_index: int
    chunk_type: ChunkType
    start_char: int
    end_char: int
    word_count: int
    char_count: int
    text_hash: str
    metadata: Dict[str, Any]
    source_document: Optional[str] = None
    page_number: Optional[int] = None
    section_title: Optional[str] = None

class TextChunkingService:
    """
    Advanced text chunking service with multiple strategies
    """
    
    def __init__(self):
        self.default_chunk_size = 1000
        self.default_overlap = 200
        self.min_chunk_size = 100
        self.max_chunk_size = 4000
        
        # Initialize chunkers
        self._init_chunkers()
    
    def _init_chunkers(self):
        """Initialize text chunkers based on available libraries"""
        self.chunkers = {}
        
        # Basic chunkers (always available)
        self.chunkers['paragraph'] = self._chunk_by_paragraph
        self.chunkers['sentence'] = self._chunk_by_sentence
        self.chunkers['fixed_size'] = self._chunk_by_fixed_size
        
        # Advanced chunkers (require langchain)
        try:
            from langchain.text_splitter import (
                RecursiveCharacterTextSplitter,
                TokenTextSplitter,
                SpacyTextSplitter
            )
            self.chunkers['recursive'] = self._chunk_recursive
            self.chunkers['token'] = self._chunk_by_token
            logger.info("LangChain text splitters enabled")
        except ImportError:
            logger.warning("LangChain text splitters disabled - install langchain")
        
        # Semantic chunkers (require sentence-transformers)
        try:
            from sentence_transformers import SentenceTransformer
            self.chunkers['semantic'] = self._chunk_semantic
            logger.info("Semantic chunking enabled")
        except ImportError:
            logger.warning("Semantic chunking disabled - install sentence-transformers")
    
    def chunk_text(
        self,
        text: str,
        chunk_strategy: str = "recursive",
        chunk_size: int = None,
        overlap: int = None,
        preserve_metadata: bool = True,
        source_document: str = None
    ) -> List[TextChunk]:
        """
        Chunk text using specified strategy
        
        Args:
            text: Text to chunk
            chunk_strategy: Chunking strategy to use
            chunk_size: Target chunk size in characters
            overlap: Overlap between chunks
            preserve_metadata: Whether to preserve metadata
            source_document: Source document name
            
        Returns:
            List of TextChunk objects
        """
        try:
            # Validate inputs
            if not text or not text.strip():
                return []
            
            # Set defaults
            chunk_size = chunk_size or self.default_chunk_size
            overlap = overlap or self.default_overlap
            
            # Validate chunk strategy
            if chunk_strategy not in self.chunkers:
                logger.warning(f"Unknown chunk strategy: {chunk_strategy}, using 'paragraph'")
                chunk_strategy = 'paragraph'
            
            # Get chunker function
            chunker = self.chunkers[chunk_strategy]
            
            # Perform chunking
            chunks = chunker(
                text=text,
                chunk_size=chunk_size,
                overlap=overlap,
                source_document=source_document
            )
            
            # Post-process chunks
            processed_chunks = self._post_process_chunks(chunks, preserve_metadata)
            
            logger.info(f"Chunked text into {len(processed_chunks)} chunks using {chunk_strategy}")
            return processed_chunks
            
        except Exception as e:
            logger.error(f"Text chunking failed: {e}")
            # Fallback to simple paragraph chunking
            return self._chunk_by_paragraph(text, chunk_size, overlap, source_document)
    
    def _chunk_by_paragraph(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        source_document: str = None
    ) -> List[TextChunk]:
        """Chunk text by paragraphs"""
        paragraphs = re.split(r'\n\s*\n', text.strip())
        chunks = []
        current_chunk = ""
        current_start = 0
        chunk_index = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # Check if adding this paragraph would exceed chunk size
            if current_chunk and len(current_chunk) + len(paragraph) > chunk_size:
                # Create chunk from current content
                if current_chunk:
                    chunk = self._create_chunk(
                        text=current_chunk,
                        chunk_index=chunk_index,
                        chunk_type=ChunkType.PARAGRAPH,
                        start_char=current_start,
                        end_char=current_start + len(current_chunk),
                        source_document=source_document
                    )
                    chunks.append(chunk)
                    chunk_index += 1
                
                # Start new chunk with overlap
                if overlap > 0 and current_chunk:
                    overlap_text = current_chunk[-overlap:]
                    current_chunk = overlap_text + "\n\n" + paragraph
                    current_start = current_start + len(current_chunk) - len(overlap_text) - len(paragraph) - 2
                else:
                    current_chunk = paragraph
                    current_start = text.find(paragraph, current_start)
            else:
                # Add paragraph to current chunk
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
                    current_start = text.find(paragraph)
        
        # Add final chunk
        if current_chunk:
            chunk = self._create_chunk(
                text=current_chunk,
                chunk_index=chunk_index,
                chunk_type=ChunkType.PARAGRAPH,
                start_char=current_start,
                end_char=current_start + len(current_chunk),
                source_document=source_document
            )
            chunks.append(chunk)
        
        return chunks
    
    def _chunk_by_sentence(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        source_document: str = None
    ) -> List[TextChunk]:
        """Chunk text by sentences"""
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+\s+', text)
        chunks = []
        current_chunk = ""
        current_start = 0
        chunk_index = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # Check if adding this sentence would exceed chunk size
            if current_chunk and len(current_chunk) + len(sentence) > chunk_size:
                # Create chunk from current content
                if current_chunk:
                    chunk = self._create_chunk(
                        text=current_chunk,
                        chunk_index=chunk_index,
                        chunk_type=ChunkType.SENTENCE,
                        start_char=current_start,
                        end_char=current_start + len(current_chunk),
                        source_document=source_document
                    )
                    chunks.append(chunk)
                    chunk_index += 1
                
                # Start new chunk
                current_chunk = sentence
                current_start = text.find(sentence, current_start)
            else:
                # Add sentence to current chunk
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence
                    current_start = text.find(sentence)
        
        # Add final chunk
        if current_chunk:
            chunk = self._create_chunk(
                text=current_chunk,
                chunk_index=chunk_index,
                chunk_type=ChunkType.SENTENCE,
                start_char=current_start,
                end_char=current_start + len(current_chunk),
                source_document=source_document
            )
            chunks.append(chunk)
        
        return chunks
    
    def _chunk_by_fixed_size(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        source_document: str = None
    ) -> List[TextChunk]:
        """Chunk text by fixed character size"""
        chunks = []
        chunk_index = 0
        start = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            
            # Try to break at word boundary
            if end < len(text):
                # Look for space within last 100 characters
                space_pos = text.rfind(' ', end - 100, end)
                if space_pos > start:
                    end = space_pos
            
            chunk_text = text[start:end].strip()
            
            if chunk_text:
                chunk = self._create_chunk(
                    text=chunk_text,
                    chunk_index=chunk_index,
                    chunk_type=ChunkType.FIXED_SIZE,
                    start_char=start,
                    end_char=end,
                    source_document=source_document
                )
                chunks.append(chunk)
                chunk_index += 1
            
            # Move start position with overlap
            start = max(start + chunk_size - overlap, end)
        
        return chunks
    
    def _chunk_recursive(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        source_document: str = None
    ) -> List[TextChunk]:
        """Chunk text using LangChain recursive splitter"""
        try:
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=overlap,
                length_function=len,
                separators=["\n\n", "\n", " ", ""]
            )
            
            text_chunks = splitter.split_text(text)
            chunks = []
            
            current_pos = 0
            for i, chunk_text in enumerate(text_chunks):
                # Find position in original text
                start_pos = text.find(chunk_text, current_pos)
                if start_pos == -1:
                    start_pos = current_pos
                
                chunk = self._create_chunk(
                    text=chunk_text,
                    chunk_index=i,
                    chunk_type=ChunkType.PARAGRAPH,
                    start_char=start_pos,
                    end_char=start_pos + len(chunk_text),
                    source_document=source_document
                )
                chunks.append(chunk)
                current_pos = start_pos + len(chunk_text)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Recursive chunking failed: {e}")
            return self._chunk_by_paragraph(text, chunk_size, overlap, source_document)
    
    def _chunk_by_token(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        source_document: str = None
    ) -> List[TextChunk]:
        """Chunk text by token count"""
        try:
            from langchain.text_splitter import TokenTextSplitter
            
            # Convert character size to approximate token count
            token_chunk_size = chunk_size // 4  # Rough approximation
            token_overlap = overlap // 4
            
            splitter = TokenTextSplitter(
                chunk_size=token_chunk_size,
                chunk_overlap=token_overlap
            )
            
            text_chunks = splitter.split_text(text)
            chunks = []
            
            current_pos = 0
            for i, chunk_text in enumerate(text_chunks):
                start_pos = text.find(chunk_text, current_pos)
                if start_pos == -1:
                    start_pos = current_pos
                
                chunk = self._create_chunk(
                    text=chunk_text,
                    chunk_index=i,
                    chunk_type=ChunkType.FIXED_SIZE,
                    start_char=start_pos,
                    end_char=start_pos + len(chunk_text),
                    source_document=source_document
                )
                chunks.append(chunk)
                current_pos = start_pos + len(chunk_text)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Token chunking failed: {e}")
            return self._chunk_by_fixed_size(text, chunk_size, overlap, source_document)
    
    def _chunk_semantic(
        self,
        text: str,
        chunk_size: int,
        overlap: int,
        source_document: str = None
    ) -> List[TextChunk]:
        """Chunk text using semantic similarity"""
        try:
            # For now, fallback to paragraph chunking
            # TODO: Implement semantic chunking using sentence embeddings
            return self._chunk_by_paragraph(text, chunk_size, overlap, source_document)
            
        except Exception as e:
            logger.error(f"Semantic chunking failed: {e}")
            return self._chunk_by_paragraph(text, chunk_size, overlap, source_document)
    
    def _create_chunk(
        self,
        text: str,
        chunk_index: int,
        chunk_type: ChunkType,
        start_char: int,
        end_char: int,
        source_document: str = None
    ) -> TextChunk:
        """Create a TextChunk object with metadata"""
        word_count = len(text.split())
        char_count = len(text)
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        metadata = {
            'chunk_strategy': chunk_type.value,
            'created_at': str(datetime.utcnow())
        }
        
        return TextChunk(
            text=text,
            chunk_index=chunk_index,
            chunk_type=chunk_type,
            start_char=start_char,
            end_char=end_char,
            word_count=word_count,
            char_count=char_count,
            text_hash=text_hash,
            metadata=metadata,
            source_document=source_document
        )
    
    def _post_process_chunks(
        self,
        chunks: List[TextChunk],
        preserve_metadata: bool
    ) -> List[TextChunk]:
        """Post-process chunks to ensure quality"""
        processed_chunks = []
        
        for chunk in chunks:
            # Skip very small chunks
            if chunk.char_count < self.min_chunk_size:
                continue
            
            # Truncate very large chunks
            if chunk.char_count > self.max_chunk_size:
                chunk.text = chunk.text[:self.max_chunk_size]
                chunk.char_count = len(chunk.text)
                chunk.word_count = len(chunk.text.split())
                chunk.end_char = chunk.start_char + chunk.char_count
            
            # Clean up text
            chunk.text = chunk.text.strip()
            
            # Update metadata if needed
            if preserve_metadata:
                chunk.metadata['post_processed'] = True
            
            processed_chunks.append(chunk)
        
        return processed_chunks
    
    def get_chunking_strategies(self) -> List[str]:
        """Get list of available chunking strategies"""
        return list(self.chunkers.keys())
    
    def estimate_chunks(self, text: str, chunk_size: int = None) -> int:
        """Estimate number of chunks for given text"""
        chunk_size = chunk_size or self.default_chunk_size
        return max(1, len(text) // chunk_size)

# Global text chunking service instance
text_chunking_service = TextChunkingService()
