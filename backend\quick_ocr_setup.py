#!/usr/bin/env python3
"""
Quick OCR Setup for AIthentiq - Windows
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
from pathlib import Path

def check_tesseract():
    """Check if Tesseract is installed and working"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Tesseract is already installed!")
            version = result.stdout.split('\n')[0] if result.stdout else "Unknown version"
            print(f"   {version}")
            return True
    except (FileNotFoundError, subprocess.TimeoutExpired):
        pass
    
    print("❌ Tesseract is not installed or not in PATH")
    return False

def download_portable_tesseract():
    """Download portable Tesseract for Windows"""
    print("📥 Downloading portable Tesseract...")
    
    # Portable Tesseract URL
    url = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3.20231005/tesseract-ocr-w64-setup-5.3.3.20231005.exe"
    
    try:
        print("Downloading Tesseract installer...")
        urllib.request.urlretrieve(url, "tesseract_installer.exe")
        print("✅ Download completed!")
        
        print("\n🚀 To install Tesseract:")
        print("1. Run: tesseract_installer.exe")
        print("2. Follow the installation wizard")
        print("3. Make sure to check 'Add to PATH' option")
        print("4. Restart your terminal after installation")
        print("5. Run this script again to verify")
        
        return True
        
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return False

def install_python_packages():
    """Install required Python packages"""
    print("📦 Installing Python OCR packages...")
    
    packages = ['pytesseract', 'pillow', 'opencv-python']
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError:
            print(f"⚠️  {package} installation failed (may already be installed)")
    
    return True

def test_ocr():
    """Test OCR functionality"""
    print("\n🧪 Testing OCR...")
    
    try:
        import pytesseract
        from PIL import Image, ImageDraw, ImageFont
        
        # Create test image
        img = Image.new('RGB', (200, 60), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        draw.text((10, 20), "Hello OCR!", fill='black', font=font)
        
        # Test OCR
        text = pytesseract.image_to_string(img)
        
        if "Hello" in text or "OCR" in text:
            print("✅ OCR is working correctly!")
            print(f"   Extracted: '{text.strip()}'")
            return True
        else:
            print(f"⚠️  OCR extracted: '{text.strip()}'")
            print("   OCR may need calibration")
            return False
            
    except ImportError as e:
        print(f"❌ Python packages missing: {e}")
        return False
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 AIthentiq Quick OCR Setup")
    print("=" * 40)
    
    # Check current status
    if check_tesseract():
        print("\n🧪 Testing OCR functionality...")
        if test_ocr():
            print("\n🎉 OCR is fully working!")
            print("\n📋 Next steps:")
            print("1. Restart your AIthentiq backend")
            print("2. Upload an image file")
            print("3. Ask questions about the image")
            return
        else:
            print("\n⚠️  OCR needs troubleshooting")
    
    print("\n🔧 Setting up OCR...")
    
    # Install Python packages
    install_python_packages()
    
    # Download Tesseract
    print("\n📥 Setting up Tesseract...")
    if download_portable_tesseract():
        print("\n⏳ Please install Tesseract using the downloaded installer")
        print("   Then run this script again to test")
    else:
        print("\n📖 Manual Installation:")
        print("1. Go to: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Download Windows installer")
        print("3. Install and add to PATH")
        print("4. Restart terminal")
        print("5. Run this script again")

if __name__ == "__main__":
    main()
