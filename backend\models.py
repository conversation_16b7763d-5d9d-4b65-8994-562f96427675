from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, Boolean, Float, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database import Base
import secrets

def generate_api_key():
    """Generate a secure API key"""
    return secrets.token_urlsafe(32)

class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    name = Column(String, nullable=True)  # Allow nullable for OAuth users
    password_hash = Column(String, nullable=True)  # Allow nullable for OAuth users
    role = Column(String, default="user")
    subscription_status = Column(String, default="free")
    stripe_customer_id = Column(String, nullable=True)
    referral_code = Column(String, unique=True, index=True)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    datasets = relationship("Dataset", back_populates="user")
    queries = relationship("Query", back_populates="user")
    saved_queries = relationship("SavedQuery", back_populates="user")
    referrals_given = relationship("Referral", foreign_keys="Referral.referrer_id", back_populates="referrer")
    referrals_received = relationship("Referral", foreign_keys="Referral.referee_id", back_populates="referee")
    api_keys = relationship("ApiKey", back_populates="user")
    predictive_models = relationship("PredictiveModel", back_populates="user")
    feedback = relationship("Feedback", back_populates="user")

class Dataset(Base):
    __tablename__ = "datasets"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    name = Column(String)
    columns = Column(Text)  # JSON string of column names
    row_count = Column(Integer)
    parsed_data = Column(Text)  # JSON string of parsed data
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Document-specific fields
    file_type = Column(String)  # csv, xlsx, pdf, docx, txt, md
    content_type = Column(String, default='tabular')  # tabular, document, mixed
    document_metadata = Column(Text)  # JSON string of document metadata
    embeddings_data = Column(Text)  # JSON string of embeddings
    processing_status = Column(String, default='completed')  # processing, completed, failed
    word_count = Column(Integer, default=0)
    character_count = Column(Integer, default=0)

    # Relationships
    user = relationship("User", back_populates="datasets")
    queries = relationship("Query", back_populates="dataset")
    chunks = relationship("DocumentChunk", back_populates="dataset", cascade="all, delete-orphan")

class Query(Base):
    __tablename__ = "queries"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    dataset_id = Column(Integer, ForeignKey("datasets.id"))
    question = Column(Text)
    answer = Column(Text)
    chart_type = Column(String, nullable=True)
    chart_data = Column(Text, nullable=True)  # JSON string of chart data
    trust_score = Column(Text, nullable=True)  # JSON string of trust score data
    reasoning_steps = Column(Text, nullable=True)  # JSON string of reasoning steps
    formula_results = Column(Text, nullable=True)  # JSON string of Excel formula results
    query_name = Column(String, nullable=True)  # Optional name for the query
    is_bookmarked = Column(Boolean, default=False)  # Quick bookmark flag
    tags = Column(Text, nullable=True)  # JSON array of tags
    processing_time = Column(Integer, nullable=True)  # Processing time in milliseconds

    # Enhanced monitoring and quality fields
    response_time_ms = Column(Float, nullable=True)
    token_count = Column(Integer, nullable=True)
    performance_grade = Column(String, nullable=True)  # excellent, good, fair, poor
    hallucination_risk = Column(Float, nullable=True)  # 0.0 to 1.0
    completeness_score = Column(Float, nullable=True)  # 0.0 to 1.0
    source_count = Column(Integer, nullable=True)  # Number of sources used

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="queries")
    dataset = relationship("Dataset", back_populates="queries")
    saved_queries = relationship("SavedQuery", back_populates="query", cascade="all, delete-orphan")
    feedback = relationship("Feedback", back_populates="query")
    source_attributions = relationship("SourceAttribution", back_populates="query", cascade="all, delete-orphan")

class SavedQuery(Base):
    __tablename__ = "saved_queries"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    query_id = Column(Integer, ForeignKey("queries.id"))
    name = Column(String, nullable=True)  # Custom name for saved query
    description = Column(Text, nullable=True)  # Optional description
    tags = Column(Text, nullable=True)  # JSON array of tags
    is_favorite = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="saved_queries")
    query = relationship("Query", back_populates="saved_queries")

class QueryCache(Base):
    __tablename__ = "query_cache"

    id = Column(Integer, primary_key=True, index=True)
    cache_key = Column(String, unique=True, index=True)  # Deterministic hash of question + dataset
    dataset_id = Column(Integer, ForeignKey("datasets.id"))
    question = Column(Text)
    question_hash = Column(String, index=True)  # Hash of normalized question
    dataset_hash = Column(String, index=True)  # Hash of dataset content
    answer = Column(Text)
    chart_type = Column(String, nullable=True)
    chart_data = Column(Text, nullable=True)  # JSON string of chart data
    trust_score = Column(Text, nullable=True)  # JSON string of trust score data
    reasoning_steps = Column(Text, nullable=True)  # JSON string of reasoning steps
    formula_results = Column(Text, nullable=True)  # JSON string of Excel formula results
    include_cot = Column(Boolean, default=False)  # Whether this was a Chain of Thought query
    hit_count = Column(Integer, default=1)  # Number of times this cache entry was used
    last_accessed = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class Conversation(Base):
    __tablename__ = "conversations"

    conversation_id = Column(String, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    dataset_id = Column(Integer, ForeignKey("datasets.id"))
    title = Column(String)
    last_message = Column(Text, nullable=True)
    message_count = Column(Integer, default=0)
    is_starred = Column(Boolean, default=False)
    is_important = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class ConversationMessage(Base):
    __tablename__ = "conversation_messages"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(String, ForeignKey("conversations.conversation_id"))
    role = Column(String)  # 'user' or 'assistant'
    content = Column(Text)
    processing_time = Column(Integer, nullable=True)
    trust_score = Column(Float, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class Referral(Base):
    __tablename__ = "referrals"

    id = Column(Integer, primary_key=True, index=True)
    referrer_id = Column(String, ForeignKey("users.id"))
    referee_id = Column(String, ForeignKey("users.id"))
    redeemed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    referrer = relationship("User", foreign_keys=[referrer_id], back_populates="referrals_given")
    referee = relationship("User", foreign_keys=[referee_id], back_populates="referrals_received")

class ApiKey(Base):
    __tablename__ = "api_keys"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    key = Column(String, unique=True, index=True, default=generate_api_key)
    name = Column(String)
    is_active = Column(Boolean, default=True)
    last_used = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="api_keys")

class PredictiveModel(Base):
    __tablename__ = "predictive_models"

    id = Column(Integer, primary_key=True, index=True)
    model_uuid = Column(String, unique=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    name = Column(String)
    description = Column(Text, nullable=True)
    model_type = Column(String)  # classification, regression, forecasting
    target_column = Column(String)
    feature_columns = Column(Text)  # JSON array of column names
    metrics = Column(Text)  # JSON object of performance metrics
    model_path = Column(String)  # Path to saved model
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="predictive_models")

class Feedback(Base):
    __tablename__ = "feedback"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    query_id = Column(Integer, ForeignKey("queries.id"))
    rating = Column(String)  # 'up', 'down', or 'neutral'
    comment = Column(Text, nullable=True)  # Optional user comment
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="feedback")
    query = relationship("Query", back_populates="feedback")

class PasswordReset(Base):
    __tablename__ = "password_resets"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    reset_token = Column(String, unique=True, index=True)
    expires_at = Column(DateTime(timezone=True))
    used = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User")


class DocumentChunk(Base):
    """
    Professional model for storing document chunks with comprehensive source attribution
    """
    __tablename__ = "document_chunks"

    id = Column(Integer, primary_key=True, index=True)
    dataset_id = Column(Integer, ForeignKey("datasets.id"), index=True)
    chunk_index = Column(Integer, index=True)  # Order within document

    # Content
    text = Column(Text, nullable=False)
    text_hash = Column(String, index=True)  # For deduplication

    # Source Attribution
    source_document = Column(String)  # Original filename
    line_start = Column(Integer)
    line_end = Column(Integer)
    char_start = Column(Integer)
    char_end = Column(Integer)
    page_number = Column(Integer, nullable=True)
    section_title = Column(String, nullable=True)
    section_level = Column(Integer, nullable=True)  # H1=1, H2=2, etc.

    # Chunk Metadata
    chunk_type = Column(String, default='paragraph')  # paragraph, table, code, list, header
    word_count = Column(Integer)
    char_count = Column(Integer)
    language = Column(String, default='en')

    # Vector Embeddings
    embedding_vector = Column(JSON, nullable=True)  # Store as JSON array
    embedding_model = Column(String, nullable=True)  # Model used for embedding

    # Quality Metrics
    readability_score = Column(Float, nullable=True)
    information_density = Column(Float, nullable=True)  # Information per word

    # Processing Metadata
    processing_version = Column(String, default='1.0')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    dataset = relationship("Dataset", back_populates="chunks")
    source_attributions = relationship("SourceAttribution", back_populates="chunk")


class SourceAttribution(Base):
    """
    Professional model for tracking source attribution in query responses
    """
    __tablename__ = "source_attributions"

    id = Column(Integer, primary_key=True, index=True)
    query_id = Column(Integer, ForeignKey("queries.id"), index=True)
    chunk_id = Column(Integer, ForeignKey("document_chunks.id"), index=True)

    # Attribution Details
    relevance_score = Column(Float)  # How relevant this source is to the query
    confidence_score = Column(Float)  # Confidence in the attribution
    rank_position = Column(Integer)  # Ranking in the response (1st, 2nd, etc.)

    # Usage Context
    quoted_text = Column(Text)  # Exact text used in the response
    context_before = Column(Text, nullable=True)  # Text before the quote
    context_after = Column(Text, nullable=True)  # Text after the quote

    # Search Method
    search_method = Column(String)  # semantic, keyword, hybrid
    search_query = Column(Text)  # The query that found this source

    # Verification
    human_verified = Column(Boolean, default=False)
    verification_notes = Column(Text, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    query = relationship("Query", back_populates="source_attributions")
    chunk = relationship("DocumentChunk", back_populates="source_attributions")


class DocumentViewer(Base):
    """
    Model for tracking document viewing sessions and annotations
    """
    __tablename__ = "document_viewers"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    dataset_id = Column(Integer, ForeignKey("datasets.id"))

    # Viewing Session
    session_id = Column(String, unique=True, index=True)
    current_page = Column(Integer, default=1)
    zoom_level = Column(Float, default=1.0)
    scroll_position = Column(Integer, default=0)

    # Annotations
    highlights = Column(JSON, nullable=True)  # Array of highlight objects
    bookmarks = Column(JSON, nullable=True)  # Array of bookmark objects
    notes = Column(JSON, nullable=True)  # Array of note objects

    # Session Metadata
    last_accessed = Column(DateTime(timezone=True), server_default=func.now())
    total_time_spent = Column(Integer, default=0)  # Seconds
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User")
    dataset = relationship("Dataset")
