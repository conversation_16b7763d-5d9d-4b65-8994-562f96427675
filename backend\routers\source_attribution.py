"""
Professional Source Attribution API Endpoints
Provides comprehensive source attribution and context viewing capabilities
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from database import get_db
from models import DocumentChunk, SourceAttribution, Dataset, User
from services.chunk_service import ChunkService
from middleware.api_auth import get_current_user_from_api_key

router = APIRouter(prefix="/api/v1/source-attribution", tags=["source-attribution"])

# Pydantic models for API responses
class ChunkResponse(BaseModel):
    id: int
    text: str
    source_document: Optional[str]
    line_start: Optional[int]
    line_end: Optional[int]
    char_start: Optional[int]
    char_end: Optional[int]
    page_number: Optional[int]
    section_title: Optional[str]
    chunk_type: Optional[str]
    word_count: Optional[int]
    readability_score: Optional[float]
    information_density: Optional[float]
    created_at: Optional[str]

class ContextResponse(BaseModel):
    target_chunk: ChunkResponse
    context_chunks: List[ChunkResponse]
    total_context_chunks: int

class SearchRequest(BaseModel):
    query: str
    limit: int = 10
    search_method: str = "hybrid"  # semantic, keyword, hybrid

class SearchResponse(BaseModel):
    chunks: List[Dict[str, Any]]
    total_results: int
    search_method: str
    processing_time_ms: int

@router.get("/chunks/{dataset_id}", response_model=List[ChunkResponse])
async def get_dataset_chunks(
    dataset_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    chunk_type: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get all chunks for a dataset with pagination and filtering
    """
    # Verify dataset ownership
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.user_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    # Build query
    query = db.query(DocumentChunk).filter(DocumentChunk.dataset_id == dataset_id)
    
    if chunk_type:
        query = query.filter(DocumentChunk.chunk_type == chunk_type)
    
    # Get total count
    total_chunks = query.count()
    
    # Apply pagination
    chunks = query.order_by(DocumentChunk.chunk_index).offset(skip).limit(limit).all()
    
    return [
        ChunkResponse(
            id=chunk.id,
            text=chunk.text,
            source_document=chunk.source_document,
            line_start=chunk.line_start,
            line_end=chunk.line_end,
            char_start=chunk.char_start,
            char_end=chunk.char_end,
            page_number=chunk.page_number,
            section_title=chunk.section_title,
            chunk_type=chunk.chunk_type,
            word_count=chunk.word_count,
            readability_score=chunk.readability_score,
            information_density=chunk.information_density,
            created_at=chunk.created_at.isoformat() if chunk.created_at else None
        )
        for chunk in chunks
    ]

# View Context functionality removed as requested

@router.post("/search/{dataset_id}", response_model=SearchResponse)
async def search_chunks(
    dataset_id: int,
    search_request: SearchRequest,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Search chunks within a dataset using professional search capabilities
    """
    import time
    start_time = time.time()
    
    # Verify dataset ownership
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.user_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    # Perform search using chunk service
    chunk_service = ChunkService()
    
    try:
        results = chunk_service.search_chunks(
            dataset_id=dataset_id,
            query=search_request.query,
            limit=search_request.limit,
            search_method=search_request.search_method,
            db=db
        )
        
        processing_time = int((time.time() - start_time) * 1000)
        
        return SearchResponse(
            chunks=results,
            total_results=len(results),
            search_method=search_request.search_method,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.get("/datasets/{dataset_id}/stats")
async def get_dataset_chunk_stats(
    dataset_id: int,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive statistics about chunks in a dataset
    """
    # Verify dataset ownership
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.user_id == current_user.id
    ).first()

    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")

    # Use chunk service for comprehensive statistics
    chunk_service = ChunkService()

    try:
        stats = chunk_service.get_chunk_statistics(dataset_id, db)
        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            **stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")

@router.get("/datasets/{dataset_id}/insights")
async def get_dataset_insights(
    dataset_id: int,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive document insights including AI analysis
    """
    # Verify dataset ownership
    dataset = db.query(Dataset).filter(
        Dataset.id == dataset_id,
        Dataset.user_id == current_user.id
    ).first()

    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")

    # Use document processor for insights
    from services.document_service import document_processor

    try:
        insights = document_processor.get_document_insights(dataset_id, db)

        if "error" in insights:
            raise HTTPException(status_code=500, detail=insights["error"])

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "file_type": dataset.file_type,
            "content_type": dataset.content_type,
            **insights
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get insights: {str(e)}")

@router.delete("/chunks/{chunk_id}")
async def delete_chunk(
    chunk_id: int,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Delete a specific chunk (admin functionality)
    """
    # Get the chunk
    chunk = db.query(DocumentChunk).filter(DocumentChunk.id == chunk_id).first()
    if not chunk:
        raise HTTPException(status_code=404, detail="Chunk not found")
    
    # Verify dataset ownership
    dataset = db.query(Dataset).filter(
        Dataset.id == chunk.dataset_id,
        Dataset.user_id == current_user.id
    ).first()
    
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found")
    
    # Delete the chunk
    db.delete(chunk)
    db.commit()
    
    return {"message": "Chunk deleted successfully"}

@router.post("/chunks/{chunk_id}/verify")
async def verify_source_attribution(
    chunk_id: int,
    verification_notes: str = "",
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Mark a source attribution as human-verified
    """
    # This would be used in conjunction with SourceAttribution records
    # For now, we'll just return success
    return {
        "message": "Source attribution verified",
        "chunk_id": chunk_id,
        "verified_by": current_user.id,
        "notes": verification_notes
    }
