"""
User Risk Prediction Service
Forecasts user risk levels using machine learning models
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc
import json
import logging

# ML imports
try:
    import xgboost as xgb
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.metrics import classification_report, roc_auc_score
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    logging.warning("ML libraries not available. Risk prediction will use fallback methods.")
    # Create dummy classes for fallback
    class StandardScaler:
        def __init__(self): pass
        def fit_transform(self, data): return data
        def transform(self, data): return data

    class LabelEncoder:
        def __init__(self): pass
        def fit_transform(self, data): return data
        def transform(self, data): return data

import models
from models_predictive_analytics import UserBehaviorLog, RiskPrediction, PredictionFeedback


class UserRiskService:
    """Service for predicting user risk levels"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.feature_columns = []
        self.model_version = "1.0"
        
    def extract_user_features(self, user_id: str, db: Session, days_back: int = 90) -> Dict[str, Any]:
        """Extract features for risk prediction from user behavior"""
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Get user info
            user = db.query(models.User).filter(models.User.id == user_id).first()
            if not user:
                print(f"User {user_id} not found in database")
                return {}

            print(f"Found user {user_id}: {user.email if hasattr(user, 'email') else 'no email'}")
            print(f"User created_at: {user.created_at if hasattr(user, 'created_at') else 'no created_at'}")
            
            # Account age - handle timezone issues
            if user.created_at:
                # Make user.created_at timezone-aware if it's naive
                if user.created_at.tzinfo is None:
                    user_created_at = user.created_at.replace(tzinfo=timezone.utc)
                else:
                    user_created_at = user.created_at
                account_age_days = (end_date - user_created_at).days
            else:
                account_age_days = 30  # Default to 30 days if no created_at
            
            # Query activity
            queries = db.query(models.Query).filter(
                and_(
                    models.Query.user_id == user_id,
                    models.Query.created_at >= start_date
                )
            ).all()
            
            # Dataset activity
            datasets = db.query(models.Dataset).filter(
                and_(
                    models.Dataset.user_id == user_id,
                    models.Dataset.created_at >= start_date
                )
            ).all()
            
            # Behavior logs
            behavior_logs = db.query(UserBehaviorLog).filter(
                and_(
                    UserBehaviorLog.user_id == user_id,
                    UserBehaviorLog.created_at >= start_date
                )
            ).all()
            
            # Calculate features
            features = {
                # User profile features
                'account_age_days': account_age_days,
                'subscription_status': 1 if user.subscription_status == 'premium' else 0,
                'failed_login_attempts': user.failed_login_attempts or 0,
                
                # Activity features
                'total_queries': len(queries),
                'total_datasets': len(datasets),
                'total_behavior_logs': len(behavior_logs),
                
                # Query patterns
                'avg_queries_per_day': len(queries) / max(days_back, 1),
                'queries_last_7_days': len([q for q in queries if (end_date - q.created_at).days <= 7]),
                'queries_last_30_days': len([q for q in queries if (end_date - q.created_at).days <= 30]),
                
                # Dataset patterns
                'avg_datasets_per_day': len(datasets) / max(days_back, 1),
                'datasets_last_7_days': len([d for d in datasets if (end_date - d.created_at).days <= 7]),
                'datasets_last_30_days': len([d for d in datasets if (end_date - d.created_at).days <= 30]),
                
                # Performance patterns
                'avg_response_time': np.mean([q.response_time_ms for q in queries if q.response_time_ms]) if queries else 0,
                'error_rate': len([q for q in queries if q.answer and 'error' in q.answer.lower()]) / max(len(queries), 1),
                
                # Trust score patterns
                'avg_trust_score': 0,  # Will calculate below
                'low_trust_queries': 0,  # Will calculate below
                
                # Feature usage diversity
                'unique_features_used': 0,  # Will calculate below
                'feature_adoption_rate': 0,  # Will calculate below
                
                # Temporal patterns
                'days_since_last_query': (end_date - queries[-1].created_at).days if queries else days_back,
                'days_since_last_dataset': (end_date - datasets[-1].created_at).days if datasets else days_back,
                'activity_consistency': 0,  # Will calculate below
            }
            
            # Calculate trust score features
            trust_scores = []
            low_trust_count = 0
            for query in queries:
                if query.trust_score:
                    try:
                        trust_data = json.loads(query.trust_score) if isinstance(query.trust_score, str) else query.trust_score
                        if isinstance(trust_data, dict) and 'overall_score' in trust_data:
                            score = trust_data['overall_score']
                            trust_scores.append(score)
                            if score < 0.6:  # Low trust threshold
                                low_trust_count += 1
                    except:
                        pass
            
            features['avg_trust_score'] = np.mean(trust_scores) if trust_scores else 0.5
            features['low_trust_queries'] = low_trust_count
            
            # Calculate feature usage diversity
            feature_types = set()
            for log in behavior_logs:
                if log.feature_used:
                    feature_types.add(log.feature_used)
            
            features['unique_features_used'] = len(feature_types)
            features['feature_adoption_rate'] = len(feature_types) / 5  # Assuming 5 main features
            
            # Calculate activity consistency (coefficient of variation)
            if len(queries) > 1:
                daily_queries = {}
                for query in queries:
                    day = query.created_at.date()
                    daily_queries[day] = daily_queries.get(day, 0) + 1
                
                if len(daily_queries) > 1:
                    query_counts = list(daily_queries.values())
                    features['activity_consistency'] = np.std(query_counts) / max(np.mean(query_counts), 1)
                else:
                    features['activity_consistency'] = 0
            
            return features
            
        except Exception as e:
            logging.error(f"Error extracting user features: {e}")
            return {}

    def extract_simple_features(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Extract simple features when full extraction fails"""
        try:
            # Get basic counts
            query_count = db.query(models.Query).filter(models.Query.user_id == user_id).count()
            behavior_count = db.query(UserBehaviorLog).filter(UserBehaviorLog.user_id == user_id).count()

            # Simple feature set
            features = {
                'total_queries': query_count,
                'total_behavior_logs': behavior_count,
                'account_age_days': 30,  # Default
                'avg_trust_score': 0.7,  # Default
                'error_rate': 0.1,  # Default
                'unique_features_used': 3,  # Default
                'activity_consistency': 0.5  # Default
            }

            return features

        except Exception as e:
            logging.error(f"Error extracting simple features: {e}")
            return {}
    
    def calculate_risk_score(self, features: Dict[str, Any]) -> Tuple[float, str, List[str]]:
        """Calculate risk score using rule-based approach (fallback when ML not available)"""
        try:
            risk_factors = []
            risk_score = 0.0
            
            # Account age risk (newer accounts are riskier)
            if features.get('account_age_days', 0) < 7:
                risk_score += 0.3
                risk_factors.append("Very new account")
            elif features.get('account_age_days', 0) < 30:
                risk_score += 0.2
                risk_factors.append("New account")
            
            # Activity level risk
            if features.get('total_queries', 0) == 0:
                risk_score += 0.4
                risk_factors.append("No query activity")
            elif features.get('queries_last_7_days', 0) == 0:
                risk_score += 0.3
                risk_factors.append("No recent activity")
            elif features.get('avg_queries_per_day', 0) < 0.1:
                risk_score += 0.2
                risk_factors.append("Low activity level")
            
            # Failed login attempts
            if features.get('failed_login_attempts', 0) > 3:
                risk_score += 0.3
                risk_factors.append("Multiple failed login attempts")
            
            # Trust score patterns
            if features.get('avg_trust_score', 0.5) < 0.4:
                risk_score += 0.2
                risk_factors.append("Low average trust scores")
            
            if features.get('low_trust_queries', 0) > 5:
                risk_score += 0.1
                risk_factors.append("Multiple low-trust queries")
            
            # Error rate
            if features.get('error_rate', 0) > 0.3:
                risk_score += 0.2
                risk_factors.append("High error rate")
            
            # Feature adoption
            if features.get('unique_features_used', 0) == 0:
                risk_score += 0.2
                risk_factors.append("No feature usage")
            elif features.get('feature_adoption_rate', 0) < 0.2:
                risk_score += 0.1
                risk_factors.append("Low feature adoption")
            
            # Activity consistency
            if features.get('activity_consistency', 0) > 2.0:
                risk_score += 0.1
                risk_factors.append("Inconsistent activity patterns")
            
            # Cap risk score at 1.0
            risk_score = min(risk_score, 1.0)
            
            # Determine risk level
            if risk_score >= 0.7:
                risk_level = "high"
            elif risk_score >= 0.4:
                risk_level = "moderate"
            else:
                risk_level = "low"
            
            return risk_score, risk_level, risk_factors
            
        except Exception as e:
            logging.error(f"Error calculating risk score: {e}")
            return 0.5, "moderate", ["Error in risk calculation"]
    
    def predict_user_risk(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Predict risk level for a specific user"""
        try:
            # Extract features
            features = self.extract_user_features(user_id, db)
            if not features:
                # Fallback: Use simple metrics if feature extraction fails
                features = self.extract_simple_features(user_id, db)
                if not features:
                    return {
                        "error": "Could not extract user features",
                        "risk_score": 0.5,
                        "risk_level": "moderate",
                        "risk_factors": ["Insufficient data"]
                    }
            
            # Calculate risk score
            risk_score, risk_level, risk_factors = self.calculate_risk_score(features)
            
            # Store prediction in database
            prediction = RiskPrediction(
                user_id=user_id,
                risk_score=risk_score,
                risk_level=risk_level,
                risk_factors=risk_factors,
                model_version=self.model_version,
                confidence_score=0.8,  # Rule-based confidence
                prediction_horizon_days=30,
                features_used=features,
                valid_until=datetime.now(timezone.utc) + timedelta(days=7)  # Valid for 7 days
            )
            
            db.add(prediction)
            db.commit()
            db.refresh(prediction)
            
            return {
                "prediction_id": prediction.id,
                "user_id": user_id,
                "risk_score": risk_score,
                "risk_level": risk_level,
                "risk_factors": risk_factors,
                "confidence_score": 0.8,
                "features_used": features,
                "model_version": self.model_version,
                "predicted_at": prediction.predicted_at.isoformat(),
                "valid_until": prediction.valid_until.isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error predicting user risk: {e}")
            return {
                "error": str(e),
                "risk_score": 0.5,
                "risk_level": "moderate",
                "risk_factors": ["Prediction error"]
            }
    
    def get_risk_trends(self, user_id: str, db: Session, days_back: int = 90) -> Dict[str, Any]:
        """Get risk trend analysis for a user"""
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Get historical predictions
            predictions = db.query(RiskPrediction).filter(
                and_(
                    RiskPrediction.user_id == user_id,
                    RiskPrediction.predicted_at >= start_date
                )
            ).order_by(RiskPrediction.predicted_at).all()
            
            if not predictions:
                return {"message": "No historical risk data available"}
            
            # Extract trend data
            dates = [p.predicted_at.isoformat() for p in predictions]
            risk_scores = [p.risk_score for p in predictions]
            risk_levels = [p.risk_level for p in predictions]
            
            # Calculate trend direction
            if len(risk_scores) >= 2:
                recent_avg = np.mean(risk_scores[-3:])  # Last 3 predictions
                older_avg = np.mean(risk_scores[:-3]) if len(risk_scores) > 3 else risk_scores[0]
                
                if recent_avg > older_avg + 0.1:
                    trend_direction = "increasing"
                elif recent_avg < older_avg - 0.1:
                    trend_direction = "decreasing"
                else:
                    trend_direction = "stable"
            else:
                trend_direction = "insufficient_data"
            
            return {
                "user_id": user_id,
                "trend_period_days": days_back,
                "total_predictions": len(predictions),
                "current_risk_score": risk_scores[-1],
                "current_risk_level": risk_levels[-1],
                "trend_direction": trend_direction,
                "risk_score_history": {
                    "dates": dates,
                    "scores": risk_scores,
                    "levels": risk_levels
                },
                "statistics": {
                    "min_risk_score": min(risk_scores),
                    "max_risk_score": max(risk_scores),
                    "avg_risk_score": np.mean(risk_scores),
                    "risk_volatility": np.std(risk_scores)
                }
            }
            
        except Exception as e:
            logging.error(f"Error getting risk trends: {e}")
            return {"error": str(e)}
