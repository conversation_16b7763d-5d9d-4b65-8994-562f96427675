'use client';

import { useState, useEffect } from 'react';
import api from '@/lib/api';

export default function TestAdminPage() {
  const [feedback, setFeedback] = useState<any[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const testAdminAPI = async () => {
    setLoading(true);
    setError('');
    
    try {
      console.log('Testing admin API...');
      
      // Test stats
      console.log('Fetching stats...');
      const statsResponse = await api.get('/feedback/admin/stats');
      console.log('Stats response:', statsResponse.data);
      setStats(statsResponse.data);
      
      // Test feedback list
      console.log('Fetching feedback...');
      const feedbackResponse = await api.get('/feedback/admin/all');
      console.log('Feedback response:', feedbackResponse.data);
      setFeedback(feedbackResponse.data);
      
    } catch (err: any) {
      console.error('Admin API test error:', err);
      setError(err.message || 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testAdminAPI();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Admin API Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <button
            onClick={testAdminAPI}
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Admin API'}
          </button>
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded">
              <p className="text-red-700">Error: {error}</p>
            </div>
          )}
        </div>
        
        {/* Stats */}
        {stats && (
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Statistics</h2>
            <div className="grid grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.total_feedback}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.positive_feedback}</div>
                <div className="text-sm text-gray-600">Positive</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.negative_feedback}</div>
                <div className="text-sm text-gray-600">Negative</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.feedback_with_comments}</div>
                <div className="text-sm text-gray-600">With Comments</div>
              </div>
            </div>
          </div>
        )}
        
        {/* Feedback List */}
        {feedback.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Feedback ({feedback.length} items)</h2>
            <div className="space-y-4">
              {feedback.map((item, index) => (
                <div key={index} className="border border-gray-200 rounded p-4">
                  <div className="flex justify-between items-start mb-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      item.rating === 'up' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.rating === 'up' ? '👍 Positive' : '👎 Negative'}
                    </span>
                    <span className="text-sm text-gray-500">{item.user_email}</span>
                  </div>
                  <div className="text-sm">
                    <div className="font-medium mb-1">Q: {item.question}</div>
                    <div className="text-gray-600 mb-1">A: {item.answer}</div>
                    {item.comment && (
                      <div className="text-gray-700 bg-gray-50 p-2 rounded">
                        Comment: {item.comment}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {!loading && feedback.length === 0 && !error && (
          <div className="bg-white rounded-lg shadow p-6">
            <p className="text-gray-500 text-center">No feedback found</p>
          </div>
        )}
      </div>
    </div>
  );
}
