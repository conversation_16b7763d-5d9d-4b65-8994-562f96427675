'use client';

import { useEffect, useRef, useState } from 'react';
import EnhancedTrustScore from './enhanced-trust-score';
import RatingComponent from './rating-component';
import ClientOnly from '../ui/client-only';
import SourcePanel from '../source-attribution/source-panel';

// Import Plotly dynamically to avoid SSR issues
let Plotly: any;

interface TrustScore {
  overall_score: number;
  factors: string[];
  explanation: string;
}

interface FormulaResult {
  formula: string;
  result: string;
}



interface AnswerCardProps {
  answer: string;
  chartType: string | null;
  chartData: any;
  trustScore?: TrustScore;
  reasoningSteps?: string[];
  formulaResults?: FormulaResult[];
  queryId?: number;
  userId?: string;
  responseTime?: number;
  question?: string;
  sources?: any[];
}

export default function AnswerCard({
  answer,
  chartType,
  chartData,
  trustScore,
  reasoningSteps,
  formulaResults,
  queryId,
  userId,
  responseTime,
  question,
  sources
}: AnswerCardProps) {
  const [showReasoningSteps, setShowReasoningSteps] = useState(false);
  const [showFormulas, setShowFormulas] = useState(false);
  const chartRef = useRef<HTMLDivElement>(null);

  // Load Plotly dynamically on client side only
  useEffect(() => {
    if (typeof window !== 'undefined' && !Plotly) {
      import('plotly.js-dist-min').then((module) => {
        Plotly = module.default;
      }).catch(err => {
        console.error('Failed to load Plotly:', err);
      });
    }
  }, []);

  useEffect(() => {
    if (chartType && chartData && chartRef.current && Plotly) {
      // Clear any existing chart
      chartRef.current.innerHTML = '';

      try {
        // Check if we have Plotly JSON data (new format)
        if (chartData.plotly_json) {
          console.log('Rendering chart with Plotly JSON data');
          const plotlyData = chartData.plotly_json;

          // Use the Plotly JSON data directly
          Plotly.newPlot(
            chartRef.current,
            plotlyData.data || [],
            plotlyData.layout || {},
            { responsive: true, displayModeBar: true }
          );
        } else {
          // Fallback to old format for backward compatibility
          console.log('Rendering chart with legacy format');

          // Create the chart based on the type and data
          switch (chartType) {
          case 'bar':
            Plotly.newPlot(chartRef.current, [
              {
                x: chartData.x,
                y: chartData.y,
                type: 'bar',
                marker: {
                  color: 'rgb(59, 130, 246)',
                },
              },
            ], {
              margin: { t: 30, r: 30, l: 50, b: 80 },
              title: chartData.title || '',
              xaxis: {
                title: chartData.xaxis || '',
                tickangle: -45,
              },
              yaxis: {
                title: chartData.yaxis || '',
              },
            });
            break;

          case 'line':
            Plotly.newPlot(chartRef.current, [
              {
                x: chartData.x,
                y: chartData.y,
                type: 'scatter',
                mode: 'lines+markers',
                marker: {
                  color: 'rgb(59, 130, 246)',
                },
                line: {
                  color: 'rgb(59, 130, 246)',
                },
              },
            ], {
              margin: { t: 30, r: 30, l: 50, b: 50 },
              title: chartData.title || '',
              xaxis: {
                title: chartData.xaxis || '',
              },
              yaxis: {
                title: chartData.yaxis || '',
              },
            });
            break;

          case 'pie':
            Plotly.newPlot(chartRef.current, [
              {
                labels: chartData.labels,
                values: chartData.values,
                type: 'pie',
                marker: {
                  colors: [
                    'rgb(59, 130, 246)',
                    'rgb(16, 185, 129)',
                    'rgb(245, 158, 11)',
                    'rgb(239, 68, 68)',
                    'rgb(139, 92, 246)',
                  ],
                },
              },
            ], {
              margin: { t: 30, r: 30, l: 30, b: 30 },
              title: chartData.title || '',
            });
            break;

          case 'scatter':
            Plotly.newPlot(chartRef.current, [
              {
                x: chartData.x,
                y: chartData.y,
                mode: 'markers',
                type: 'scatter',
                marker: {
                  color: 'rgb(59, 130, 246)',
                  size: 8,
                },
              },
            ], {
              margin: { t: 30, r: 30, l: 50, b: 50 },
              title: chartData.title || '',
              xaxis: {
                title: chartData.xaxis || '',
              },
              yaxis: {
                title: chartData.yaxis || '',
              },
            });
            break;

          default:
            console.warn(`Unsupported chart type: ${chartType}`);
          }
        }
      } catch (err) {
        console.error('Error rendering chart:', err);
      }
    }
  }, [chartType, chartData]);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-black mb-4">Answer</h2>

      {/* Question Display */}
      {question && (
        <div className="mb-4 p-4 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-blue-800">Question:</p>
              <p className="text-sm text-blue-700 mt-1">{question}</p>
            </div>
          </div>
        </div>
      )}

      {/* Response Time Display */}
      {responseTime && (
        <div className="mb-4 flex items-center text-sm text-gray-600">
          <svg className="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Response time: <span className="font-medium text-green-600">{responseTime < 1000 ? `${responseTime}ms` : `${(responseTime / 1000).toFixed(2)}s`}</span></span>
        </div>
      )}

      {trustScore && (
        <div className="mb-4">
          <EnhancedTrustScore trustScore={trustScore} />
        </div>
      )}

      {reasoningSteps && reasoningSteps.length > 0 && (
        <div className="mb-4">
          <button
            className="text-sm text-blue-600 hover:underline mb-2"
            onClick={() => setShowReasoningSteps(!showReasoningSteps)}
          >
            {showReasoningSteps ? 'Hide Reasoning Steps' : 'Show Reasoning Steps'}
          </button>

          {showReasoningSteps && (
            <div className="border rounded-md p-3 bg-gray-50">
              <h3 className="font-medium text-black mb-2">Chain of Thought Reasoning:</h3>
              <ol className="list-decimal pl-5 space-y-2">
                {reasoningSteps.map((step, index) => (
                  <li key={index} className="text-black">{step}</li>
                ))}
              </ol>
            </div>
          )}
        </div>
      )}

      {formulaResults && formulaResults.length > 0 && (
        <div className="mb-4">
          <button
            className="text-sm text-blue-600 hover:underline mb-2"
            onClick={() => setShowFormulas(!showFormulas)}
          >
            {showFormulas ? 'Hide Excel Formulas' : 'Show Excel Formulas'}
          </button>

          {showFormulas && (
            <div className="border rounded-md p-3 bg-gray-50">
              <h3 className="font-medium text-black mb-2">Excel Formulas Used:</h3>
              <div className="space-y-2">
                {formulaResults.map((item, index) => (
                  <div key={index} className="bg-white p-2 rounded border">
                    <div className="font-mono text-sm text-blue-700">{item.formula}</div>
                    <div className="text-sm text-black mt-1">
                      <span className="font-medium">Result:</span> {item.result}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="prose max-w-none">
        <p className="text-black">{answer}</p>
      </div>

      {/* Source Attribution */}
      {sources && sources.length > 0 && (
        <div className="mt-6">
          <SourcePanel sources={sources} />
        </div>
      )}



      {chartType && chartData && (
        <ClientOnly fallback={<div className="mt-6 h-64 bg-gray-100 rounded-lg flex items-center justify-center">Loading chart...</div>}>
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-black mb-3">📊 Visualization</h3>
            <div
              ref={chartRef}
              className="w-full h-64 md:h-80 border border-gray-200 rounded-lg bg-white p-2"
            />
            {chartData.plotly_json && (
              <div className="mt-2 text-xs text-gray-500 flex items-center justify-between">
                <span>Interactive chart powered by Plotly</span>
                <span className="text-green-600">✅ Chart rendered successfully</span>
              </div>
            )}
          </div>
        </ClientOnly>
      )}

      {/* Rating Component */}
      {queryId && userId && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <RatingComponent
            queryId={queryId}
            userId={userId}
            onRatingSubmitted={(rating) => {
              console.log(`User rated this answer: ${rating}`);
            }}
          />
        </div>
      )}
    </div>
  );
}
