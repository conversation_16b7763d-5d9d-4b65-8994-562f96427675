-- Database Migration Script for User-Specific Queries and History
-- Run this script to add user-specific saved queries functionality

-- 1. Create saved_queries table for user-specific saved queries
CREATE TABLE IF NOT EXISTS saved_queries (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    query_id INTEGER NOT NULL,
    name <PERSON><PERSON>HA<PERSON>,
    description TEXT,
    tags TEXT, -- JSON array of tags
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (query_id) REFERENCES queries (id) ON DELETE CASCADE,
    UNIQUE(user_id, query_id) -- Prevent duplicate saves
);

-- 2. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_saved_queries_user_id ON saved_queries(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_queries_query_id ON saved_queries(query_id);
CREATE INDEX IF NOT EXISTS idx_saved_queries_is_favorite ON saved_queries(is_favorite);
CREATE INDEX IF NOT EXISTS idx_saved_queries_created_at ON saved_queries(created_at);

-- 3. Add user_id to query_cache table if not exists (for user-specific caching)
ALTER TABLE query_cache ADD COLUMN IF NOT EXISTS user_id VARCHAR;
CREATE INDEX IF NOT EXISTS idx_query_cache_user_id ON query_cache(user_id);

-- 4. Add query metadata columns to queries table if not exists
ALTER TABLE queries ADD COLUMN IF NOT EXISTS query_name VARCHAR;
ALTER TABLE queries ADD COLUMN IF NOT EXISTS is_bookmarked BOOLEAN DEFAULT FALSE;
ALTER TABLE queries ADD COLUMN IF NOT EXISTS tags TEXT; -- JSON array

-- 5. Create indexes on queries table for better performance
CREATE INDEX IF NOT EXISTS idx_queries_user_id_created_at ON queries(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_queries_dataset_user ON queries(dataset_id, user_id);
CREATE INDEX IF NOT EXISTS idx_queries_is_bookmarked ON queries(is_bookmarked);

-- 6. Create view for user query history with dataset info
CREATE OR REPLACE VIEW user_query_history AS
SELECT 
    q.id,
    q.user_id,
    q.dataset_id,
    d.name as dataset_name,
    q.question,
    q.answer,
    q.chart_type,
    q.chart_data,
    q.trust_score,
    q.reasoning_steps,
    q.formula_results,
    q.query_name,
    q.is_bookmarked,
    q.tags,
    q.created_at,
    CASE WHEN sq.id IS NOT NULL THEN TRUE ELSE FALSE END as is_saved
FROM queries q
LEFT JOIN datasets d ON q.dataset_id = d.id
LEFT JOIN saved_queries sq ON q.id = sq.query_id AND q.user_id = sq.user_id
ORDER BY q.created_at DESC;

-- 7. Create view for user saved queries with full details
CREATE OR REPLACE VIEW user_saved_queries AS
SELECT 
    sq.id as saved_query_id,
    sq.user_id,
    sq.name as saved_name,
    sq.description,
    sq.tags as saved_tags,
    sq.is_favorite,
    sq.created_at as saved_at,
    q.id as query_id,
    q.dataset_id,
    d.name as dataset_name,
    q.question,
    q.answer,
    q.chart_type,
    q.chart_data,
    q.trust_score,
    q.reasoning_steps,
    q.formula_results,
    q.created_at as query_created_at
FROM saved_queries sq
JOIN queries q ON sq.query_id = q.id
LEFT JOIN datasets d ON q.dataset_id = d.id
ORDER BY sq.created_at DESC;

-- 8. Sample data verification queries (run these to test)
-- SELECT COUNT(*) as total_queries FROM queries;
-- SELECT COUNT(*) as total_saved_queries FROM saved_queries;
-- SELECT user_id, COUNT(*) as query_count FROM queries GROUP BY user_id;
-- SELECT * FROM user_query_history WHERE user_id = 'demo-user-12345' LIMIT 5;
-- SELECT * FROM user_saved_queries WHERE user_id = 'demo-user-12345' LIMIT 5;
