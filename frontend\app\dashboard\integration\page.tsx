'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
// Temporarily disabled for deployment
// import ApiKeyManager from '@/components/integration/api-key-manager';
// import ApiDocumentation from '@/components/integration/api-documentation';

export default function IntegrationPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<string>('api-keys');

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-black mb-2">API & Integrations</h1>
        <p className="text-md text-black mb-8">Manage API keys and integrate AskData.ai with your applications</p>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('api-keys')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'api-keys'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                API Keys
              </button>
              <button
                onClick={() => setActiveTab('documentation')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'documentation'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Documentation
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'api-keys' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-black mb-4">API Keys</h2>
            <p className="text-gray-600">API key management is temporarily disabled during deployment. This feature will be available soon.</p>
          </div>
        )}
        {activeTab === 'documentation' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-black mb-4">API Documentation</h2>
            <p className="text-gray-600">API documentation is temporarily disabled during deployment. This feature will be available soon.</p>
          </div>
        )}
      </div>
    </div>
  );
}
