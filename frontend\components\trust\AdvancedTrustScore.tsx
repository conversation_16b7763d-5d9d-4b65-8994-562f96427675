'use client';

import React, { useState } from 'react';
import {
  Star,
  Clock,
  FileText,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface AdvancedTrustScoreProps {
  trustScore: {
    overall_score: number;
    explanation: string;
    factors?: string[];
    processing_time_ms?: number;
  };
  responseTime?: number; // In seconds
  sources?: any[];
  onFeedback?: (feedback: 'up' | 'down' | 'neutral', comment?: string) => void;
}

const AdvancedTrustScore: React.FC<AdvancedTrustScoreProps> = ({
  trustScore,
  responseTime,
  sources = [],
  onFeedback
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Get trust level description
  const getTrustDescription = (score: number) => {
    if (score >= 0.8) return 'High reliability, can be trusted';
    if (score >= 0.6) return 'Moderate reliability, verify key points';
    return 'Low reliability, requires verification';
  };

  // Get trust level color
  const getTrustColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="border border-gray-300 rounded-lg p-3 mb-4 bg-white"
         style={{ fontFamily: 'monospace' }}>
      {/* Single Row Layout */}
      <div className="grid grid-cols-3 gap-4">
        {/* Trust Score Box */}
        <div className="flex flex-col">
          <div className="flex items-center space-x-2 mb-1">
            <Star className={`w-4 h-4 ${getTrustColor(trustScore.overall_score)}`} />
            <span className="text-sm font-medium">Trust: {(trustScore.overall_score * 100).toFixed(0)}%</span>
          </div>
          <div className="text-xs text-gray-600">
            {getTrustDescription(trustScore.overall_score)}
          </div>
        </div>

        {/* Response Time Box */}
        <div className="flex flex-col">
          <div className="flex items-center space-x-2 mb-1">
            <Clock className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium">
              Response: {responseTime ? `${responseTime.toFixed(2)}s` : 'N/A'}
            </span>
          </div>
          <div className="text-xs text-gray-600">
            Processing complete
          </div>
        </div>

        {/* Sources Box */}
        <div className="flex flex-col">
          <div className="flex items-center space-x-2 mb-1">
            <FileText className="w-4 h-4 text-purple-600" />
            <span className="text-sm font-medium">
              Sources ({sources.length})
            </span>
            {sources.length > 0 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="ml-auto"
              >
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                )}
              </button>
            )}
          </div>
          <div className="text-xs text-gray-600">
            {sources.length > 0 ? 'Click to expand' : 'No sources available'}
          </div>
        </div>
      </div>

      {/* Expanded Sources Details */}
      {isExpanded && sources.length > 0 && (
        <div className="border-t border-gray-200 pt-3 mt-3">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Sources & Citations</h4>
          <div className="space-y-2">
            {sources.slice(0, 3).map((source, index) => (
              <div key={index} className="text-xs text-gray-600 p-2 bg-gray-50 rounded">
                <div className="font-medium">{source.filename || `Source ${index + 1}`}</div>
                <div className="text-gray-500 mt-1">
                  {source.content ? source.content.substring(0, 100) + '...' : 'Content preview not available'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedTrustScore;
