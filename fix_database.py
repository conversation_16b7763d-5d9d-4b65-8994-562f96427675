#!/usr/bin/env python3
"""
Emergency database schema fix for AIthentiq PostgreSQL database
Run this script to add missing columns and tables
"""

import os
import psycopg2
from urllib.parse import urlparse

def fix_database_schema():
    """Fix the PostgreSQL database schema"""
    
    # Database URL from environment or default
    database_url = os.getenv('DATABASE_URL', 'postgresql://aithentiq_user:your_password@localhost/aithentiq_db')
    
    # Parse the database URL
    url = urlparse(database_url)
    
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=url.hostname,
            port=url.port or 5432,
            database=url.path[1:],  # Remove leading slash
            user=url.username,
            password=url.password
        )
        
        cursor = conn.cursor()
        
        print("🔧 Fixing PostgreSQL database schema...")
        
        # Step 1: Add missing columns to users table
        user_columns = [
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS name VARCHAR",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0",
            "ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP"
        ]
        
        for sql in user_columns:
            try:
                cursor.execute(sql)
                conn.commit()
                print(f"✅ {sql}")
            except Exception as e:
                print(f"❌ {sql} - {str(e)}")
                conn.rollback()
        
        # Step 2: Create password_resets table
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS password_resets (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    reset_token VARCHAR UNIQUE NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()
            print("✅ Created password_resets table")
        except Exception as e:
            print(f"❌ Error creating password_resets table: {str(e)}")
            conn.rollback()
        
        # Step 3: Create api_keys table
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS api_keys (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    key VARCHAR UNIQUE NOT NULL,
                    name VARCHAR,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_used TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()
            print("✅ Created api_keys table")
        except Exception as e:
            print(f"❌ Error creating api_keys table: {str(e)}")
            conn.rollback()
        
        # Step 4: Create demo user and API key
        try:
            import secrets
            import hashlib
            
            # Check if demo user exists
            cursor.execute("SELECT id FROM users WHERE email = '<EMAIL>'")
            demo_user = cursor.fetchone()
            
            demo_user_id = "demo-user-12345"
            
            if not demo_user:
                # Hash password (simple version)
                password_hash = hashlib.sha256("demo123".encode()).hexdigest()
                
                cursor.execute("""
                    INSERT INTO users (id, email, name, password_hash, role, subscription_status, referral_code, failed_login_attempts, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                """, (demo_user_id, "<EMAIL>", "Demo User", password_hash, "user", "free", "DEMO123", 0))
                conn.commit()
                print("✅ Created demo user")
            else:
                demo_user_id = demo_user[0]
                print("⚠️ Demo user already exists")
            
            # Check if API key exists
            cursor.execute("SELECT key FROM api_keys WHERE user_id = %s", (demo_user_id,))
            api_key_row = cursor.fetchone()
            
            if not api_key_row:
                # Create API key
                api_key = secrets.token_urlsafe(32)
                cursor.execute("""
                    INSERT INTO api_keys (user_id, key, name, is_active, created_at)
                    VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
                """, (demo_user_id, api_key, "Demo API Key", True))
                conn.commit()
                print(f"✅ Created demo API key: {api_key}")
                print(f"🔑 User ID: {demo_user_id}")
            else:
                api_key = api_key_row[0]
                print(f"⚠️ Demo API key already exists: {api_key}")
                print(f"🔑 User ID: {demo_user_id}")
        
        except Exception as e:
            print(f"❌ Error creating demo data: {str(e)}")
            conn.rollback()
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Database schema fix completed!")
        print("\n📝 Next steps:")
        print("1. Use the API key shown above in your frontend requests")
        print("2. Add X-API-Key header to all /api/v1/* requests")
        print("3. Test the /api/v1/ask endpoint")
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        print("\n💡 Make sure you have the correct DATABASE_URL environment variable set")
        print("   Example: export DATABASE_URL='postgresql://user:pass@host:port/dbname'")

if __name__ == "__main__":
    fix_database_schema()
