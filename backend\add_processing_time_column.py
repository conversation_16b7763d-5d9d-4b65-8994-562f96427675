#!/usr/bin/env python3
"""
Add processing_time column to queries table
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import ProgrammingError

def add_processing_time_column():
    """Add processing_time column to queries table if it doesn't exist"""
    
    # Get database URL from environment
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        return False
    
    try:
        # Create engine
        engine = create_engine(database_url)
        
        # Check if column exists
        with engine.connect() as conn:
            try:
                # Try to select the column - if it fails, column doesn't exist
                result = conn.execute(text("SELECT processing_time FROM queries LIMIT 1"))
                print("✅ processing_time column already exists")
                return True
            except ProgrammingError:
                # Column doesn't exist, add it
                print("📝 Adding processing_time column to queries table...")
                conn.execute(text("ALTER TABLE queries ADD COLUMN processing_time INTEGER"))
                conn.commit()
                print("✅ processing_time column added successfully")
                return True
                
    except Exception as e:
        print(f"❌ Error adding processing_time column: {str(e)}")
        return False

if __name__ == "__main__":
    success = add_processing_time_column()
    sys.exit(0 if success else 1)
