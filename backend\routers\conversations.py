"""
Conversation API endpoints for RAG Chat
"""

from fastapi import APIRouter, HTTPException, Depends, Head<PERSON>
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from services.conversation_service import conversation_service, ConversationContext
from services import llm_service
from database import get_db
import models

router = APIRouter(prefix="/api/v1/conversations", tags=["conversations"])

# Request/Response Models
class CreateConversationRequest(BaseModel):
    dataset_id: int
    title: Optional[str] = None

class AddMessageRequest(BaseModel):
    conversation_id: str
    content: str
    role: str = "user"

class ConversationalQueryRequest(BaseModel):
    conversation_id: str
    question: str
    dataset_id: int
    include_sources: bool = True

class UpdateConversationRequest(BaseModel):
    title: str

class MarkImportantRequest(BaseModel):
    is_important: bool

class StarConversationRequest(BaseModel):
    is_starred: bool

class ConversationResponse(BaseModel):
    conversation_id: str
    title: str
    dataset_id: int
    user_id: str
    created_at: str
    updated_at: str
    message_count: int
    last_message: Optional[str] = None

class MessageResponse(BaseModel):
    id: str
    role: str
    content: str
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None

class ConversationDetailResponse(BaseModel):
    conversation_id: str
    title: str
    dataset_id: int
    user_id: str
    created_at: str
    updated_at: str
    messages: List[MessageResponse]

# Helper function to get user ID from API key
def get_user_id_from_api_key(x_api_key: str = Header(...)) -> str:
    # Look up API key in database
    from database import get_db
    from models import ApiKey

    db = next(get_db())
    try:
        api_key_record = db.query(ApiKey).filter(ApiKey.key == x_api_key, ApiKey.is_active == True).first()
        if api_key_record:
            return api_key_record.user_id
        else:
            raise HTTPException(status_code=401, detail="Invalid API key")
    finally:
        db.close()

@router.post("/", response_model=Dict[str, str])
async def create_conversation(
    request: CreateConversationRequest,
    user_id: str = Depends(get_user_id_from_api_key),
    db: Session = Depends(get_db)
):
    """Create a new conversation"""
    try:
        import uuid
        conversation_id = str(uuid.uuid4())
        title = request.title or f"Chat about Dataset {request.dataset_id}"

        # Create conversation in database
        conversation = models.Conversation(
            conversation_id=conversation_id,
            user_id=user_id,
            dataset_id=request.dataset_id,
            title=title,
            message_count=0
        )
        db.add(conversation)
        db.commit()

        return {
            "conversation_id": conversation_id,
            "message": "Conversation created successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[ConversationResponse])
async def get_conversations(
    limit: int = 50,
    user_id: str = Depends(get_user_id_from_api_key),
    db: Session = Depends(get_db)
):
    """Get all conversations for the current user"""
    try:
        # Get conversations from database
        conversations = db.query(models.Conversation).filter(
            models.Conversation.user_id == user_id
        ).order_by(models.Conversation.updated_at.desc()).limit(limit).all()

        response = []
        for conv in conversations:
            response.append(ConversationResponse(
                conversation_id=conv.conversation_id,
                title=conv.title,
                dataset_id=conv.dataset_id,
                user_id=conv.user_id,
                created_at=conv.created_at.isoformat(),
                updated_at=conv.updated_at.isoformat(),
                message_count=conv.message_count,
                last_message=conv.last_message
            ))

        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{conversation_id}", response_model=ConversationDetailResponse)
async def get_conversation(
    conversation_id: str,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Get a specific conversation with all messages"""
    try:
        conversation = conversation_service.get_conversation(conversation_id)
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        if conversation.user_id != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        messages = [
            MessageResponse(
                id=msg.id,
                role=msg.role,
                content=msg.content,
                timestamp=msg.timestamp.isoformat(),
                metadata=msg.metadata
            )
            for msg in conversation.messages
        ]
        
        return ConversationDetailResponse(
            conversation_id=conversation.conversation_id,
            title=conversation.title,
            dataset_id=conversation.dataset_id,
            user_id=conversation.user_id,
            created_at=conversation.created_at.isoformat(),
            updated_at=conversation.updated_at.isoformat(),
            messages=messages
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/query", response_model=Dict[str, Any])
async def conversational_query(
    request: ConversationalQueryRequest,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Ask a question in the context of a conversation"""
    try:
        # Verify conversation ownership
        conversation = conversation_service.get_conversation(request.conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        if conversation.user_id != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Add user message to conversation
        conversation_service.add_message(
            conversation_id=request.conversation_id,
            role="user",
            content=request.question
        )
        
        # Build contextual query
        contextual_query = conversation_service.build_contextual_query(
            conversation_id=request.conversation_id,
            current_query=request.question
        )

        # Get answer using existing LLM service
        db = next(get_db())
        try:
            # Get dataset
            dataset = db.query(models.Dataset).filter(models.Dataset.id == request.dataset_id).first()
            if not dataset:
                raise HTTPException(status_code=404, detail="Dataset not found")

            # Process query using LLM service
            result = await llm_service.process_query(
                question=contextual_query,
                dataset_data=dataset.data,
                dataset_id=request.dataset_id,
                user_id=user_id
            )
        finally:
            db.close()
        
        # Add assistant response to conversation
        conversation_service.add_message(
            conversation_id=request.conversation_id,
            role="assistant",
            content=result["answer"],
            metadata={
                "sources": result.get("sources", []),
                "trust_score": result.get("trust_score"),
                "processing_time": result.get("processing_time")
            }
        )
        
        return {
            "success": True,
            "conversation_id": request.conversation_id,
            "answer": result["answer"],
            "sources": result.get("sources", []),
            "trust_score": result.get("trust_score"),
            "processing_time": result.get("processing_time"),
            "context_used": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{conversation_id}", response_model=Dict[str, str])
async def update_conversation(
    conversation_id: str,
    request: UpdateConversationRequest,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Update conversation title"""
    try:
        success = conversation_service.update_conversation_title(
            conversation_id=conversation_id,
            title=request.title,
            user_id=user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found or access denied")
        
        return {"message": "Conversation updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{conversation_id}", response_model=Dict[str, str])
async def delete_conversation(
    conversation_id: str,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Delete a conversation"""
    try:
        success = conversation_service.delete_conversation(
            conversation_id=conversation_id,
            user_id=user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found or access denied")
        
        return {"message": "Conversation deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{conversation_id}/summary", response_model=Dict[str, Any])
async def get_conversation_summary(
    conversation_id: str,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Get conversation summary and statistics"""
    try:
        conversation = conversation_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        if conversation.user_id != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        summary = conversation_service.get_conversation_summary(conversation_id)
        return summary
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/search/{query}", response_model=List[ConversationResponse])
async def search_conversations(
    query: str,
    limit: int = 20,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Search conversations by content"""
    try:
        conversations = conversation_service.search_conversations(
            user_id=user_id,
            query=query,
            limit=limit
        )
        
        response = []
        for conv in conversations:
            last_message = conv.messages[-1].content if conv.messages else None
            response.append(ConversationResponse(
                conversation_id=conv.conversation_id,
                title=conv.title,
                dataset_id=conv.dataset_id,
                user_id=conv.user_id,
                created_at=conv.created_at.isoformat(),
                updated_at=conv.updated_at.isoformat(),
                message_count=len(conv.messages),
                last_message=last_message
            ))
        
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{conversation_id}/important", response_model=Dict[str, str])
async def mark_conversation_important(
    conversation_id: str,
    request: MarkImportantRequest,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Mark conversation as important"""
    try:
        success = conversation_service.mark_conversation_important(
            conversation_id=conversation_id,
            is_important=request.is_important,
            user_id=user_id
        )

        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found or access denied")

        return {"message": f"Conversation {'marked as important' if request.is_important else 'unmarked as important'}"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{conversation_id}/star", response_model=Dict[str, str])
async def star_conversation(
    conversation_id: str,
    request: StarConversationRequest,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Star/unstar conversation"""
    try:
        success = conversation_service.star_conversation(
            conversation_id=conversation_id,
            is_starred=request.is_starred,
            user_id=user_id
        )

        if not success:
            raise HTTPException(status_code=404, detail="Conversation not found or access denied")

        return {"message": f"Conversation {'starred' if request.is_starred else 'unstarred'}"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/important", response_model=List[ConversationResponse])
async def get_important_conversations(
    limit: int = 20,
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Get important conversations for the current user"""
    try:
        conversations = conversation_service.get_user_conversations(user_id, limit=1000)
        important_conversations = [conv for conv in conversations if conv.is_important]

        response = []
        for conv in important_conversations[:limit]:
            last_message = conv.messages[-1].content if conv.messages else None
            response.append(ConversationResponse(
                conversation_id=conv.conversation_id,
                title=conv.title,
                dataset_id=conv.dataset_id,
                user_id=conv.user_id,
                created_at=conv.created_at.isoformat(),
                updated_at=conv.updated_at.isoformat(),
                message_count=len(conv.messages),
                last_message=last_message
            ))

        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/summary", response_model=Dict[str, Any])
async def get_conversation_analytics(
    user_id: str = Depends(get_user_id_from_api_key)
):
    """Get conversation analytics for the user"""
    try:
        analytics = conversation_service.get_conversation_analytics(user_id)
        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
