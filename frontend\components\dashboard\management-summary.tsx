'use client';

import { useState } from 'react';

interface ManagementSummaryProps {
  datasetsCount: number;
  queriesCount: number;
  savedQueriesCount: number;
}

export default function ManagementSummary({ 
  datasetsCount, 
  queriesCount, 
  savedQueriesCount 
}: ManagementSummaryProps) {
  const [showFeatures, setShowFeatures] = useState(false);

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Data Management</h3>
        <button
          onClick={() => setShowFeatures(!showFeatures)}
          className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
        >
          {showFeatures ? 'Hide Features' : 'Show Features'}
        </button>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{datasetsCount}</div>
          <div className="text-sm text-gray-600">Datasets</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{queriesCount}</div>
          <div className="text-sm text-gray-600">Query History</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">{savedQueriesCount}</div>
          <div className="text-sm text-gray-600">Saved Queries</div>
        </div>
      </div>

      {showFeatures && (
        <div className="mt-4 space-y-3">
          <h4 className="font-medium text-gray-800">✨ New Management Features:</h4>
          
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span><strong>Dataset Management:</strong> View details, see column structure, delete with confirmation</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              <span><strong>Query History:</strong> View full details, see answers & trust scores, delete individual queries</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
              <span><strong>Saved Queries:</strong> View details, manage favorites, delete with confirmation</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 bg-red-500 rounded-full"></span>
              <span><strong>Safety Features:</strong> Confirmation dialogs, cascade deletion warnings</span>
            </div>
          </div>

          <div className="mt-3 p-3 bg-blue-100 rounded-md">
            <p className="text-xs text-blue-800">
              💡 <strong>Tip:</strong> Look for the "View Details" (👁️) and "Delete" (🗑️) buttons next to each item!
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
