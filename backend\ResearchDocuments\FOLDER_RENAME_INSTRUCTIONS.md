# 📁 ROOT FOLDER RENAME INSTRUCTIONS

## 🎯 Goal: Rename 'AskData - Copy (2)' → 'AIthentiq'

### ✅ Pre-Rename Checklist

I have already updated all internal references:
- ✅ Virtual environment paths updated
- ✅ Database configurations updated  
- ✅ Deployment configurations ready
- ✅ All scripts and configs use "AIthentiq"

### 🚀 OPTION 1: Automated PowerShell (Recommended)

1. **Right-click on `rename_folder.ps1`**
2. **Select "Run with PowerShell"**
3. **Follow the prompts**

The script will:
- Stop running services
- Rename the folder automatically
- Navigate to the new location
- Confirm everything is working

### 🖱️ OPTION 2: Manual File Explorer

1. **Stop all services first:**
   ```
   Double-click: stop_services.bat
   ```

2. **Close all applications using this folder:**
   - VS Code / IDEs
   - Command prompts
   - File explorers

3. **Open File Explorer:**
   - Navigate to: `C:\Users\<USER>\Documents\augment-projects\`
   - Find folder: `AskData - Copy (2)`

4. **Rename the folder:**
   - Right-click on `AskData - Copy (2)`
   - Select "Rename"
   - Type: `AIthentiq`
   - Press Enter

5. **Test the rename:**
   - Navigate into `AIthentiq` folder
   - Double-click `start_both.bat`

### 💻 OPTION 3: Command Prompt

1. **Open Command Prompt as Administrator**

2. **Navigate to parent directory:**
   ```cmd
   cd "C:\Users\<USER>\Documents\augment-projects"
   ```

3. **Rename the folder:**
   ```cmd
   ren "AskData - Copy (2)" "AIthentiq"
   ```

4. **Navigate to new folder:**
   ```cmd
   cd AIthentiq
   ```

5. **Test:**
   ```cmd
   start_both.bat
   ```

### 🔍 Verification

After renaming, run the verification:
```bash
python verify_rename.py
```

Should show:
- ✅ Root folder correctly named!
- ✅ All configuration files updated
- ✅ Virtual environment paths correct
- ✅ Database configurations ready

### 🎉 Success Indicators

After successful rename:
1. **Folder name:** `AIthentiq` (not "AskData - Copy (2)")
2. **Application starts:** `start_both.bat` works
3. **URLs work:** 
   - Frontend: http://localhost:3000
   - Backend: http://localhost:8000
4. **No errors:** All services start cleanly

### 🚨 Troubleshooting

#### "Access Denied" Error:
- Close all applications using the folder
- Run Command Prompt as Administrator
- Use PowerShell script instead

#### "Folder in use" Error:
- Run `stop_services.bat`
- Close VS Code and terminals
- Wait 30 seconds, try again

#### Virtual Environment Issues:
- Delete `backend/venv` folder
- Recreate: `cd backend && python -m venv venv`
- Reinstall: `pip install -r requirements.txt`

### 📋 Post-Rename Checklist

- [ ] Folder renamed to `AIthentiq`
- [ ] Application starts successfully
- [ ] Frontend loads at http://localhost:3000
- [ ] Backend responds at http://localhost:8000
- [ ] Database connections work
- [ ] No error messages in console

### 🎊 You're Done!

Your AIthentIQ application is now properly renamed and ready for:
- ✅ Local development
- ✅ Git commits
- ✅ Deployment to hosting platforms
- ✅ Sharing with others

**Welcome to AIthentIQ! 🚀**
