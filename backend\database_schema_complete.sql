-- AIthentiq Phase 1 Complete Database Schema
-- Multi-tenant PostgreSQL schema with proper CASCADE DELETE relationships

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (for clean migration)
DROP TABLE IF EXISTS tenant_source_attributions CASCADE;
DROP TABLE IF EXISTS tenant_queries CASCADE;
DROP TABLE IF EXISTS tenant_documents CASCADE;
DROP TABLE IF EXISTS tenant_datasets CASCADE;
DROP TABLE IF EXISTS tenant_users CASCADE;
DROP TABLE IF EXISTS tenants CASCADE;

-- Create Tenants table (root table)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    domain VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    subscription_plan VARCHAR(20) DEFAULT 'free' CHECK (subscription_plan IN ('free', 'starter', 'professional', 'enterprise')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'cancelled')),
    
    -- Limits
    max_users INTEGER DEFAULT 5,
    max_datasets INTEGER DEFAULT 10,
    max_storage_gb DECIMAL(10,2) DEFAULT 1.0,
    
    -- Configuration
    settings JSONB DEFAULT '{}',
    features JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    CONSTRAINT tenants_name_check CHECK (length(name) >= 1),
    CONSTRAINT tenants_domain_check CHECK (length(domain) >= 1)
);

-- Create Tenant Users table
CREATE TABLE tenant_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- User Identity
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    avatar_url VARCHAR(500),
    
    -- Authentication
    auth_provider VARCHAR(50) DEFAULT 'email',
    auth_provider_id VARCHAR(255),
    password_hash VARCHAR(255),
    
    -- Role and Permissions (Phase 1 requirement)
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user', 'guest')),
    permissions JSONB DEFAULT '{
        "can_upload_datasets": true,
        "can_create_queries": true,
        "can_manage_connectors": false,
        "can_view_analytics": false,
        "can_manage_users": false
    }',
    
    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    email_verified BOOLEAN DEFAULT false,
    
    -- Subscription
    subscription_status VARCHAR(20) DEFAULT 'free',
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT tenant_users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT tenant_users_name_check CHECK (length(name) >= 1)
);

-- Create Tenant Datasets table
CREATE TABLE tenant_datasets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES tenant_users(id) ON DELETE CASCADE,
    
    -- Dataset Identity
    name VARCHAR(255) NOT NULL,
    description TEXT,
    filename VARCHAR(255),
    
    -- File Information
    file_type VARCHAR(20),
    content_type VARCHAR(20) DEFAULT 'tabular' CHECK (content_type IN ('tabular', 'document', 'mixed')),
    file_size_bytes BIGINT DEFAULT 0,
    
    -- Processing Status
    processing_status VARCHAR(20) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    processing_error TEXT,
    
    -- Content Metrics
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    row_count INTEGER DEFAULT 0,
    column_count INTEGER DEFAULT 0,
    
    -- Metadata
    document_metadata JSONB DEFAULT '{}',
    embeddings_data JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tenant_datasets_name_check CHECK (length(name) >= 1),
    CONSTRAINT tenant_datasets_file_size_check CHECK (file_size_bytes >= 0)
);

-- Create Tenant Documents table (for document chunks)
CREATE TABLE tenant_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    dataset_id UUID NOT NULL REFERENCES tenant_datasets(id) ON DELETE CASCADE,
    
    -- Document Content
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    
    -- Chunk Information
    chunk_index INTEGER DEFAULT 0,
    chunk_size INTEGER DEFAULT 0,
    overlap_size INTEGER DEFAULT 0,
    
    -- Embedding Information
    embedding_vector VECTOR(1536), -- OpenAI ada-002 dimension
    embedding_model VARCHAR(100),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    source_reference VARCHAR(500),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tenant_documents_content_check CHECK (length(content) >= 1),
    CONSTRAINT tenant_documents_chunk_index_check CHECK (chunk_index >= 0)
);

-- Create Tenant Queries table
CREATE TABLE tenant_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES tenant_users(id) ON DELETE CASCADE,
    dataset_id UUID REFERENCES tenant_datasets(id) ON DELETE CASCADE,
    
    -- Query Content
    question TEXT NOT NULL,
    answer TEXT,
    
    -- Query Classification
    query_type VARCHAR(50) DEFAULT 'general' CHECK (query_type IN ('general', 'analytical', 'chart', 'formula', 'summary')),
    query_name VARCHAR(255),
    
    -- Response Data
    chart_type VARCHAR(50),
    chart_data JSONB,
    trust_score_data JSONB,
    reasoning_steps JSONB,
    formula_results JSONB,
    
    -- Query Metadata
    tags JSONB DEFAULT '[]',
    is_bookmarked BOOLEAN DEFAULT false,
    is_saved BOOLEAN DEFAULT false,
    
    -- Performance Metrics
    processing_time_ms INTEGER,
    token_count INTEGER,
    source_count INTEGER,
    
    -- Quality Metrics
    trust_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    hallucination_risk DECIMAL(3,2),
    completeness_score DECIMAL(3,2),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tenant_queries_question_check CHECK (length(question) >= 1),
    CONSTRAINT tenant_queries_trust_score_check CHECK (trust_score >= 0 AND trust_score <= 1),
    CONSTRAINT tenant_queries_confidence_score_check CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- Create Tenant Source Attributions table (for citations)
CREATE TABLE tenant_source_attributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    query_id UUID NOT NULL REFERENCES tenant_queries(id) ON DELETE CASCADE,
    document_id UUID REFERENCES tenant_documents(id) ON DELETE CASCADE,
    
    -- Attribution Information
    source_text TEXT NOT NULL,
    attributed_text TEXT NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.0,
    
    -- Position Information
    start_position INTEGER DEFAULT 0,
    end_position INTEGER DEFAULT 0,
    
    -- Metadata
    attribution_type VARCHAR(50) DEFAULT 'direct',
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT tenant_source_attributions_source_text_check CHECK (length(source_text) >= 1),
    CONSTRAINT tenant_source_attributions_attributed_text_check CHECK (length(attributed_text) >= 1),
    CONSTRAINT tenant_source_attributions_confidence_check CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- Create indexes for performance
CREATE INDEX idx_tenants_domain ON tenants(domain);
CREATE INDEX idx_tenants_status ON tenants(status);

CREATE INDEX idx_tenant_users_tenant_id ON tenant_users(tenant_id);
CREATE INDEX idx_tenant_users_email ON tenant_users(email);
CREATE INDEX idx_tenant_users_role ON tenant_users(role);
CREATE INDEX idx_tenant_users_status ON tenant_users(status);

CREATE INDEX idx_tenant_datasets_tenant_id ON tenant_datasets(tenant_id);
CREATE INDEX idx_tenant_datasets_user_id ON tenant_datasets(user_id);
CREATE INDEX idx_tenant_datasets_processing_status ON tenant_datasets(processing_status);
CREATE INDEX idx_tenant_datasets_created_at ON tenant_datasets(created_at);

CREATE INDEX idx_tenant_documents_tenant_id ON tenant_documents(tenant_id);
CREATE INDEX idx_tenant_documents_dataset_id ON tenant_documents(dataset_id);
CREATE INDEX idx_tenant_documents_content_hash ON tenant_documents(content_hash);

CREATE INDEX idx_tenant_queries_tenant_id ON tenant_queries(tenant_id);
CREATE INDEX idx_tenant_queries_user_id ON tenant_queries(user_id);
CREATE INDEX idx_tenant_queries_dataset_id ON tenant_queries(dataset_id);
CREATE INDEX idx_tenant_queries_query_type ON tenant_queries(query_type);
CREATE INDEX idx_tenant_queries_is_bookmarked ON tenant_queries(is_bookmarked);
CREATE INDEX idx_tenant_queries_created_at ON tenant_queries(created_at);

CREATE INDEX idx_tenant_source_attributions_tenant_id ON tenant_source_attributions(tenant_id);
CREATE INDEX idx_tenant_source_attributions_query_id ON tenant_source_attributions(query_id);
CREATE INDEX idx_tenant_source_attributions_document_id ON tenant_source_attributions(document_id);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenant_users_updated_at BEFORE UPDATE ON tenant_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenant_datasets_updated_at BEFORE UPDATE ON tenant_datasets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenant_queries_updated_at BEFORE UPDATE ON tenant_queries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO tenants (name, domain, display_name, subscription_plan) VALUES 
('AIthentiq Demo', 'demo', 'AIthentiq Demo Organization', 'enterprise')
ON CONFLICT (domain) DO NOTHING;

-- Get the demo tenant ID for sample user
DO $$
DECLARE
    demo_tenant_id UUID;
BEGIN
    SELECT id INTO demo_tenant_id FROM tenants WHERE domain = 'demo';
    
    IF demo_tenant_id IS NOT NULL THEN
        INSERT INTO tenant_users (tenant_id, email, name, role, permissions) VALUES 
        (demo_tenant_id, '<EMAIL>', 'Demo Admin', 'admin', '{
            "can_upload_datasets": true,
            "can_create_queries": true,
            "can_manage_connectors": true,
            "can_view_analytics": true,
            "can_manage_users": true
        }'),
        (demo_tenant_id, '<EMAIL>', 'Demo User', 'user', '{
            "can_upload_datasets": true,
            "can_create_queries": true,
            "can_manage_connectors": false,
            "can_view_analytics": false,
            "can_manage_users": false
        }'),
        (demo_tenant_id, '<EMAIL>', 'Demo Guest', 'guest', '{
            "can_upload_datasets": false,
            "can_create_queries": true,
            "can_manage_connectors": false,
            "can_view_analytics": false,
            "can_manage_users": false
        }')
        ON CONFLICT (email) DO NOTHING;
    END IF;
END $$;

-- Verify schema creation
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename LIKE 'tenant%'
ORDER BY tablename;

-- Display success message
SELECT 'AIthentiq Phase 1 Database Schema Created Successfully!' as status,
       'All tables created with proper CASCADE DELETE relationships' as details;
