import axios from 'axios';

// Point to backend - use environment variable or fallback to localhost for development
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Log the API URL for debugging in production
console.log('🔗 API URL:', API_URL);

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Access-Control-Allow-Origin': '*',
  },
  // Add timeout and proxy settings
  timeout: 300000, // 5 minutes for large dataset operations
  withCredentials: false, // Don't send cookies
});

// Helper function to get user ID from session
// Note: This function should be used within React components that have access to session
export const getUserId = (session?: any) => {
  // Use real user ID from NextAuth session
  if (session?.user?.id) {
    return session.user.id;
  }

  // No fallback - require real authentication
  throw new Error('User not authenticated');
};

// Helper function to get API key from session
export const getUserApiKey = (session?: any) => {
  // Use real API key from NextAuth session with type assertion
  const userWithApiKey = session?.user as any;
  if (userWithApiKey?.apiKey) {
    return userWithApiKey.apiKey;
  }

  // Fallback: Create a temporary API key request
  if (session?.user?.id) {
    console.warn('⚠️ No API key in session, will attempt to create one');
    return 'temp-key-' + session.user.id; // Temporary identifier
  }

  // No session at all
  throw new Error('User not authenticated');
};

// Create API instance with dynamic headers
export const createApiInstance = (session?: any) => {
  return axios.create({
    baseURL: API_URL,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'X-API-Key': getUserApiKey(session),
    },
    timeout: 300000,
    withCredentials: false,
  });
};

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`, config.data);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status}`, response.data);
    return response;
  },
  (error) => {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('❌ API Response Error:', {
        url: error.config?.url,
        method: error.config?.method,
        data: error.response.data,
        status: error.response.status,
        headers: error.response.headers,
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error('❌ API Request Error: No response received', {
        url: error.config?.url,
        method: error.config?.method,
        message: 'Backend server may be down or unreachable'
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('❌ API Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default api;
