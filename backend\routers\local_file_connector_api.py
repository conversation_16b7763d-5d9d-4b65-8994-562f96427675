"""
Local File Connector API Router
Handles local and network path document ingestion for RAG
"""

from fastapi import APIRouter, Depends, HTTPException, Header, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
import logging
from datetime import datetime
import uuid
import os

from database import get_db
from services.local_file_connector import LocalFileConnector, ConnectorConfig, create_local_connector

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/local-file-connector", tags=["Local File Connector"])

def get_user_id_from_header(x_user_id: str = Header(None)) -> str:
    """Simple user ID extraction from header"""
    if not x_user_id:
        return "104938478886224793097"  # Default user ID for testing
    return x_user_id

class LocalConnectorCreate(BaseModel):
    """Request to create a local file connector"""
    name: str = Field(..., description="Connector name")
    description: Optional[str] = Field(None, description="Connector description")
    base_path: str = Field(..., description="Base path to scan (local or network)")
    include_extensions: List[str] = Field(default=[], description="File extensions to include (e.g., ['.pdf', '.docx'])")
    exclude_extensions: List[str] = Field(default=['.tmp', '.log', '.cache'], description="File extensions to exclude")
    max_file_size_mb: int = Field(default=100, description="Maximum file size in MB")
    recursive: bool = Field(default=True, description="Scan subdirectories recursively")
    follow_symlinks: bool = Field(default=False, description="Follow symbolic links")
    exclude_patterns: List[str] = Field(default=['__pycache__', '.git', 'node_modules'], description="Patterns to exclude")
    auto_sync: bool = Field(default=True, description="Enable automatic synchronization")
    sync_frequency: str = Field(default="daily", description="Sync frequency: hourly, daily, weekly")

class PathValidationRequest(BaseModel):
    """Request to validate a path"""
    path: str = Field(..., description="Path to validate")
    recursive: bool = Field(default=True, description="Check recursively")

class SyncRequest(BaseModel):
    """Request to sync a connector"""
    force_full_sync: bool = Field(default=False, description="Force full resync")

@router.post("/validate-path")
async def validate_path(
    request: PathValidationRequest,
    user_id: str = Depends(get_user_id_from_header)
):
    """
    Validate a local or network path before creating connector
    """
    try:
        # Create temporary connector for validation
        temp_config = ConnectorConfig(
            base_path=request.path,
            include_extensions=[],
            exclude_extensions=[],
            max_file_size_mb=100,
            recursive=request.recursive,
            follow_symlinks=False,
            exclude_patterns=[]
        )
        
        temp_connector = LocalFileConnector("temp", temp_config)
        validation_result = temp_connector.validate_path()
        
        return {
            "success": True,
            "validation": validation_result,
            "recommendations": {
                "suggested_extensions": [".pdf", ".docx", ".txt", ".md", ".html"],
                "performance_tips": [
                    "Consider excluding large binary files",
                    "Use specific file extensions for better performance",
                    "Exclude temporary and cache directories"
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"Path validation error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Path validation failed: {str(e)}"
        )

@router.post("/")
async def create_local_connector(
    connector_data: LocalConnectorCreate,
    background_tasks: BackgroundTasks,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Create a new local file connector
    """
    try:
        # Validate path first
        connector = create_local_connector(
            connector_id=str(uuid.uuid4()),
            base_path=connector_data.base_path,
            include_extensions=connector_data.include_extensions,
            exclude_extensions=connector_data.exclude_extensions,
            max_file_size_mb=connector_data.max_file_size_mb,
            recursive=connector_data.recursive,
            follow_symlinks=connector_data.follow_symlinks,
            exclude_patterns=connector_data.exclude_patterns
        )
        
        validation = connector.validate_path()
        if not validation["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid path: {validation['error']}"
            )
        
        # Store connector configuration in database
        from sqlalchemy import text
        
        connector_id = str(uuid.uuid4())
        db.execute(text("""
            INSERT INTO local_file_connectors (
                id, user_id, name, description, base_path, 
                include_extensions, exclude_extensions, max_file_size_mb,
                recursive, follow_symlinks, exclude_patterns,
                auto_sync, sync_frequency, status, created_at
            ) VALUES (
                :id, :user_id, :name, :description, :base_path,
                :include_extensions, :exclude_extensions, :max_file_size_mb,
                :recursive, :follow_symlinks, :exclude_patterns,
                :auto_sync, :sync_frequency, 'active', :created_at
            )
        """), {
            "id": connector_id,
            "user_id": user_id,
            "name": connector_data.name,
            "description": connector_data.description,
            "base_path": connector_data.base_path,
            "include_extensions": ",".join(connector_data.include_extensions),
            "exclude_extensions": ",".join(connector_data.exclude_extensions),
            "max_file_size_mb": connector_data.max_file_size_mb,
            "recursive": connector_data.recursive,
            "follow_symlinks": connector_data.follow_symlinks,
            "exclude_patterns": ",".join(connector_data.exclude_patterns),
            "auto_sync": connector_data.auto_sync,
            "sync_frequency": connector_data.sync_frequency,
            "created_at": datetime.utcnow()
        })
        
        db.commit()
        
        # Start initial sync in background
        if connector_data.auto_sync:
            background_tasks.add_task(
                perform_initial_sync,
                connector_id,
                user_id,
                connector_data.dict()
            )
        
        return {
            "success": True,
            "connector_id": connector_id,
            "message": "Local file connector created successfully",
            "validation": validation,
            "initial_sync_started": connector_data.auto_sync
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating local connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create connector: {str(e)}"
        )

@router.get("/")
async def list_local_connectors(
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    List all local file connectors for user
    """
    try:
        from sqlalchemy import text
        
        connectors = db.execute(text("""
            SELECT 
                id, name, description, base_path, status,
                auto_sync, sync_frequency, created_at, last_sync_at,
                total_documents, total_size_mb, last_error
            FROM local_file_connectors 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC
        """), {"user_id": user_id}).fetchall()
        
        result = []
        for conn in connectors:
            result.append({
                "id": conn.id,
                "name": conn.name,
                "description": conn.description,
                "base_path": conn.base_path,
                "status": conn.status,
                "auto_sync": conn.auto_sync,
                "sync_frequency": conn.sync_frequency,
                "created_at": conn.created_at.isoformat() if conn.created_at else None,
                "last_sync_at": conn.last_sync_at.isoformat() if conn.last_sync_at else None,
                "total_documents": conn.total_documents or 0,
                "total_size_mb": float(conn.total_size_mb or 0),
                "last_error": conn.last_error,
                "connector_type": "local_file"
            })
        
        return {"connectors": result}
        
    except Exception as e:
        logger.error(f"Error listing local connectors: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list connectors: {str(e)}"
        )

@router.get("/{connector_id}")
async def get_local_connector(
    connector_id: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a local file connector
    """
    try:
        from sqlalchemy import text
        
        connector = db.execute(text("""
            SELECT * FROM local_file_connectors 
            WHERE id = :connector_id AND user_id = :user_id
        """), {
            "connector_id": connector_id,
            "user_id": user_id
        }).fetchone()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="Connector not found"
            )
        
        # Get document statistics
        doc_stats = db.execute(text("""
            SELECT 
                COUNT(*) as total_docs,
                SUM(file_size) as total_size,
                COUNT(CASE WHEN needs_processing = true THEN 1 END) as pending_processing
            FROM documents 
            WHERE connector_id = :connector_id
        """), {"connector_id": connector_id}).fetchone()
        
        return {
            "id": connector.id,
            "name": connector.name,
            "description": connector.description,
            "base_path": connector.base_path,
            "include_extensions": connector.include_extensions.split(",") if connector.include_extensions else [],
            "exclude_extensions": connector.exclude_extensions.split(",") if connector.exclude_extensions else [],
            "max_file_size_mb": connector.max_file_size_mb,
            "recursive": connector.recursive,
            "follow_symlinks": connector.follow_symlinks,
            "exclude_patterns": connector.exclude_patterns.split(",") if connector.exclude_patterns else [],
            "auto_sync": connector.auto_sync,
            "sync_frequency": connector.sync_frequency,
            "status": connector.status,
            "created_at": connector.created_at.isoformat() if connector.created_at else None,
            "last_sync_at": connector.last_sync_at.isoformat() if connector.last_sync_at else None,
            "last_error": connector.last_error,
            "statistics": {
                "total_documents": doc_stats.total_docs if doc_stats else 0,
                "total_size_bytes": doc_stats.total_size if doc_stats else 0,
                "total_size_mb": round((doc_stats.total_size or 0) / (1024 * 1024), 2),
                "pending_processing": doc_stats.pending_processing if doc_stats else 0
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting local connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get connector: {str(e)}"
        )

@router.post("/{connector_id}/sync")
async def sync_local_connector(
    connector_id: str,
    sync_request: SyncRequest,
    background_tasks: BackgroundTasks,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Manually trigger sync for local file connector
    """
    try:
        from sqlalchemy import text
        
        # Get connector configuration
        connector = db.execute(text("""
            SELECT * FROM local_file_connectors 
            WHERE id = :connector_id AND user_id = :user_id
        """), {
            "connector_id": connector_id,
            "user_id": user_id
        }).fetchone()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="Connector not found"
            )
        
        # Start sync in background
        background_tasks.add_task(
            perform_sync,
            connector_id,
            user_id,
            sync_request.force_full_sync
        )
        
        return {
            "success": True,
            "message": f"Sync started for connector {connector.name}",
            "connector_id": connector_id,
            "force_full_sync": sync_request.force_full_sync,
            "sync_started_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting sync: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start sync: {str(e)}"
        )

async def perform_initial_sync(connector_id: str, user_id: str, config: Dict[str, Any]):
    """Perform initial sync for a new connector"""
    await perform_sync(connector_id, user_id, force_full=True)

async def perform_sync(connector_id: str, user_id: str, force_full: bool = False):
    """Perform sync operation for a connector"""
    from database import SessionLocal
    
    db = SessionLocal()
    try:
        from sqlalchemy import text
        
        # Get connector configuration
        connector = db.execute(text("""
            SELECT * FROM local_file_connectors 
            WHERE id = :connector_id AND user_id = :user_id
        """), {
            "connector_id": connector_id,
            "user_id": user_id
        }).fetchone()
        
        if not connector:
            logger.error(f"Connector {connector_id} not found")
            return
        
        # Update sync status
        db.execute(text("""
            UPDATE local_file_connectors 
            SET sync_status = 'syncing', last_error = NULL
            WHERE id = :connector_id
        """), {"connector_id": connector_id})
        db.commit()
        
        # Create connector instance
        file_connector = create_local_connector(
            connector_id=connector_id,
            base_path=connector.base_path,
            include_extensions=connector.include_extensions.split(",") if connector.include_extensions else [],
            exclude_extensions=connector.exclude_extensions.split(",") if connector.exclude_extensions else [],
            max_file_size_mb=connector.max_file_size_mb,
            recursive=connector.recursive,
            follow_symlinks=connector.follow_symlinks,
            exclude_patterns=connector.exclude_patterns.split(",") if connector.exclude_patterns else []
        )
        
        # Perform sync
        sync_stats = file_connector.sync_to_database(db, user_id)
        
        # Update connector with sync results
        db.execute(text("""
            UPDATE local_file_connectors SET
                sync_status = 'completed',
                last_sync_at = :last_sync_at,
                total_documents = :total_documents,
                total_size_mb = :total_size_mb
            WHERE id = :connector_id
        """), {
            "connector_id": connector_id,
            "last_sync_at": datetime.utcnow(),
            "total_documents": sync_stats["total_found"],
            "total_size_mb": 0  # Will be calculated separately
        })
        
        db.commit()
        logger.info(f"Sync completed for connector {connector_id}: {sync_stats}")
        
    except Exception as e:
        logger.error(f"Sync failed for connector {connector_id}: {e}")
        
        # Update error status
        db.execute(text("""
            UPDATE local_file_connectors 
            SET sync_status = 'error', last_error = :error
            WHERE id = :connector_id
        """), {
            "connector_id": connector_id,
            "error": str(e)
        })
        db.commit()
        
    finally:
        db.close()
