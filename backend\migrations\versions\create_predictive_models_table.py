"""create_predictive_models_table

Revision ID: 4
Revises: 3
Create Date: 2023-05-05 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4'
down_revision = '3'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'predictive_models',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('model_uuid', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('model_type', sa.String(), nullable=False),
        sa.Column('target_column', sa.String(), nullable=False),
        sa.Column('feature_columns', sa.Text(), nullable=False),
        sa.Column('metrics', sa.Text(), nullable=False),
        sa.Column('model_path', sa.String(), nullable=False),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_predictive_models_id'), 'predictive_models', ['id'], unique=False)
    op.create_index(op.f('ix_predictive_models_model_uuid'), 'predictive_models', ['model_uuid'], unique=True)


def downgrade():
    op.drop_index(op.f('ix_predictive_models_model_uuid'), table_name='predictive_models')
    op.drop_index(op.f('ix_predictive_models_id'), table_name='predictive_models')
    op.drop_table('predictive_models')
