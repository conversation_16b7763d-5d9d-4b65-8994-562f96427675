"""
Enterprise RAG Service for AIthentiq
Advanced retrieval-augmented generation with multi-LLM support, caching, and optimization
"""

import os
import json
import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import redis
from concurrent.futures import ThreadPoolExecutor
import numpy as np

logger = logging.getLogger(__name__)

class RAGStrategy(Enum):
    SIMPLE = "simple"
    HYBRID = "hybrid"
    ADAPTIVE = "adaptive"
    MULTI_QUERY = "multi_query"
    HIERARCHICAL = "hierarchical"

class LLMProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"

@dataclass
class RAGContext:
    query: str
    tenant_id: str
    user_id: str
    dataset_ids: List[str]
    strategy: RAGStrategy
    llm_provider: LLMProvider
    max_chunks: int = 10
    similarity_threshold: float = 0.7
    temperature: float = 0.7
    max_tokens: int = 2048
    include_sources: bool = True
    use_cache: bool = True

@dataclass
class RetrievalResult:
    chunk_id: str
    content: str
    score: float
    metadata: Dict[str, Any]
    source_document: str
    chunk_index: int

@dataclass
class RAGResponse:
    answer: str
    sources: List[RetrievalResult]
    confidence_score: float
    processing_time_ms: float
    llm_provider: str
    tokens_used: int
    cache_hit: bool
    strategy_used: str
    query_id: str

class EnterpriseRAGService:
    """Enterprise-grade RAG service with advanced features"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.llm_providers = self._init_llm_providers()
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
        self.cache_ttl = 3600  # 1 hour
        
        # Performance optimization
        self.chunk_cache = {}
        self.embedding_cache = {}
        
        # Load balancing for LLM providers
        self.provider_health = {provider: True for provider in LLMProvider}
        self.provider_response_times = {provider: [] for provider in LLMProvider}
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis for caching"""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/3")
            client = redis.from_url(redis_url, decode_responses=True)
            client.ping()
            logger.info("RAG Redis cache initialized")
            return client
        except Exception as e:
            logger.warning(f"RAG Redis cache unavailable: {e}")
            return None
    
    def _init_llm_providers(self) -> Dict[LLMProvider, Any]:
        """Initialize LLM provider clients"""
        providers = {}
        
        # OpenAI
        if os.getenv("OPENAI_API_KEY"):
            try:
                import openai
                providers[LLMProvider.OPENAI] = openai
                logger.info("OpenAI provider initialized")
            except ImportError:
                logger.warning("OpenAI not available")
        
        # Anthropic
        if os.getenv("ANTHROPIC_API_KEY"):
            try:
                import anthropic
                providers[LLMProvider.ANTHROPIC] = anthropic.Anthropic(
                    api_key=os.getenv("ANTHROPIC_API_KEY")
                )
                logger.info("Anthropic provider initialized")
            except ImportError:
                logger.warning("Anthropic not available")
        
        # Google
        if os.getenv("GOOGLE_API_KEY"):
            try:
                import google.generativeai as genai
                genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
                providers[LLMProvider.GOOGLE] = genai
                logger.info("Google provider initialized")
            except ImportError:
                logger.warning("Google provider not available")
        
        # Azure OpenAI
        if os.getenv("AZURE_OPENAI_KEY"):
            try:
                import openai
                providers[LLMProvider.AZURE] = openai
                logger.info("Azure OpenAI provider initialized")
            except ImportError:
                logger.warning("Azure OpenAI not available")
        
        return providers
    
    async def process_query(self, context: RAGContext) -> RAGResponse:
        """Process RAG query with enterprise features"""
        start_time = time.time()
        query_id = hashlib.md5(f"{context.query}{context.tenant_id}{time.time()}".encode()).hexdigest()
        
        try:
            # Check cache first
            if context.use_cache:
                cached_response = await self._get_cached_response(context)
                if cached_response:
                    cached_response.query_id = query_id
                    return cached_response
            
            # Retrieve relevant chunks
            retrieval_results = await self._retrieve_chunks(context)
            
            if not retrieval_results:
                return RAGResponse(
                    answer="I couldn't find relevant information to answer your question.",
                    sources=[],
                    confidence_score=0.0,
                    processing_time_ms=(time.time() - start_time) * 1000,
                    llm_provider="none",
                    tokens_used=0,
                    cache_hit=False,
                    strategy_used=context.strategy.value,
                    query_id=query_id
                )
            
            # Generate response using LLM
            llm_response = await self._generate_response(context, retrieval_results)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence(context, retrieval_results, llm_response)
            
            # Create response
            response = RAGResponse(
                answer=llm_response["answer"],
                sources=retrieval_results,
                confidence_score=confidence_score,
                processing_time_ms=(time.time() - start_time) * 1000,
                llm_provider=llm_response["provider"],
                tokens_used=llm_response["tokens_used"],
                cache_hit=False,
                strategy_used=context.strategy.value,
                query_id=query_id
            )
            
            # Cache response
            if context.use_cache:
                await self._cache_response(context, response)
            
            return response
            
        except Exception as e:
            logger.error(f"RAG query processing failed: {e}")
            return RAGResponse(
                answer=f"I encountered an error processing your query: {str(e)}",
                sources=[],
                confidence_score=0.0,
                processing_time_ms=(time.time() - start_time) * 1000,
                llm_provider="error",
                tokens_used=0,
                cache_hit=False,
                strategy_used=context.strategy.value,
                query_id=query_id
            )
    
    async def _retrieve_chunks(self, context: RAGContext) -> List[RetrievalResult]:
        """Retrieve relevant chunks using specified strategy"""
        if context.strategy == RAGStrategy.SIMPLE:
            return await self._simple_retrieval(context)
        elif context.strategy == RAGStrategy.HYBRID:
            return await self._hybrid_retrieval(context)
        elif context.strategy == RAGStrategy.ADAPTIVE:
            return await self._adaptive_retrieval(context)
        elif context.strategy == RAGStrategy.MULTI_QUERY:
            return await self._multi_query_retrieval(context)
        elif context.strategy == RAGStrategy.HIERARCHICAL:
            return await self._hierarchical_retrieval(context)
        else:
            return await self._simple_retrieval(context)
    
    async def _simple_retrieval(self, context: RAGContext) -> List[RetrievalResult]:
        """Simple vector similarity retrieval"""
        try:
            from services.vector_storage_service import vector_storage_service
            from services.embedding_service import create_embedding_service
            
            # Generate query embedding
            embedding_service = create_embedding_service("openai")
            query_embedding = await embedding_service.generate_embeddings([context.query])
            
            if not query_embedding:
                return []
            
            # Search vector store
            vector_store = vector_storage_service.get_vector_store(context.tenant_id)
            search_results = vector_store.search(
                query_vector=query_embedding[0],
                top_k=context.max_chunks,
                filter_metadata={"dataset_id": context.dataset_ids} if context.dataset_ids else None
            )
            
            # Convert to RetrievalResult objects
            results = []
            for result in search_results:
                if result["score"] >= context.similarity_threshold:
                    results.append(RetrievalResult(
                        chunk_id=result["id"],
                        content=result["metadata"].get("text", ""),
                        score=result["score"],
                        metadata=result["metadata"],
                        source_document=result["metadata"].get("source_document", ""),
                        chunk_index=result["metadata"].get("chunk_index", 0)
                    ))
            
            return results
            
        except Exception as e:
            logger.error(f"Simple retrieval failed: {e}")
            return []
    
    async def _hybrid_retrieval(self, context: RAGContext) -> List[RetrievalResult]:
        """Hybrid retrieval combining vector and keyword search"""
        try:
            # Get vector results
            vector_results = await self._simple_retrieval(context)
            
            # Get keyword results (simplified implementation)
            keyword_results = await self._keyword_search(context)
            
            # Combine and rerank results
            combined_results = self._combine_results(vector_results, keyword_results)
            
            return combined_results[:context.max_chunks]
            
        except Exception as e:
            logger.error(f"Hybrid retrieval failed: {e}")
            return await self._simple_retrieval(context)
    
    async def _adaptive_retrieval(self, context: RAGContext) -> List[RetrievalResult]:
        """Adaptive retrieval that adjusts based on query characteristics"""
        try:
            # Analyze query to determine best strategy
            query_analysis = self._analyze_query(context.query)
            
            if query_analysis["is_factual"]:
                return await self._simple_retrieval(context)
            elif query_analysis["is_complex"]:
                return await self._hierarchical_retrieval(context)
            else:
                return await self._hybrid_retrieval(context)
                
        except Exception as e:
            logger.error(f"Adaptive retrieval failed: {e}")
            return await self._simple_retrieval(context)
    
    async def _multi_query_retrieval(self, context: RAGContext) -> List[RetrievalResult]:
        """Multi-query retrieval with query expansion"""
        try:
            # Generate multiple query variations
            query_variations = await self._generate_query_variations(context.query)
            
            all_results = []
            for query_var in query_variations:
                var_context = RAGContext(
                    query=query_var,
                    tenant_id=context.tenant_id,
                    user_id=context.user_id,
                    dataset_ids=context.dataset_ids,
                    strategy=RAGStrategy.SIMPLE,
                    llm_provider=context.llm_provider,
                    max_chunks=context.max_chunks // len(query_variations),
                    similarity_threshold=context.similarity_threshold
                )
                results = await self._simple_retrieval(var_context)
                all_results.extend(results)
            
            # Deduplicate and rerank
            unique_results = self._deduplicate_results(all_results)
            return unique_results[:context.max_chunks]
            
        except Exception as e:
            logger.error(f"Multi-query retrieval failed: {e}")
            return await self._simple_retrieval(context)
    
    async def _hierarchical_retrieval(self, context: RAGContext) -> List[RetrievalResult]:
        """Hierarchical retrieval with document structure awareness"""
        try:
            # First pass: get document-level matches
            doc_results = await self._document_level_search(context)
            
            # Second pass: get chunk-level matches within relevant documents
            chunk_results = []
            for doc_result in doc_results[:5]:  # Top 5 documents
                doc_context = RAGContext(
                    query=context.query,
                    tenant_id=context.tenant_id,
                    user_id=context.user_id,
                    dataset_ids=[doc_result.metadata.get("dataset_id")],
                    strategy=RAGStrategy.SIMPLE,
                    llm_provider=context.llm_provider,
                    max_chunks=context.max_chunks // 5,
                    similarity_threshold=context.similarity_threshold * 0.8  # Lower threshold
                )
                results = await self._simple_retrieval(doc_context)
                chunk_results.extend(results)
            
            return chunk_results[:context.max_chunks]
            
        except Exception as e:
            logger.error(f"Hierarchical retrieval failed: {e}")
            return await self._simple_retrieval(context)
    
    async def _generate_response(self, context: RAGContext, chunks: List[RetrievalResult]) -> Dict[str, Any]:
        """Generate response using selected LLM provider"""
        try:
            # Select best available provider
            provider = self._select_best_provider(context.llm_provider)
            
            # Prepare context for LLM
            context_text = self._prepare_context(chunks)
            
            # Generate prompt
            prompt = self._create_prompt(context.query, context_text)
            
            # Call LLM
            if provider == LLMProvider.OPENAI:
                return await self._call_openai(prompt, context)
            elif provider == LLMProvider.ANTHROPIC:
                return await self._call_anthropic(prompt, context)
            elif provider == LLMProvider.GOOGLE:
                return await self._call_google(prompt, context)
            else:
                return await self._call_openai(prompt, context)  # Fallback
                
        except Exception as e:
            logger.error(f"LLM response generation failed: {e}")
            return {
                "answer": "I encountered an error generating the response.",
                "provider": "error",
                "tokens_used": 0
            }
    
    def _select_best_provider(self, preferred: LLMProvider) -> LLMProvider:
        """Select best available LLM provider based on health and performance"""
        # Check if preferred provider is healthy
        if self.provider_health.get(preferred, False) and preferred in self.llm_providers:
            return preferred
        
        # Find best alternative based on response times
        available_providers = [p for p in self.llm_providers.keys() if self.provider_health.get(p, False)]
        
        if not available_providers:
            return LLMProvider.OPENAI  # Fallback
        
        # Select provider with best average response time
        best_provider = min(available_providers, 
                          key=lambda p: np.mean(self.provider_response_times[p]) if self.provider_response_times[p] else float('inf'))
        
        return best_provider
    
    async def _call_openai(self, prompt: str, context: RAGContext) -> Dict[str, Any]:
        """Call OpenAI API"""
        try:
            import openai
            
            start_time = time.time()
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a helpful AI assistant that provides accurate answers based on the given context."},
                    {"role": "user", "content": prompt}
                ],
                temperature=context.temperature,
                max_tokens=context.max_tokens
            )
            
            response_time = (time.time() - start_time) * 1000
            self.provider_response_times[LLMProvider.OPENAI].append(response_time)
            
            return {
                "answer": response.choices[0].message.content,
                "provider": "openai",
                "tokens_used": response.usage.total_tokens
            }
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            self.provider_health[LLMProvider.OPENAI] = False
            raise
    
    async def _call_anthropic(self, prompt: str, context: RAGContext) -> Dict[str, Any]:
        """Call Anthropic Claude API"""
        try:
            start_time = time.time()

            response = await self.llm_providers[LLMProvider.ANTHROPIC].messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=context.max_tokens,
                temperature=context.temperature,
                messages=[{"role": "user", "content": prompt}]
            )

            response_time = (time.time() - start_time) * 1000
            self.provider_response_times[LLMProvider.ANTHROPIC].append(response_time)

            return {
                "answer": response.content[0].text,
                "provider": "anthropic",
                "tokens_used": response.usage.input_tokens + response.usage.output_tokens
            }

        except Exception as e:
            logger.error(f"Anthropic API call failed: {e}")
            self.provider_health[LLMProvider.ANTHROPIC] = False
            raise

    async def _call_google(self, prompt: str, context: RAGContext) -> Dict[str, Any]:
        """Call Google Gemini API"""
        try:
            start_time = time.time()

            model = self.llm_providers[LLMProvider.GOOGLE].GenerativeModel('gemini-pro')
            response = await model.generate_content_async(
                prompt,
                generation_config={
                    "temperature": context.temperature,
                    "max_output_tokens": context.max_tokens
                }
            )

            response_time = (time.time() - start_time) * 1000
            self.provider_response_times[LLMProvider.GOOGLE].append(response_time)

            return {
                "answer": response.text,
                "provider": "google",
                "tokens_used": response.usage_metadata.total_token_count if hasattr(response, 'usage_metadata') else 0
            }

        except Exception as e:
            logger.error(f"Google API call failed: {e}")
            self.provider_health[LLMProvider.GOOGLE] = False
            raise

    async def _get_cached_response(self, context: RAGContext) -> Optional[RAGResponse]:
        """Get cached response if available"""
        if not self.redis_client:
            return None

        try:
            cache_key = self._generate_cache_key(context)
            cached_data = self.redis_client.get(cache_key)

            if cached_data:
                response_data = json.loads(cached_data)
                response_data["cache_hit"] = True
                return RAGResponse(**response_data)

        except Exception as e:
            logger.error(f"Cache retrieval failed: {e}")

        return None

    async def _cache_response(self, context: RAGContext, response: RAGResponse):
        """Cache response for future use"""
        if not self.redis_client:
            return

        try:
            cache_key = self._generate_cache_key(context)
            response_data = asdict(response)
            response_data["cache_hit"] = False

            self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(response_data, default=str)
            )

        except Exception as e:
            logger.error(f"Cache storage failed: {e}")

    def _generate_cache_key(self, context: RAGContext) -> str:
        """Generate cache key for context"""
        key_data = f"{context.query}{context.tenant_id}{context.dataset_ids}{context.strategy.value}"
        return f"rag_cache:{hashlib.md5(key_data.encode()).hexdigest()}"

    def _calculate_confidence(self, context: RAGContext, chunks: List[RetrievalResult], llm_response: Dict[str, Any]) -> float:
        """Calculate confidence score for response"""
        if not chunks:
            return 0.0

        # Base confidence from retrieval scores
        avg_retrieval_score = sum(chunk.score for chunk in chunks) / len(chunks)

        # Adjust based on number of sources
        source_factor = min(len(chunks) / context.max_chunks, 1.0)

        # Adjust based on response length (longer responses often more confident)
        response_length_factor = min(len(llm_response.get("answer", "")) / 500, 1.0)

        # Combine factors
        confidence = (avg_retrieval_score * 0.6 + source_factor * 0.2 + response_length_factor * 0.2)

        return min(confidence, 1.0)

    async def _keyword_search(self, context: RAGContext) -> List[RetrievalResult]:
        """Simplified keyword search implementation"""
        # This would integrate with a full-text search engine like Elasticsearch
        # For now, return empty list as placeholder
        return []

    def _combine_results(self, vector_results: List[RetrievalResult], keyword_results: List[RetrievalResult]) -> List[RetrievalResult]:
        """Combine and rerank vector and keyword results"""
        # Simple combination - in production would use more sophisticated ranking
        all_results = vector_results + keyword_results

        # Remove duplicates based on chunk_id
        seen_ids = set()
        unique_results = []
        for result in all_results:
            if result.chunk_id not in seen_ids:
                seen_ids.add(result.chunk_id)
                unique_results.append(result)

        # Sort by score
        return sorted(unique_results, key=lambda x: x.score, reverse=True)

    def _analyze_query(self, query: str) -> Dict[str, bool]:
        """Analyze query characteristics"""
        query_lower = query.lower()

        # Simple heuristics - in production would use ML models
        is_factual = any(word in query_lower for word in ["what", "when", "where", "who", "how many"])
        is_complex = len(query.split()) > 10 or "and" in query_lower or "or" in query_lower

        return {
            "is_factual": is_factual,
            "is_complex": is_complex
        }

    async def _generate_query_variations(self, query: str) -> List[str]:
        """Generate query variations for multi-query retrieval"""
        # Simple variations - in production would use LLM for better variations
        variations = [query]

        # Add question variations
        if not query.endswith("?"):
            variations.append(query + "?")

        # Add keyword version
        words = query.split()
        if len(words) > 3:
            variations.append(" ".join(words[:3]))

        return variations[:3]  # Limit to 3 variations

    def _deduplicate_results(self, results: List[RetrievalResult]) -> List[RetrievalResult]:
        """Remove duplicate results"""
        seen_ids = set()
        unique_results = []

        for result in results:
            if result.chunk_id not in seen_ids:
                seen_ids.add(result.chunk_id)
                unique_results.append(result)

        return sorted(unique_results, key=lambda x: x.score, reverse=True)

    async def _document_level_search(self, context: RAGContext) -> List[RetrievalResult]:
        """Document-level search for hierarchical retrieval"""
        # Placeholder - would implement document-level embeddings
        return await self._simple_retrieval(context)

    def _prepare_context(self, chunks: List[RetrievalResult]) -> str:
        """Prepare context text from retrieved chunks"""
        context_parts = []
        for i, chunk in enumerate(chunks):
            context_parts.append(f"[Source {i+1}] {chunk.content}")
        return "\n\n".join(context_parts)

    def _create_prompt(self, query: str, context: str) -> str:
        """Create prompt for LLM"""
        return f"""Based on the following context, please answer the question. If the context doesn't contain enough information to answer the question, please say so.

Context:
{context}

Question: {query}

Answer:"""

# Global enterprise RAG service instance
enterprise_rag_service = EnterpriseRAGService()
