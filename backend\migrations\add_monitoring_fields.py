"""
Database Migration: Add Professional Monitoring Fields
Adds comprehensive monitoring and quality tracking fields to existing tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from database import DATABASE_URL
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_migration():
    """
    Add monitoring and quality fields to the queries table
    """
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)
        
        logger.info("🚀 Starting monitoring fields migration...")
        
        with engine.connect() as conn:
            # Add new monitoring fields to queries table
            monitoring_fields = [
                "ALTER TABLE queries ADD COLUMN response_time_ms REAL;",
                "ALTER TABLE queries ADD COLUMN token_count INTEGER;",
                "ALTER TABLE queries ADD COLUMN performance_grade VARCHAR;",
                "ALTER TABLE queries ADD COLUMN hallucination_risk REAL;",
                "ALTER TABLE queries ADD COLUMN completeness_score REAL;",
                "ALTER TABLE queries ADD COLUMN source_count INTEGER;"
            ]
            
            for field_sql in monitoring_fields:
                try:
                    conn.execute(text(field_sql))
                    logger.info(f"✅ Added field: {field_sql.split('ADD COLUMN')[1].split()[0]}")
                except Exception as e:
                    if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                        logger.info(f"⚠️ Field already exists: {field_sql.split('ADD COLUMN')[1].split()[0]}")
                    else:
                        logger.error(f"❌ Failed to add field: {e}")
            
            # Add indexes for performance
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_queries_performance_grade ON queries(performance_grade);",
                "CREATE INDEX IF NOT EXISTS idx_queries_response_time ON queries(response_time_ms);",
                "CREATE INDEX IF NOT EXISTS idx_queries_hallucination_risk ON queries(hallucination_risk);",
                "CREATE INDEX IF NOT EXISTS idx_queries_completeness ON queries(completeness_score);",
                "CREATE INDEX IF NOT EXISTS idx_queries_created_performance ON queries(created_at, performance_grade);"
            ]
            
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                    logger.info(f"✅ Created index: {index_sql.split('idx_')[1].split()[0]}")
                except Exception as e:
                    logger.info(f"⚠️ Index may already exist: {e}")
            
            # Add triggers for automatic performance grade calculation
            trigger_sql = """
            CREATE TRIGGER IF NOT EXISTS update_performance_grade
            AFTER UPDATE OF response_time_ms ON queries
            BEGIN
                UPDATE queries 
                SET performance_grade = CASE
                    WHEN NEW.response_time_ms < 1000 THEN 'excellent'
                    WHEN NEW.response_time_ms < 3000 THEN 'good'
                    WHEN NEW.response_time_ms < 5000 THEN 'fair'
                    ELSE 'poor'
                END
                WHERE id = NEW.id AND NEW.response_time_ms IS NOT NULL;
            END;
            """
            
            try:
                conn.execute(text(trigger_sql))
                logger.info("✅ Created performance grade trigger")
            except Exception as e:
                logger.info(f"⚠️ Trigger may already exist: {e}")
            
            conn.commit()
            
        logger.info("🎉 Monitoring fields migration completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {str(e)}")
        return False

def rollback_migration():
    """
    Rollback the monitoring fields migration (for development/testing)
    """
    try:
        engine = create_engine(DATABASE_URL)
        
        logger.info("🔄 Rolling back monitoring fields migration...")
        
        with engine.connect() as conn:
            # Drop trigger
            conn.execute(text("DROP TRIGGER IF EXISTS update_performance_grade;"))
            
            # Drop indexes
            indexes_to_drop = [
                "DROP INDEX IF EXISTS idx_queries_performance_grade;",
                "DROP INDEX IF EXISTS idx_queries_response_time;",
                "DROP INDEX IF EXISTS idx_queries_hallucination_risk;",
                "DROP INDEX IF EXISTS idx_queries_completeness;",
                "DROP INDEX IF EXISTS idx_queries_created_performance;"
            ]
            
            for drop_sql in indexes_to_drop:
                try:
                    conn.execute(text(drop_sql))
                except Exception as e:
                    logger.info(f"⚠️ Index may not exist: {e}")
            
            # Note: SQLite doesn't support DROP COLUMN, so we can't easily remove the columns
            # In production, you might want to create a new table and migrate data
            logger.info("⚠️ Note: SQLite doesn't support DROP COLUMN. Fields remain but indexes/triggers removed.")
            
            conn.commit()
            
        logger.info("✅ Migration rolled back successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Rollback failed: {str(e)}")
        return False

def verify_migration():
    """
    Verify that the migration was successful
    """
    try:
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # Check if new columns exist
            result = conn.execute(text("PRAGMA table_info(queries);"))
            columns = [row[1] for row in result.fetchall()]
            
            expected_columns = [
                'response_time_ms',
                'token_count', 
                'performance_grade',
                'hallucination_risk',
                'completeness_score',
                'source_count'
            ]
            
            missing_columns = [col for col in expected_columns if col not in columns]
            
            if missing_columns:
                logger.error(f"❌ Missing columns: {missing_columns}")
                return False
            else:
                logger.info("✅ All monitoring columns present")
                
            # Check if indexes exist
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_queries_%';"))
            indexes = [row[0] for row in result.fetchall()]
            
            logger.info(f"✅ Found {len(indexes)} monitoring indexes")
            
            # Check if trigger exists
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='trigger' AND name='update_performance_grade';"))
            triggers = result.fetchall()
            
            if triggers:
                logger.info("✅ Performance grade trigger exists")
            else:
                logger.warning("⚠️ Performance grade trigger not found")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Verification failed: {str(e)}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "rollback":
            rollback_migration()
        elif sys.argv[1] == "verify":
            verify_migration()
        else:
            print("Usage: python add_monitoring_fields.py [rollback|verify]")
    else:
        success = run_migration()
        if success:
            verify_migration()
