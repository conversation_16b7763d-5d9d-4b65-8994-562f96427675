#!/usr/bin/env python3
"""
Startup script for AIthentiq Multi-tenant Phase 1
Tests the foundation and core infrastructure
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        "fastapi",
        "uvicorn", 
        "sqlalchemy",
        "psycopg2",
        "pydantic"
    ]
    
    optional_packages = [
        "redis",
        "celery", 
        "rq",
        "openai",
        "cohere"
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_required.append(package)
            print(f"  ❌ {package} (REQUIRED)")
    
    for package in optional_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_optional.append(package)
            print(f"  ⚠️  {package} (optional)")
    
    if missing_required:
        print(f"\n❌ Missing required packages: {', '.join(missing_required)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    if missing_optional:
        print(f"\n⚠️  Missing optional packages: {', '.join(missing_optional)}")
        print("Some features may not be available.")
    
    print("\n✅ Dependency check completed")
    return True

def check_environment():
    """Check environment variables"""
    print("\n🔍 Checking environment variables...")
    
    required_env = []
    optional_env = [
        "DATABASE_URL",
        "REDIS_URL", 
        "OPENAI_API_KEY",
        "COHERE_API_KEY",
        "FRONTEND_URL"
    ]
    
    missing_required = []
    missing_optional = []
    
    for env_var in required_env:
        if os.getenv(env_var):
            print(f"  ✅ {env_var}")
        else:
            missing_required.append(env_var)
            print(f"  ❌ {env_var} (REQUIRED)")
    
    for env_var in optional_env:
        if os.getenv(env_var):
            print(f"  ✅ {env_var}")
        else:
            missing_optional.append(env_var)
            print(f"  ⚠️  {env_var} (optional)")
    
    if missing_required:
        print(f"\n❌ Missing required environment variables: {', '.join(missing_required)}")
        return False
    
    if missing_optional:
        print(f"\n⚠️  Missing optional environment variables: {', '.join(missing_optional)}")
        print("Some features may not be available.")
    
    print("\n✅ Environment check completed")
    return True

def test_database_connection():
    """Test database connection"""
    print("\n🔍 Testing database connection...")
    
    try:
        from database import get_db, engine
        from sqlalchemy import text
        
        # Test connection
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db.close()
        
        print("  ✅ Database connection successful")
        return True
        
    except Exception as e:
        print(f"  ❌ Database connection failed: {e}")
        print("  💡 Make sure PostgreSQL is running and DATABASE_URL is set")
        return False

def create_sample_tenant():
    """Create a sample tenant for testing"""
    print("\n🔍 Creating sample tenant...")
    
    try:
        from database import get_db
        from models_multitenant import Tenant, TenantUser, TenantApiKey
        
        db = next(get_db())
        
        # Check if sample tenant already exists
        existing_tenant = db.query(Tenant).filter(Tenant.domain == "demo").first()
        if existing_tenant:
            print("  ⚠️  Sample tenant 'demo' already exists")
            db.close()
            return True
        
        # Create sample tenant
        tenant = Tenant(
            name="Demo Tenant",
            domain="demo", 
            display_name="Demo Organization",
            subscription_plan="free"
        )
        
        db.add(tenant)
        db.commit()
        db.refresh(tenant)
        
        # Create admin user
        admin_user = TenantUser(
            tenant_id=tenant.id,
            email="<EMAIL>",
            name="Demo Admin",
            role="admin",
            is_verified=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # Create API key
        api_key = TenantApiKey(
            tenant_id=tenant.id,
            user_id=admin_user.id,
            name="Demo API Key",
            description="Sample API key for testing"
        )
        
        db.add(api_key)
        db.commit()
        db.refresh(api_key)
        
        print(f"  ✅ Sample tenant created:")
        print(f"     Tenant ID: {tenant.id}")
        print(f"     Domain: {tenant.domain}")
        print(f"     Admin Email: {admin_user.email}")
        print(f"     API Key: {api_key.key}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Failed to create sample tenant: {e}")
        return False

def test_task_queue():
    """Test task queue system"""
    print("\n🔍 Testing task queue system...")
    
    try:
        from services.task_queue_service import task_queue
        
        # Get queue stats
        stats = task_queue.get_queue_stats()
        print(f"  ✅ Task queue backend: {stats.get('backend', 'unknown')}")
        
        if stats.get('backend') == 'sync':
            print("  ⚠️  Using synchronous execution (Redis not available)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Task queue test failed: {e}")
        return False

def test_embedding_service():
    """Test embedding service"""
    print("\n🔍 Testing embedding service...")
    
    try:
        from services.embedding_service import EmbeddingService
        
        # Get available providers
        available_providers = EmbeddingService.get_available_providers()
        print(f"  ✅ Available embedding providers: {available_providers}")
        
        if not available_providers:
            print("  ⚠️  No embedding providers available")
            return True
        
        # Test with the first available provider
        provider = available_providers[0]
        embedding_service = EmbeddingService(provider=provider)
        
        # Test embedding generation
        test_text = "This is a test document for embedding generation."
        embedding = embedding_service.generate_embedding(test_text)
        
        print(f"  ✅ Generated embedding with {provider} (dimension: {len(embedding)})")
        return True
        
    except Exception as e:
        print(f"  ❌ Embedding service test failed: {e}")
        return False

async def run_startup_tests():
    """Run all startup tests"""
    print("🚀 AIthentiq Multi-tenant Phase 1 Startup Tests")
    print("=" * 50)
    
    tests = [
        ("Dependencies", check_dependencies),
        ("Environment", check_environment), 
        ("Database", test_database_connection),
        ("Sample Tenant", create_sample_tenant),
        ("Task Queue", test_task_queue),
        ("Embedding Service", test_embedding_service)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"  ❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready to start the server.")
        return True
    else:
        print("⚠️  Some tests failed. The server may not work correctly.")
        return False

def start_server():
    """Start the FastAPI server"""
    print("\n🚀 Starting AIthentiq Multi-tenant server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API documentation: http://localhost:8000/docs")
    print("🔧 Health check: http://localhost:8000/health")
    print("\n💡 To test the API, use the sample tenant:")
    print("   Domain: demo")
    print("   Admin Email: <EMAIL>")
    print("\nPress Ctrl+C to stop the server")
    
    try:
        uvicorn.run(
            "main_multitenant:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server failed to start: {e}")

async def main():
    """Main startup function"""
    # Run startup tests
    tests_passed = await run_startup_tests()
    
    # Ask user if they want to continue if tests failed
    if not tests_passed:
        response = input("\nSome tests failed. Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("👋 Startup cancelled")
            return
    
    # Start the server
    start_server()

if __name__ == "__main__":
    asyncio.run(main())
