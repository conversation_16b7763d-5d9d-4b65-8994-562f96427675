#!/usr/bin/env python3
"""
Verify trust comparison API endpoint
"""

import requests
import json

def test_api():
    print("🌐 Testing Trust Comparison API...")
    
    # Test data
    test_data = {
        "query": "What is the average sales for Q1?",
        "answer": "Based on the data analysis, the average sales for Q1 is $45,000.",
        "sources": [
            {"text": "Q1 sales data shows total revenue of $135,000 across 3 months"},
            {"text": "Sales performance report indicates consistent monthly averages"}
        ],
        "selected_methods": ["legacy_deterministic", "aithentiq_convergence"]
    }
    
    try:
        # Test methods endpoint
        print("📋 Testing /methods endpoint...")
        response = requests.get("http://localhost:8000/api/v1/trust-comparison/methods")
        if response.status_code == 200:
            methods = response.json()
            print(f"✅ Methods endpoint OK - {len(methods['methods'])} methods available")
        else:
            print(f"❌ Methods endpoint failed: {response.status_code}")
            return
        
        # Test comparison endpoint
        print("🔍 Testing /compare endpoint...")
        response = requests.post(
            "http://localhost:8000/api/v1/trust-comparison/compare",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Comparison endpoint OK")
            print(f"   Methods tested: {result['analysis']['summary']['methods_tested']}")
            print(f"   Successful: {result['analysis']['summary']['successful_methods']}")
            
            for res in result['results']:
                status = "✅" if not res.get('error') else "❌"
                print(f"   {status} {res['method_name']}: {res['score_percentage']}")
        else:
            print(f"❌ Comparison endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
        # Test demo endpoint
        print("🎮 Testing /demo endpoint...")
        response = requests.post("http://localhost:8000/api/v1/trust-comparison/demo")
        if response.status_code == 200:
            print("✅ Demo endpoint OK")
        else:
            print(f"❌ Demo endpoint failed: {response.status_code}")
    
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure backend is running on port 8000")
    except Exception as e:
        print(f"❌ API test failed: {e}")

if __name__ == "__main__":
    test_api()
