# ============================================
# AIthentiq Project .gitignore
# ============================================

# ============================================
# SECURITY & ENVIRONMENT FILES (NEVER COMMIT)
# ============================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env
backend/.env
frontend/.env.local

# API Keys and Secrets
**/api_keys.txt
**/secrets.json
**/*secret*
**/*key*
**/*token*

# ============================================
# PYTHON BACKEND
# ============================================
# Virtual environments
backend/venv/
backend/venv_new/
backend/env/
backend/.venv/
**/venv/
**/env/

# Python cache
backend/__pycache__/
backend/**/__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
backend/.Python
backend/build/
backend/develop-eggs/
backend/dist/
backend/downloads/
backend/eggs/
backend/.eggs/
backend/lib/
backend/lib64/
backend/parts/
backend/sdist/
backend/var/
backend/wheels/
backend/pip-wheel-metadata/
backend/share/python-wheels/
*.egg-info/
backend/.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ============================================
# DATABASES (NEVER COMMIT)
# ============================================
*.db
*.sqlite
*.sqlite3
backend/*.db
backend/*.sqlite
backend/*.sqlite3
backend/aithentiq.db
backend/askdata.db

# SQLite WAL (Write-Ahead Logging) and SHM (Shared Memory) files
*.db-shm
*.db-wal
*.sqlite-shm
*.sqlite-wal
*.sqlite3-shm
*.sqlite3-wal
backend/*.db-shm
backend/*.db-wal
backend/aithentiq.db-shm
backend/aithentiq.db-wal

# ============================================
# FRONTEND (NODE.JS/NEXT.JS)
# ============================================
# Dependencies
frontend/node_modules/
node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Testing
frontend/coverage/
coverage/

# Next.js
frontend/.next/
frontend/out/
.next/
out/

# Production
frontend/build/
build/

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
# next-env.d.ts  # Allow this file - it's needed for TypeScript

# Clerk configuration
frontend/.clerk/
.clerk/

# ============================================
# DATASETS & UPLOADS (LARGE FILES)
# ============================================
backend/datasets/
backend/uploads/
**/datasets/
**/uploads/
*.csv
*.xlsx
*.xls
# *.json  # Allow package.json and other config files
**/datasets/*.json
**/uploads/*.json
*.parquet

# ============================================
# LOGS & TEMPORARY FILES
# ============================================
*.log
logs/
*.tmp
*.temp
temp/
tmp/

# ============================================
# SYSTEM FILES
# ============================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================
# IDE & EDITOR FILES
# ============================================
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================
# TESTING & DEVELOPMENT
# ============================================
comprehensive_test.py
test_*.py
*_test.py
debug_*.py

# ============================================
# DOCKER & DEPLOYMENT
# ============================================
docker-compose.override.yml
.dockerignore

# ============================================
# MISC
# ============================================
*.bak
*.backup
*.old
*.orig
*.rej
*.swp
*.tmp

# Compressed files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
