"""
Task Queue Service for AIthentiq
Supports both Celery + Redis and RQ for background processing
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from enum import Enum

# Try to import Celery first
try:
    from celery import Celery
    from celery.result import AsyncResult
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False

# Try to import RQ as fallback
try:
    import redis
    from rq import Queue, Worker, Connection
    from rq.job import Job
    RQ_AVAILABLE = True
except ImportError:
    RQ_AVAILABLE = False

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskType(Enum):
    DOCUMENT_PROCESSING = "document_processing"
    EMBEDDING_GENERATION = "embedding_generation"
    CONNECTOR_SYNC = "connector_sync"
    TRUST_SCORE_CALCULATION = "trust_score_calculation"
    DATA_EXPORT = "data_export"
    CLEANUP = "cleanup"

class TaskQueueService:
    """
    Unified task queue service supporting both Celery and RQ
    """
    
    def __init__(self):
        self.backend_type = self._determine_backend()
        self.celery_app = None
        self.redis_conn = None
        self.rq_queue = None
        
        if self.backend_type == "celery":
            self._setup_celery()
        elif self.backend_type == "rq":
            self._setup_rq()
        else:
            logger.warning("No task queue backend available. Tasks will run synchronously.")

    def _determine_backend(self) -> str:
        """
        Determine which task queue backend to use
        """
        # Check environment preference
        preferred = os.getenv("TASK_QUEUE_BACKEND", "auto").lower()
        
        if preferred == "celery" and CELERY_AVAILABLE:
            return "celery"
        elif preferred == "rq" and RQ_AVAILABLE:
            return "rq"
        elif preferred == "auto":
            # Auto-detect based on availability
            if CELERY_AVAILABLE:
                return "celery"
            elif RQ_AVAILABLE:
                return "rq"
        
        return "sync"  # Fallback to synchronous execution

    def _setup_celery(self):
        """
        Setup Celery with Redis broker
        """
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        
        self.celery_app = Celery(
            'aithentiq_tasks',
            broker=redis_url,
            backend=redis_url,
            include=['services.task_workers']
        )
        
        # Celery configuration
        self.celery_app.conf.update(
            task_serializer='json',
            accept_content=['json'],
            result_serializer='json',
            timezone='UTC',
            enable_utc=True,
            task_track_started=True,
            task_time_limit=30 * 60,  # 30 minutes
            task_soft_time_limit=25 * 60,  # 25 minutes
            worker_prefetch_multiplier=1,
            worker_max_tasks_per_child=1000,
        )
        
        logger.info("Celery task queue initialized")

    def _setup_rq(self):
        """
        Setup RQ with Redis
        """
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        
        try:
            self.redis_conn = redis.from_url(redis_url)
            self.rq_queue = Queue('aithentiq_tasks', connection=self.redis_conn)
            logger.info("RQ task queue initialized")
        except Exception as e:
            logger.error(f"Failed to setup RQ: {e}")
            self.backend_type = "sync"

    def enqueue_task(
        self,
        task_type: TaskType,
        task_data: Dict[str, Any],
        tenant_id: str,
        user_id: str,
        priority: str = "normal",
        delay_seconds: int = 0
    ) -> str:
        """
        Enqueue a task for background processing
        
        Args:
            task_type: Type of task to execute
            task_data: Data needed for task execution
            tenant_id: Tenant ID for isolation
            user_id: User ID who initiated the task
            priority: Task priority (high, normal, low)
            delay_seconds: Delay before execution
            
        Returns:
            Task ID for tracking
        """
        task_payload = {
            "task_type": task_type.value,
            "task_data": task_data,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "priority": priority,
            "created_at": datetime.utcnow().isoformat(),
            "status": TaskStatus.PENDING.value
        }
        
        if self.backend_type == "celery":
            return self._enqueue_celery_task(task_payload, delay_seconds)
        elif self.backend_type == "rq":
            return self._enqueue_rq_task(task_payload, delay_seconds)
        else:
            # Synchronous execution
            return self._execute_sync_task(task_payload)

    def _enqueue_celery_task(self, task_payload: Dict[str, Any], delay_seconds: int) -> str:
        """
        Enqueue task using Celery
        """
        from services.task_workers import process_task
        
        if delay_seconds > 0:
            eta = datetime.utcnow() + timedelta(seconds=delay_seconds)
            result = process_task.apply_async(args=[task_payload], eta=eta)
        else:
            result = process_task.delay(task_payload)
        
        return result.id

    def _enqueue_rq_task(self, task_payload: Dict[str, Any], delay_seconds: int) -> str:
        """
        Enqueue task using RQ
        """
        from services.task_workers import process_task_rq
        
        if delay_seconds > 0:
            job = self.rq_queue.enqueue_in(
                timedelta(seconds=delay_seconds),
                process_task_rq,
                task_payload
            )
        else:
            job = self.rq_queue.enqueue(process_task_rq, task_payload)
        
        return job.id

    def _execute_sync_task(self, task_payload: Dict[str, Any]) -> str:
        """
        Execute task synchronously (fallback)
        """
        from services.task_workers import execute_task_sync
        
        task_id = f"sync_{datetime.utcnow().timestamp()}"
        task_payload["task_id"] = task_id
        
        try:
            result = execute_task_sync(task_payload)
            logger.info(f"Synchronous task {task_id} completed: {result}")
        except Exception as e:
            logger.error(f"Synchronous task {task_id} failed: {e}")
        
        return task_id

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get the status of a task
        """
        if self.backend_type == "celery":
            return self._get_celery_task_status(task_id)
        elif self.backend_type == "rq":
            return self._get_rq_task_status(task_id)
        else:
            return {
                "task_id": task_id,
                "status": TaskStatus.COMPLETED.value,
                "result": "Executed synchronously"
            }

    def _get_celery_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get Celery task status
        """
        result = AsyncResult(task_id, app=self.celery_app)
        
        return {
            "task_id": task_id,
            "status": result.status.lower(),
            "result": result.result,
            "traceback": result.traceback
        }

    def _get_rq_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get RQ task status
        """
        try:
            job = Job.fetch(task_id, connection=self.redis_conn)
            
            return {
                "task_id": task_id,
                "status": job.get_status().lower(),
                "result": job.result,
                "exc_info": job.exc_info
            }
        except Exception as e:
            return {
                "task_id": task_id,
                "status": "not_found",
                "error": str(e)
            }

    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a pending or running task
        """
        if self.backend_type == "celery":
            return self._cancel_celery_task(task_id)
        elif self.backend_type == "rq":
            return self._cancel_rq_task(task_id)
        else:
            return False  # Cannot cancel synchronous tasks

    def _cancel_celery_task(self, task_id: str) -> bool:
        """
        Cancel Celery task
        """
        try:
            self.celery_app.control.revoke(task_id, terminate=True)
            return True
        except Exception as e:
            logger.error(f"Failed to cancel Celery task {task_id}: {e}")
            return False

    def _cancel_rq_task(self, task_id: str) -> bool:
        """
        Cancel RQ task
        """
        try:
            job = Job.fetch(task_id, connection=self.redis_conn)
            job.cancel()
            return True
        except Exception as e:
            logger.error(f"Failed to cancel RQ task {task_id}: {e}")
            return False

    def get_queue_stats(self) -> Dict[str, Any]:
        """
        Get queue statistics
        """
        if self.backend_type == "celery":
            return self._get_celery_stats()
        elif self.backend_type == "rq":
            return self._get_rq_stats()
        else:
            return {
                "backend": "sync",
                "pending_tasks": 0,
                "active_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0
            }

    def _get_celery_stats(self) -> Dict[str, Any]:
        """
        Get Celery queue statistics
        """
        try:
            inspect = self.celery_app.control.inspect()
            active = inspect.active()
            scheduled = inspect.scheduled()
            
            active_count = sum(len(tasks) for tasks in (active or {}).values())
            scheduled_count = sum(len(tasks) for tasks in (scheduled or {}).values())
            
            return {
                "backend": "celery",
                "active_tasks": active_count,
                "scheduled_tasks": scheduled_count,
                "workers": list((active or {}).keys())
            }
        except Exception as e:
            logger.error(f"Failed to get Celery stats: {e}")
            return {"backend": "celery", "error": str(e)}

    def _get_rq_stats(self) -> Dict[str, Any]:
        """
        Get RQ queue statistics
        """
        try:
            return {
                "backend": "rq",
                "pending_tasks": len(self.rq_queue),
                "failed_tasks": len(self.rq_queue.failed_job_registry),
                "workers": Worker.count(connection=self.redis_conn)
            }
        except Exception as e:
            logger.error(f"Failed to get RQ stats: {e}")
            return {"backend": "rq", "error": str(e)}

# Global task queue service instance
task_queue = TaskQueueService()
