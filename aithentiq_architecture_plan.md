# AIthentiq: Client-Centric AI Answering System with Trust Score

## 📌 Objective

To build a secure, scalable, multi-tenant Retrieval-Augmented Generation (RAG) platform that allows clients to connect their own data sources (e.g. SharePoint, GitHub, OneDrive, Network Folders), generate LLM-based insights, and validate every response using a multi-factor trust scoring mechanism.

By default, all connected data sources from a client's workspace are automatically included in query processing. Users do not need to select a dataset explicitly unless they upload a new document manually. The system ensures comprehensive, context-rich results without additional user input.

---

## ⚙️ System Modules Overview

### 1. Data Connector Service

- **Purpose**: Ingest documents and metadata from client-approved data sources.
- **Adapters**:
  - SharePoint / OneDrive: Microsoft Graph API
  - GitHub: GitHub REST API or `git clone`
  - Network Folder: Agent-based sync with `watchdog`, `smbprotocol`
  - Google Drive / Dropbox: SDK-based integration
- **Access Control**: Only <PERSON>mins can authorize or manage connectors.
- **Output**: Extracted files + document metadata queued for processing

### 2. Embedding & Indexing Service

- **Chunking**: Text splitting with `langchain.text_splitter`
- **Embedding Providers**: OpenAI, Cohere, HuggingFace, InstructorXL
- **Vector Stores**:
  - Pinecone (with namespaces per tenant)
  - FAISS/Weaviate (separate indexes per tenant)
- **Metadata**: File path, client ID, source type, doc ID

### 3. LLM Service

- **Primary Model**: GPT-4, Claude, Gemini (client-selectable)
- **RAG Flow**: Query → Retrieve Top K Chunks → Augment → Prompt LLM → Return Answer

### 4. Trust Score Engine

- **Inputs**: LLM output + retrieved chunks + reference answers (if enabled)
- **Factors**:
  1. Multi-LLM Convergence (40%)
  2. Contextual Faithfulness (30%)
  3. Model Confidence Score (20%)
  4. Citation Accuracy (10%)
- **Output**: Trust Score (0–100), rationale, and citations

### 5. Client Configuration Service

- Per-tenant config:
  - Allowed data connectors
  - LLM preferences
  - Embedding model choice
  - Role-based access

### 6. User Management & Auth

- Authentication: NextAuth.js, JWT, API Keys
- Roles: Admin, User, Guest
- Features gated by role

### 7. Query Interface (Frontend)

- Upload file or select data source (based on role)
- Ask natural language questions
- Get:
  - Answer
  - Trust Score
  - Source Snippets
  - Citation Trace

---

## 🧙‍♂️ Technical Stack

| Layer        | Technologies Used                        |
| ------------ | ---------------------------------------- |
| Backend API  | FastAPI + Pydantic                       |
| Frontend     | Next.js + Tailwind CSS + Plotly          |
| Auth         | NextAuth.js, JWT, RBAC                   |
| Database     | PostgreSQL + SQLAlchemy                  |
| Task Queue   | Celery + Redis (or RQ)                   |
| Vector Store | FAISS / Pinecone / Weaviate              |
| LLMs         | GPT-4, Claude, Gemini, Mistral (via API) |
| Embeddings   | OpenAI, Cohere, HuggingFace              |

---

## 🧐 Unique Capabilities

- **Client-specific "LLM-as-a-logic" layer**: Logical separation without fine-tuning
- **Trust Score per Query**: Full traceability, repeatability, and logic transparency
- **Hybrid Connect/Upload UX**: Admins connect sources; users upload files
- **Auto-search on Client Data**: All connected sources are searched by default
- **Audit Logs**: Every query + response + trust score logged per user
- **Agent Deployment**: Optional agent for secure, on-prem network ingestion

---

## 🛡️ Security, Compliance & Observability

- ✅ Data Isolation (per tenant)
- ✅ OAuth, 2FA, and Token Rotation
- ✅ Encrypted storage (AES256, TLS in transit)
- ✅ GDPR-compliant audit logging and deletion
- ✅ Prometheus + Grafana for metrics

---

## 🕸️ Suggested MVP Scope for Development

1. Multi-tenant FastAPI base with PostgreSQL
2. Document ingestion from GitHub and manual upload
3. Vector DB setup with FAISS per tenant
4. LLM response generation with GPT-4
5. Basic trust score using similarity and citation
6. Admin UI to add users, upload files, configure sources
7. User chat interface with answer + traceability

---

## 💡 Future Enhancements

- Custom RAG agents per industry
- Auto-summarization of data sources
- Time-sensitive document ranking
- Feedback loop to fine-tune prompts
- Bring-your-own-LLM via LangChain router
- Self-hosting support for full data control

---

## 📁 Suggested Directory Structure

```plaintext
backend/
├── main.py
├── routers/
│   ├── ask.py
│   ├── upload.py
│   ├── trust_score.py
│   └── connector.py
├── services/
│   ├── embedding_service.py
│   ├── llm_service.py
│   ├── trust_score_service.py
│   └── connector_service/
│       ├── sharepoint.py
│       ├── github.py
│       └── local_agent.py
├── vector_store/
├── database/
└── models/
frontend/
├── components/
├── pages/
│   ├── index.tsx
│   ├── admin/
│   └── chat/
└── utils/
```

---

Let me know when you're ready to generate code modules from this design or if you’d like to add use cases like BI dashboards, forecasting, or multi-tenant billing.

