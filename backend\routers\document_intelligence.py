"""
Document Intelligence Router for AIthentiq
Enhanced document processing with RAGFlow-inspired features
"""

from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import json

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
import models, schemas
from middleware.api_auth import get_current_user_from_api_key
from services.document_service import document_processor, ChunkingTemplate

router = APIRouter(prefix="/api/v1/document-intelligence", tags=["document-intelligence"])


@router.get("/chunking-templates", response_model=List[str])
async def get_chunking_templates():
    """
    Get available chunking templates
    """
    return document_processor.get_chunking_templates()


@router.post("/analyze-document")
async def analyze_document(
    file: UploadFile = File(...),
    template: Optional[str] = Form(None),
    user: models.User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Analyze a document with enhanced processing and chunking
    """
    # Validate file type
    file_extension = file.filename.split('.')[-1].lower()
    supported_formats = ['pdf', 'docx', 'txt', 'md', 'jpg', 'jpeg', 'png']
    if file_extension not in supported_formats:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Supported formats: {', '.join(supported_formats)}"
        )

    # Read file content
    contents = await file.read()

    try:
        if template and template in document_processor.get_chunking_templates():
            # Use custom template
            result = document_processor.process_with_custom_template(
                contents, file.filename, file_extension, template
            )
        else:
            # Use automatic template detection
            result = document_processor.process_document(
                contents, file.filename, file_extension
            )

        if result.get('error'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Document processing failed: {result['error']}"
            )

        # Prepare response with enhanced metadata
        response = {
            "filename": file.filename,
            "file_type": file_extension,
            "processing_status": result.get('processing_status', 'success'),
            "chunking_template": result.get('chunking_template', 'unknown'),
            "total_chunks": result.get('total_chunks', 0),
            "word_count": result.get('word_count', 0),
            "character_count": result.get('character_count', 0),
            "metadata": result.get('metadata', {}),
            "chunks": result.get('chunks', []),
            "has_embeddings": bool(result.get('embeddings')),
            "embedding_model": result.get('embedding_model'),
            "processed_at": result.get('processed_at')
        }

        return response

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document analysis failed: {str(e)}"
        )


@router.post("/chunk-text")
async def chunk_text(
    text: str = Form(...),
    template: str = Form("general"),
    user: models.User = Depends(get_current_user_from_api_key)
):
    """
    Chunk raw text using specified template
    """
    try:
        if template not in document_processor.get_chunking_templates():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid template. Available: {document_processor.get_chunking_templates()}"
            )

        template_enum = ChunkingTemplate(template)
        chunks = document_processor.chunking_strategy.chunk_text_with_template(
            text, template_enum
        )

        return {
            "text_length": len(text),
            "word_count": len(text.split()),
            "template_used": template,
            "total_chunks": len(chunks),
            "chunks": chunks
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Text chunking failed: {str(e)}"
        )


@router.post("/detect-document-type")
async def detect_document_type(
    text: str = Form(...),
    metadata: Optional[str] = Form("{}"),
    user: models.User = Depends(get_current_user_from_api_key)
):
    """
    Detect the best chunking template for given text
    """
    try:
        metadata_dict = json.loads(metadata) if metadata else {}
        
        detected_template = document_processor.chunking_strategy.detect_document_type(
            text, metadata_dict
        )

        # Get template configuration
        template_config = document_processor.chunking_strategy.templates[detected_template]

        return {
            "detected_template": detected_template.value,
            "confidence": "auto-detected",
            "template_config": template_config,
            "text_analysis": {
                "word_count": len(text.split()),
                "character_count": len(text),
                "line_count": len(text.split('\n')),
                "has_code_blocks": '```' in text,
                "has_numbered_sections": bool(len([line for line in text.split('\n') 
                                                 if line.strip() and 
                                                 (line.strip().startswith(tuple('123456789')) and '.' in line[:10])]))
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document type detection failed: {str(e)}"
        )


@router.get("/template-info/{template_name}")
async def get_template_info(
    template_name: str,
    user: models.User = Depends(get_current_user_from_api_key)
):
    """
    Get detailed information about a specific chunking template
    """
    try:
        if template_name not in document_processor.get_chunking_templates():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template '{template_name}' not found"
            )

        template_enum = ChunkingTemplate(template_name)
        template_config = document_processor.chunking_strategy.templates[template_enum]

        return {
            "template_name": template_name,
            "description": f"Optimized for {template_name} documents",
            "config": template_config,
            "use_cases": {
                "general": "General purpose documents, articles, basic text",
                "academic": "Research papers, academic articles, theses",
                "technical": "Technical documentation, code, manuals",
                "legal": "Legal documents, contracts, terms of service",
                "financial": "Financial reports, statements, analysis",
                "research": "Research papers, scientific documents"
            }.get(template_name, "Specialized document processing")
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Template info retrieval failed: {str(e)}"
        )


@router.post("/compare-chunking-strategies")
async def compare_chunking_strategies(
    text: str = Form(...),
    templates: List[str] = Form(...),
    user: models.User = Depends(get_current_user_from_api_key)
):
    """
    Compare different chunking strategies on the same text
    """
    try:
        results = {}
        
        for template_name in templates:
            if template_name not in document_processor.get_chunking_templates():
                continue
                
            template_enum = ChunkingTemplate(template_name)
            chunks = document_processor.chunking_strategy.chunk_text_with_template(
                text, template_enum
            )
            
            results[template_name] = {
                "total_chunks": len(chunks),
                "avg_chunk_size": sum(chunk['word_count'] for chunk in chunks) / len(chunks) if chunks else 0,
                "chunk_types": list(set(chunk.get('chunk_type', 'unknown') for chunk in chunks)),
                "chunks": chunks[:3]  # First 3 chunks as examples
            }

        return {
            "text_stats": {
                "word_count": len(text.split()),
                "character_count": len(text)
            },
            "comparison_results": results
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chunking comparison failed: {str(e)}"
        )
