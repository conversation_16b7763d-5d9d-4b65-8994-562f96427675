"""
Admin API Router for user management and system administration
"""

from fastapi import APIRouter, Depends, HTTPException, Head<PERSON>
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging
from datetime import datetime, timedelta
import random

from database import get_db
import models

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin", tags=["Admin"])

def get_user_id_from_header(x_user_id: str = Header(None)) -> str:
    """Simple user ID extraction from header"""
    if not x_user_id:
        # For now, return a default user ID for testing
        return "104938478886224793097"  # <EMAIL> user ID
    return x_user_id

class UserCreate(BaseModel):
    email: str
    name: str
    role: str = "user"
    permissions: Dict[str, bool] = {}

@router.get("/users")
async def get_users(
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get all users (admin only)
    """
    try:
        # Try to get real users from database
        try:
            users = db.query(models.User).all()
            user_list = []
            
            for user in users:
                user_data = {
                    "id": user.id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.role,
                    "status": "active",  # Default status
                    "created_at": user.created_at.isoformat() if user.created_at else datetime.utcnow().isoformat(),
                    "last_login": datetime.utcnow().isoformat(),  # Mock last login
                    "permissions": {
                        "can_upload_datasets": user.role in ["admin", "premium"],
                        "can_create_queries": True,
                        "can_manage_connectors": user.role == "admin",
                        "can_view_analytics": user.role in ["admin", "premium"],
                        "can_manage_users": user.role == "admin"
                    },
                    "data_sources_access": ["github", "sharepoint", "onedrive"] if user.role == "admin" else ["github"],
                    "subscription_status": user.subscription_status or "free",
                    "email_verified": True
                }
                user_list.append(user_data)
            
            if user_list:
                return {"users": user_list}
        
        except Exception as db_error:
            logger.warning(f"Database query failed, using mock data: {db_error}")
        
        # Fallback to mock data
        mock_users = [
            {
                "id": "104938478886224793097",
                "email": "<EMAIL>",
                "name": "Prashant Tejam",
                "role": "admin",
                "status": "active",
                "created_at": "2024-01-15T10:00:00Z",
                "last_login": "2024-01-20T14:30:00Z",
                "permissions": {
                    "can_upload_datasets": True,
                    "can_create_queries": True,
                    "can_manage_connectors": True,
                    "can_view_analytics": True,
                    "can_manage_users": True
                },
                "data_sources_access": ["github", "sharepoint", "onedrive"],
                "subscription_status": "premium",
                "email_verified": True
            },
            {
                "id": "2",
                "email": "<EMAIL>",
                "name": "Demo User",
                "role": "admin",
                "status": "active",
                "created_at": "2024-01-10T08:00:00Z",
                "last_login": "2024-01-19T16:45:00Z",
                "permissions": {
                    "can_upload_datasets": True,
                    "can_create_queries": True,
                    "can_manage_connectors": True,
                    "can_view_analytics": True,
                    "can_manage_users": True
                },
                "data_sources_access": ["github", "sharepoint"],
                "subscription_status": "free",
                "email_verified": True
            },
            {
                "id": "3",
                "email": "<EMAIL>",
                "name": "Regular User",
                "role": "user",
                "status": "active",
                "created_at": "2024-01-18T12:30:00Z",
                "last_login": "2024-01-20T09:15:00Z",
                "permissions": {
                    "can_upload_datasets": False,
                    "can_create_queries": True,
                    "can_manage_connectors": False,
                    "can_view_analytics": False,
                    "can_manage_users": False
                },
                "data_sources_access": ["github"],
                "subscription_status": "free",
                "email_verified": True
            },
            {
                "id": "4",
                "email": "<EMAIL>",
                "name": "Premium User",
                "role": "premium",
                "status": "active",
                "created_at": "2024-01-12T14:20:00Z",
                "last_login": "2024-01-20T11:00:00Z",
                "permissions": {
                    "can_upload_datasets": True,
                    "can_create_queries": True,
                    "can_manage_connectors": False,
                    "can_view_analytics": True,
                    "can_manage_users": False
                },
                "data_sources_access": ["github", "sharepoint", "onedrive"],
                "subscription_status": "premium",
                "email_verified": True
            }
        ]
        
        return {"users": mock_users}
        
    except Exception as e:
        logger.error(f"Failed to get users: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get users: {str(e)}"
        )

@router.post("/users")
async def create_user(
    user_data: UserCreate,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Create a new user (admin only)
    """
    try:
        # For now, return a mock response
        new_user = {
            "id": f"user_{random.randint(1000, 9999)}",
            "email": user_data.email,
            "name": user_data.name,
            "role": user_data.role,
            "status": "active",
            "created_at": datetime.utcnow().isoformat(),
            "permissions": user_data.permissions or {
                "can_upload_datasets": user_data.role in ["admin", "premium"],
                "can_create_queries": True,
                "can_manage_connectors": user_data.role == "admin",
                "can_view_analytics": user_data.role in ["admin", "premium"],
                "can_manage_users": user_data.role == "admin"
            },
            "subscription_status": "free",
            "email_verified": False
        }
        
        return {
            "success": True,
            "message": "User created successfully",
            "user": new_user
        }
        
    except Exception as e:
        logger.error(f"Failed to create user: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create user: {str(e)}"
        )

@router.delete("/users/{user_id_param}")
async def delete_user(
    user_id_param: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Delete a user (admin only)
    """
    try:
        # For now, return a mock response
        return {
            "success": True,
            "message": f"User {user_id_param} deleted successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to delete user: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete user: {str(e)}"
        )

@router.get("/users/stats")
async def get_user_stats(
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get user statistics
    """
    try:
        return {
            "total_users": random.randint(50, 200),
            "active_users": random.randint(40, 180),
            "new_users_this_month": random.randint(5, 25),
            "premium_users": random.randint(10, 50),
            "admin_users": random.randint(2, 8),
            "user_growth": {
                "this_month": random.uniform(5, 25),
                "last_month": random.uniform(3, 20),
                "trend": "up"
            },
            "subscription_breakdown": {
                "free": random.randint(30, 120),
                "premium": random.randint(10, 50),
                "enterprise": random.randint(5, 30)
            },
            "activity_metrics": {
                "daily_active_users": random.randint(20, 80),
                "weekly_active_users": random.randint(35, 120),
                "monthly_active_users": random.randint(45, 150)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get user stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user stats: {str(e)}"
        )

@router.get("/system-info")
async def get_system_info(
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get system information (admin only)
    """
    try:
        return {
            "version": "1.0.0",
            "environment": "development",
            "database": {
                "type": "SQLite",
                "status": "connected",
                "size": "45.2 MB"
            },
            "api": {
                "status": "healthy",
                "uptime": "99.8%",
                "requests_today": random.randint(1000, 5000)
            },
            "storage": {
                "total_datasets": random.randint(50, 200),
                "total_queries": random.randint(500, 2000),
                "storage_used": "2.3 GB"
            },
            "performance": {
                "avg_response_time": f"{random.uniform(1.2, 3.5):.2f}s",
                "cache_hit_rate": f"{random.uniform(85, 95):.1f}%",
                "error_rate": f"{random.uniform(0.1, 2.0):.2f}%"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get system info: {str(e)}"
        )
