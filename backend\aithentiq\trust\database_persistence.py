"""
Database Persistence for Trust System
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc

from .bayesian import UserPrior, TopicPrior
from ..config import trust_config


class TrustDatabaseManager:
    """Manages database persistence for trust system components"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def save_trust_score(self, db: Session, trust_result: Dict[str, Any], 
                        user_id: str, query_id: Optional[int] = None) -> int:
        """Save trust score to database"""
        try:
            from models_trust import TrustScore
            
            # Extract confidence interval
            conf_interval = trust_result.get('confidence_interval')
            conf_lower = conf_interval[0] if conf_interval else None
            conf_upper = conf_interval[1] if conf_interval else None
            
            trust_score = TrustScore(
                query_id=query_id,
                user_id=user_id,
                overall_score=trust_result['overall_score'],
                calibrated_score=trust_result.get('calibrated_score', trust_result['overall_score']),
                base_score=trust_result.get('base_score', trust_result['overall_score']),
                
                # Component scores
                model_confidence=trust_result['component_scores']['model_confidence'],
                qat_regressor=trust_result['component_scores']['qat_regressor'],
                refusal_detector=trust_result['component_scores']['refusal_detector'],
                citation_precision=trust_result['component_scores']['citation_precision'],
                
                # Bayesian components
                user_posterior=trust_result.get('user_posterior'),
                topic_posterior=trust_result.get('topic_posterior'),
                bayesian_adjustment=trust_result.get('bayesian_adjustment', 0.0),
                
                # Metadata
                topic=trust_result.get('topic', 'general'),
                explanation=trust_result['explanation'],
                factors=trust_result['factors'],
                confidence_interval_lower=conf_lower,
                confidence_interval_upper=conf_upper,
                processing_time_ms=trust_result.get('processing_time_ms')
            )
            
            db.add(trust_score)
            db.commit()
            db.refresh(trust_score)
            
            return trust_score.id
            
        except Exception as e:
            self.logger.error(f"Failed to save trust score: {e}")
            db.rollback()
            return None
    
    def load_user_prior(self, db: Session, user_id: str) -> Optional[UserPrior]:
        """Load user prior from database"""
        try:
            from models_trust import UserTrustPrior
            
            db_prior = db.query(UserTrustPrior).filter(
                UserTrustPrior.user_id == user_id
            ).first()
            
            if db_prior:
                return UserPrior(
                    alpha=db_prior.alpha,
                    beta=db_prior.beta,
                    last_updated=db_prior.last_updated,
                    total_interactions=db_prior.total_interactions
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to load user prior: {e}")
            return None
    
    def save_user_prior(self, db: Session, user_id: str, prior: UserPrior) -> bool:
        """Save user prior to database"""
        try:
            from models_trust import UserTrustPrior
            
            # Check if prior exists
            db_prior = db.query(UserTrustPrior).filter(
                UserTrustPrior.user_id == user_id
            ).first()
            
            if db_prior:
                # Update existing
                db_prior.alpha = prior.alpha
                db_prior.beta = prior.beta
                db_prior.total_interactions = prior.total_interactions
                db_prior.mean_trust = prior.mean
                db_prior.variance = prior.variance
                db_prior.last_updated = prior.last_updated
            else:
                # Create new
                db_prior = UserTrustPrior(
                    user_id=user_id,
                    alpha=prior.alpha,
                    beta=prior.beta,
                    total_interactions=prior.total_interactions,
                    mean_trust=prior.mean,
                    variance=prior.variance,
                    last_updated=prior.last_updated
                )
                db.add(db_prior)
            
            db.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save user prior: {e}")
            db.rollback()
            return False
    
    def load_topic_prior(self, db: Session, topic: str) -> Optional[TopicPrior]:
        """Load topic prior from database"""
        try:
            from models_trust import TopicTrustPrior
            
            db_prior = db.query(TopicTrustPrior).filter(
                TopicTrustPrior.topic == topic
            ).first()
            
            if db_prior:
                return TopicPrior(
                    alpha=db_prior.alpha,
                    beta=db_prior.beta,
                    last_updated=db_prior.last_updated,
                    total_interactions=db_prior.total_interactions,
                    topic_keywords=db_prior.keywords
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to load topic prior: {e}")
            return None
    
    def save_topic_prior(self, db: Session, topic: str, prior: TopicPrior) -> bool:
        """Save topic prior to database"""
        try:
            from models_trust import TopicTrustPrior
            
            # Check if prior exists
            db_prior = db.query(TopicTrustPrior).filter(
                TopicTrustPrior.topic == topic
            ).first()
            
            if db_prior:
                # Update existing
                db_prior.alpha = prior.alpha
                db_prior.beta = prior.beta
                db_prior.total_interactions = prior.total_interactions
                db_prior.mean_trust = prior.mean
                db_prior.variance = prior.variance
                db_prior.keywords = prior.topic_keywords
                db_prior.last_updated = prior.last_updated
            else:
                # Create new
                db_prior = TopicTrustPrior(
                    topic=topic,
                    alpha=prior.alpha,
                    beta=prior.beta,
                    total_interactions=prior.total_interactions,
                    mean_trust=prior.mean,
                    variance=prior.variance,
                    keywords=prior.topic_keywords,
                    last_updated=prior.last_updated
                )
                db.add(db_prior)
            
            db.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save topic prior: {e}")
            db.rollback()
            return False
    
    def save_trust_feedback(self, db: Session, feedback_data: Dict[str, Any]) -> bool:
        """Save trust feedback to database"""
        try:
            from models_trust import TrustFeedback
            
            feedback = TrustFeedback(
                user_id=feedback_data['user_id'],
                query_id=feedback_data.get('query_id'),
                trust_score_id=feedback_data.get('trust_score_id'),
                predicted_trust=feedback_data['predicted_trust'],
                actual_correctness=feedback_data['actual_correctness'],
                feedback_weight=feedback_data.get('feedback_weight', 1.0),
                topic=feedback_data.get('topic', 'general'),
                user_rating=feedback_data.get('user_rating'),
                user_comment=feedback_data.get('user_comment')
            )
            
            db.add(feedback)
            db.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save trust feedback: {e}")
            db.rollback()
            return False
    
    def save_audit_report(self, db: Session, audit_report: Dict[str, Any]) -> bool:
        """Save audit report to database"""
        try:
            from models_trust import TrustAuditReport
            
            report = TrustAuditReport(
                explainability_score=audit_report['metrics']['explainability'],
                source_diversity_score=audit_report['metrics']['source_diversity'],
                fairness_score=audit_report['metrics']['fairness'],
                robustness_score=audit_report['metrics']['robustness'],
                sample_size=audit_report['sample_size'],
                audit_period_start=audit_report.get('audit_period_start'),
                audit_period_end=audit_report.get('audit_period_end'),
                threshold_breaches=audit_report['threshold_breaches'],
                recommendations=audit_report['recommendations'],
                risk_level=audit_report['risk_level']
            )
            
            db.add(report)
            db.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save audit report: {e}")
            db.rollback()
            return False
    
    def save_evaluation_report(self, db: Session, eval_report: Dict[str, Any]) -> bool:
        """Save evaluation report to database"""
        try:
            from models_trust import TrustEvaluationReport
            
            report = TrustEvaluationReport(
                roc_auc=eval_report['metrics']['roc_auc'],
                calibration_error=eval_report['metrics']['calibration_error'],
                accuracy=eval_report['metrics']['accuracy'],
                f1_score=eval_report['metrics']['f1_score'],
                precision_at_thresholds=eval_report['metrics']['precision_at_threshold'],
                recall_at_thresholds=eval_report['metrics']['recall_at_threshold'],
                sample_size=eval_report['sample_size'],
                evaluation_type=eval_report.get('evaluation_type', 'nightly'),
                threshold_alerts=eval_report['threshold_alerts'],
                recommendations=eval_report['recommendations'],
                performance_trend=eval_report.get('performance_trend')
            )
            
            db.add(report)
            db.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save evaluation report: {e}")
            db.rollback()
            return False
    
    def get_trust_score_history(self, db: Session, user_id: str, 
                               limit: int = 100) -> List[Dict[str, Any]]:
        """Get trust score history for a user"""
        try:
            from models_trust import TrustScore
            
            scores = db.query(TrustScore).filter(
                TrustScore.user_id == user_id
            ).order_by(desc(TrustScore.created_at)).limit(limit).all()
            
            return [
                {
                    'id': score.id,
                    'overall_score': score.overall_score,
                    'topic': score.topic,
                    'created_at': score.created_at.isoformat(),
                    'explanation': score.explanation,
                    'factors': score.factors
                }
                for score in scores
            ]
            
        except Exception as e:
            self.logger.error(f"Failed to get trust score history: {e}")
            return []
    
    def get_system_performance_metrics(self, db: Session, 
                                     days: int = 30) -> Dict[str, Any]:
        """Get system performance metrics"""
        try:
            from models_trust import TrustScore, TrustEvaluationReport
            from datetime import timedelta
            
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # Trust score statistics
            recent_scores = db.query(TrustScore).filter(
                TrustScore.created_at >= cutoff_date
            ).all()
            
            if recent_scores:
                scores = [s.overall_score for s in recent_scores]
                avg_score = sum(scores) / len(scores)
                min_score = min(scores)
                max_score = max(scores)
            else:
                avg_score = min_score = max_score = 0.0
            
            # Latest evaluation report
            latest_eval = db.query(TrustEvaluationReport).order_by(
                desc(TrustEvaluationReport.created_at)
            ).first()
            
            return {
                'period_days': days,
                'total_trust_scores': len(recent_scores),
                'avg_trust_score': avg_score,
                'min_trust_score': min_score,
                'max_trust_score': max_score,
                'latest_evaluation': {
                    'roc_auc': latest_eval.roc_auc if latest_eval else None,
                    'calibration_error': latest_eval.calibration_error if latest_eval else None,
                    'performance_trend': latest_eval.performance_trend if latest_eval else None,
                    'created_at': latest_eval.created_at.isoformat() if latest_eval else None
                } if latest_eval else None
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get performance metrics: {e}")
            return {"error": str(e)}


# Global database manager instance
trust_db_manager = TrustDatabaseManager()
