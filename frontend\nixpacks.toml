# Nixpacks configuration for Railway deployment
# This optimizes the build process for memory-constrained environments

[phases.setup]
nixPkgs = ["nodejs-18_x", "npm-9_x"]

[phases.install]
cmds = [
    "npm ci --prefer-offline --no-audit --progress=false",
]

[phases.build]
cmds = [
    "NODE_OPTIONS='--max-old-space-size=4096' npm run build"
]

[start]
cmd = "npm start"

[variables]
NODE_ENV = "production"
NPM_CONFIG_PRODUCTION = "false"
