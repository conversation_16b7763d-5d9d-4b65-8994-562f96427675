<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1166.91015625 1584.6016845703125" style="max-width: 1166.91015625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091"><style>#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .error-icon{fill:#a44141;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edge-thickness-normal{stroke-width:1px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .marker.cross{stroke:lightgrey;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 p{margin:0;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .cluster-label text{fill:#F9FFFE;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .cluster-label span{color:#F9FFFE;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .cluster-label span p{background-color:transparent;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .label text,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 span{fill:#ccc;color:#ccc;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node rect,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node circle,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node ellipse,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node polygon,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .rough-node .label text,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node .label text,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .image-shape .label,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .icon-shape .label{text-anchor:middle;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .rough-node .label,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node .label,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .image-shape .label,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .icon-shape .label{text-align:center;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .node.clickable{cursor:pointer;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .arrowheadPath{fill:lightgrey;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .cluster text{fill:#F9FFFE;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .cluster span{color:#F9FFFE;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 rect.text{fill:none;stroke-width:0;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .icon-shape,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .icon-shape p,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .icon-shape rect,#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .input&gt;*{fill:#e3f2fd!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .input span{fill:#e3f2fd!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .processing&gt;*{fill:#f1f8e9!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .processing span{fill:#f1f8e9!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .storage&gt;*{fill:#fff3e0!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .storage span{fill:#fff3e0!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .ai&gt;*{fill:#fce4ec!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .ai span{fill:#fce4ec!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .trust&gt;*{fill:#e8f5e8!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .trust span{fill:#e8f5e8!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .output&gt;*{fill:#f3e5f5!important;}#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091 .output span{fill:#f3e5f5!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Files_Upload_0" d="M384.234,86L384.234,90.167C384.234,94.333,384.234,102.667,384.234,110.333C384.234,118,384.234,125,384.234,128.5L384.234,132"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Upload_OCR_1" d="M333.742,190L325.95,194.167C318.158,198.333,302.574,206.667,294.782,214.333C286.99,222,286.99,229,286.99,232.5L286.99,236"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Upload_Parser_2" d="M480.84,184.124L504.373,189.27C527.906,194.416,574.973,204.708,598.506,213.354C622.039,222,622.039,229,622.039,232.5L622.039,236"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OCR_Embeddings_3" d="M286.99,318L286.99,322.167C286.99,326.333,286.99,334.667,287.538,342.341C288.086,350.016,289.183,357.032,289.731,360.54L290.279,364.048"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Parser_Embeddings_4" d="M529.934,307.727L511.085,313.606C492.236,319.485,454.538,331.242,428.474,340.974C402.411,350.705,387.981,358.411,380.767,362.263L373.552,366.116"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Embeddings_VectorDB_5" d="M296.99,446L296.99,450.167C296.99,454.333,296.99,462.667,318.226,474.944C339.461,487.222,381.931,503.445,403.167,511.556L424.402,519.667"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Parser_PostgreSQL_6" d="M714.145,310.073L730.411,315.561C746.678,321.049,779.211,332.024,795.478,348.179C811.744,364.333,811.744,385.667,811.744,407C811.744,428.333,811.744,449.667,820.381,467.84C829.018,486.014,846.293,501.028,854.93,508.535L863.567,516.043"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Query_NLP_7" d="M860.379,190L860.379,194.167C860.379,198.333,860.379,206.667,860.379,214.333C860.379,222,860.379,229,860.379,232.5L860.379,236"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NLP_Retrieval_8" d="M764.145,311.466L748.566,316.722C732.988,321.977,701.831,332.489,686.252,343.244C670.674,354,670.674,365,670.674,370.5L670.674,376"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Retrieval_VectorDB_9" d="M670.674,434L670.674,440.167C670.674,446.333,670.674,458.667,653.491,472.313C636.309,485.959,601.944,500.918,584.762,508.397L567.58,515.877"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Retrieval_PostgreSQL_10" d="M776.744,432.12L804.106,438.6C831.467,445.08,886.191,458.04,913.08,468.121C939.969,478.202,939.024,485.404,938.552,489.005L938.08,492.606"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VectorDB_Context_11" d="M496.025,598.049L496.025,602.216C496.025,606.383,496.025,614.716,502.747,622.719C509.468,630.722,522.911,638.394,529.632,642.23L536.353,646.066"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PostgreSQL_Context_12" d="M930.914,597.545L930.914,601.796C930.914,606.046,930.914,614.548,892.024,624.681C853.135,634.814,775.355,646.579,736.466,652.461L697.576,658.344"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Context_LLM1_13" d="M480.645,686.427L417.277,693.197C353.91,699.968,227.176,713.508,163.809,723.779C100.441,734.049,100.441,741.049,100.441,744.549L100.441,748.049"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Context_LLM2_14" d="M587.133,702.049L587.133,706.216C587.133,710.383,587.133,718.716,587.133,726.383C587.133,734.049,587.133,741.049,587.133,744.549L587.133,748.049"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM1_TrustCalc_15" d="M168.447,830.049L175.713,834.216C182.979,838.383,197.51,846.716,259.586,857.465C321.663,868.215,431.285,881.381,486.096,887.963L540.907,894.546"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM2_TrustCalc_16" d="M587.133,830.049L587.133,834.216C587.133,838.383,587.133,846.716,591.275,854.604C595.416,862.491,603.7,869.934,607.842,873.655L611.984,877.376"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Context_TrustCalc_17" d="M657.622,702.049L668.5,706.216C679.379,710.383,701.135,718.716,712.013,733.549C722.891,748.383,722.891,769.716,722.891,791.049C722.891,812.383,722.891,833.716,717.205,848.179C711.519,862.642,700.147,870.235,694.461,874.032L688.775,877.828"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustCalc_Score1_18" d="M544.879,921.52L501.597,927.775C458.315,934.03,371.751,946.539,328.469,956.294C285.188,966.049,285.188,973.049,285.188,976.549L285.188,980.049"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustCalc_Score2_19" d="M598.267,934.049L591.053,938.216C583.839,942.383,569.412,950.716,562.198,958.383C554.984,966.049,554.984,973.049,554.984,976.549L554.984,980.049"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustCalc_Score3_20" d="M734.449,934.049L748.251,938.216C762.053,942.383,789.658,950.716,803.46,958.383C817.262,966.049,817.262,973.049,817.262,976.549L817.262,980.049"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustCalc_Score4_21" d="M745.145,919.448L798.449,926.048C851.754,932.648,958.363,945.849,1011.668,955.949C1064.973,966.049,1064.973,973.049,1064.973,976.549L1064.973,980.049"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Score1_FinalTrust_22" d="M285.188,1062.049L285.188,1066.216C285.188,1070.383,285.188,1078.716,334.233,1090.711C383.278,1102.707,481.368,1118.365,530.413,1126.194L579.458,1134.023"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Score2_FinalTrust_23" d="M554.984,1062.049L554.984,1066.216C554.984,1070.383,554.984,1078.716,562.923,1086.757C570.861,1094.798,586.739,1102.546,594.677,1106.421L602.616,1110.295"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Score3_FinalTrust_24" d="M817.262,1062.049L817.262,1066.216C817.262,1070.383,817.262,1078.716,809.323,1086.757C801.385,1094.798,785.508,1102.546,777.569,1106.421L769.63,1110.295"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Score4_FinalTrust_25" d="M1064.973,1062.049L1064.973,1066.216C1064.973,1070.383,1064.973,1078.716,1019.608,1090.546C974.242,1102.377,883.512,1117.704,838.147,1125.367L792.782,1133.031"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM1_Response_26" d="M94.348,830.049L93.697,834.216C93.046,838.383,91.743,846.716,91.092,859.549C90.441,872.383,90.441,889.716,90.441,907.049C90.441,924.383,90.441,941.716,90.441,961.049C90.441,980.383,90.441,1001.716,90.441,1023.049C90.441,1044.383,90.441,1065.716,90.441,1087.049C90.441,1108.383,90.441,1129.716,90.441,1151.049C90.441,1172.383,90.441,1193.716,139.719,1210.861C188.996,1228.007,287.551,1240.965,336.829,1247.444L386.106,1253.922"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FinalTrust_Response_27" d="M686.123,1190.049L686.123,1194.216C686.123,1198.383,686.123,1206.716,669.385,1215.231C652.647,1223.745,619.17,1232.442,602.432,1236.79L585.694,1241.138"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Response_Cache_28" d="M390.072,1284.52L358.491,1290.275C326.909,1296.03,263.745,1307.539,232.164,1316.794C200.582,1326.049,200.582,1333.049,200.582,1336.549L200.582,1340.049"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Response_Analytics_29" d="M575.629,1294.049L589.469,1298.216C603.309,1302.383,630.988,1310.716,644.828,1320.595C658.668,1330.475,658.668,1341.9,658.668,1347.613L658.668,1353.325"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Response_Audit_30" d="M454.275,1294.049L449.388,1298.216C444.5,1302.383,434.725,1310.716,429.837,1320.595C424.949,1330.475,424.949,1341.9,424.949,1347.613L424.949,1353.325"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Response_UI_31" d="M396.265,1294.049L382.426,1298.216C368.586,1302.383,340.906,1310.716,327.066,1327.762C313.227,1344.808,313.227,1370.567,313.227,1396.325C313.227,1422.084,313.227,1447.843,325.65,1465.326C338.073,1482.808,362.92,1492.015,375.343,1496.618L387.767,1501.222"></path><path marker-end="url(#mermaid-20ae6d14-1d0a-45b1-a3b9-84e20dbe2091_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Analytics_UI_32" d="M658.668,1435.325L658.668,1441.705C658.668,1448.084,658.668,1460.843,646.245,1471.826C633.821,1482.808,608.974,1492.015,596.551,1496.618L584.128,1501.222"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(384.234375, 47)" id="flowchart-Files-290" class="node default input"><rect height="78" width="227.984375" y="-39" x="-113.9921875" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-83.9921875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="167.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📁 Data Files<br>CSV, Excel, PDF, Images</p></span></div></foreignObject></g></g><g transform="translate(384.234375, 163)" id="flowchart-Upload-291" class="node default input"><rect height="54" width="193.2109375" y="-27" x="-96.60546875" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-66.60546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="133.2109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📤 Upload Service</p></span></div></foreignObject></g></g><g transform="translate(286.990234375, 279)" id="flowchart-OCR-293" class="node default processing"><rect height="78" width="195.1875" y="-39" x="-97.59375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-67.59375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="135.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔍 OCR Processing<br>Tesseract</p></span></div></foreignObject></g></g><g transform="translate(622.0390625, 279)" id="flowchart-Parser-295" class="node default processing"><rect height="78" width="184.2109375" y="-39" x="-92.10546875" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-62.10546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="124.2109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Data Parser<br>Pandas/Openpyxl</p></span></div></foreignObject></g></g><g transform="translate(296.990234375, 407)" id="flowchart-Embeddings-297" class="node default"><rect height="78" width="226.515625" y="-39" x="-113.2578125" style="" class="basic label-container"></rect><g transform="translate(-83.2578125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="166.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🧠 Vector Embeddings<br>OpenAI/Sentence-BERT</p></span></div></foreignObject></g></g><g transform="translate(496.025390625, 547.0246238708496)" id="flowchart-VectorDB-301" class="node default storage"><path transform="translate(-67.88671875, -51.02462626202104)" style="fill:#fff3e0 !important" class="basic label-container" d="M0,13.016417508014023 a67.88671875,13.016417508014023 0,0,0 135.7734375,0 a67.88671875,13.016417508014023 0,0,0 -135.7734375,0 l0,76.01641750801403 a67.88671875,13.016417508014023 0,0,0 135.7734375,0 l0,-76.01641750801403"></path><g transform="translate(-60.38671875, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="120.7734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗄️ Vector Store<br>Similarity Search</p></span></div></foreignObject></g></g><g transform="translate(930.9140625, 547.0246238708496)" id="flowchart-PostgreSQL-303" class="node default storage"><path transform="translate(-64.328125, -50.520266108168045)" style="fill:#fff3e0 !important" class="basic label-container" d="M0,12.680177405445361 a64.328125,12.680177405445361 0,0,0 128.65625,0 a64.328125,12.680177405445361 0,0,0 -128.65625,0 l0,75.68017740544536 a64.328125,12.680177405445361 0,0,0 128.65625,0 l0,-75.68017740544536"></path><g transform="translate(-56.828125, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="113.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🐘 PostgreSQL<br>Structured Data</p></span></div></foreignObject></g></g><g transform="translate(860.37890625, 163)" id="flowchart-Query-304" class="node default input"><rect height="54" width="165.796875" y="-27" x="-82.8984375" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-52.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="105.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>❓ User Query</p></span></div></foreignObject></g></g><g transform="translate(860.37890625, 279)" id="flowchart-NLP-305" class="node default processing"><rect height="78" width="192.46875" y="-39" x="-96.234375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-66.234375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="132.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔤 NLP Processing<br>Intent Recognition</p></span></div></foreignObject></g></g><g transform="translate(670.673828125, 407)" id="flowchart-Retrieval-307" class="node default processing"><rect height="54" width="212.140625" y="-27" x="-106.0703125" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-76.0703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔍 Context Retrieval</p></span></div></foreignObject></g></g><g transform="translate(587.1328125, 675.0492477416992)" id="flowchart-Context-313" class="node default processing"><rect height="54" width="212.9765625" y="-27" x="-106.48828125" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-76.48828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152.9765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 Context Assembly</p></span></div></foreignObject></g></g><g transform="translate(100.44140625, 791.0492477416992)" id="flowchart-LLM1-317" class="node default ai"><rect height="78" width="184.8828125" y="-39" x="-92.44140625" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-62.44140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="124.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 OpenAI GPT<br>Primary Response</p></span></div></foreignObject></g></g><g transform="translate(587.1328125, 791.0492477416992)" id="flowchart-LLM2-319" class="node default ai"><rect height="78" width="201.515625" y="-39" x="-100.7578125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-70.7578125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="141.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 Google Gemini<br>Validation Response</p></span></div></foreignObject></g></g><g transform="translate(645.01171875, 907.0492477416992)" id="flowchart-TrustCalc-321" class="node default trust"><rect height="54" width="200.265625" y="-27" x="-100.1328125" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-70.1328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="140.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🛡️ Trust Calculator</p></span></div></foreignObject></g></g><g transform="translate(285.1875, 1023.0492477416992)" id="flowchart-Score1-327" class="node default trust"><rect height="78" width="222.5859375" y="-39" x="-111.29296875" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-81.29296875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="162.5859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Convergence Score<br>40% Weight</p></span></div></foreignObject></g></g><g transform="translate(554.984375, 1023.0492477416992)" id="flowchart-Score2-329" class="node default trust"><rect height="78" width="217.0078125" y="-39" x="-108.50390625" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-78.50390625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="157.0078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Faithfulness Score<br>30% Weight</p></span></div></foreignObject></g></g><g transform="translate(817.26171875, 1023.0492477416992)" id="flowchart-Score3-331" class="node default trust"><rect height="78" width="207.546875" y="-39" x="-103.7734375" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-73.7734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="147.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Production Score<br>20% Weight</p></span></div></foreignObject></g></g><g transform="translate(1064.97265625, 1023.0492477416992)" id="flowchart-Score4-333" class="node default trust"><rect height="78" width="187.875" y="-39" x="-93.9375" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-63.9375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="127.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Citation Score<br>10% Weight</p></span></div></foreignObject></g></g><g transform="translate(686.123046875, 1151.0492477416992)" id="flowchart-FinalTrust-335" class="node default trust"><rect height="78" width="205.4296875" y="-39" x="-102.71484375" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-72.71484375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="145.4296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 Final Trust Score<br>0-100%</p></span></div></foreignObject></g></g><g transform="translate(485.947265625, 1267.0492477416992)" id="flowchart-Response-343" class="node default output"><rect height="54" width="191.75" y="-27" x="-95.875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-65.875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="131.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📝 Final Response</p></span></div></foreignObject></g></g><g transform="translate(200.58203125, 1396.3254470825195)" id="flowchart-Cache-347" class="node default storage"><path transform="translate(-77.64453125, -52.2761936616774)" style="fill:#fff3e0 !important" class="basic label-container" d="M0,13.8507957744516 a77.64453125,13.8507957744516 0,0,0 155.2890625,0 a77.64453125,13.8507957744516 0,0,0 -155.2890625,0 l0,76.8507957744516 a77.64453125,13.8507957744516 0,0,0 155.2890625,0 l0,-76.8507957744516"></path><g transform="translate(-70.14453125, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="140.2890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚡ Response Cache<br>Performance</p></span></div></foreignObject></g></g><g transform="translate(658.66796875, 1396.3254470825195)" id="flowchart-Analytics-349" class="node default output"><rect height="78" width="213.9921875" y="-39" x="-106.99609375" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-76.99609375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="153.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📈 Analytics Engine<br>Insights &amp; Predictions</p></span></div></foreignObject></g></g><g transform="translate(424.94921875, 1396.3254470825195)" id="flowchart-Audit-351" class="node default output"><rect height="78" width="153.4453125" y="-39" x="-76.72265625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-46.72265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="93.4453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 Audit Log<br>Compliance</p></span></div></foreignObject></g></g><g transform="translate(485.947265625, 1537.6016464233398)" id="flowchart-UI-353" class="node default output"><rect height="78" width="188.859375" y="-39" x="-94.4296875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-64.4296875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="128.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🖥️ User Interface<br>Chat/Dashboard</p></span></div></foreignObject></g></g></g></g></g></svg>