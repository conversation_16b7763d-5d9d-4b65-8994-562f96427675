@echo off
echo ==========================================
echo      Stopping AIthentiq Services
echo ==========================================
echo.

echo Stopping all Node.js processes (Frontend)...
taskkill /f /im node.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend processes stopped
) else (
    echo ℹ️ No frontend processes found
)

echo.

echo Stopping all Python processes (Backend)...
taskkill /f /im python.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend processes stopped
) else (
    echo ℹ️ No backend processes found
)

echo.

echo Stopping any remaining processes on ports 3000 and 8000...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 2^>nul') do (
    taskkill /f /pid %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000 2^>nul') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo ==========================================
echo     AIthentiq Services Stopped
echo ==========================================
echo.
echo All services have been stopped.
echo.
echo You can restart them using:
echo - start_backend.bat (Backend only)
echo - start_frontend.bat (Frontend only)
echo - start_both.bat (Both services)
echo.
pause
