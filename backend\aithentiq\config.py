"""
Trust Score Configuration
"""

import os
from typing import Dict, List, Optional


class TrustConfig:
    """Configuration for Trust Score System"""

    def __init__(self):
        # Component Fusion Weights (optimistic - trust AI more)
        self.model_confidence_weight: float = 0.4  # Trust the AI more
        self.source_quality_weight: float = 0.3    # Good sources matter most
        self.citation_accuracy_weight: float = 0.2 # Less strict on citations
        self.question_match_weight: float = 0.1    # Less strict on perfect matching

        # Bayesian Prior Parameters
        self.user_prior_alpha: float = 2.0
        self.user_prior_beta: float = 2.0
        self.topic_prior_alpha: float = 1.5
        self.topic_prior_beta: float = 1.5

        # Bayesian Fusion Weights
        self.base_trust_weight: float = 0.5
        self.user_posterior_weight: float = 0.3
        self.topic_posterior_weight: float = 0.2

        # Trust Score Thresholds (more realistic)
        self.high_trust_threshold: float = 0.7     # High reliability: 70-100%
        self.moderate_trust_threshold: float = 0.5 # Moderate reliability: 50-69%
        self.low_trust_threshold: float = 0.0      # Low reliability: 0-49%

        # Audit Thresholds
        self.explainability_threshold: float = 0.7
        self.source_diversity_threshold: float = 0.6
        self.fairness_threshold: float = 0.8
        self.robustness_threshold: float = 0.75

        # Evaluation Parameters
        self.evaluation_sample_size: int = 1000
        self.evaluation_frequency_hours: int = 24
        self.roc_auc_threshold: float = 0.75
        self.calibration_error_threshold: float = 0.1

        # Model Paths
        self.qat_regressor_path: Optional[str] = None
        self.refusal_detector_path: Optional[str] = None

        # Cache Settings
        self.enable_caching: bool = True
        self.cache_ttl_seconds: int = 3600

        # Monitoring
        self.enable_monitoring: bool = True
        self.alert_email: Optional[str] = None
        self.slack_webhook: Optional[str] = None


# Global configuration instance
try:
    trust_config = TrustConfig()
    print("✅ Trust config loaded successfully")
except Exception as e:
    print(f"Warning: Failed to load trust config: {e}")
    # Create a minimal config for testing
    trust_config = None
