"""
Multi-tenant FastAPI application for AIthentiq
Phase 1: Foundation & Core Infrastructure Implementation
"""

from fastapi import FastAPI, HTTPException, Depends, status, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any, List, Optional
import os
import json
import uuid
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Import multi-tenant models and middleware
from database import get_db, engine
from models_multitenant import (
    Base, Tenant, TenantUser, TenantApi<PERSON>ey, TenantDataset, 
    TenantQuery, DataConnector, TenantDocumentChunk
)
from middleware.tenant_isolation import (
    TenantIsolationMiddleware, get_current_tenant, get_current_user,
    get_current_tenant_id, get_current_user_id
)
from services.task_queue_service import task_queue, TaskType

# Load environment variables
load_dotenv()

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="AIthentiq Multi-Tenant API",
    description="Multi-tenant RAG platform with trust scoring and data connectors",
    version="1.0.0"
)

# Configure CORS
FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://localhost:3000')
CORS_ORIGINS = os.getenv('CORS_ORIGINS', '*')

if CORS_ORIGINS == '*':
    allowed_origins = ["*"]
else:
    allowed_origins = [
        FRONTEND_URL,
        "http://localhost:3000",
        "https://aithentiq-6m1a.onrender.com",
        "https://aithentiq.com",
        "https://www.aithentiq.com"
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Add tenant isolation middleware
app.add_middleware(TenantIsolationMiddleware)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db = next(get_db())
        db.execute(text("SELECT 1"))
        db.close()
        
        return {
            "status": "healthy",
            "service": "aithentiq-multitenant",
            "version": "1.0.0",
            "database": "connected",
            "task_queue": task_queue.backend_type,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "aithentiq-multitenant",
            "version": "1.0.0",
            "database": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

# Tenant Management Endpoints

@app.post("/tenants/register")
async def register_tenant(
    tenant_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """
    Register a new tenant
    """
    try:
        # Validate required fields
        required_fields = ["name", "domain", "display_name", "admin_email"]
        for field in required_fields:
            if field not in tenant_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required field: {field}"
                )
        
        # Check if domain is already taken
        existing_tenant = db.query(Tenant).filter(Tenant.domain == tenant_data["domain"]).first()
        if existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Domain already exists"
            )
        
        # Create tenant
        tenant = Tenant(
            name=tenant_data["name"],
            domain=tenant_data["domain"],
            display_name=tenant_data["display_name"],
            subscription_plan=tenant_data.get("subscription_plan", "free"),
            max_users=tenant_data.get("max_users", 5),
            max_datasets=tenant_data.get("max_datasets", 10),
            max_storage_gb=tenant_data.get("max_storage_gb", 1.0)
        )
        
        db.add(tenant)
        db.commit()
        db.refresh(tenant)
        
        # Create admin user
        admin_user = TenantUser(
            tenant_id=tenant.id,
            email=tenant_data["admin_email"],
            name=tenant_data.get("admin_name", "Admin"),
            role="admin",
            is_verified=True,
            permissions={
                "can_upload_datasets": True,
                "can_create_queries": True,
                "can_manage_connectors": True,
                "can_view_analytics": True,
                "can_manage_users": True
            }
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # Create API key for admin user
        api_key = TenantApiKey(
            tenant_id=tenant.id,
            user_id=admin_user.id,
            name="Admin API Key",
            description="Default API key for admin user"
        )
        
        db.add(api_key)
        db.commit()
        db.refresh(api_key)
        
        return {
            "status": "success",
            "tenant": {
                "id": tenant.id,
                "name": tenant.name,
                "domain": tenant.domain,
                "display_name": tenant.display_name
            },
            "admin_user": {
                "id": admin_user.id,
                "email": admin_user.email,
                "name": admin_user.name
            },
            "api_key": api_key.key
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to register tenant: {str(e)}"
        )

@app.get("/tenants/current")
async def get_current_tenant_info(
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get current tenant information
    """
    return {
        "id": current_tenant.id,
        "name": current_tenant.name,
        "domain": current_tenant.domain,
        "display_name": current_tenant.display_name,
        "subscription_plan": current_tenant.subscription_plan,
        "features_enabled": current_tenant.features_enabled,
        "preferred_llm": current_tenant.preferred_llm,
        "preferred_embedding": current_tenant.preferred_embedding,
        "preferred_vector_store": current_tenant.preferred_vector_store,
        "is_active": current_tenant.is_active,
        "is_trial": current_tenant.is_trial,
        "created_at": current_tenant.created_at.isoformat()
    }

@app.put("/tenants/current/config")
async def update_tenant_config(
    config_data: Dict[str, Any],
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: TenantUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update tenant configuration (admin only)
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    # Update allowed fields
    allowed_fields = [
        "preferred_llm", "preferred_embedding", "preferred_vector_store",
        "features_enabled", "require_2fa", "allowed_domains", "ip_whitelist"
    ]
    
    for field in allowed_fields:
        if field in config_data:
            setattr(current_tenant, field, config_data[field])
    
    current_tenant.updated_at = datetime.utcnow()
    db.commit()
    
    return {"status": "success", "message": "Tenant configuration updated"}

# User Management Endpoints

@app.get("/users/current")
async def get_current_user_info(
    current_user: TenantUser = Depends(get_current_user)
):
    """
    Get current user information
    """
    return {
        "id": current_user.id,
        "email": current_user.email,
        "name": current_user.name,
        "role": current_user.role,
        "permissions": current_user.permissions,
        "is_active": current_user.is_active,
        "is_verified": current_user.is_verified,
        "last_login": current_user.last_login.isoformat() if current_user.last_login else None,
        "created_at": current_user.created_at.isoformat()
    }

@app.get("/users")
async def list_tenant_users(
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: TenantUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List all users in the current tenant (admin only)
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    users = db.query(TenantUser).filter(TenantUser.tenant_id == current_tenant.id).all()
    
    return {
        "users": [
            {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "role": user.role,
                "is_active": user.is_active,
                "is_verified": user.is_verified,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "created_at": user.created_at.isoformat()
            }
            for user in users
        ]
    }

# Dataset Management Endpoints

@app.get("/datasets")
async def list_datasets(
    current_tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    List datasets for the current tenant
    """
    datasets = db.query(TenantDataset).filter(
        TenantDataset.tenant_id == current_tenant_id
    ).all()
    
    return {
        "datasets": [
            {
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "source_type": dataset.source_type,
                "file_type": dataset.file_type,
                "content_type": dataset.content_type,
                "processing_status": dataset.processing_status,
                "row_count": dataset.row_count,
                "word_count": dataset.word_count,
                "chunk_count": dataset.chunk_count,
                "created_at": dataset.created_at.isoformat()
            }
            for dataset in datasets
        ]
    }

# Task Queue Endpoints

@app.post("/tasks/enqueue")
async def enqueue_task(
    task_data: Dict[str, Any],
    current_tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Enqueue a background task
    """
    try:
        task_type_str = task_data.get("task_type")
        if not task_type_str:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="task_type is required"
            )
        
        # Convert string to TaskType enum
        try:
            task_type = TaskType(task_type_str)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid task_type: {task_type_str}"
            )
        
        # Enqueue the task
        task_id = task_queue.enqueue_task(
            task_type=task_type,
            task_data=task_data.get("data", {}),
            tenant_id=current_tenant_id,
            user_id=current_user_id,
            priority=task_data.get("priority", "normal"),
            delay_seconds=task_data.get("delay_seconds", 0)
        )
        
        return {
            "status": "success",
            "task_id": task_id,
            "task_type": task_type_str
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to enqueue task: {str(e)}"
        )

@app.get("/tasks/{task_id}/status")
async def get_task_status(task_id: str):
    """
    Get the status of a task
    """
    try:
        status_info = task_queue.get_task_status(task_id)
        return status_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task status: {str(e)}"
        )

@app.get("/tasks/queue/stats")
async def get_queue_stats():
    """
    Get task queue statistics
    """
    try:
        stats = task_queue.get_queue_stats()
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get queue stats: {str(e)}"
        )

# Database Migration Endpoint

@app.post("/admin/migrate-database")
async def migrate_multitenant_database(
    current_user: TenantUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Migrate database to multi-tenant schema (admin only)
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    try:
        # Create all multi-tenant tables
        Base.metadata.create_all(bind=engine)
        
        return {
            "status": "success",
            "message": "Multi-tenant database schema created successfully",
            "tables_created": [
                "tenants",
                "tenant_users", 
                "tenant_api_keys",
                "data_connectors",
                "tenant_datasets",
                "tenant_queries",
                "tenant_document_chunks",
                "tenant_source_attributions"
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database migration failed: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
