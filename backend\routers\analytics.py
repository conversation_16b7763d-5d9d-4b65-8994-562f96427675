from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
import json

from database import get_db
import models
import schemas
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.statistical_analysis_service import StatisticalAnalysisService
from services.time_series_service import TimeSeriesService
from services.business_templates_service import BusinessTemplatesService

router = APIRouter(
    prefix="/analytics",
    tags=["analytics"]
)

@router.get("/summary/{dataset_id}")
async def get_summary_statistics(
    dataset_id: int,
    columns: Optional[List[str]] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Get summary statistics for a dataset
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Get summary statistics
    try:
        summary_stats = stats_service.get_summary_statistics(columns)
        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "summary_statistics": summary_stats
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error calculating summary statistics: {str(e)}"
        )

@router.post("/correlation/{dataset_id}")
async def get_correlation_analysis(
    dataset_id: int,
    request: schemas.CorrelationRequest,
    db: Session = Depends(get_db)
):
    """
    Get correlation analysis for a dataset
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Get correlation analysis
    try:
        correlation_results = stats_service.correlation_analysis(
            columns=request.columns,
            method=request.method
        )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "correlation_analysis": correlation_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error calculating correlation analysis: {str(e)}"
        )

@router.post("/hypothesis-test/{dataset_id}")
async def perform_hypothesis_test(
    dataset_id: int,
    request: schemas.HypothesisTestRequest,
    db: Session = Depends(get_db)
):
    """
    Perform a hypothesis test on a dataset
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Perform hypothesis test
    try:
        test_results = stats_service.perform_hypothesis_test(
            test_type=request.test_type,
            column1=request.column1,
            column2=request.column2,
            alpha=request.alpha,
            alternative=request.alternative
        )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "hypothesis_test": test_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error performing hypothesis test: {str(e)}"
        )

@router.post("/outliers/{dataset_id}")
async def detect_outliers(
    dataset_id: int,
    request: schemas.OutlierDetectionRequest,
    db: Session = Depends(get_db)
):
    """
    Detect outliers in a dataset
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Detect outliers
    try:
        outlier_results = stats_service.detect_outliers(
            column=request.column,
            method=request.method,
            threshold=request.threshold
        )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "outlier_detection": outlier_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting outliers: {str(e)}"
        )

@router.post("/time-series/forecast/{dataset_id}")
async def forecast_time_series(
    dataset_id: int,
    request: schemas.TimeSeriesForecastRequest,
    db: Session = Depends(get_db)
):
    """
    Forecast future values in a time series
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create time series service
    ts_service = TimeSeriesService(df)

    # Generate forecast
    try:
        forecast_results = ts_service.forecast(
            value_column=request.value_column,
            date_column=request.date_column,
            forecast_periods=request.forecast_periods,
            frequency=request.frequency,
            method=request.method
        )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "forecast": forecast_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating forecast: {str(e)}"
        )

@router.post("/time-series/seasonality/{dataset_id}")
async def analyze_seasonality(
    dataset_id: int,
    request: schemas.TimeSeriesAnalysisRequest,
    db: Session = Depends(get_db)
):
    """
    Analyze seasonality in a time series
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create time series service
    ts_service = TimeSeriesService(df)

    # Analyze seasonality
    try:
        seasonality_results = ts_service.analyze_seasonality(
            value_column=request.value_column,
            date_column=request.date_column,
            frequency=request.frequency
        )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "seasonality_analysis": seasonality_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing seasonality: {str(e)}"
        )

@router.post("/time-series/trend/{dataset_id}")
async def analyze_trend(
    dataset_id: int,
    request: schemas.TimeSeriesAnalysisRequest,
    db: Session = Depends(get_db)
):
    """
    Analyze trend in a time series
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create time series service
    ts_service = TimeSeriesService(df)

    # Analyze trend
    try:
        trend_results = ts_service.analyze_trend(
            value_column=request.value_column,
            date_column=request.date_column,
            frequency=request.frequency
        )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "trend_analysis": trend_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing trend: {str(e)}"
        )

@router.post("/time-series/anomalies/{dataset_id}")
async def detect_time_series_anomalies(
    dataset_id: int,
    request: schemas.TimeSeriesAnomalyRequest,
    db: Session = Depends(get_db)
):
    """
    Detect anomalies in a time series
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load the dataset from parsed_data
    try:
        df = pd.read_json(pd.io.common.StringIO(dataset.parsed_data))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create time series service
    ts_service = TimeSeriesService(df)

    # Detect anomalies
    try:
        anomaly_results = ts_service.detect_anomalies(
            value_column=request.value_column,
            date_column=request.date_column,
            method=request.method,
            threshold=request.threshold,
            frequency=request.frequency
        )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "anomaly_detection": anomaly_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting anomalies: {str(e)}"
        )

# Enhanced Statistical Analysis Endpoints

@router.get("/distribution/{dataset_id}")
async def get_distribution_analysis(
    dataset_id: int,
    column: str = Query(..., description="Column name for distribution analysis"),
    db: Session = Depends(get_db)
):
    """
    Get distribution analysis for a specific column
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load dataset
    try:
        parsed_data = json.loads(dataset.parsed_data)
        df = pd.DataFrame(parsed_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Get distribution analysis
    try:
        distribution_results = stats_service.get_distribution_analysis(column)
        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "distribution_analysis": distribution_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing distribution: {str(e)}"
        )

@router.get("/boxplot/{dataset_id}")
async def get_box_plot_analysis(
    dataset_id: int,
    columns: Optional[List[str]] = Query(None, description="Columns for box plot analysis"),
    db: Session = Depends(get_db)
):
    """
    Get box plot analysis for outlier detection
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load dataset
    try:
        parsed_data = json.loads(dataset.parsed_data)
        df = pd.DataFrame(parsed_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Get box plot analysis
    try:
        boxplot_results = stats_service.get_box_plot_analysis(columns)
        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "boxplot_analysis": boxplot_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating box plot analysis: {str(e)}"
        )

@router.get("/correlation-heatmap/{dataset_id}")
async def get_correlation_heatmap(
    dataset_id: int,
    columns: Optional[List[str]] = Query(None, description="Columns for correlation analysis"),
    method: str = Query("pearson", description="Correlation method: pearson, spearman, kendall"),
    db: Session = Depends(get_db)
):
    """
    Get correlation heatmap visualization
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load dataset
    try:
        parsed_data = json.loads(dataset.parsed_data)
        df = pd.DataFrame(parsed_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Get correlation heatmap
    try:
        heatmap_results = stats_service.get_correlation_heatmap(columns, method)
        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "correlation_heatmap": heatmap_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating correlation heatmap: {str(e)}"
        )

@router.post("/pivot-table/{dataset_id}")
async def create_pivot_table(
    dataset_id: int,
    request: schemas.PivotTableRequest,
    db: Session = Depends(get_db)
):
    """
    Create pivot table analysis
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load dataset
    try:
        parsed_data = json.loads(dataset.parsed_data)
        df = pd.DataFrame(parsed_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create statistical analysis service
    stats_service = StatisticalAnalysisService(df)

    # Create pivot table
    try:
        pivot_results = stats_service.create_pivot_table(
            index_col=request.index_column,
            columns_col=request.columns_column,
            values_col=request.values_column,
            aggfunc=request.aggregation_function
        )
        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "pivot_table": pivot_results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating pivot table: {str(e)}"
        )

# Business Templates Endpoints

@router.post("/business-template/{dataset_id}")
async def apply_business_template(
    dataset_id: int,
    request: schemas.BusinessTemplateRequest,
    db: Session = Depends(get_db)
):
    """
    Apply business analysis template to dataset
    """
    # Get the dataset
    dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found"
        )

    # Load dataset
    try:
        parsed_data = json.loads(dataset.parsed_data)
        df = pd.DataFrame(parsed_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error loading dataset: {str(e)}"
        )

    if df is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dataset"
        )

    # Create business templates service
    templates_service = BusinessTemplatesService(df)

    # Apply the requested template
    try:
        if request.template_type == "revenue_trend":
            results = templates_service.revenue_trend_analysis(
                request.date_column,
                request.value_column,
                request.forecast_periods,
                request.frequency
            )
        elif request.template_type == "sales_forecast":
            results = templates_service.sales_forecasting_template(
                request.date_column,
                request.value_column,
                request.forecast_periods,
                request.frequency
            )
        elif request.template_type == "demand_planning":
            results = templates_service.demand_planning_template(
                request.date_column,
                request.value_column,
                request.forecast_periods,
                request.frequency
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported template type: {request.template_type}"
            )

        return {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "template_analysis": results
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error applying business template: {str(e)}"
        )

@router.get("/templates/list")
async def list_business_templates():
    """
    List available business analysis templates
    """
    templates = [
        {
            "id": "revenue_trend",
            "name": "Revenue Trend Analysis",
            "description": "Analyze revenue trends and forecast future revenue",
            "required_columns": ["date_column", "value_column"],
            "use_case": "Track revenue performance and predict future earnings"
        },
        {
            "id": "sales_forecast",
            "name": "Weekly Sales Forecasting",
            "description": "Forecast sales with multiple methods and seasonality analysis",
            "required_columns": ["date_column", "value_column"],
            "use_case": "Plan inventory and sales targets based on historical patterns"
        },
        {
            "id": "demand_planning",
            "name": "Demand Planning for Inventory",
            "description": "Optimize inventory levels with demand forecasting and safety stock calculations",
            "required_columns": ["date_column", "value_column"],
            "use_case": "Minimize stockouts while reducing inventory carrying costs"
        },
        {
            "id": "traffic_analysis",
            "name": "Website Traffic Tracking",
            "description": "Analyze website traffic patterns and predict visitor trends",
            "required_columns": ["date_column", "value_column"],
            "use_case": "Optimize marketing campaigns and server capacity planning"
        },
        {
            "id": "energy_usage",
            "name": "Energy Usage Comparison",
            "description": "Compare energy consumption patterns across time periods",
            "required_columns": ["date_column", "value_column"],
            "use_case": "Identify energy saving opportunities and budget planning"
        }
    ]

    return {
        "templates": templates,
        "total_count": len(templates)
    }
