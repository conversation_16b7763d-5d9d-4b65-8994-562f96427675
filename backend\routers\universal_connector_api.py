"""
Universal Data Connector API Router
Handles sync operations for all connector types (GitHub, SharePoint, OneDrive, etc.)
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
import logging
from datetime import datetime

from database import get_db
from middleware.tenant_isolation import get_current_tenant_id
from middleware.auth import get_current_user_id
from middleware.rbac_complete import require_permission, Permission
from models_multitenant import DataConnector
from services.task_workers import queue_connector_sync_task

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/connectors", tags=["Universal Connectors"])

# Pydantic models for API

class ConnectorSyncRequest(BaseModel):
    """Request to sync a data connector"""
    force_full_sync: bool = Field(default=False, description="Force full sync instead of incremental")
    sync_options: Optional[Dict[str, Any]] = Field(default=None, description="Connector-specific sync options")

class BulkSyncRequest(BaseModel):
    """Request to sync multiple connectors"""
    connector_ids: List[str] = Field(..., description="List of connector IDs to sync")
    force_full_sync: bool = Field(default=False, description="Force full sync for all connectors")

class ConnectorStatusUpdate(BaseModel):
    """Update connector status"""
    status: Optional[str] = Field(default=None, description="New connector status")
    auto_sync_enabled: Optional[bool] = Field(default=None, description="Enable/disable auto sync")
    sync_frequency: Optional[str] = Field(default=None, description="Sync frequency")

@router.get("/")
async def list_connectors(
    connector_type: Optional[str] = None,
    status: Optional[str] = None,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.VIEW_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    List all data connectors for tenant with optional filtering
    """
    try:
        query = db.query(DataConnector).filter(DataConnector.tenant_id == tenant_id)
        
        # Apply filters
        if connector_type:
            query = query.filter(DataConnector.connector_type == connector_type)
        
        if status:
            query = query.filter(DataConnector.status == status)
        
        connectors = query.order_by(DataConnector.created_at.desc()).all()
        
        return {
            "connectors": [
                {
                    "id": connector.id,
                    "name": connector.name,
                    "connector_type": connector.connector_type,
                    "description": connector.description,
                    "status": connector.status,
                    "auto_sync_enabled": connector.auto_sync_enabled,
                    "sync_frequency": connector.sync_frequency,
                    "last_sync_at": connector.last_sync_at.isoformat() if connector.last_sync_at else None,
                    "next_sync_at": connector.next_sync_at.isoformat() if connector.next_sync_at else None,
                    "sync_count": connector.sync_count,
                    "last_error": connector.last_error,
                    "created_at": connector.created_at.isoformat(),
                    "config": connector.config
                }
                for connector in connectors
            ],
            "total": len(connectors),
            "filters": {
                "connector_type": connector_type,
                "status": status
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to list connectors: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list connectors: {str(e)}"
        )

@router.get("/{connector_id}")
async def get_connector(
    connector_id: str,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.VIEW_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific connector
    """
    try:
        connector = db.query(DataConnector).filter(
            DataConnector.id == connector_id,
            DataConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="Connector not found"
            )
        
        return {
            "id": connector.id,
            "name": connector.name,
            "connector_type": connector.connector_type,
            "description": connector.description,
            "status": connector.status,
            "auto_sync_enabled": connector.auto_sync_enabled,
            "sync_frequency": connector.sync_frequency,
            "last_sync_at": connector.last_sync_at.isoformat() if connector.last_sync_at else None,
            "next_sync_at": connector.next_sync_at.isoformat() if connector.next_sync_at else None,
            "sync_count": connector.sync_count,
            "last_error": connector.last_error,
            "created_at": connector.created_at.isoformat(),
            "updated_at": connector.updated_at.isoformat() if connector.updated_at else None,
            "config": connector.config,
            "credentials": "***HIDDEN***" if connector.credentials else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get connector: {str(e)}"
        )

@router.post("/{connector_id}/sync")
async def sync_connector(
    connector_id: str,
    sync_request: ConnectorSyncRequest,
    background_tasks: BackgroundTasks,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Manually trigger sync for any data connector type
    
    Universal sync endpoint that works for all connector types:
    - GitHub repositories
    - SharePoint sites
    - OneDrive files
    - Google Drive
    - Dropbox
    - Network folders
    - Databases
    - APIs
    - File uploads
    """
    try:
        # Verify connector exists and belongs to tenant
        connector = db.query(DataConnector).filter(
            DataConnector.id == connector_id,
            DataConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="Connector not found"
            )
        
        # Check if connector is in a syncable state
        if connector.status == "disconnected":
            raise HTTPException(
                status_code=400,
                detail="Cannot sync disconnected connector. Please reconnect first."
            )
        
        # Prepare sync task data
        task_data = {
            "connector_id": connector_id,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "force_full_sync": sync_request.force_full_sync,
            "sync_options": sync_request.sync_options or {},
            "triggered_by": "manual",
            "triggered_at": datetime.utcnow().isoformat()
        }
        
        # Queue the sync task (universal for all connector types)
        task_id = await queue_connector_sync_task(task_data)
        
        # Update connector status to indicate sync is starting
        connector.status = "syncing"
        db.commit()
        
        logger.info(f"Queued sync task {task_id} for {connector.connector_type} connector {connector_id}")
        
        return {
            "success": True,
            "message": f"Sync started for {connector.connector_type} connector '{connector.name}'",
            "task_id": task_id,
            "connector_id": connector_id,
            "connector_type": connector.connector_type,
            "force_full_sync": sync_request.force_full_sync,
            "estimated_duration": get_estimated_sync_duration(connector.connector_type)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to sync connector {connector_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start sync: {str(e)}"
        )

@router.post("/bulk-sync")
async def bulk_sync_connectors(
    sync_request: BulkSyncRequest,
    background_tasks: BackgroundTasks,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Trigger sync for multiple connectors at once
    """
    try:
        # Verify all connectors exist and belong to tenant
        connectors = db.query(DataConnector).filter(
            DataConnector.id.in_(sync_request.connector_ids),
            DataConnector.tenant_id == tenant_id
        ).all()
        
        if len(connectors) != len(sync_request.connector_ids):
            found_ids = [c.id for c in connectors]
            missing_ids = [cid for cid in sync_request.connector_ids if cid not in found_ids]
            raise HTTPException(
                status_code=404,
                detail=f"Connectors not found: {missing_ids}"
            )
        
        # Queue sync tasks for all connectors
        sync_results = []
        
        for connector in connectors:
            if connector.status == "disconnected":
                sync_results.append({
                    "connector_id": connector.id,
                    "connector_name": connector.name,
                    "connector_type": connector.connector_type,
                    "success": False,
                    "error": "Connector is disconnected"
                })
                continue
            
            try:
                task_data = {
                    "connector_id": connector.id,
                    "tenant_id": tenant_id,
                    "user_id": user_id,
                    "force_full_sync": sync_request.force_full_sync,
                    "triggered_by": "bulk_manual",
                    "triggered_at": datetime.utcnow().isoformat()
                }
                
                task_id = await queue_connector_sync_task(task_data)
                
                # Update connector status
                connector.status = "syncing"
                
                sync_results.append({
                    "connector_id": connector.id,
                    "connector_name": connector.name,
                    "connector_type": connector.connector_type,
                    "success": True,
                    "task_id": task_id
                })
                
            except Exception as e:
                sync_results.append({
                    "connector_id": connector.id,
                    "connector_name": connector.name,
                    "connector_type": connector.connector_type,
                    "success": False,
                    "error": str(e)
                })
        
        db.commit()
        
        successful_syncs = [r for r in sync_results if r["success"]]
        failed_syncs = [r for r in sync_results if not r["success"]]
        
        return {
            "success": True,
            "message": f"Bulk sync initiated for {len(successful_syncs)} connectors",
            "total_requested": len(sync_request.connector_ids),
            "successful": len(successful_syncs),
            "failed": len(failed_syncs),
            "results": sync_results,
            "force_full_sync": sync_request.force_full_sync
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to bulk sync connectors: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start bulk sync: {str(e)}"
        )

@router.put("/{connector_id}")
async def update_connector(
    connector_id: str,
    update_data: ConnectorStatusUpdate,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Update connector settings and status
    """
    try:
        connector = db.query(DataConnector).filter(
            DataConnector.id == connector_id,
            DataConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="Connector not found"
            )
        
        # Update fields
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(connector, field, value)
        
        connector.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(connector)
        
        logger.info(f"Updated connector {connector_id}: {update_dict}")
        
        return {
            "success": True,
            "message": "Connector updated successfully",
            "connector_id": connector_id,
            "updated_fields": list(update_dict.keys())
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update connector {connector_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update connector: {str(e)}"
        )

@router.get("/types/supported")
async def get_supported_connector_types(
    _: None = Depends(require_permission(Permission.VIEW_CONNECTORS))
):
    """
    Get list of supported connector types
    """
    return {
        "supported_types": [
            {
                "type": "github",
                "name": "GitHub",
                "description": "Git repositories and source code",
                "features": ["incremental_sync", "webhooks", "file_filtering"],
                "auth_required": True
            },
            {
                "type": "sharepoint",
                "name": "SharePoint",
                "description": "SharePoint sites and document libraries",
                "features": ["incremental_sync", "permissions", "metadata"],
                "auth_required": True
            },
            {
                "type": "onedrive",
                "name": "OneDrive",
                "description": "OneDrive files and folders",
                "features": ["incremental_sync", "file_watching", "sharing"],
                "auth_required": True
            },
            {
                "type": "google_drive",
                "name": "Google Drive",
                "description": "Google Drive documents and files",
                "features": ["incremental_sync", "real_time_updates", "collaboration"],
                "auth_required": True
            },
            {
                "type": "dropbox",
                "name": "Dropbox",
                "description": "Dropbox files and shared folders",
                "features": ["incremental_sync", "file_watching", "sharing"],
                "auth_required": True
            },
            {
                "type": "network_folder",
                "name": "Network Folder",
                "description": "Local network drives and shared folders",
                "features": ["file_watching", "permissions", "local_agent"],
                "auth_required": False
            },
            {
                "type": "database",
                "name": "Database",
                "description": "SQL databases and data warehouses",
                "features": ["scheduled_sync", "query_based", "schema_detection"],
                "auth_required": True
            },
            {
                "type": "api",
                "name": "API",
                "description": "REST APIs and web services",
                "features": ["scheduled_sync", "custom_endpoints", "authentication"],
                "auth_required": True
            },
            {
                "type": "file_upload",
                "name": "File Upload",
                "description": "Manually uploaded documents",
                "features": ["manual_upload", "batch_processing", "metadata"],
                "auth_required": False
            }
        ]
    }

def get_estimated_sync_duration(connector_type: str) -> str:
    """Get estimated sync duration for connector type"""
    durations = {
        "github": "2-10 minutes",
        "sharepoint": "5-30 minutes",
        "onedrive": "3-15 minutes",
        "google_drive": "3-15 minutes",
        "dropbox": "2-10 minutes",
        "network_folder": "1-5 minutes",
        "database": "5-60 minutes",
        "api": "1-10 minutes",
        "file_upload": "1-2 minutes"
    }
    return durations.get(connector_type, "5-15 minutes")
