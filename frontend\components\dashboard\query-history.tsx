'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { createApiInstance, getUserId } from '@/lib/api';

interface QueryHistoryItem {
  id: number;
  user_id: string;
  dataset_id: number;
  dataset_name?: string;
  question: string;
  answer: string;
  chart_type?: string;
  chart_data?: any;
  trust_score?: any;
  reasoning_steps?: string[];
  query_name?: string;
  is_bookmarked: boolean;
  tags?: string[];
  created_at: string;
  is_saved: boolean;
}

interface QueryHistoryProps {
  onSelectQuery: (query: QueryHistoryItem) => void;
  currentDatasetId: number | null;
}

export default function QueryHistory({ onSelectQuery, currentDatasetId }: QueryHistoryProps) {
  const { data: session } = useSession();
  const [queries, setQueries] = useState<QueryHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'current'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState<number | null>(null);
  const [viewQuery, setViewQuery] = useState<QueryHistoryItem | null>(null);

  // Load query history from backend API
  useEffect(() => {
    const loadQueries = async () => {
      if (!session) return;

      try {
        setIsLoading(true);
        setError(null);

        const userId = getUserId(session);
        const api = createApiInstance(session);
        const response = await api.get(`/queries/${userId}`);

        setQueries(response.data);
      } catch (err) {
        console.error('Error loading query history:', err);
        setError('Failed to load query history');
      } finally {
        setIsLoading(false);
      }
    };

    loadQueries();
  }, [session]);

  // Filter and sort queries
  const filteredQueries = queries
    .filter(item => {
      // Apply dataset filter
      if (filter === 'current' && item.dataset_id !== currentDatasetId) {
        return false;
      }

      // Apply search term
      if (searchTerm && !item.question.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  // Save query to favorites
  const saveQuery = async (query: QueryHistoryItem) => {
    if (!session) return;

    try {
      const userId = getUserId(session);

      // Call backend API to save query
      const api = createApiInstance(session);
      await api.post('/api/v1/saved-queries/', {
        query_id: query.id,
        name: null,
        description: null,
        tags: null,
        is_favorite: false
      }, {
        params: { user_id: userId }
      });

      // Update local state to show it's saved
      setQueries(prev => prev.map(q =>
        q.id === query.id ? { ...q, is_saved: true } : q
      ));

      alert('Query saved successfully!');
    } catch (err: any) {
      console.error('Error saving query:', err);
      if (err.response?.status === 400) {
        alert('This query is already saved');
      } else {
        alert('Failed to save query');
      }
    }
  };

  // Clear history
  const clearHistory = () => {
    if (confirm('Are you sure you want to clear your query history?')) {
      setQueries([]);
      // Note: Backend deletion would require individual API calls
      // For now, just clear the local state and let it reload from backend
    }
  };

  // Delete single query
  const deleteQuery = async (queryId: number) => {
    try {
      // Delete from backend using session-aware API
      const sessionApi = createApiInstance(session);
      await sessionApi.delete(`/queries/${queryId}?user_id=${getUserId(session)}`);

      // Update local state
      setQueries(prev => prev.filter(q => q.id !== queryId));
      setDeleteConfirm(null);
    } catch (err: any) {
      console.error('Error deleting query:', err);
      setError('Failed to delete query');
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-sm text-gray-600">Loading query history...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-600">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold text-black">Query History</h3>
        {queries.length > 0 && (
          <button
            onClick={clearHistory}
            className="text-xs text-red-600 hover:text-red-800 hover:underline"
          >
            Clear History
          </button>
        )}
      </div>

      <div className="mb-4">
        <div className="flex mb-2">
          <input
            type="text"
            placeholder="Search history..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 text-xs rounded-full ${
              filter === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All Datasets
          </button>
          <button
            onClick={() => setFilter('current')}
            className={`px-3 py-1 text-xs rounded-full ${
              filter === 'current'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            disabled={!currentDatasetId}
          >
            Current Dataset
          </button>
        </div>
      </div>

      {filteredQueries.length === 0 ? (
        <div className="text-center py-6 text-gray-500">
          <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="mt-2">No query history found</p>
          {searchTerm && <p className="text-sm mt-1">Try a different search term</p>}
        </div>
      ) : (
        <div className="space-y-2 max-h-64 overflow-y-auto pr-1">
          {filteredQueries.map((item) => (
            <div
              key={item.id}
              className="p-3 border rounded-md hover:bg-gray-50 transition-colors relative group"
            >
              <div className="flex justify-between items-start">
                <button
                  onClick={() => onSelectQuery(item)}
                  className="text-left text-sm text-black font-medium hover:text-blue-600 truncate max-w-[80%]"
                >
                  {item.question}
                </button>
                <div className="flex space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setViewQuery(item);
                    }}
                    className="text-gray-400 hover:text-green-500 focus:outline-none"
                    title="View details"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => saveQuery(item)}
                    className="text-gray-400 hover:text-blue-500 focus:outline-none"
                    title="Save query"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                    </svg>
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setDeleteConfirm(item.id);
                    }}
                    className="text-gray-400 hover:text-red-500 focus:outline-none"
                    title="Delete query"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-1 flex items-center">
                <span className="mr-2">
                  {new Date(item.created_at).toLocaleDateString()} {new Date(item.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
                <span className="bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full text-xs">
                  {item.dataset_name || `Dataset #${item.dataset_id}`}
                </span>
                {item.is_saved && (
                  <span className="ml-2 bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full text-xs">
                    Saved
                  </span>
                )}
                {item.trust_score && item.trust_score.overall_score !== undefined && (
                  <span className={`ml-2 px-1.5 py-0.5 rounded-full text-xs ${
                    item.trust_score.overall_score > 0.8 ? 'bg-green-100 text-green-800' :
                    item.trust_score.overall_score > 0.6 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    Trust: {Math.round(item.trust_score.overall_score * 100)}%
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Query</h3>
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete this query from your history? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteQuery(deleteConfirm)}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* View Query Modal */}
      {viewQuery && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Query Details</h3>
              <button
                onClick={() => setViewQuery(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Question</label>
                <p className="text-gray-900 bg-gray-50 p-3 rounded">{viewQuery.question}</p>
              </div>

              {viewQuery.answer && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Answer</label>
                  <p className="text-gray-900 bg-gray-50 p-3 rounded">{viewQuery.answer}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Dataset</label>
                  <p className="text-gray-900">{viewQuery.dataset_name || `Dataset #${viewQuery.dataset_id}`}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Created</label>
                  <p className="text-gray-900">{new Date(viewQuery.created_at).toLocaleString()}</p>
                </div>
              </div>

              {viewQuery.trust_score && viewQuery.trust_score.overall_score !== undefined && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Trust Score</label>
                  <div className={`inline-block px-3 py-1 rounded-full text-sm ${
                    viewQuery.trust_score.overall_score > 0.8 ? 'bg-green-100 text-green-800' :
                    viewQuery.trust_score.overall_score > 0.6 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {Math.round(viewQuery.trust_score.overall_score * 100)}% Trust Score
                  </div>
                </div>
              )}

              {viewQuery.is_saved && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <span className="inline-block px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                    Saved Query
                  </span>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  onSelectQuery(viewQuery);
                  setViewQuery(null);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Use This Query
              </button>
              <button
                onClick={() => setViewQuery(null)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
