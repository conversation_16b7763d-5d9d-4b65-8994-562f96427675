import requests
import json

# Demo chart questions for testing in the dashboard
print("🎯 Chart Generation Demo - Ready to Test!")

base_url = "http://localhost:8000"

# Get available datasets
response = requests.get(f"{base_url}/datasets/demo-user-id")
if response.status_code == 200:
    datasets = response.json()
    if datasets:
        dataset = datasets[0]
        print(f"📊 Dataset: {dataset['name']}")
        print(f"📋 Columns: {', '.join(dataset['columns'][:5])}...")
        print(f"📈 Data Points: {dataset['row_count']} rows")
        
        print("\n" + "="*60)
        print("🚀 CHART DEMO QUESTIONS - Copy and paste these into the dashboard:")
        print("="*60)
        
        demo_questions = [
            {
                "type": "📊 Bar Chart",
                "question": "Create a bar chart using the first column as x-axis and the second column as y-axis",
                "description": "Basic bar chart with two columns"
            },
            {
                "type": "📈 Line Chart", 
                "question": "Generate a line chart showing the relationship between the first two numeric columns",
                "description": "Line chart for trend analysis"
            },
            {
                "type": "🔍 Scatter Plot",
                "question": "Create a scatter plot visualization of the first two numeric columns",
                "description": "Scatter plot for correlation analysis"
            },
            {
                "type": "📊 Top 10 Bar Chart",
                "question": "Show me a bar chart of the top 10 highest values in the first column",
                "description": "Filtered bar chart with top values"
            },
            {
                "type": "📈 Multi-line Chart",
                "question": "Create a line chart comparing the first three numeric columns",
                "description": "Multiple series line chart"
            },
            {
                "type": "🎯 Custom Visualization",
                "question": "Make a chart that best represents the data distribution",
                "description": "AI-chosen optimal chart type"
            }
        ]
        
        for i, demo in enumerate(demo_questions, 1):
            print(f"\n{i}. {demo['type']}")
            print(f"   Question: \"{demo['question']}\"")
            print(f"   Purpose: {demo['description']}")
        
        print("\n" + "="*60)
        print("✅ WHAT TO EXPECT:")
        print("="*60)
        print("✅ Interactive Plotly charts with zoom, pan, hover")
        print("✅ Professional styling with proper titles and axes")
        print("✅ Responsive design that works on all screen sizes")
        print("✅ Chart type indicator and success message")
        print("✅ Trust Score showing chart generation quality")
        
        print("\n" + "="*60)
        print("🎯 HOW TO TEST:")
        print("="*60)
        print("1. Open the dashboard: http://localhost:3001/dashboard")
        print("2. Select the dataset (should auto-select)")
        print("3. Copy any question above and paste into the question box")
        print("4. Enable 'Include Trust Score' for quality assessment")
        print("5. Click 'Ask Question' and watch the magic! ✨")
        
        print("\n" + "="*60)
        print("🔧 TECHNICAL DETAILS:")
        print("="*60)
        print("✅ Backend: Generating Plotly JSON with full chart data")
        print("✅ Frontend: Rendering with Plotly.js for interactivity")
        print("✅ Data: 3000+ data points handled efficiently")
        print("✅ Types: Bar, Line, Scatter plots fully supported")
        print("✅ Fallback: Legacy chart format still supported")
        
        print("\n🎉 Charts are now fully functional! Test them in the dashboard! 🚀")

else:
    print("❌ Could not get datasets. Make sure the backend is running.")
