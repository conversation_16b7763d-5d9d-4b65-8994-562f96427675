'use client';

import { useState, useEffect, useRef } from 'react';
import api from '@/lib/api';

// Import Plotly dynamically to avoid SSR issues
let Plotly: any;
if (typeof window !== 'undefined') {
  import('plotly.js-dist-min').then((module) => {
    Plotly = module.default;
  }).catch(err => {
    console.error('Failed to load Plotly:', err);
  });
}

interface ModelManagerProps {
  selectedModelId?: string;
  onSelectModel?: (modelId: string) => void;
}

interface PredictiveModel {
  id: string;
  name: string;
  description: string | null;
  model_type: string;
  target_column: string;
  feature_columns: string[];
  metrics: any;
  feature_importance: Record<string, number> | null;
  created_at: string;
  is_active: boolean;
}

export default function ModelManager({ selectedModelId, onSelectModel }: ModelManagerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [models, setModels] = useState<PredictiveModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<PredictiveModel | null>(null);
  const [predictionInput, setPredictionInput] = useState<Record<string, any>>({});
  const [predictionResults, setPredictionResults] = useState<any>(null);
  const [predictionLoading, setPredictionLoading] = useState(false);
  const [predictionError, setPredictionError] = useState<string | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);
  
  const featureImportanceChartRef = useRef<HTMLDivElement>(null);

  // Fetch user's models
  useEffect(() => {
    const fetchModels = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await api.get(`/predictive/models`);

        // The API returns { models: [...], total_models: ..., ... }
        const modelsData = response.data.models || [];

        // Map the backend model structure to frontend structure
        const mappedModels = modelsData.map((model: any) => ({
          id: model.model_id,
          name: model.name,
          description: model.description || null,
          model_type: model.model_type,
          target_column: model.target_column || 'unknown',
          feature_columns: model.feature_columns || [],
          metrics: model.status === 'trained' ? {
            accuracy: model.accuracy || null,
            precision: model.precision || null,
            recall: model.recall || null,
            f1: model.f1_score || null,
            mae: model.mae || null,
            rmse: model.rmse || null,
            r2: model.r2 || null
          } : null, // No metrics for models that aren't trained yet
          feature_importance: model.feature_importance || null,
          created_at: model.created_at,
          is_active: model.status === 'trained' // Consider trained models as active
        }));

        setModels(mappedModels);

        // If selectedModelId is provided, select that model
        if (selectedModelId) {
          const model = mappedModels.find((m: PredictiveModel) => m.id === selectedModelId);
          if (model) {
            setSelectedModel(model);
          }
        }
      } catch (err: any) {
        console.error('Error fetching models:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching models');
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, [selectedModelId]);

  // Handle model selection
  const handleSelectModel = (model: PredictiveModel) => {
    setSelectedModel(model);
    setPredictionInput({});
    setPredictionResults(null);
    setPredictionError(null);
    
    // Initialize prediction input with empty values for each feature
    const initialInput: Record<string, any> = {};
    model.feature_columns.forEach(feature => {
      initialInput[feature] = '';
    });
    setPredictionInput(initialInput);
    
    // Call the onSelectModel callback if provided
    if (onSelectModel) {
      onSelectModel(model.id);
    }
    
    // Render feature importance chart if available
    setTimeout(() => {
      if (featureImportanceChartRef.current && Plotly && model.feature_importance) {
        const features = Object.keys(model.feature_importance);
        const importances = features.map(f => model.feature_importance![f]);
        
        // Sort by importance
        const combined = features.map((f, i) => ({ feature: f, importance: importances[i] }));
        combined.sort((a, b) => b.importance - a.importance);
        
        const sortedFeatures = combined.map(c => c.feature);
        const sortedImportances = combined.map(c => c.importance);
        
        const data = [{
          x: sortedImportances,
          y: sortedFeatures,
          type: 'bar',
          orientation: 'h',
          marker: {
            color: 'rgba(50, 171, 96, 0.7)',
            line: {
              color: 'rgba(50, 171, 96, 1.0)',
              width: 1
            }
          }
        }];
        
        const layout = {
          title: 'Feature Importance',
          xaxis: { title: 'Importance' },
          yaxis: { title: 'Feature' },
          margin: { l: 150 }
        };
        
        Plotly.newPlot(featureImportanceChartRef.current, data, layout);
      }
    }, 100);
  };

  // Handle input change for prediction
  const handleInputChange = (feature: string, value: string) => {
    setPredictionInput(prev => ({
      ...prev,
      [feature]: value
    }));
  };

  // Generate prediction
  const generatePrediction = async () => {
    if (!selectedModel) return;
    
    // Validate input
    const missingFeatures = selectedModel.feature_columns.filter(
      feature => !predictionInput[feature] && predictionInput[feature] !== 0
    );
    
    if (missingFeatures.length > 0) {
      setPredictionError(`Please provide values for: ${missingFeatures.join(', ')}`);
      return;
    }
    
    setPredictionLoading(true);
    setPredictionError(null);
    
    try {
      // Convert input values to appropriate types
      const typedInput: Record<string, any> = {};
      for (const [feature, value] of Object.entries(predictionInput)) {
        // Try to convert to number if possible
        const numValue = Number(value);
        typedInput[feature] = isNaN(numValue) ? value : numValue;
      }
      
      const response = await api.post(`/predictive/models/predict/${selectedModel.id}`, {
        model_id: selectedModel.id,
        input_data: typedInput
      });
      
      setPredictionResults(response.data);
    } catch (err: any) {
      console.error('Error generating prediction:', err);
      setPredictionError(err.response?.data?.detail || err.message || 'Error generating prediction');
    } finally {
      setPredictionLoading(false);
    }
  };

  // Toggle model active status
  const toggleModelStatus = async (model: PredictiveModel) => {
    try {
      const response = await api.put(`/predictive/models/${model.id}`, null, {
        params: {
          is_active: !model.is_active
        }
      });
      
      // Update models list
      setModels(prev => prev.map(m => 
        m.id === model.id ? { ...m, is_active: !m.is_active } : m
      ));
      
      // Update selected model if it's the one being toggled
      if (selectedModel && selectedModel.id === model.id) {
        setSelectedModel({ ...selectedModel, is_active: !selectedModel.is_active });
      }
    } catch (err: any) {
      console.error('Error updating model status:', err);
      setError(err.response?.data?.detail || err.message || 'Error updating model status');
    }
  };

  // Delete model
  const deleteModel = async (modelId: string) => {
    try {
      await api.delete(`/predictive/models/${modelId}`);
      
      // Remove from models list
      setModels(prev => prev.filter(m => m.id !== modelId));
      
      // Clear selected model if it's the one being deleted
      if (selectedModel && selectedModel.id === modelId) {
        setSelectedModel(null);
        setPredictionInput({});
        setPredictionResults(null);
      }
      
      // Clear delete confirmation
      setDeleteConfirm(null);
    } catch (err: any) {
      console.error('Error deleting model:', err);
      setError(err.response?.data?.detail || err.message || 'Error deleting model');
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format number
  const formatNumber = (num: number | null | undefined) => {
    if (num === null || num === undefined || isNaN(num)) {
      return 'N/A';
    }
    return Number.isInteger(num) ? num.toString() : num.toFixed(4);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-black mb-4">Predictive Models</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : models.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No models found. Train a model to get started.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Models List */}
          <div className="lg:col-span-1">
            <h3 className="text-md font-medium text-black mb-2">Your Models</h3>
            <div className="space-y-2 max-h-[600px] overflow-y-auto pr-2">
              {models.map(model => (
                <div
                  key={model.id}
                  onClick={() => handleSelectModel(model)}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedModel?.id === model.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <h4 className="font-medium text-black">{model.name}</h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      model.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {model.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    {model.model_type.charAt(0).toUpperCase() + model.model_type.slice(1)}
                  </p>
                  <p className="text-sm text-gray-500">
                    Target: {model.target_column}
                  </p>
                  <p className="text-xs text-gray-400 mt-2">
                    Created: {formatDate(model.created_at)}
                  </p>
                </div>
              ))}
            </div>
          </div>
          
          {/* Model Details and Prediction */}
          <div className="lg:col-span-2">
            {selectedModel ? (
              <div>
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-md font-medium text-black">{selectedModel.name}</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => toggleModelStatus(selectedModel)}
                      className={`px-2 py-1 text-xs rounded ${
                        selectedModel.is_active
                          ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                          : 'bg-green-100 text-green-800 hover:bg-green-200'
                      }`}
                    >
                      {selectedModel.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                    {deleteConfirm === selectedModel.id ? (
                      <div className="flex space-x-1">
                        <button
                          onClick={() => deleteModel(selectedModel.id)}
                          className="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
                        >
                          Confirm
                        </button>
                        <button
                          onClick={() => setDeleteConfirm(null)}
                          className="px-2 py-1 bg-gray-200 text-gray-800 text-xs rounded hover:bg-gray-300"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setDeleteConfirm(selectedModel.id)}
                        className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded hover:bg-red-200"
                      >
                        Delete
                      </button>
                    )}
                  </div>
                </div>
                
                {selectedModel.description && (
                  <p className="text-sm text-gray-600 mb-4">{selectedModel.description}</p>
                )}
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h4 className="text-sm font-medium text-black mb-2">Model Details</h4>
                    <div className="space-y-1">
                      <p className="text-sm">
                        <span className="text-gray-500">Type:</span> {selectedModel.model_type.charAt(0).toUpperCase() + selectedModel.model_type.slice(1)}
                      </p>
                      <p className="text-sm">
                        <span className="text-gray-500">Target:</span> {selectedModel.target_column}
                      </p>
                      <p className="text-sm">
                        <span className="text-gray-500">Features:</span> {selectedModel.feature_columns.length}
                      </p>
                      <p className="text-sm">
                        <span className="text-gray-500">Created:</span> {formatDate(selectedModel.created_at)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h4 className="text-sm font-medium text-black mb-2">Performance Metrics</h4>
                    <div className="space-y-1">
                      {selectedModel.metrics && selectedModel.model_type === 'classification' ? (
                        <>
                          <p className="text-sm">
                            <span className="text-gray-500">Accuracy:</span> {selectedModel.metrics.accuracy ? formatNumber(selectedModel.metrics.accuracy * 100) + '%' : 'N/A'}
                          </p>
                          <p className="text-sm">
                            <span className="text-gray-500">Precision:</span> {selectedModel.metrics.precision ? formatNumber(selectedModel.metrics.precision * 100) + '%' : 'N/A'}
                          </p>
                          <p className="text-sm">
                            <span className="text-gray-500">Recall:</span> {selectedModel.metrics.recall ? formatNumber(selectedModel.metrics.recall * 100) + '%' : 'N/A'}
                          </p>
                          <p className="text-sm">
                            <span className="text-gray-500">F1 Score:</span> {selectedModel.metrics.f1 ? formatNumber(selectedModel.metrics.f1 * 100) + '%' : 'N/A'}
                          </p>
                        </>
                      ) : selectedModel.metrics ? (
                        <>
                          <p className="text-sm">
                            <span className="text-gray-500">MAE:</span> {formatNumber(selectedModel.metrics.mae)}
                          </p>
                          <p className="text-sm">
                            <span className="text-gray-500">RMSE:</span> {formatNumber(selectedModel.metrics.rmse)}
                          </p>
                          <p className="text-sm">
                            <span className="text-gray-500">R²:</span> {selectedModel.metrics.r2 ? formatNumber(selectedModel.metrics.r2 * 100) + '%' : 'N/A'}
                          </p>
                        </>
                      ) : (
                        <p className="text-sm text-gray-500">Metrics not available (model may still be training)</p>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Feature Importance */}
                {selectedModel.feature_importance && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-black mb-2">Feature Importance</h4>
                    <div ref={featureImportanceChartRef} className="w-full h-[300px]" />
                  </div>
                )}
                
                {/* Prediction Form */}
                <div className="border-t pt-6">
                  <h4 className="text-md font-medium text-black mb-4">Generate Prediction</h4>
                  
                  {predictionError && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 text-sm">
                      {predictionError}
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {selectedModel.feature_columns.map(feature => (
                      <div key={feature}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {feature}
                        </label>
                        <input
                          type="text"
                          value={predictionInput[feature] || ''}
                          onChange={(e) => handleInputChange(feature, e.target.value)}
                          placeholder={`Enter value for ${feature}`}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        />
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-center">
                    <button
                      onClick={generatePrediction}
                      disabled={predictionLoading || !selectedModel.is_active}
                      className={`px-4 py-2 rounded-md text-sm font-medium ${
                        predictionLoading || !selectedModel.is_active
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {predictionLoading ? 'Generating...' : 'Generate Prediction'}
                    </button>
                  </div>
                  
                  {/* Prediction Results */}
                  {predictionResults && (
                    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                      <h4 className="text-sm font-medium text-black mb-2">Prediction Results</h4>
                      
                      {selectedModel.model_type === 'classification' ? (
                        <div>
                          <p className="text-md font-medium text-blue-800 mb-2">
                            Predicted Class: {predictionResults.predictions[0]}
                          </p>
                          
                          {predictionResults.probabilities && (
                            <div>
                              <p className="text-sm text-gray-700 mb-1">Class Probabilities:</p>
                              <div className="grid grid-cols-2 gap-2">
                                {Object.entries(predictionResults.probabilities[0]).map(([className, prob]: [string, any]) => (
                                  <div key={className} className="flex justify-between">
                                    <span className="text-sm">{className}:</span>
                                    <span className="text-sm font-medium">{formatNumber(prob * 100)}%</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div>
                          <p className="text-md font-medium text-blue-800">
                            Predicted Value: {formatNumber(predictionResults.predictions[0])}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <p className="text-gray-500">Select a model to view details and generate predictions</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
