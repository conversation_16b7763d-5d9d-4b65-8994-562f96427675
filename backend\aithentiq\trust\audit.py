"""
Trust Score Audit and Explainability System
"""

import numpy as np
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from collections import Counter, defaultdict
import re

from .score import TrustResult
from ..config import trust_config


@dataclass
class AuditMetrics:
    """Individual audit metric scores"""
    explainability: float
    source_diversity: float
    fairness: float
    robustness: float
    
    def to_dict(self) -> Dict[str, float]:
        return {
            "explainability": self.explainability,
            "source_diversity": self.source_diversity,
            "fairness": self.fairness,
            "robustness": self.robustness
        }


@dataclass
class AuditReport:
    """Comprehensive audit report"""
    metrics: AuditMetrics
    threshold_breaches: List[str]
    recommendations: List[str]
    risk_level: str
    audit_timestamp: datetime
    sample_size: int
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "metrics": self.metrics.to_dict(),
            "threshold_breaches": self.threshold_breaches,
            "recommendations": self.recommendations,
            "risk_level": self.risk_level,
            "audit_timestamp": self.audit_timestamp.isoformat(),
            "sample_size": self.sample_size
        }


class TrustAuditor:
    """Trust Score Audit and Monitoring System"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or trust_config
        self.logger = logging.getLogger(__name__)
        
        # Audit thresholds
        self.thresholds = {
            "explainability": self.config.explainability_threshold,
            "source_diversity": self.config.source_diversity_threshold,
            "fairness": self.config.fairness_threshold,
            "robustness": self.config.robustness_threshold
        }
    
    def compute_audit_metrics(self, responses: List[Dict[str, Any]]) -> AuditReport:
        """
        Compute comprehensive audit metrics for a batch of responses
        
        Args:
            responses: List of response dictionaries with trust scores and metadata
            
        Returns:
            AuditReport with metrics and recommendations
        """
        try:
            if not responses:
                return self._empty_audit_report()
            
            # Compute individual metrics
            explainability = self._compute_explainability_score(responses)
            source_diversity = self._compute_source_diversity(responses)
            fairness = self._compute_fairness_metrics(responses)
            robustness = self._compute_robustness_scores(responses)
            
            # Create metrics object
            metrics = AuditMetrics(
                explainability=explainability,
                source_diversity=source_diversity,
                fairness=fairness,
                robustness=robustness
            )
            
            # Detect threshold breaches
            breaches = self._detect_threshold_breaches(metrics)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(metrics, breaches)
            
            # Determine risk level
            risk_level = self._assess_risk_level(metrics, breaches)
            
            return AuditReport(
                metrics=metrics,
                threshold_breaches=breaches,
                recommendations=recommendations,
                risk_level=risk_level,
                audit_timestamp=datetime.utcnow(),
                sample_size=len(responses)
            )
            
        except Exception as e:
            self.logger.error(f"Audit computation failed: {str(e)}")
            return self._error_audit_report(str(e))
    
    def _compute_explainability_score(self, responses: List[Dict[str, Any]]) -> float:
        """Compute explainability score based on explanation quality"""
        if not responses:
            return 0.0
        
        explainability_scores = []
        
        for response in responses:
            trust_result = response.get("trust_score", {})
            explanation = trust_result.get("explanation", "")
            factors = trust_result.get("factors", [])
            
            # Score based on explanation completeness
            explanation_score = 0.0
            
            # Check explanation length and detail
            if len(explanation) > 50:
                explanation_score += 0.3
            if len(explanation) > 100:
                explanation_score += 0.2
            
            # Check for specific factors
            if len(factors) >= 3:
                explanation_score += 0.3
            elif len(factors) >= 1:
                explanation_score += 0.2
            
            # Check for quantitative details
            if re.search(r'\d+\.?\d*', explanation):
                explanation_score += 0.2
            
            explainability_scores.append(min(1.0, explanation_score))
        
        return np.mean(explainability_scores)
    
    def _compute_source_diversity(self, responses: List[Dict[str, Any]]) -> float:
        """Compute source diversity score"""
        if not responses:
            return 0.0
        
        diversity_scores = []
        
        for response in responses:
            sources = response.get("sources", [])
            
            if not sources:
                diversity_scores.append(0.5)  # Neutral score for no sources
                continue
            
            # Analyze source diversity
            source_types = set()
            source_domains = set()
            
            for source in sources:
                # Extract source type
                source_text = source.get("text", "")
                filename = source.get("filename", "")
                
                # Classify source type
                if filename.endswith(('.pdf', '.docx', '.txt')):
                    source_types.add('document')
                elif filename.endswith(('.csv', '.xlsx')):
                    source_types.add('data')
                else:
                    source_types.add('text')
                
                # Extract domain/topic indicators
                words = re.findall(r'\b\w+\b', source_text.lower())
                if words:
                    # Use first few words as domain indicators
                    source_domains.update(words[:5])
            
            # Calculate diversity score
            type_diversity = len(source_types) / max(1, len(sources))
            domain_diversity = min(1.0, len(source_domains) / max(1, len(sources) * 2))
            
            diversity_score = 0.6 * type_diversity + 0.4 * domain_diversity
            diversity_scores.append(diversity_score)
        
        return np.mean(diversity_scores)
    
    def _compute_fairness_metrics(self, responses: List[Dict[str, Any]]) -> float:
        """Compute fairness metrics across different user groups"""
        if not responses:
            return 0.0
        
        # Group responses by user characteristics
        user_groups = defaultdict(list)
        
        for response in responses:
            user_id = response.get("user_id", "unknown")
            trust_score = response.get("trust_score", {}).get("overall_score", 0.5)
            
            # Simple grouping by user ID prefix (would use actual demographics in production)
            group = user_id[:3] if len(user_id) >= 3 else "default"
            user_groups[group].append(trust_score)
        
        if len(user_groups) < 2:
            return 0.8  # High fairness if only one group
        
        # Calculate variance in mean trust scores across groups
        group_means = [np.mean(scores) for scores in user_groups.values()]
        fairness_score = 1.0 - np.std(group_means)  # Lower std = higher fairness
        
        return max(0.0, min(1.0, fairness_score))
    
    def _compute_robustness_scores(self, responses: List[Dict[str, Any]]) -> float:
        """Compute robustness score based on consistency"""
        if not responses:
            return 0.0
        
        # Analyze trust score consistency for similar queries
        query_groups = defaultdict(list)
        
        for response in responses:
            query = response.get("question", "")
            trust_score = response.get("trust_score", {}).get("overall_score", 0.5)
            
            # Group similar queries (simple word-based similarity)
            query_words = set(re.findall(r'\b\w+\b', query.lower()))
            
            # Find similar existing groups
            matched_group = None
            for existing_query, scores in query_groups.items():
                existing_words = set(re.findall(r'\b\w+\b', existing_query.lower()))
                overlap = len(query_words.intersection(existing_words))
                
                if overlap >= min(3, len(query_words) * 0.5):
                    matched_group = existing_query
                    break
            
            if matched_group:
                query_groups[matched_group].append(trust_score)
            else:
                query_groups[query].append(trust_score)
        
        # Calculate consistency within groups
        consistency_scores = []
        for scores in query_groups.values():
            if len(scores) > 1:
                consistency = 1.0 - np.std(scores)  # Lower std = higher consistency
                consistency_scores.append(max(0.0, consistency))
        
        if not consistency_scores:
            return 0.8  # Default high robustness if no similar queries
        
        return np.mean(consistency_scores)
    
    def _detect_threshold_breaches(self, metrics: AuditMetrics) -> List[str]:
        """Detect which metrics breach their thresholds"""
        breaches = []
        
        if metrics.explainability < self.thresholds["explainability"]:
            breaches.append(f"Explainability below threshold: {metrics.explainability:.3f} < {self.thresholds['explainability']}")
        
        if metrics.source_diversity < self.thresholds["source_diversity"]:
            breaches.append(f"Source diversity below threshold: {metrics.source_diversity:.3f} < {self.thresholds['source_diversity']}")
        
        if metrics.fairness < self.thresholds["fairness"]:
            breaches.append(f"Fairness below threshold: {metrics.fairness:.3f} < {self.thresholds['fairness']}")
        
        if metrics.robustness < self.thresholds["robustness"]:
            breaches.append(f"Robustness below threshold: {metrics.robustness:.3f} < {self.thresholds['robustness']}")
        
        return breaches
    
    def _generate_recommendations(self, metrics: AuditMetrics, breaches: List[str]) -> List[str]:
        """Generate actionable recommendations based on audit results"""
        recommendations = []
        
        if metrics.explainability < self.thresholds["explainability"]:
            recommendations.append("Improve explanation quality by providing more detailed factor analysis")
            recommendations.append("Include quantitative confidence intervals in explanations")
        
        if metrics.source_diversity < self.thresholds["source_diversity"]:
            recommendations.append("Increase diversity of information sources")
            recommendations.append("Implement cross-domain source validation")
        
        if metrics.fairness < self.thresholds["fairness"]:
            recommendations.append("Review trust score distribution across user groups")
            recommendations.append("Implement bias detection and mitigation strategies")
        
        if metrics.robustness < self.thresholds["robustness"]:
            recommendations.append("Improve consistency of trust scores for similar queries")
            recommendations.append("Implement query normalization and caching")
        
        if not breaches:
            recommendations.append("All audit metrics are within acceptable thresholds")
            recommendations.append("Continue monitoring for sustained performance")
        
        return recommendations
    
    def _assess_risk_level(self, metrics: AuditMetrics, breaches: List[str]) -> str:
        """Assess overall risk level based on metrics and breaches"""
        if len(breaches) == 0:
            return "LOW"
        elif len(breaches) <= 2:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def _empty_audit_report(self) -> AuditReport:
        """Generate empty audit report"""
        return AuditReport(
            metrics=AuditMetrics(0.0, 0.0, 0.0, 0.0),
            threshold_breaches=["No data available for audit"],
            recommendations=["Collect sufficient response data for meaningful audit"],
            risk_level="UNKNOWN",
            audit_timestamp=datetime.utcnow(),
            sample_size=0
        )
    
    def _error_audit_report(self, error_msg: str) -> AuditReport:
        """Generate error audit report"""
        return AuditReport(
            metrics=AuditMetrics(0.0, 0.0, 0.0, 0.0),
            threshold_breaches=[f"Audit computation error: {error_msg}"],
            recommendations=["Fix audit computation issues", "Review system logs"],
            risk_level="HIGH",
            audit_timestamp=datetime.utcnow(),
            sample_size=0
        )
    
    def generate_audit_summary(self, reports: List[AuditReport]) -> Dict[str, Any]:
        """Generate summary across multiple audit reports"""
        if not reports:
            return {"error": "No audit reports provided"}
        
        # Aggregate metrics
        all_metrics = [report.metrics for report in reports]
        
        summary = {
            "total_reports": len(reports),
            "date_range": {
                "start": min(report.audit_timestamp for report in reports).isoformat(),
                "end": max(report.audit_timestamp for report in reports).isoformat()
            },
            "average_metrics": {
                "explainability": np.mean([m.explainability for m in all_metrics]),
                "source_diversity": np.mean([m.source_diversity for m in all_metrics]),
                "fairness": np.mean([m.fairness for m in all_metrics]),
                "robustness": np.mean([m.robustness for m in all_metrics])
            },
            "risk_distribution": Counter(report.risk_level for report in reports),
            "total_breaches": sum(len(report.threshold_breaches) for report in reports),
            "total_sample_size": sum(report.sample_size for report in reports)
        }
        
        return summary
