from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import os
from database import get_db, engine
import models
from routers import api_v1, conversations
from datetime import datetime

# Create database tables
models.Base.metadata.create_all(bind=engine)

# Create demo user and API key for testing
def create_demo_data():
    """Create demo user and API key if they don't exist"""
    db = next(get_db())
    try:
        # Check if demo user exists
        user = db.query(models.User).filter(models.User.id == "demo-user-id").first()
        if not user:
            user = models.User(
                id="demo-user-id",
                email="<EMAIL>",
                role="user",
                subscription_status="free",
                referral_code="DEMO123"
            )
            db.add(user)
            db.commit()
            print("✅ Demo user created")

        # Check if demo API key exists
        api_key = db.query(models.ApiKey).filter(models.ApiKey.key == "gYZVZK4VT2bf8zc8r6EBHpBeKEwPASb4hEoXSP1fYJo").first()
        if not api_key:
            api_key = models.ApiKey(
                user_id="demo-user-id",
                key="gYZVZK4VT2bf8zc8r6EBHpBeKEwPASb4hEoXSP1fYJo",
                name="Demo API Key",
                is_active=True,
                created_at=datetime.now()
            )
            db.add(api_key)
            db.commit()
            print("✅ Demo API key created")

        print(f"✅ Demo setup complete - API key ready")

    except Exception as e:
        print(f"⚠️ Error creating demo data: {e}")
        db.rollback()
    finally:
        db.close()

# Create demo data on startup
create_demo_data()

app = FastAPI(title="AIthentiq API - Minimal")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "backend-minimal", "version": "1.0"}

# Include routers
app.include_router(api_v1.router)
app.include_router(conversations.router)

# Test endpoint
@app.get("/test")
async def test():
    return {"message": "Server is working"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_minimal:app", host="0.0.0.0", port=8000, reload=False)
