"""
Document Processing Service for AIthentiq
Handles upload, parsing, and processing of various document formats
Supports PDF, DOCX, TXT, MD, CSV, XLSX with OCR capabilities
"""

import os
import io
import logging
import hashlib
import mimetypes
from typing import Dict, Any, List, Optional, Tuple, BinaryIO
from pathlib import Path
from datetime import datetime
import tempfile

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """
    Main document processing class supporting multiple file formats
    """
    
    def __init__(self):
        self.supported_formats = {
            'text': ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml'],
            'pdf': ['.pdf'],
            'word': ['.docx', '.doc'],
            'excel': ['.xlsx', '.xls', '.csv'],
            'image': ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']
        }
        
        # Initialize processors
        self._init_processors()
    
    def _init_processors(self):
        """Initialize document processors based on available libraries"""
        self.processors = {}
        
        # Text processor (always available)
        self.processors['text'] = self._process_text_file
        
        # PDF processor
        try:
            import PyPDF2
            import pdfplumber
            self.processors['pdf'] = self._process_pdf_file
            logger.info("PDF processing enabled")
        except ImportError:
            logger.warning("PDF processing disabled - install PyPDF2 and pdfplumber")
        
        # Word processor
        try:
            import python_docx2txt
            self.processors['word'] = self._process_word_file
            logger.info("Word processing enabled")
        except ImportError:
            logger.warning("Word processing disabled - install python-docx2txt")
        
        # Excel processor
        try:
            import pandas as pd
            self.processors['excel'] = self._process_excel_file
            logger.info("Excel processing enabled")
        except ImportError:
            logger.warning("Excel processing disabled - install pandas")
        
        # Image processor (OCR)
        try:
            import pytesseract
            from PIL import Image
            self.processors['image'] = self._process_image_file
            logger.info("Image OCR processing enabled")
        except ImportError:
            logger.warning("Image OCR disabled - install pytesseract and Pillow")
    
    def get_file_info(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Extract file information and metadata
        """
        file_size = len(file_content)
        file_hash = hashlib.sha256(file_content).hexdigest()
        file_ext = Path(filename).suffix.lower()
        
        # Detect MIME type
        mime_type, _ = mimetypes.guess_type(filename)
        
        # Determine file category
        file_category = self._get_file_category(file_ext)
        
        return {
            'filename': filename,
            'file_size': file_size,
            'file_hash': file_hash,
            'file_extension': file_ext,
            'mime_type': mime_type,
            'file_category': file_category,
            'is_supported': file_category in self.processors
        }
    
    def _get_file_category(self, file_ext: str) -> str:
        """Determine file category based on extension"""
        for category, extensions in self.supported_formats.items():
            if file_ext in extensions:
                return category
        return 'unknown'
    
    def process_document(
        self, 
        file_content: bytes, 
        filename: str,
        extract_metadata: bool = True
    ) -> Dict[str, Any]:
        """
        Process a document and extract text content
        
        Args:
            file_content: Raw file bytes
            filename: Original filename
            extract_metadata: Whether to extract metadata
            
        Returns:
            Dictionary with extracted content and metadata
        """
        try:
            # Get file info
            file_info = self.get_file_info(file_content, filename)
            
            if not file_info['is_supported']:
                raise ValueError(f"Unsupported file type: {file_info['file_extension']}")
            
            # Get appropriate processor
            processor = self.processors[file_info['file_category']]
            
            # Process the file
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_info['file_extension']) as temp_file:
                temp_file.write(file_content)
                temp_file.flush()
                
                try:
                    content_data = processor(temp_file.name, file_content)
                finally:
                    # Clean up temp file
                    os.unlink(temp_file.name)
            
            # Combine file info with content data
            result = {
                **file_info,
                **content_data,
                'processed_at': datetime.utcnow().isoformat(),
                'processing_status': 'completed'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Document processing failed for {filename}: {e}")
            return {
                'filename': filename,
                'processing_status': 'failed',
                'error': str(e),
                'processed_at': datetime.utcnow().isoformat()
            }
    
    def _process_text_file(self, file_path: str, file_content: bytes) -> Dict[str, Any]:
        """Process plain text files"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            text_content = None
            
            for encoding in encodings:
                try:
                    text_content = file_content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if text_content is None:
                raise ValueError("Could not decode text file with any supported encoding")
            
            # Basic text statistics
            word_count = len(text_content.split())
            char_count = len(text_content)
            line_count = len(text_content.splitlines())
            
            return {
                'text_content': text_content,
                'word_count': word_count,
                'character_count': char_count,
                'line_count': line_count,
                'content_type': 'text'
            }
            
        except Exception as e:
            raise ValueError(f"Text processing failed: {e}")
    
    def _process_pdf_file(self, file_path: str, file_content: bytes) -> Dict[str, Any]:
        """Process PDF files"""
        try:
            import pdfplumber
            
            text_content = ""
            page_count = 0
            
            with pdfplumber.open(file_path) as pdf:
                page_count = len(pdf.pages)
                
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
            
            # Basic statistics
            word_count = len(text_content.split())
            char_count = len(text_content)
            
            return {
                'text_content': text_content,
                'word_count': word_count,
                'character_count': char_count,
                'page_count': page_count,
                'content_type': 'document'
            }
            
        except Exception as e:
            raise ValueError(f"PDF processing failed: {e}")
    
    def _process_word_file(self, file_path: str, file_content: bytes) -> Dict[str, Any]:
        """Process Word documents"""
        try:
            import docx2txt
            
            text_content = docx2txt.process(file_path)
            
            # Basic statistics
            word_count = len(text_content.split())
            char_count = len(text_content)
            
            return {
                'text_content': text_content,
                'word_count': word_count,
                'character_count': char_count,
                'content_type': 'document'
            }
            
        except Exception as e:
            raise ValueError(f"Word processing failed: {e}")
    
    def _process_excel_file(self, file_path: str, file_content: bytes) -> Dict[str, Any]:
        """Process Excel and CSV files"""
        try:
            import pandas as pd
            
            # Determine file type and read accordingly
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.csv':
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            # Convert to text representation
            text_content = df.to_string()
            
            # Extract column information
            columns = df.columns.tolist()
            row_count, col_count = df.shape
            
            # Basic statistics
            word_count = len(text_content.split())
            char_count = len(text_content)
            
            return {
                'text_content': text_content,
                'word_count': word_count,
                'character_count': char_count,
                'row_count': row_count,
                'column_count': col_count,
                'columns': columns,
                'content_type': 'tabular',
                'data_preview': df.head().to_dict('records') if row_count > 0 else []
            }
            
        except Exception as e:
            raise ValueError(f"Excel processing failed: {e}")
    
    def _process_image_file(self, file_path: str, file_content: bytes) -> Dict[str, Any]:
        """Process images with OCR"""
        try:
            import pytesseract
            from PIL import Image
            
            # Open image
            image = Image.open(file_path)
            
            # Extract text using OCR
            text_content = pytesseract.image_to_string(image)
            
            # Basic statistics
            word_count = len(text_content.split())
            char_count = len(text_content)
            
            # Image metadata
            width, height = image.size
            
            return {
                'text_content': text_content,
                'word_count': word_count,
                'character_count': char_count,
                'image_width': width,
                'image_height': height,
                'content_type': 'image_ocr'
            }
            
        except Exception as e:
            raise ValueError(f"Image OCR processing failed: {e}")
    
    def validate_file(
        self, 
        file_content: bytes, 
        filename: str,
        max_size_mb: int = 50
    ) -> Tuple[bool, Optional[str]]:
        """
        Validate file before processing
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check file size
            file_size_mb = len(file_content) / (1024 * 1024)
            if file_size_mb > max_size_mb:
                return False, f"File size ({file_size_mb:.1f}MB) exceeds limit ({max_size_mb}MB)"
            
            # Check if file is empty
            if len(file_content) == 0:
                return False, "File is empty"
            
            # Check file extension
            file_ext = Path(filename).suffix.lower()
            if not file_ext:
                return False, "File has no extension"
            
            # Check if format is supported
            file_category = self._get_file_category(file_ext)
            if file_category == 'unknown':
                return False, f"Unsupported file format: {file_ext}"
            
            if file_category not in self.processors:
                return False, f"Processor not available for {file_category} files"
            
            # Basic content validation
            if file_ext in ['.txt', '.md', '.py', '.js']:
                # Try to decode as text
                try:
                    file_content.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        file_content.decode('latin-1')
                    except UnicodeDecodeError:
                        return False, "Text file contains invalid characters"
            
            return True, None
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """Get list of supported file formats"""
        available_formats = {}
        
        for category, extensions in self.supported_formats.items():
            if category in self.processors:
                available_formats[category] = extensions
        
        return available_formats

# Global document processor instance
document_processor = DocumentProcessor()
