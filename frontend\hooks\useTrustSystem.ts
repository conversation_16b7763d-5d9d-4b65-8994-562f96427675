'use client';

import { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface TrustScoreRequest {
  query: string;
  answer: string;
  user_id: string;
  topic?: string;
  sources?: any[];
  metadata?: any;
}

interface TrustFeedbackRequest {
  query: string;
  predicted_trust: number;
  actual_correctness: boolean;
  user_id: string;
  topic?: string;
  feedback_weight?: number;
}

interface TrustScore {
  overall_score: number;
  base_score: number;
  component_scores: {
    model_confidence: number;
    qat_regressor: number;
    refusal_detector: number;
    citation_precision: number;
  };
  explanation: string;
  factors: string[];
  confidence_interval?: [number, number];
  processing_time_ms?: number;
  version?: string;
}

interface BayesianInfo {
  user_posterior: number;
  topic_posterior: number;
  user_confidence: number;
  topic_confidence: number;
  user_interactions: number;
  topic_interactions: number;
}

interface TrustComputeResponse {
  trust_score: TrustScore;
  bayesian_info: BayesianInfo;
  computed_at: string;
}

interface UserTrustProfile {
  user_id: string;
  trust_level: string;
  mean_trust: number;
  confidence_interval: [number, number];
  total_interactions: number;
  last_updated: string;
  variance: number;
}

export const useTrustSystem = () => {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getApiKey = () => {
    return localStorage.getItem('api_key') || '';
  };

  const computeTrustScore = useCallback(async (request: TrustScoreRequest): Promise<TrustComputeResponse | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/trust/compute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': getApiKey(),
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`Trust score computation failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Trust score computation error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const submitTrustFeedback = useCallback(async (request: TrustFeedbackRequest): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/trust/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': getApiKey(),
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`Trust feedback submission failed: ${response.statusText}`);
      }

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Trust feedback error:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const getUserTrustProfile = useCallback(async (userId: string): Promise<UserTrustProfile | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/trust/user/${userId}/profile`, {
        method: 'GET',
        headers: {
          'X-API-Key': getApiKey(),
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get user trust profile: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('User trust profile error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getTopicTrustProfile = useCallback(async (topic: string): Promise<any> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/trust/topic/${topic}/profile`, {
        method: 'GET',
        headers: {
          'X-API-Key': getApiKey(),
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get topic trust profile: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Topic trust profile error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const runTrustAudit = useCallback(async (auditRequest: any): Promise<any> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/trust/audit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': getApiKey(),
        },
        body: JSON.stringify(auditRequest),
      });

      if (!response.ok) {
        throw new Error(`Trust audit failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Trust audit error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getTrustSystemHealth = useCallback(async (): Promise<any> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/trust/health', {
        method: 'GET',
        headers: {
          'X-API-Key': getApiKey(),
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get trust system health: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Trust system health error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getTrustConfig = useCallback(async (): Promise<any> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/trust/config', {
        method: 'GET',
        headers: {
          'X-API-Key': getApiKey(),
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get trust config: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Trust config error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getEvaluationSummary = useCallback(async (days: number = 30): Promise<any> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/trust/evaluation/summary?days=${days}`, {
        method: 'GET',
        headers: {
          'X-API-Key': getApiKey(),
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get evaluation summary: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Evaluation summary error:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Helper function to classify feedback type
  const classifyFeedback = (feedbackType: 'up' | 'down' | 'neutral'): boolean => {
    switch (feedbackType) {
      case 'up':
        return true;
      case 'down':
        return false;
      case 'neutral':
      default:
        return true; // Treat neutral as positive for now
    }
  };

  // Helper function to get current user ID
  const getCurrentUserId = (): string => {
    return session?.user?.email || 'anonymous_user';
  };

  // Helper function to classify topic from query
  const classifyTopic = (query: string): string => {
    const queryLower = query.toLowerCase();
    
    if (queryLower.includes('revenue') || queryLower.includes('profit') || queryLower.includes('sales') || queryLower.includes('financial')) {
      return 'finance';
    } else if (queryLower.includes('customer') || queryLower.includes('user') || queryLower.includes('marketing')) {
      return 'marketing';
    } else if (queryLower.includes('average') || queryLower.includes('mean') || queryLower.includes('analysis')) {
      return 'analytics';
    } else {
      return 'general';
    }
  };

  return {
    // Core functions
    computeTrustScore,
    submitTrustFeedback,
    
    // Profile functions
    getUserTrustProfile,
    getTopicTrustProfile,
    
    // Monitoring functions
    runTrustAudit,
    getTrustSystemHealth,
    getTrustConfig,
    getEvaluationSummary,
    
    // Helper functions
    classifyFeedback,
    getCurrentUserId,
    classifyTopic,
    
    // State
    loading,
    error,
  };
};
