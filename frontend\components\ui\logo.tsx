import Link from 'next/link'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  className?: string
}

export default function Logo({ size = 'md', showText = true, className = '' }: LogoProps) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  }

  const textSizeClasses = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl'
  }

  return (
    <Link href="/" className={`flex items-center space-x-3 ${className}`}>
      {/* AIthentiq Logo */}
      <img
        src="https://www.aithentiq.com/AIthentiq_Logo.png?v=1749663648545"
        alt="AIthentiq"
        className={`${sizeClasses[size]} object-contain`}
      />
      
      {showText && (
        <div className="flex flex-col">
          <span className={`${textSizeClasses[size]} font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent`}>
            AIthentiq
          </span>
          <span className="text-xs text-gray-500 -mt-1">
            Authentic AI Analysis
          </span>
        </div>
      )}
    </Link>
  )
}
