#!/bin/bash

# AIthentiq Database Schema Fix Script
# This script pushes database schema changes to the deployed backend

echo "🔧 AIthentiq Database Schema Fix Script"
echo "========================================"

# Configuration
BACKEND_URL="https://aithentiq-backend.onrender.com"  # Update with your actual backend URL
API_KEY="your-api-key-here"  # Update with your actual API key

echo "📡 Backend URL: $BACKEND_URL"
echo "🔑 Using API Key: ${API_KEY:0:10}..."
echo ""

# Function to make API call with error handling
make_api_call() {
    local endpoint=$1
    local description=$2
    
    echo "🚀 Calling: $description"
    echo "📍 Endpoint: $endpoint"
    
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        "$BACKEND_URL$endpoint")
    
    # Split response and status code
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "📊 Status Code: $http_code"
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
        echo "✅ Success: $description"
        echo "📄 Response: $body"
    else
        echo "❌ Failed: $description"
        echo "📄 Error Response: $body"
    fi
    
    echo "----------------------------------------"
    echo ""
}

# Main database fix endpoints
echo "🔧 Starting Database Schema Fixes..."
echo ""

# 1. Emergency Schema Fix
make_api_call "/emergency-schema-fix" "Emergency Schema Fix"

# 2. Fix PostgreSQL Schema
make_api_call "/fix-postgresql-schema" "PostgreSQL Schema Fix"

# 3. Fix Database Schema (Main Fix)
make_api_call "/fix-database-schema" "Main Database Schema Fix"

# 4. Analytics Database Migration
make_api_call "/migrate-analytics-database" "Analytics Database Migration"

echo "🎉 Database Schema Fix Script Completed!"
echo ""
echo "📝 Next Steps:"
echo "1. Check your backend logs for any errors"
echo "2. Test the frontend functionality"
echo "3. Verify all features are working correctly"
echo ""
echo "🔗 Backend Health Check: $BACKEND_URL/health"
echo "🔗 API Documentation: $BACKEND_URL/docs"
