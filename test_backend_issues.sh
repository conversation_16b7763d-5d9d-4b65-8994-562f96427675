#!/bin/bash

# AIthentiq Backend Issue Testing Script
# Tests for: 1) Missing test files visibility, 2) User Management database error

BASE_URL="http://localhost:8000"
ADMIN_USER_ID="<EMAIL>"

echo "🔧 AIthentiq Backend Issue Testing Script"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo "Admin User: $ADMIN_USER_ID"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "ERROR") echo -e "${RED}❌ $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "INFO") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Test 1: Basic Backend Health
echo "1. Testing Backend Health"
echo "------------------------"
response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$BASE_URL/test" 2>/dev/null)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status "SUCCESS" "Backend is running"
    cat /tmp/health_response.json | jq . 2>/dev/null || cat /tmp/health_response.json
else
    print_status "ERROR" "Backend not responding (HTTP: $http_code)"
    exit 1
fi
echo ""

# Test 2: Database Migration Check
echo "2. Testing Database Schema"
echo "-------------------------"
response=$(curl -s -w "%{http_code}" -o /tmp/debug_response.json "$BASE_URL/debug/queries" 2>/dev/null)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status "SUCCESS" "Database queries endpoint working"
    cat /tmp/debug_response.json | jq . 2>/dev/null || cat /tmp/debug_response.json
else
    print_status "ERROR" "Database queries endpoint failed (HTTP: $http_code)"
fi
echo ""

# Test 3: Create Admin User (if needed)
echo "3. Creating/Verifying Admin User"
echo "--------------------------------"
response=$(curl -s -w "%{http_code}" -o /tmp/admin_create.json -X POST "$BASE_URL/make-admin/$ADMIN_USER_ID" 2>/dev/null)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status "SUCCESS" "Admin user created/verified"
    cat /tmp/admin_create.json | jq . 2>/dev/null || cat /tmp/admin_create.json
else
    print_status "ERROR" "Failed to create admin user (HTTP: $http_code)"
fi
echo ""

# Test 4: User Management Endpoint (The failing one)
echo "4. Testing User Management (The Problem)"
echo "---------------------------------------"
response=$(curl -s -w "%{http_code}" -o /tmp/users_response.json "$BASE_URL/admin/users?user_id=$ADMIN_USER_ID" 2>/dev/null)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status "SUCCESS" "User management endpoint working"
    cat /tmp/users_response.json | jq . 2>/dev/null || cat /tmp/users_response.json
else
    print_status "ERROR" "User management endpoint failed (HTTP: $http_code)"
    echo "Response content:"
    cat /tmp/users_response.json
fi
echo ""

# Test 5: Test Scripts Endpoint
echo "5. Testing Test Scripts Visibility"
echo "----------------------------------"
response=$(curl -s -w "%{http_code}" -o /tmp/scripts_response.json "$BASE_URL/admin/test-scripts?user_id=$ADMIN_USER_ID" 2>/dev/null)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status "SUCCESS" "Test scripts endpoint working"
    scripts_count=$(cat /tmp/scripts_response.json | jq '.scripts | length' 2>/dev/null || echo "0")
    print_status "INFO" "Found $scripts_count test scripts"
    cat /tmp/scripts_response.json | jq . 2>/dev/null || cat /tmp/scripts_response.json
else
    print_status "ERROR" "Test scripts endpoint failed (HTTP: $http_code)"
    cat /tmp/scripts_response.json
fi
echo ""

# Test 6: Debug Users Endpoint
echo "6. Testing Debug Users Endpoint"
echo "-------------------------------"
response=$(curl -s -w "%{http_code}" -o /tmp/debug_users.json "$BASE_URL/debug/users" 2>/dev/null)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status "SUCCESS" "Debug users endpoint working"
    cat /tmp/debug_users.json | jq . 2>/dev/null || cat /tmp/debug_users.json
else
    print_status "ERROR" "Debug users endpoint failed (HTTP: $http_code)"
fi
echo ""

# Test 7: Database Migration Status
echo "7. Testing Database Migration Status"
echo "------------------------------------"
echo "Attempting to run database migration..."

# Try to run the migration script
if [ -f "backend/migrations/add_monitoring_fields.py" ]; then
    print_status "INFO" "Found migration script, attempting to run..."
    cd backend
    python migrations/add_monitoring_fields.py > /tmp/migration_output.txt 2>&1
    migration_exit_code=$?
    cd ..
    
    if [ $migration_exit_code -eq 0 ]; then
        print_status "SUCCESS" "Migration completed successfully"
    else
        print_status "ERROR" "Migration failed"
    fi
    
    echo "Migration output:"
    cat /tmp/migration_output.txt
else
    print_status "WARNING" "Migration script not found"
fi
echo ""

# Test 8: Re-test User Management After Migration
echo "8. Re-testing User Management After Migration"
echo "---------------------------------------------"
response=$(curl -s -w "%{http_code}" -o /tmp/users_retest.json "$BASE_URL/admin/users?user_id=$ADMIN_USER_ID" 2>/dev/null)
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    print_status "SUCCESS" "User management now working!"
    cat /tmp/users_retest.json | jq . 2>/dev/null || cat /tmp/users_retest.json
else
    print_status "ERROR" "User management still failing (HTTP: $http_code)"
    cat /tmp/users_retest.json
fi
echo ""

# Summary
echo "🎯 SUMMARY"
echo "=========="
echo "Issues tested:"
echo "1. Test files visibility - Check test scripts endpoint result above"
echo "2. User Management database error - Check user management endpoint results above"
echo ""
echo "If user management is still failing, the response_time_ms column is missing from the database."
echo "If test scripts show 0 results, there may be a path resolution issue."
echo ""
echo "Next steps:"
echo "- If migration failed, manually add the missing column to the database"
echo "- If test scripts not visible, check the Test folder path resolution"

# Cleanup
rm -f /tmp/health_response.json /tmp/debug_response.json /tmp/admin_create.json
rm -f /tmp/users_response.json /tmp/scripts_response.json /tmp/debug_users.json
rm -f /tmp/users_retest.json /tmp/migration_output.txt
