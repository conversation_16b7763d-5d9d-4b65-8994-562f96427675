'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import dynamic from 'next/dynamic';
import { TrendingUp, BarChart3, Calendar, Filter, Download } from 'lucide-react';
import { createApiInstance, getUserId } from '@/lib/api';

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

interface TrustScoreData {
  date: string;
  overall_score: number;
  model_confidence: number;
  citation_accuracy: number;
  source_quality: number;
  question_match: number;
  trust_level: string;
  query_count: number;
}

interface TrustScoreTrendsProps {
  className?: string;
}

export default function TrustScoreTrends({ className = '' }: TrustScoreTrendsProps) {
  const { data: session } = useSession();
  const [trendData, setTrendData] = useState<TrustScoreData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [viewType, setViewType] = useState<'overall' | 'components' | 'distribution'>('overall');

  useEffect(() => {
    if (session) {
      loadTrustScoreTrends();
    }
  }, [session, timeRange]);

  const loadTrustScoreTrends = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const userId = getUserId(session);
      
      // Get trust score trends from backend
      const response = await api.get(`/trust/trends/${userId}`, {
        params: {
          time_range: timeRange,
          include_components: true
        }
      });

      setTrendData(response.data.trends || []);
    } catch (err) {
      console.error('Error loading trust score trends:', err);
      
      // Generate mock data for demonstration
      const mockData = generateMockTrustData(timeRange);
      setTrendData(mockData);
    } finally {
      setLoading(false);
    }
  };

  const generateMockTrustData = (range: string): TrustScoreData[] => {
    const days = range === '7d' ? 7 : range === '30d' ? 30 : range === '90d' ? 90 : 365;
    const data: TrustScoreData[] = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      // Generate realistic trust score data with some variation
      const baseScore = 0.75 + (Math.random() - 0.5) * 0.3;
      const modelConf = Math.max(0.5, Math.min(1.0, baseScore + (Math.random() - 0.5) * 0.2));
      const citationAcc = Math.max(0.6, Math.min(1.0, baseScore + (Math.random() - 0.5) * 0.15));
      const sourceQual = Math.max(0.7, Math.min(1.0, baseScore + (Math.random() - 0.5) * 0.1));
      const questionMatch = Math.max(0.6, Math.min(1.0, baseScore + (Math.random() - 0.5) * 0.2));
      
      const overallScore = (modelConf * 0.4 + citationAcc * 0.2 + sourceQual * 0.3 + questionMatch * 0.1);
      
      data.push({
        date: date.toISOString().split('T')[0],
        overall_score: overallScore,
        model_confidence: modelConf,
        citation_accuracy: citationAcc,
        source_quality: sourceQual,
        question_match: questionMatch,
        trust_level: overallScore >= 0.8 ? 'high' : overallScore >= 0.6 ? 'moderate' : 'low',
        query_count: Math.floor(Math.random() * 20) + 5
      });
    }
    
    return data;
  };

  const renderOverallTrend = () => {
    const plotData = [
      {
        x: trendData.map(d => d.date),
        y: trendData.map(d => d.overall_score * 100),
        type: 'scatter' as const,
        mode: 'lines+markers' as const,
        name: 'Trust Score',
        line: {
          color: '#3B82F6',
          width: 3
        },
        marker: {
          color: trendData.map(d => 
            d.trust_level === 'high' ? '#10B981' : 
            d.trust_level === 'moderate' ? '#F59E0B' : '#EF4444'
          ),
          size: 8,
          line: { color: '#FFFFFF', width: 2 }
        },
        hovertemplate: '<b>%{x}</b><br>Trust Score: %{y:.1f}%<br>Queries: %{text}<extra></extra>',
        text: trendData.map(d => `${d.query_count} queries`)
      }
    ];

    const layout = {
      title: {
        text: 'Trust Score Trend Over Time',
        font: { size: 16, color: '#1F2937' }
      },
      xaxis: {
        title: 'Date',
        gridcolor: '#F3F4F6',
        tickfont: { size: 12, color: '#6B7280' }
      },
      yaxis: {
        title: 'Trust Score (%)',
        range: [0, 100],
        gridcolor: '#F3F4F6',
        tickfont: { size: 12, color: '#6B7280' }
      },
      plot_bgcolor: '#FFFFFF',
      paper_bgcolor: '#FFFFFF',
      margin: { t: 50, r: 30, b: 50, l: 60 },
      height: 400,
      showlegend: false,
      hovermode: 'closest' as const
    };

    return <Plot data={plotData} layout={layout} config={{ displayModeBar: false, responsive: true }} style={{ width: '100%' }} />;
  };

  const renderComponentsTrend = () => {
    const plotData = [
      {
        x: trendData.map(d => d.date),
        y: trendData.map(d => d.model_confidence * 100),
        type: 'scatter' as const,
        mode: 'lines' as const,
        name: 'Model Confidence',
        line: { color: '#3B82F6', width: 2 }
      },
      {
        x: trendData.map(d => d.date),
        y: trendData.map(d => d.citation_accuracy * 100),
        type: 'scatter' as const,
        mode: 'lines' as const,
        name: 'Citation Accuracy',
        line: { color: '#10B981', width: 2 }
      },
      {
        x: trendData.map(d => d.date),
        y: trendData.map(d => d.source_quality * 100),
        type: 'scatter' as const,
        mode: 'lines' as const,
        name: 'Source Quality',
        line: { color: '#F59E0B', width: 2 }
      },
      {
        x: trendData.map(d => d.date),
        y: trendData.map(d => d.question_match * 100),
        type: 'scatter' as const,
        mode: 'lines' as const,
        name: 'Question Match',
        line: { color: '#EF4444', width: 2 }
      }
    ];

    const layout = {
      title: {
        text: 'Trust Score Components Over Time',
        font: { size: 16, color: '#1F2937' }
      },
      xaxis: {
        title: 'Date',
        gridcolor: '#F3F4F6',
        tickfont: { size: 12, color: '#6B7280' }
      },
      yaxis: {
        title: 'Score (%)',
        range: [0, 100],
        gridcolor: '#F3F4F6',
        tickfont: { size: 12, color: '#6B7280' }
      },
      plot_bgcolor: '#FFFFFF',
      paper_bgcolor: '#FFFFFF',
      margin: { t: 50, r: 30, b: 50, l: 60 },
      height: 400,
      hovermode: 'closest' as const
    };

    return <Plot data={plotData} layout={layout} config={{ displayModeBar: false, responsive: true }} style={{ width: '100%' }} />;
  };

  const renderDistribution = () => {
    const trustLevels = ['low', 'moderate', 'high'];
    const counts = trustLevels.map(level => 
      trendData.filter(d => d.trust_level === level).length
    );

    const plotData = [
      {
        x: trustLevels.map(level => level.charAt(0).toUpperCase() + level.slice(1)),
        y: counts,
        type: 'bar' as const,
        marker: {
          color: ['#EF4444', '#F59E0B', '#10B981']
        },
        hovertemplate: '<b>%{x} Trust</b><br>Count: %{y}<br>Percentage: %{text}<extra></extra>',
        text: counts.map(count => `${((count / trendData.length) * 100).toFixed(1)}%`)
      }
    ];

    const layout = {
      title: {
        text: 'Trust Score Distribution',
        font: { size: 16, color: '#1F2937' }
      },
      xaxis: {
        title: 'Trust Level',
        gridcolor: '#F3F4F6',
        tickfont: { size: 12, color: '#6B7280' }
      },
      yaxis: {
        title: 'Number of Queries',
        gridcolor: '#F3F4F6',
        tickfont: { size: 12, color: '#6B7280' }
      },
      plot_bgcolor: '#FFFFFF',
      paper_bgcolor: '#FFFFFF',
      margin: { t: 50, r: 30, b: 50, l: 60 },
      height: 400,
      showlegend: false
    };

    return <Plot data={plotData} layout={layout} config={{ displayModeBar: false, responsive: true }} style={{ width: '100%' }} />;
  };

  const exportData = () => {
    const csvContent = [
      ['Date', 'Overall Score', 'Model Confidence', 'Citation Accuracy', 'Source Quality', 'Question Match', 'Trust Level', 'Query Count'],
      ...trendData.map(d => [
        d.date,
        (d.overall_score * 100).toFixed(1),
        (d.model_confidence * 100).toFixed(1),
        (d.citation_accuracy * 100).toFixed(1),
        (d.source_quality * 100).toFixed(1),
        (d.question_match * 100).toFixed(1),
        d.trust_level,
        d.query_count.toString()
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `trust-score-trends-${timeRange}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="text-center text-red-600">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <TrendingUp className="h-6 w-6 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">Trust Score Analytics</h3>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={exportData}
            className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between mb-6 gap-4">
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-700">Time Range:</span>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <BarChart3 className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-700">View:</span>
          <select
            value={viewType}
            onChange={(e) => setViewType(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="overall">Overall Trend</option>
            <option value="components">Components</option>
            <option value="distribution">Distribution</option>
          </select>
        </div>
      </div>

      {/* Chart */}
      <div className="mb-4">
        {viewType === 'overall' && renderOverallTrend()}
        {viewType === 'components' && renderComponentsTrend()}
        {viewType === 'distribution' && renderDistribution()}
      </div>

      {/* Summary Stats */}
      {trendData.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {(trendData.reduce((sum, d) => sum + d.overall_score, 0) / trendData.length * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Avg Trust Score</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {trendData.filter(d => d.trust_level === 'high').length}
            </div>
            <div className="text-sm text-gray-600">High Trust Days</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {trendData.reduce((sum, d) => sum + d.query_count, 0)}
            </div>
            <div className="text-sm text-gray-600">Total Queries</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Math.max(...trendData.map(d => d.overall_score * 100)).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Peak Score</div>
          </div>
        </div>
      )}
    </div>
  );
}
