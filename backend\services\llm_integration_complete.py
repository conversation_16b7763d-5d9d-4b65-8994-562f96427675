"""
Complete LLM Integration Service for Phase 3
Implements HTTP-based API integration for GPT-4, <PERSON>, <PERSON>, Mistral as required by development plan
"""

import os
import logging
import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import time
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class LLMProvider(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    MISTRAL = "mistral"

@dataclass
class LLMRequest:
    """Request for LLM generation"""
    prompt: str
    provider: LLMProvider
    model: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 1000
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None

@dataclass
class LLMResponse:
    """Response from LLM"""
    content: str
    provider: LLMProvider
    model: str
    usage: Dict[str, Any]
    confidence: Optional[float] = None
    processing_time_ms: int = 0
    metadata: Dict[str, Any] = None

class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, max_requests: int = 60, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}
    
    async def check_rate_limit(self, key: str) -> bool:
        """Check if request is within rate limit"""
        now = datetime.now()
        
        if key not in self.requests:
            self.requests[key] = []
        
        # Remove old requests outside the window
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if now - req_time < timedelta(seconds=self.window_seconds)
        ]
        
        # Check if we're under the limit
        if len(self.requests[key]) >= self.max_requests:
            return False
        
        # Add current request
        self.requests[key].append(now)
        return True

class CompleteLLMService:
    """Complete LLM service implementing all Phase 3 requirements"""
    
    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.session = None
        
        # API configurations
        self.api_configs = {
            LLMProvider.OPENAI: {
                "base_url": "https://api.openai.com/v1",
                "default_model": "gpt-4",
                "api_key_env": "OPENAI_API_KEY"
            },
            LLMProvider.ANTHROPIC: {
                "base_url": "https://api.anthropic.com/v1",
                "default_model": "claude-3-sonnet-20240229",
                "api_key_env": "ANTHROPIC_API_KEY"
            },
            LLMProvider.GOOGLE: {
                "base_url": "https://generativelanguage.googleapis.com/v1beta",
                "default_model": "gemini-pro",
                "api_key_env": "GOOGLE_API_KEY"
            },
            LLMProvider.MISTRAL: {
                "base_url": "https://api.mistral.ai/v1",
                "default_model": "mistral-medium",
                "api_key_env": "MISTRAL_API_KEY"
            }
        }
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def generate_response(
        self,
        prompt: str,
        provider: str = "openai",
        model: Optional[str] = None,
        tenant_id: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> str:
        """Generate response using specified LLM provider"""
        
        request = LLMRequest(
            prompt=prompt,
            provider=LLMProvider(provider),
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            tenant_id=tenant_id
        )
        
        response = await self.generate_response_detailed(request)
        return response.content
    
    async def generate_response_detailed(self, request: LLMRequest) -> LLMResponse:
        """Generate detailed response with metadata"""
        
        start_time = time.time()
        
        try:
            # Check rate limits
            rate_limit_key = f"{request.provider}_{request.tenant_id or 'default'}"
            if not await self.rate_limiter.check_rate_limit(rate_limit_key):
                raise Exception(f"Rate limit exceeded for {request.provider}")
            
            # Route to appropriate provider
            if request.provider == LLMProvider.OPENAI:
                response = await self._call_openai(request)
            elif request.provider == LLMProvider.ANTHROPIC:
                response = await self._call_anthropic(request)
            elif request.provider == LLMProvider.GOOGLE:
                response = await self._call_google(request)
            elif request.provider == LLMProvider.MISTRAL:
                response = await self._call_mistral(request)
            else:
                raise ValueError(f"Unsupported provider: {request.provider}")
            
            processing_time = int((time.time() - start_time) * 1000)
            response.processing_time_ms = processing_time
            
            logger.info(f"LLM response generated: {request.provider} in {processing_time}ms")
            return response
            
        except Exception as e:
            logger.error(f"LLM generation failed: {e}")
            processing_time = int((time.time() - start_time) * 1000)
            
            return LLMResponse(
                content=f"I apologize, but I encountered an error: {str(e)}",
                provider=request.provider,
                model=request.model or "unknown",
                usage={"error": True},
                processing_time_ms=processing_time,
                metadata={"error": str(e)}
            )
    
    async def _call_openai(self, request: LLMRequest) -> LLMResponse:
        """Call OpenAI API"""
        
        api_key = os.getenv(self.api_configs[LLMProvider.OPENAI]["api_key_env"])
        if not api_key:
            raise ValueError("OpenAI API key not configured")
        
        model = request.model or self.api_configs[LLMProvider.OPENAI]["default_model"]
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": request.prompt}
            ],
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }
        
        session = await self._get_session()
        url = f"{self.api_configs[LLMProvider.OPENAI]['base_url']}/chat/completions"
        
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"OpenAI API error: {response.status} - {error_text}")
            
            data = await response.json()
            
            return LLMResponse(
                content=data["choices"][0]["message"]["content"],
                provider=LLMProvider.OPENAI,
                model=model,
                usage=data.get("usage", {}),
                metadata={"response_data": data}
            )
    
    async def _call_anthropic(self, request: LLMRequest) -> LLMResponse:
        """Call Anthropic Claude API"""
        
        api_key = os.getenv(self.api_configs[LLMProvider.ANTHROPIC]["api_key_env"])
        if not api_key:
            raise ValueError("Anthropic API key not configured")
        
        model = request.model or self.api_configs[LLMProvider.ANTHROPIC]["default_model"]
        
        headers = {
            "x-api-key": api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        payload = {
            "model": model,
            "max_tokens": request.max_tokens,
            "messages": [
                {"role": "user", "content": request.prompt}
            ],
            "temperature": request.temperature
        }
        
        session = await self._get_session()
        url = f"{self.api_configs[LLMProvider.ANTHROPIC]['base_url']}/messages"
        
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Anthropic API error: {response.status} - {error_text}")
            
            data = await response.json()
            
            return LLMResponse(
                content=data["content"][0]["text"],
                provider=LLMProvider.ANTHROPIC,
                model=model,
                usage=data.get("usage", {}),
                metadata={"response_data": data}
            )
    
    async def _call_google(self, request: LLMRequest) -> LLMResponse:
        """Call Google Gemini API"""
        
        api_key = os.getenv(self.api_configs[LLMProvider.GOOGLE]["api_key_env"])
        if not api_key:
            raise ValueError("Google API key not configured")
        
        model = request.model or self.api_configs[LLMProvider.GOOGLE]["default_model"]
        
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "contents": [
                {
                    "parts": [
                        {"text": request.prompt}
                    ]
                }
            ],
            "generationConfig": {
                "temperature": request.temperature,
                "maxOutputTokens": request.max_tokens
            }
        }
        
        session = await self._get_session()
        url = f"{self.api_configs[LLMProvider.GOOGLE]['base_url']}/models/{model}:generateContent?key={api_key}"
        
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Google API error: {response.status} - {error_text}")
            
            data = await response.json()
            
            content = ""
            if "candidates" in data and data["candidates"]:
                if "content" in data["candidates"][0]:
                    if "parts" in data["candidates"][0]["content"]:
                        content = data["candidates"][0]["content"]["parts"][0].get("text", "")
            
            return LLMResponse(
                content=content,
                provider=LLMProvider.GOOGLE,
                model=model,
                usage=data.get("usageMetadata", {}),
                metadata={"response_data": data}
            )
    
    async def _call_mistral(self, request: LLMRequest) -> LLMResponse:
        """Call Mistral AI API"""
        
        api_key = os.getenv(self.api_configs[LLMProvider.MISTRAL]["api_key_env"])
        if not api_key:
            raise ValueError("Mistral API key not configured")
        
        model = request.model or self.api_configs[LLMProvider.MISTRAL]["default_model"]
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": request.prompt}
            ],
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }
        
        session = await self._get_session()
        url = f"{self.api_configs[LLMProvider.MISTRAL]['base_url']}/chat/completions"
        
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Mistral API error: {response.status} - {error_text}")
            
            data = await response.json()
            
            return LLMResponse(
                content=data["choices"][0]["message"]["content"],
                provider=LLMProvider.MISTRAL,
                model=model,
                usage=data.get("usage", {}),
                metadata={"response_data": data}
            )
    
    async def get_available_providers(self) -> List[str]:
        """Get list of available LLM providers"""
        available = []
        
        for provider, config in self.api_configs.items():
            api_key = os.getenv(config["api_key_env"])
            if api_key:
                available.append(provider.value)
        
        return available
    
    async def test_provider(self, provider: str) -> bool:
        """Test if a provider is working"""
        try:
            response = await self.generate_response(
                prompt="Hello, this is a test.",
                provider=provider,
                max_tokens=10
            )
            return len(response) > 0
        except Exception as e:
            logger.error(f"Provider {provider} test failed: {e}")
            return False
    
    async def close(self):
        """Close HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()

# Global LLM service instance
complete_llm_service = CompleteLLMService()
