"""
GitHub Connector API Router for Phase 5
Implements GitHub connector configuration and management endpoints as required by development plan
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
import logging
import json

from database import get_db
from middleware.tenant_isolation import get_current_tenant_id
from middleware.auth import get_current_user_id
from middleware.rbac_complete import require_permission, Permission
from services.github_connector_complete import complete_github_connector
from services.github_sync_manager import github_sync_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/connectors/github", tags=["GitHub Connector"])

# Pydantic models for API

class GitHubOAuthRequest(BaseModel):
    """Request for GitHub OAuth authorization"""
    scopes: List[str] = Field(default=["repo", "read:user", "user:email"], description="OAuth scopes")
    state: Optional[str] = Field(default=None, description="OAuth state parameter")

class GitHubOAuthCallback(BaseModel):
    """GitHub OAuth callback data"""
    code: str = Field(..., description="OAuth authorization code")
    state: Optional[str] = Field(default=None, description="OAuth state parameter")

class GitHubConnectorCreate(BaseModel):
    """Create GitHub connector request"""
    name: str = Field(..., description="Connector name")
    description: Optional[str] = Field(default=None, description="Connector description")
    access_token: str = Field(..., description="GitHub access token")
    auto_sync: bool = Field(default=True, description="Enable automatic sync")
    sync_frequency: int = Field(default=300, description="Sync frequency in seconds")
    include_private_repos: bool = Field(default=False, description="Include private repositories")
    max_file_size: int = Field(default=1048576, description="Maximum file size in bytes")
    max_files_per_repo: int = Field(default=1000, description="Maximum files per repository")

class GitHubConnectorUpdate(BaseModel):
    """Update GitHub connector request"""
    name: Optional[str] = Field(default=None, description="Connector name")
    description: Optional[str] = Field(default=None, description="Connector description")
    auto_sync: Optional[bool] = Field(default=None, description="Enable automatic sync")
    sync_frequency: Optional[int] = Field(default=None, description="Sync frequency in seconds")
    include_private_repos: Optional[bool] = Field(default=None, description="Include private repositories")
    max_file_size: Optional[int] = Field(default=None, description="Maximum file size in bytes")
    max_files_per_repo: Optional[int] = Field(default=None, description="Maximum files per repository")
    is_active: Optional[bool] = Field(default=None, description="Connector active status")

class RepositorySyncRequest(BaseModel):
    """Request to sync specific repositories"""
    repository_ids: List[int] = Field(..., description="GitHub repository IDs to sync")
    force_full_sync: bool = Field(default=False, description="Force full sync instead of incremental")

@router.get("/oauth/authorize")
async def get_oauth_authorization_url(
    request: GitHubOAuthRequest,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS))
):
    """
    Get GitHub OAuth authorization URL
    
    Admin-only endpoint to start GitHub OAuth flow
    """
    try:
        auth_manager = complete_github_connector.auth_manager
        
        # Generate state parameter if not provided
        state = request.state or f"{tenant_id}:{user_id}"
        
        oauth_url = await auth_manager.get_oauth_url(state, request.scopes)
        
        return {
            "authorization_url": oauth_url,
            "state": state,
            "scopes": request.scopes
        }
        
    except Exception as e:
        logger.error(f"Failed to generate OAuth URL: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate OAuth URL: {str(e)}"
        )

@router.post("/oauth/callback")
async def handle_oauth_callback(
    callback: GitHubOAuthCallback,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Handle GitHub OAuth callback
    
    Admin-only endpoint to complete GitHub OAuth flow
    """
    try:
        auth_manager = complete_github_connector.auth_manager
        
        # Exchange code for token
        token_data = await auth_manager.exchange_code_for_token(callback.code)
        
        if "access_token" not in token_data:
            raise HTTPException(
                status_code=400,
                detail="Failed to obtain access token from GitHub"
            )
        
        # Validate token and get user info
        validation_result = await auth_manager.validate_token(token_data["access_token"])
        
        if not validation_result.get("valid"):
            raise HTTPException(
                status_code=400,
                detail="Invalid access token received from GitHub"
            )
        
        github_user = validation_result["user"]
        
        return {
            "success": True,
            "access_token": token_data["access_token"],
            "github_user": {
                "id": github_user["id"],
                "login": github_user["login"],
                "name": github_user.get("name"),
                "email": github_user.get("email"),
                "avatar_url": github_user.get("avatar_url")
            },
            "scopes": validation_result.get("scopes", [])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth callback failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"OAuth callback failed: {str(e)}"
        )

@router.post("/connectors")
async def create_github_connector(
    connector_data: GitHubConnectorCreate,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Create a new GitHub connector
    
    Admin-only endpoint to create GitHub connector
    """
    try:
        # Validate access token
        auth_manager = complete_github_connector.auth_manager
        validation_result = await auth_manager.validate_token(connector_data.access_token)
        
        if not validation_result.get("valid"):
            raise HTTPException(
                status_code=400,
                detail="Invalid GitHub access token"
            )
        
        github_user = validation_result["user"]
        
        # Create connector in database
        from models.github_connector_models import GitHubConnector
        
        connector = GitHubConnector(
            tenant_id=tenant_id,
            access_token=connector_data.access_token,  # Should be encrypted in production
            github_user_id=github_user["id"],
            github_username=github_user["login"],
            github_email=github_user.get("email"),
            name=connector_data.name,
            description=connector_data.description,
            auto_sync=connector_data.auto_sync,
            sync_frequency=connector_data.sync_frequency,
            include_private_repos=connector_data.include_private_repos,
            max_file_size=connector_data.max_file_size,
            max_files_per_repo=connector_data.max_files_per_repo,
            status="active"
        )
        
        db.add(connector)
        db.commit()
        db.refresh(connector)
        
        logger.info(f"Created GitHub connector {connector.id} for tenant {tenant_id}")
        
        return {
            "id": connector.id,
            "name": connector.name,
            "github_username": connector.github_username,
            "status": connector.status,
            "created_at": connector.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create GitHub connector: {str(e)}"
        )

@router.get("/connectors")
async def list_github_connectors(
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.VIEW_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    List GitHub connectors for tenant
    """
    try:
        from models.github_connector_models import GitHubConnector
        
        connectors = db.query(GitHubConnector).filter(
            GitHubConnector.tenant_id == tenant_id
        ).all()
        
        return {
            "connectors": [
                {
                    "id": connector.id,
                    "name": connector.name,
                    "description": connector.description,
                    "github_username": connector.github_username,
                    "status": connector.status,
                    "is_active": connector.is_active,
                    "auto_sync": connector.auto_sync,
                    "total_repositories": connector.total_repositories,
                    "total_files_synced": connector.total_files_synced,
                    "last_sync_at": connector.last_sync_at.isoformat() if connector.last_sync_at else None,
                    "created_at": connector.created_at.isoformat()
                }
                for connector in connectors
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to list GitHub connectors: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list GitHub connectors: {str(e)}"
        )

@router.get("/connectors/{connector_id}")
async def get_github_connector(
    connector_id: str,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.VIEW_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Get GitHub connector details
    """
    try:
        from models.github_connector_models import GitHubConnector
        
        connector = db.query(GitHubConnector).filter(
            GitHubConnector.id == connector_id,
            GitHubConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="GitHub connector not found"
            )
        
        return {
            "id": connector.id,
            "name": connector.name,
            "description": connector.description,
            "github_username": connector.github_username,
            "github_email": connector.github_email,
            "status": connector.status,
            "is_active": connector.is_active,
            "auto_sync": connector.auto_sync,
            "sync_frequency": connector.sync_frequency,
            "include_private_repos": connector.include_private_repos,
            "max_file_size": connector.max_file_size,
            "max_files_per_repo": connector.max_files_per_repo,
            "total_repositories": connector.total_repositories,
            "total_files_synced": connector.total_files_synced,
            "last_sync_at": connector.last_sync_at.isoformat() if connector.last_sync_at else None,
            "last_error": connector.last_error,
            "created_at": connector.created_at.isoformat(),
            "updated_at": connector.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get GitHub connector: {str(e)}"
        )

@router.put("/connectors/{connector_id}")
async def update_github_connector(
    connector_id: str,
    update_data: GitHubConnectorUpdate,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Update GitHub connector
    
    Admin-only endpoint to update GitHub connector settings
    """
    try:
        from models.github_connector_models import GitHubConnector
        
        connector = db.query(GitHubConnector).filter(
            GitHubConnector.id == connector_id,
            GitHubConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="GitHub connector not found"
            )
        
        # Update fields
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(connector, field, value)
        
        db.commit()
        db.refresh(connector)
        
        logger.info(f"Updated GitHub connector {connector_id}")
        
        return {
            "id": connector.id,
            "name": connector.name,
            "status": connector.status,
            "updated_at": connector.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update GitHub connector: {str(e)}"
        )

@router.delete("/connectors/{connector_id}")
async def delete_github_connector(
    connector_id: str,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Delete GitHub connector
    
    Admin-only endpoint to delete GitHub connector
    """
    try:
        from models.github_connector_models import GitHubConnector
        
        connector = db.query(GitHubConnector).filter(
            GitHubConnector.id == connector_id,
            GitHubConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="GitHub connector not found"
            )
        
        # Close any active API clients
        await complete_github_connector.close_client(tenant_id)
        
        # Delete connector (CASCADE will handle related records)
        db.delete(connector)
        db.commit()
        
        logger.info(f"Deleted GitHub connector {connector_id}")
        
        return {"success": True, "message": "GitHub connector deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete GitHub connector: {str(e)}"
        )

@router.get("/connectors/{connector_id}/repositories")
async def list_connector_repositories(
    connector_id: str,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.VIEW_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    List repositories for GitHub connector
    """
    try:
        from models.github_connector_models import GitHubConnector, GitHubRepository
        
        # Verify connector exists and belongs to tenant
        connector = db.query(GitHubConnector).filter(
            GitHubConnector.id == connector_id,
            GitHubConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="GitHub connector not found"
            )
        
        # Get repositories
        repositories = db.query(GitHubRepository).filter(
            GitHubRepository.connector_id == connector_id
        ).all()
        
        return {
            "repositories": [
                {
                    "id": repo.id,
                    "github_id": repo.github_id,
                    "name": repo.name,
                    "full_name": repo.full_name,
                    "description": repo.description,
                    "private": repo.private,
                    "language": repo.language,
                    "topics": repo.topics,
                    "size": repo.size,
                    "stargazers_count": repo.stargazers_count,
                    "forks_count": repo.forks_count,
                    "is_enabled": repo.is_enabled,
                    "sync_status": repo.sync_status,
                    "files_count": repo.files_count,
                    "last_sync_at": repo.last_sync_at.isoformat() if repo.last_sync_at else None,
                    "github_updated_at": repo.github_updated_at.isoformat() if repo.github_updated_at else None
                }
                for repo in repositories
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list connector repositories: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list connector repositories: {str(e)}"
        )

@router.post("/connectors/{connector_id}/sync")
async def sync_connector_repositories(
    connector_id: str,
    sync_request: RepositorySyncRequest,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_CONNECTORS)),
    db: Session = Depends(get_db)
):
    """
    Manually trigger sync for connector repositories
    
    Admin-only endpoint to trigger manual sync
    """
    try:
        from models.github_connector_models import GitHubConnector, GitHubRepository
        
        # Verify connector exists and belongs to tenant
        connector = db.query(GitHubConnector).filter(
            GitHubConnector.id == connector_id,
            GitHubConnector.tenant_id == tenant_id
        ).first()
        
        if not connector:
            raise HTTPException(
                status_code=404,
                detail="GitHub connector not found"
            )
        
        # Schedule sync jobs for specified repositories
        sync_jobs_scheduled = 0
        
        for repo_id in sync_request.repository_ids:
            # Get repository info
            repository = db.query(GitHubRepository).filter(
                GitHubRepository.github_id == repo_id,
                GitHubRepository.connector_id == connector_id
            ).first()
            
            if repository:
                # Schedule manual sync
                success = await github_sync_manager.schedule_manual_sync(
                    tenant_id=tenant_id,
                    repository_full_name=repository.full_name,
                    repository_id=repository.github_id
                )
                
                if success:
                    sync_jobs_scheduled += 1
        
        return {
            "success": True,
            "message": f"Scheduled sync for {sync_jobs_scheduled} repositories",
            "repositories_scheduled": sync_jobs_scheduled,
            "force_full_sync": sync_request.force_full_sync
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to sync connector repositories: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to sync connector repositories: {str(e)}"
        )

@router.post("/webhook")
async def handle_github_webhook(
    request: Request,
    tenant_id: str = Query(..., description="Tenant ID for webhook"),
    db: Session = Depends(get_db)
):
    """
    Handle GitHub webhook events
    
    Public endpoint for GitHub webhook delivery
    """
    try:
        # Get headers
        headers = dict(request.headers)
        
        # Get payload
        payload_bytes = await request.body()
        
        # Handle webhook
        result = await github_sync_manager.handle_webhook(headers, payload_bytes, tenant_id)
        
        if result["success"]:
            return {"success": True, "message": "Webhook processed successfully"}
        else:
            raise HTTPException(
                status_code=400,
                detail=result.get("error", "Webhook processing failed")
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Webhook handling failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Webhook handling failed: {str(e)}"
        )
