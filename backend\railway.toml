# Railway Configuration for AIthentiq Backend

[build]
builder = "nixpacks"

[deploy]
startCommand = "uvicorn main:app --host 0.0.0.0 --port $PORT"
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

# Environment variables to be set in Railway dashboard:
# - OPENAI_API_KEY
# - STRIPE_API_KEY  
# - STRIPE_WEBHOOK_SECRET
# - DATABASE_URL (automatically provided by Railway)
# - PORT (automatically provided by Railway)
# - HOST=0.0.0.0
