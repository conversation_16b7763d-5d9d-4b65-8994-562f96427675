'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import api, { getUserId, createApiInstance } from '@/lib/api';

interface TrustScore {
  trust_score: number;
  explanation: string;
  component_scores: {
    coherence: number;
    factual_accuracy: number;
    reasoning_quality: number;
    data_alignment: number;
  };
}

interface FormulaResult {
  formula: string;
  result: string;
}



interface QuestionBoxProps {
  datasetId: number;
  currentQuestion?: string;
  onQuestionChange?: (question: string) => void;
  onQuestionSubmitted?: () => void;
  onAnswerReceived: (
    answer: string,
    chartType: string | null,
    chartData: any,
    trustScore?: TrustScore,
    reasoningSteps?: string[],
    formulaResults?: FormulaResult[],
    queryId?: number,
    responseTime?: number,
    question?: string
  ) => void;
}

export default function QuestionBox({
  datasetId,
  currentQuestion = '',
  onQuestionChange,
  onQuestionSubmitted,
  onAnswerReceived
}: QuestionBoxProps) {
  const { data: session } = useSession();
  const [question, setQuestion] = useState(currentQuestion);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [includeTrustScore, setIncludeTrustScore] = useState(true);
  const [includeCoT, setIncludeCoT] = useState(false);
  const [startTime, setStartTime] = useState<number | null>(null);



  // Update question when currentQuestion prop changes
  useEffect(() => {
    if (currentQuestion !== question) {
      setQuestion(currentQuestion);
    }
  }, [currentQuestion]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!question.trim()) return;

    setLoading(true);
    setError(null);

    // Record start time for response timing
    const requestStartTime = performance.now();
    setStartTime(requestStartTime);

    // Notify parent that a new question is being submitted
    if (onQuestionSubmitted) {
      onQuestionSubmitted();
    }

    try {
      console.log('Asking question:', question.trim());
      console.log('Request parameters:', {
        datasetId,
        includeTrustScore,
        includeCoT
      });

      const userId = getUserId(session);

      // Choose endpoint based on user type
      let endpoint: string;
      let apiInstance: any;

      // Type-safe check for apiKey
      const userWithApiKey = session?.user as any;
      if (userWithApiKey?.apiKey) {
        // OAuth user with API key - use API v1
        endpoint = '/api/v1/ask';
        apiInstance = createApiInstance(session);
      } else {
        // Demo user or fallback - use direct endpoint
        endpoint = '/ask';
        apiInstance = api;
      }

      const requestData: any = {
        user_id: userId,
        dataset_id: datasetId,
        question: question.trim(),
        include_trust_score: includeTrustScore,
        include_cot: includeCoT
      };

      const response = await apiInstance.post(endpoint, requestData);

      console.log('Question response:', response);

      if (response && response.data) {
        // Calculate response time
        const responseTime = Math.round(performance.now() - requestStartTime);

        console.log('Processing answer with data:', {
          hasAnswer: !!response.data.answer,
          hasChartType: !!response.data.chart_type,
          hasChartData: !!response.data.chart_data,
          hasTrustScore: !!response.data.trust_score,
          hasReasoningSteps: !!response.data.reasoning_steps,
          hasFormulaResults: !!response.data.formula_results,
          responseTime: `${responseTime}ms`
        });

        onAnswerReceived(
          response.data.answer,
          response.data.chart_type,
          response.data.chart_data,
          response.data.trust_score,
          response.data.reasoning_steps,
          response.data.formula_results,
          response.data.id,
          responseTime,
          question.trim() // Pass the question that was asked
        );

        // Clear the question after successful submission
        setQuestion('');
      } else {
        console.error('Invalid response format:', response);
        setError('Invalid response format from server');
      }
    } catch (err: any) {
      console.error('Error processing question:', err);
      setError(err.response?.data?.detail || err.message || 'Error processing your question');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <input
            type="text"
            value={question}
            onChange={(e) => {
              setQuestion(e.target.value);
              if (onQuestionChange) {
                onQuestionChange(e.target.value);
              }
            }}
            placeholder="Ask a question about your data..."
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-black"
            disabled={loading}
          />
          <button
            type="submit"
            disabled={loading || !question.trim()}
            className={`absolute right-2 top-2 px-4 py-1 rounded-md ${
              loading || !question.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? (
              <div className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
            ) : (
              'Ask'
            )}
          </button>
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        <div className="space-y-3 mb-3">
          {/* Basic Options */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="trust-score"
                checked={includeTrustScore}
                onChange={(e) => setIncludeTrustScore(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="trust-score" className="ml-2 block text-sm text-black">
                Include Trust Score
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="chain-of-thought"
                checked={includeCoT}
                onChange={(e) => setIncludeCoT(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="chain-of-thought" className="ml-2 block text-sm text-black">
                Use Chain of Thought
              </label>
            </div>
          </div>


        </div>

        <div className="text-xs text-black">
          <div className="mb-2">
            <span className="font-medium">Data Analysis Examples:</span>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'What is the average value in the dataset?';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Average value
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Show me a bar chart of the top 5 categories';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Bar chart
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'What are the trends over time?';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Trends
            </button>
          </div>

          <div className="mb-2">
            <span className="font-medium">Excel Functions Examples:</span>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Calculate the correlation between the first two numeric columns';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Correlation
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Forecast the next 3 values based on the trend in the data';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Forecast
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Calculate percentiles for the numeric columns';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Percentiles
            </button>
          </div>

          <div className="mb-2">
            <span className="font-medium">Advanced Chart Examples:</span>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Show a heatmap of correlations between numeric columns';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Heatmap
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Create a box plot showing the distribution of values';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Box Plot
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Generate a radar chart comparing key metrics';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Radar
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Show a treemap of hierarchical data';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Treemap
            </button>
          </div>

          <div>
            <span className="font-medium">Information Retrieval Examples:</span>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Find all records where the value is greater than 500';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Find records
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Show me information about the highest value in the dataset';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Specific info
            </button>
            <button
              type="button"
              onClick={() => {
                const newQuestion = 'Which rows have missing values?';
                setQuestion(newQuestion);
                if (onQuestionChange) {
                  onQuestionChange(newQuestion);
                }
              }}
              className="ml-2 text-blue-600 hover:underline"
            >
              Data quality
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
