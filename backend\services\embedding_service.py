"""
Embedding Service for AIthentiq
Supports multiple embedding providers: OpenAI, Cohere, HuggingFace, InstructorXL
"""

import os
import logging
from typing import List, Dict, Any, Optional
import numpy as np
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class EmbeddingProvider(ABC):
    """Abstract base class for embedding providers"""
    
    @abstractmethod
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        pass
    
    @abstractmethod
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        pass
    
    @property
    @abstractmethod
    def dimension(self) -> int:
        """Get the dimension of embeddings"""
        pass

class OpenAIEmbeddingProvider(EmbeddingProvider):
    """OpenAI embedding provider"""
    
    def __init__(self, model: str = "text-embedding-ada-002"):
        self.model = model
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        # Try to import OpenAI
        try:
            import openai
            self.client = openai.OpenAI(api_key=self.api_key)
        except ImportError:
            raise ImportError("openai package is required for OpenAI embeddings")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        try:
            response = self.client.embeddings.create(
                model=self.model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"OpenAI embedding generation failed: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        try:
            response = self.client.embeddings.create(
                model=self.model,
                input=texts
            )
            return [data.embedding for data in response.data]
        except Exception as e:
            logger.error(f"OpenAI batch embedding generation failed: {e}")
            raise
    
    @property
    def dimension(self) -> int:
        """Get the dimension of embeddings"""
        if self.model == "text-embedding-ada-002":
            return 1536
        elif self.model == "text-embedding-3-small":
            return 1536
        elif self.model == "text-embedding-3-large":
            return 3072
        else:
            return 1536  # Default

class CohereEmbeddingProvider(EmbeddingProvider):
    """Cohere embedding provider"""
    
    def __init__(self, model: str = "embed-english-v3.0"):
        self.model = model
        self.api_key = os.getenv("COHERE_API_KEY")
        if not self.api_key:
            raise ValueError("COHERE_API_KEY environment variable is required")
        
        # Try to import Cohere
        try:
            import cohere
            self.client = cohere.Client(self.api_key)
        except ImportError:
            raise ImportError("cohere package is required for Cohere embeddings")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        return self.generate_embeddings([text])[0]
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        try:
            response = self.client.embed(
                texts=texts,
                model=self.model,
                input_type="search_document"
            )
            return response.embeddings
        except Exception as e:
            logger.error(f"Cohere embedding generation failed: {e}")
            raise
    
    @property
    def dimension(self) -> int:
        """Get the dimension of embeddings"""
        if "v3.0" in self.model:
            return 1024
        else:
            return 4096  # Default for older models

class HuggingFaceEmbeddingProvider(EmbeddingProvider):
    """HuggingFace embedding provider"""
    
    def __init__(self, model: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.model = model
        
        # Try to import sentence-transformers
        try:
            from sentence_transformers import SentenceTransformer
            self.model_instance = SentenceTransformer(model)
        except ImportError:
            raise ImportError("sentence-transformers package is required for HuggingFace embeddings")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        try:
            embedding = self.model_instance.encode(text)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"HuggingFace embedding generation failed: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        try:
            embeddings = self.model_instance.encode(texts)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"HuggingFace batch embedding generation failed: {e}")
            raise
    
    @property
    def dimension(self) -> int:
        """Get the dimension of embeddings"""
        return self.model_instance.get_sentence_embedding_dimension()

class InstructorXLEmbeddingProvider(EmbeddingProvider):
    """InstructorXL embedding provider"""
    
    def __init__(self, model: str = "hkunlp/instructor-xl"):
        self.model = model
        
        # Try to import InstructorEmbedding
        try:
            from InstructorEmbedding import INSTRUCTOR
            self.model_instance = INSTRUCTOR(model)
        except ImportError:
            raise ImportError("InstructorEmbedding package is required for InstructorXL embeddings")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        try:
            # Use a generic instruction for document embedding
            instruction = "Represent the document for retrieval:"
            embedding = self.model_instance.encode([[instruction, text]])
            return embedding[0].tolist()
        except Exception as e:
            logger.error(f"InstructorXL embedding generation failed: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        try:
            # Use a generic instruction for document embedding
            instruction = "Represent the document for retrieval:"
            instruction_text_pairs = [[instruction, text] for text in texts]
            embeddings = self.model_instance.encode(instruction_text_pairs)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"InstructorXL batch embedding generation failed: {e}")
            raise
    
    @property
    def dimension(self) -> int:
        """Get the dimension of embeddings"""
        return 768  # InstructorXL dimension

class EmbeddingService:
    """
    Unified embedding service supporting multiple providers
    """
    
    def __init__(self, provider: str = "openai", **kwargs):
        self.provider_name = provider.lower()
        self.provider = self._create_provider(provider, **kwargs)
    
    def _create_provider(self, provider: str, **kwargs) -> EmbeddingProvider:
        """Create the appropriate embedding provider"""
        provider = provider.lower()
        
        if provider == "openai":
            model = kwargs.get("model", "text-embedding-ada-002")
            return OpenAIEmbeddingProvider(model=model)
        elif provider == "cohere":
            model = kwargs.get("model", "embed-english-v3.0")
            return CohereEmbeddingProvider(model=model)
        elif provider == "huggingface":
            model = kwargs.get("model", "sentence-transformers/all-MiniLM-L6-v2")
            return HuggingFaceEmbeddingProvider(model=model)
        elif provider == "instructorxl":
            model = kwargs.get("model", "hkunlp/instructor-xl")
            return InstructorXLEmbeddingProvider(model=model)
        else:
            raise ValueError(f"Unsupported embedding provider: {provider}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")
        
        return self.provider.generate_embedding(text.strip())
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts"""
        if not texts:
            raise ValueError("Texts list cannot be empty")

        # Filter out empty texts
        valid_texts = [text.strip() for text in texts if text and text.strip()]
        if not valid_texts:
            raise ValueError("No valid texts provided")

        return self.provider.generate_embeddings(valid_texts)

    async def generate_embeddings_batch(
        self,
        texts: List[str],
        batch_size: int = 100,
        tenant_id: str = None,
        progress_callback: callable = None
    ) -> List[List[float]]:
        """
        Generate embeddings in batches for efficiency (Phase 2 requirement)
        Implements batch processing as required by development plan
        """
        if not texts:
            raise ValueError("Texts list cannot be empty")

        # Filter out empty texts
        valid_texts = [text.strip() for text in texts if text and text.strip()]
        if not valid_texts:
            raise ValueError("No valid texts provided")

        all_embeddings = []
        total_batches = (len(valid_texts) - 1) // batch_size + 1

        logger.info(f"Starting batch embedding generation: {len(valid_texts)} texts, {total_batches} batches, tenant: {tenant_id}")

        for i in range(0, len(valid_texts), batch_size):
            batch_num = i // batch_size + 1
            batch = valid_texts[i:i + batch_size]

            try:
                # Generate embeddings for this batch
                batch_embeddings = self.provider.generate_embeddings(batch)
                all_embeddings.extend(batch_embeddings)

                # Log progress for large batches
                if total_batches > 1:
                    logger.info(f"Completed batch {batch_num}/{total_batches} ({len(batch)} texts) for tenant {tenant_id}")

                # Call progress callback if provided
                if progress_callback:
                    progress = batch_num / total_batches
                    await progress_callback(progress, batch_num, total_batches)

            except Exception as e:
                logger.error(f"Batch embedding failed for batch {batch_num}/{total_batches}: {e}")
                # Add zero embeddings for failed batch to maintain alignment
                zero_embedding = [0.0] * self.provider.dimension
                all_embeddings.extend([zero_embedding for _ in batch])

                # Continue with next batch instead of failing completely
                continue

        logger.info(f"Batch embedding generation completed: {len(all_embeddings)} embeddings generated")
        return all_embeddings
    
    def get_dimension(self) -> int:
        """Get the dimension of embeddings"""
        return self.provider.dimension
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current provider"""
        return {
            "provider": self.provider_name,
            "dimension": self.provider.dimension,
            "model": getattr(self.provider, 'model', 'unknown')
        }
    
    @staticmethod
    def get_available_providers() -> List[str]:
        """Get list of available embedding providers"""
        providers = []
        
        # Check OpenAI
        if os.getenv("OPENAI_API_KEY"):
            try:
                import openai
                providers.append("openai")
            except ImportError:
                pass
        
        # Check Cohere
        if os.getenv("COHERE_API_KEY"):
            try:
                import cohere
                providers.append("cohere")
            except ImportError:
                pass
        
        # Check HuggingFace
        try:
            from sentence_transformers import SentenceTransformer
            providers.append("huggingface")
        except ImportError:
            pass
        
        # Check InstructorXL
        try:
            from InstructorEmbedding import INSTRUCTOR
            providers.append("instructorxl")
        except ImportError:
            pass
        
        return providers
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """Calculate cosine similarity between two embeddings"""
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Calculate cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Similarity calculation failed: {e}")
            return 0.0

# Global embedding service instance (will be initialized based on tenant preferences)
def create_embedding_service(provider: str = "openai", **kwargs) -> EmbeddingService:
    """Create an embedding service instance"""
    return EmbeddingService(provider=provider, **kwargs)
