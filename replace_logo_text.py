#!/usr/bin/env python3
"""
Script to replace text in logo image
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
except ImportError:
    print("❌ PIL (Pillow) not installed. Installing...")
    import subprocess
    subprocess.check_call(["pip", "install", "Pillow"])
    from PIL import Image, ImageDraw, ImageFont
    import os

def replace_logo_text():
    """Replace text in the logo image"""
    
    logo_path = r"Logo\Logo.jpg"
    output_path = r"Logo\AIthentiq_Logo.jpg"
    
    print("🎨 REPLACING LOGO TEXT")
    print("="*30)
    
    if not os.path.exists(logo_path):
        print(f"❌ Logo file not found: {logo_path}")
        return False
    
    try:
        # Open the original image
        print(f"📂 Opening logo: {logo_path}")
        image = Image.open(logo_path)
        
        # Get image dimensions
        width, height = image.size
        print(f"📏 Image size: {width}x{height}")
        
        # Create a copy for editing
        new_image = image.copy()
        draw = ImageDraw.Draw(new_image)
        
        # Try to load a font (fallback to default if not available)
        try:
            # Try to use a nice font
            font_size = max(width // 15, 24)  # Scale font based on image width
            font = ImageFont.truetype("arial.ttf", font_size)
            print(f"✅ Using Arial font, size: {font_size}")
        except:
            try:
                font = ImageFont.load_default()
                print("✅ Using default font")
            except:
                font = None
                print("⚠️ No font available, using basic text")
        
        # Define the new text
        new_text = "AIthentiq"
        
        # Calculate text position (center bottom)
        if font:
            bbox = draw.textbbox((0, 0), new_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            # Estimate text size for default font
            text_width = len(new_text) * 10
            text_height = 15
        
        # Position text at bottom center
        x = (width - text_width) // 2
        y = height - text_height - 20  # 20 pixels from bottom
        
        print(f"📍 Text position: ({x}, {y})")
        print(f"📝 Text: '{new_text}'")
        
        # Add a semi-transparent background for the text
        padding = 10
        bg_x1 = x - padding
        bg_y1 = y - padding
        bg_x2 = x + text_width + padding
        bg_y2 = y + text_height + padding
        
        # Draw background rectangle (semi-transparent white)
        draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill=(255, 255, 255, 200))
        
        # Draw the text
        if font:
            draw.text((x, y), new_text, fill=(0, 0, 0), font=font)
        else:
            draw.text((x, y), new_text, fill=(0, 0, 0))
        
        # Save the new image
        print(f"💾 Saving new logo: {output_path}")
        new_image.save(output_path, "JPEG", quality=95)
        
        print("✅ Logo text replacement complete!")
        print(f"📁 New logo saved as: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing image: {e}")
        return False

def create_simple_text_logo():
    """Create a simple text-based logo if image processing fails"""
    
    output_path = r"Logo\AIthentiq_Simple_Logo.jpg"
    
    print("\n🎨 CREATING SIMPLE TEXT LOGO")
    print("="*35)
    
    try:
        # Create a new image with a nice background
        width, height = 400, 100
        image = Image.new('RGB', (width, height), color=(45, 55, 72))  # Dark blue-gray
        draw = ImageDraw.Draw(image)
        
        # Try to load a font
        try:
            font = ImageFont.truetype("arial.ttf", 36)
        except:
            font = ImageFont.load_default()
        
        # Text to draw
        text = "AIthentiq"
        
        # Calculate text position (center)
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = len(text) * 20
            text_height = 20
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # Draw the text with a nice color
        draw.text((x, y), text, fill=(74, 222, 128), font=font)  # Green color
        
        # Add a subtle border
        draw.rectangle([0, 0, width-1, height-1], outline=(156, 163, 175), width=2)
        
        # Save the image
        image.save(output_path, "JPEG", quality=95)
        
        print(f"✅ Simple logo created: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating simple logo: {e}")
        return False

if __name__ == "__main__":
    print("🏷️ LOGO TEXT REPLACEMENT TOOL")
    print("="*40)
    
    # Try to replace text in existing logo
    success = replace_logo_text()
    
    if not success:
        print("\n⚠️ Image processing failed, creating simple text logo...")
        create_simple_text_logo()
    
    print("\n🎯 LOGO OPTIONS:")
    print("1. AIthentiq_Logo.jpg - Modified original with new text")
    print("2. AIthentiq_Simple_Logo.jpg - Simple text-based logo")
    print("\nYou can use either logo in your application!")
