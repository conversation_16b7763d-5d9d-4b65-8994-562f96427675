import sqlite3
import os

def clear_feedback_table():
    """Clear all feedback data using direct SQLite connection"""
    
    # Database path
    db_path = os.path.join('backend', 'aithentiq.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Count existing records
        cursor.execute("SELECT COUNT(*) FROM feedback")
        existing_count = cursor.fetchone()[0]
        print(f"📊 Found {existing_count} existing feedback records")
        
        if existing_count == 0:
            print("✅ No feedback records to delete")
            conn.close()
            return True
        
        # Delete all feedback records
        cursor.execute("DELETE FROM feedback")
        deleted_count = cursor.rowcount
        
        # Commit changes
        conn.commit()
        
        print(f"✅ Successfully deleted {deleted_count} feedback records")
        
        # Verify deletion
        cursor.execute("SELECT COUNT(*) FROM feedback")
        remaining = cursor.fetchone()[0]
        print(f"✅ Remaining feedback records: {remaining}")
        
        # Close connection
        conn.close()
        
        if remaining == 0:
            print("🎯 Database cleaned successfully!")
            return True
        else:
            print(f"⚠️ Warning: {remaining} records still remain")
            return False
            
    except Exception as e:
        print(f"❌ Error clearing feedback: {e}")
        return False

if __name__ == "__main__":
    print("🧹 Clearing all feedback data...")
    
    success = clear_feedback_table()
    
    if success:
        print("\n🎉 FEEDBACK DATA CLEARED!")
        print("="*40)
        print("✅ All feedback records have been deleted")
        print("✅ Database is now clean")
        print("✅ Ready for fresh testing")
        print("\n🔧 Next steps:")
        print("1. Open: http://localhost:3000/dashboard")
        print("2. Ask a question")
        print("3. Test rating functionality")
        print("4. Check admin panel: http://localhost:3000/admin")
    else:
        print("\n❌ Failed to clear feedback data")
        print("Please check the error messages above")
