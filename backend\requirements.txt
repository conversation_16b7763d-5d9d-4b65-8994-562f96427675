fastapi==0.115.6
uvicorn==0.32.1
pydantic==2.10.3
sqlalchemy==2.0.36
psycopg2-binary==2.9.10
python-dotenv==1.0.1
python-multipart==0.0.17
pandas==2.2.3
openai==1.12.0
plotly==5.24.1
kaleido==0.2.1
stripe==11.2.0
httpx==0.26.0
pytest==8.3.4
alembic==1.14.0
passlib[bcrypt]==1.7.4
scipy==1.14.1
statsmodels==0.14.4
scikit-learn==1.5.2
pmdarima==2.0.4
openpyxl==3.1.5

# Document processing libraries
PyMuPDF==1.23.26
python-docx==1.1.2
python-magic==0.4.27
# Enhanced text processing (lightweight alternatives)
nltk==3.8.1
rank-bm25==0.2.2
# OCR libraries for image processing
pytesseract==0.3.10
opencv-python==********
Pillow==10.1.0

numpy==1.24.3

# Multi-tenant and task queue dependencies
redis==5.0.1
celery==5.3.4
rq==1.15.1

# Embedding providers (optional)
cohere==4.37.0
sentence-transformers==2.2.2
InstructorEmbedding==1.0.1

# Document processing dependencies
PyPDF2==3.0.1
pdfplumber==0.10.3
python-docx2txt==0.8
pytesseract==0.3.10
Pillow==10.1.0

# Vector storage dependencies
faiss-cpu==1.7.4
pinecone-client==2.2.4
weaviate-client==3.25.3

# Text processing dependencies
langchain==0.0.350

# Advanced enterprise dependencies
cryptography==41.0.7
python-magic==0.4.27
yara-python==4.3.1
lz4==4.3.2
psutil==5.9.6

# Monitoring and observability
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0

# Security and compliance
bcrypt==4.1.2
passlib==1.7.4
python-jose[cryptography]==3.3.0

# Performance optimization
uvloop==0.19.0
orjson==3.9.10

# Phase 3 RAG & LLM dependencies
openai==1.3.7
anthropic==0.7.8
google-generativeai==0.3.2
cohere==4.37.0

# Advanced NLP and ML
transformers==4.36.2
torch==2.1.2
numpy==1.24.4

# Phase 4 Analytics & Insights dependencies
pandas==2.1.4
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
scikit-learn==1.3.2
openpyxl==3.1.2

# Revolutionary Analytics dependencies
scipy==1.11.4
