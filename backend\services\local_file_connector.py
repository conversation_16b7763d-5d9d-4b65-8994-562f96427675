"""
Local File System Connector for RAG Document Ingestion
Supports local drives, network paths, and shared folders
"""

import os
import logging
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Generator
from datetime import datetime
import mimetypes
import json
from dataclasses import dataclass, asdict

from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)

@dataclass
class DocumentMetadata:
    """Document metadata structure"""
    file_path: str
    file_name: str
    file_size: int
    file_type: str
    mime_type: str
    created_at: datetime
    modified_at: datetime
    file_hash: str
    relative_path: str
    connector_id: str

@dataclass
class ConnectorConfig:
    """Local file connector configuration"""
    base_path: str
    include_extensions: List[str]
    exclude_extensions: List[str]
    max_file_size_mb: int
    recursive: bool
    follow_symlinks: bool
    exclude_patterns: List[str]

class LocalFileConnector:
    """
    Local File System Connector for RAG document processing
    """
    
    def __init__(self, connector_id: str, config: ConnectorConfig):
        self.connector_id = connector_id
        self.config = config
        self.supported_extensions = {
            '.txt', '.md', '.pdf', '.docx', '.doc', '.rtf',
            '.html', '.htm', '.xml', '.json', '.csv',
            '.py', '.js', '.ts', '.java', '.cpp', '.c',
            '.sql', '.yaml', '.yml', '.ini', '.cfg'
        }
        
    def validate_path(self) -> Dict[str, Any]:
        """Validate the configured path"""
        try:
            path = Path(self.config.base_path)
            
            if not path.exists():
                return {
                    "valid": False,
                    "error": f"Path does not exist: {self.config.base_path}",
                    "error_type": "path_not_found"
                }
            
            if not path.is_dir():
                return {
                    "valid": False,
                    "error": f"Path is not a directory: {self.config.base_path}",
                    "error_type": "not_directory"
                }
            
            # Test read access
            try:
                list(path.iterdir())
            except PermissionError:
                return {
                    "valid": False,
                    "error": f"No read permission for path: {self.config.base_path}",
                    "error_type": "permission_denied"
                }
            
            # Count accessible files
            file_count = self._count_files(path)
            
            return {
                "valid": True,
                "path": str(path.absolute()),
                "accessible_files": file_count,
                "is_network_path": self._is_network_path(path),
                "total_size_mb": self._calculate_total_size(path)
            }
            
        except Exception as e:
            logger.error(f"Path validation error: {e}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}",
                "error_type": "validation_error"
            }
    
    def _is_network_path(self, path: Path) -> bool:
        """Check if path is a network location"""
        return str(path).startswith(('\\\\', '//', 'smb://', 'ftp://'))
    
    def _count_files(self, path: Path) -> int:
        """Count accessible files in the path"""
        count = 0
        try:
            for item in self._scan_directory(path):
                if self._should_include_file(item):
                    count += 1
                if count > 10000:  # Limit for performance
                    break
        except Exception as e:
            logger.warning(f"Error counting files: {e}")
        return count
    
    def _calculate_total_size(self, path: Path) -> float:
        """Calculate total size of accessible files in MB"""
        total_size = 0
        try:
            for item in self._scan_directory(path):
                if self._should_include_file(item):
                    try:
                        total_size += item.stat().st_size
                    except (OSError, PermissionError):
                        continue
                if total_size > 10 * 1024 * 1024 * 1024:  # 10GB limit for calculation
                    break
        except Exception as e:
            logger.warning(f"Error calculating size: {e}")
        
        return total_size / (1024 * 1024)  # Convert to MB
    
    def _scan_directory(self, path: Path) -> Generator[Path, None, None]:
        """Scan directory for files"""
        try:
            if self.config.recursive:
                for item in path.rglob('*'):
                    if item.is_file():
                        yield item
            else:
                for item in path.iterdir():
                    if item.is_file():
                        yield item
        except (PermissionError, OSError) as e:
            logger.warning(f"Cannot access directory {path}: {e}")
    
    def _should_include_file(self, file_path: Path) -> bool:
        """Check if file should be included based on configuration"""
        try:
            # Check file size
            file_size = file_path.stat().st_size
            max_size_bytes = self.config.max_file_size_mb * 1024 * 1024
            if file_size > max_size_bytes:
                return False
            
            # Check extension
            extension = file_path.suffix.lower()
            
            # If include_extensions is specified, file must be in the list
            if self.config.include_extensions:
                if extension not in [ext.lower() for ext in self.config.include_extensions]:
                    return False
            
            # If exclude_extensions is specified, file must not be in the list
            if self.config.exclude_extensions:
                if extension in [ext.lower() for ext in self.config.exclude_extensions]:
                    return False
            
            # Check if extension is supported
            if extension not in self.supported_extensions:
                return False
            
            # Check exclude patterns
            file_name = file_path.name.lower()
            for pattern in self.config.exclude_patterns:
                if pattern.lower() in file_name:
                    return False
            
            return True
            
        except (OSError, PermissionError):
            return False
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of file for change detection"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"Cannot calculate hash for {file_path}: {e}")
            return ""
    
    def scan_documents(self) -> List[DocumentMetadata]:
        """Scan and return all documents with metadata"""
        documents = []
        base_path = Path(self.config.base_path)
        
        try:
            for file_path in self._scan_directory(base_path):
                if not self._should_include_file(file_path):
                    continue
                
                try:
                    stat_info = file_path.stat()
                    mime_type, _ = mimetypes.guess_type(str(file_path))
                    
                    # Calculate relative path
                    try:
                        relative_path = str(file_path.relative_to(base_path))
                    except ValueError:
                        relative_path = str(file_path)
                    
                    document = DocumentMetadata(
                        file_path=str(file_path.absolute()),
                        file_name=file_path.name,
                        file_size=stat_info.st_size,
                        file_type=file_path.suffix.lower(),
                        mime_type=mime_type or 'application/octet-stream',
                        created_at=datetime.fromtimestamp(stat_info.st_ctime),
                        modified_at=datetime.fromtimestamp(stat_info.st_mtime),
                        file_hash=self._calculate_file_hash(file_path),
                        relative_path=relative_path,
                        connector_id=self.connector_id
                    )
                    
                    documents.append(document)
                    
                except (OSError, PermissionError) as e:
                    logger.warning(f"Cannot access file {file_path}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error scanning documents: {e}")
            raise
        
        return documents
    
    def read_document_content(self, file_path: str) -> Optional[str]:
        """Read and return document content"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                logger.error(f"File not found: {file_path}")
                return None
            
            # Handle different file types
            extension = path.suffix.lower()
            
            if extension in ['.txt', '.md', '.py', '.js', '.ts', '.java', '.cpp', '.c', '.sql', '.yaml', '.yml', '.ini', '.cfg', '.html', '.htm', '.xml', '.json', '.csv']:
                # Text files - try different encodings
                for encoding in ['utf-8', 'utf-16', 'latin-1', 'cp1252']:
                    try:
                        with open(path, 'r', encoding=encoding) as f:
                            return f.read()
                    except UnicodeDecodeError:
                        continue
                
                logger.warning(f"Could not decode text file: {file_path}")
                return None
            
            elif extension == '.pdf':
                # PDF files - would need PyPDF2 or similar
                logger.info(f"PDF processing not implemented yet: {file_path}")
                return None
            
            elif extension in ['.docx', '.doc']:
                # Word documents - would need python-docx
                logger.info(f"Word document processing not implemented yet: {file_path}")
                return None
            
            else:
                logger.warning(f"Unsupported file type: {extension}")
                return None
                
        except Exception as e:
            logger.error(f"Error reading document {file_path}: {e}")
            return None
    
    def sync_to_database(self, db: Session, user_id: str) -> Dict[str, Any]:
        """Sync discovered documents to database for RAG processing"""
        try:
            documents = self.scan_documents()
            
            sync_stats = {
                "total_found": len(documents),
                "new_documents": 0,
                "updated_documents": 0,
                "errors": 0,
                "sync_time": datetime.utcnow().isoformat()
            }
            
            for doc in documents:
                try:
                    # Check if document already exists
                    existing = db.execute(text("""
                        SELECT id, file_hash FROM documents 
                        WHERE file_path = :file_path AND user_id = :user_id
                    """), {
                        "file_path": doc.file_path,
                        "user_id": user_id
                    }).fetchone()
                    
                    if existing:
                        # Check if file has changed
                        if existing.file_hash != doc.file_hash:
                            # Update existing document
                            db.execute(text("""
                                UPDATE documents SET
                                    file_hash = :file_hash,
                                    file_size = :file_size,
                                    modified_at = :modified_at,
                                    needs_reprocessing = true,
                                    updated_at = :updated_at
                                WHERE id = :doc_id
                            """), {
                                "file_hash": doc.file_hash,
                                "file_size": doc.file_size,
                                "modified_at": doc.modified_at,
                                "updated_at": datetime.utcnow(),
                                "doc_id": existing.id
                            })
                            sync_stats["updated_documents"] += 1
                    else:
                        # Insert new document
                        db.execute(text("""
                            INSERT INTO documents (
                                user_id, connector_id, file_path, file_name, 
                                file_size, file_type, mime_type, created_at, 
                                modified_at, file_hash, relative_path, 
                                needs_processing, created_at_db
                            ) VALUES (
                                :user_id, :connector_id, :file_path, :file_name,
                                :file_size, :file_type, :mime_type, :created_at,
                                :modified_at, :file_hash, :relative_path,
                                true, :created_at_db
                            )
                        """), {
                            "user_id": user_id,
                            "connector_id": doc.connector_id,
                            "file_path": doc.file_path,
                            "file_name": doc.file_name,
                            "file_size": doc.file_size,
                            "file_type": doc.file_type,
                            "mime_type": doc.mime_type,
                            "created_at": doc.created_at,
                            "modified_at": doc.modified_at,
                            "file_hash": doc.file_hash,
                            "relative_path": doc.relative_path,
                            "created_at_db": datetime.utcnow()
                        })
                        sync_stats["new_documents"] += 1
                
                except Exception as e:
                    logger.error(f"Error syncing document {doc.file_path}: {e}")
                    sync_stats["errors"] += 1
            
            db.commit()
            return sync_stats
            
        except Exception as e:
            logger.error(f"Error in sync_to_database: {e}")
            db.rollback()
            raise

def create_local_connector(connector_id: str, base_path: str, **kwargs) -> LocalFileConnector:
    """Factory function to create a local file connector"""
    config = ConnectorConfig(
        base_path=base_path,
        include_extensions=kwargs.get('include_extensions', []),
        exclude_extensions=kwargs.get('exclude_extensions', ['.tmp', '.log', '.cache']),
        max_file_size_mb=kwargs.get('max_file_size_mb', 100),
        recursive=kwargs.get('recursive', True),
        follow_symlinks=kwargs.get('follow_symlinks', False),
        exclude_patterns=kwargs.get('exclude_patterns', ['__pycache__', '.git', 'node_modules'])
    )
    
    return LocalFileConnector(connector_id, config)
