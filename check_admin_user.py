#!/usr/bin/env python3
"""
Check if admin user exists and create if needed
"""

import requests
import sys

BASE_URL = "http://localhost:8001"
ADMIN_USER_ID = "<EMAIL>"

def check_user_exists():
    """Check if user exists in database"""
    print(f"Checking if user {ADMIN_USER_ID} exists...")
    
    try:
        response = requests.get(f"{BASE_URL}/debug/users", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            users = data.get('users', [])
            
            print(f"Found {len(users)} users in database:")
            for user in users:
                print(f"  - {user['email']} (role: {user['role']})")
                
            # Check if our admin user exists
            admin_user = next((u for u in users if u['email'] == ADMIN_USER_ID), None)
            if admin_user:
                print(f"\n✅ Admin user found: {admin_user['email']} (role: {admin_user['role']})")
                return admin_user['role'] == 'admin'
            else:
                print(f"\n❌ Admin user {ADMIN_USER_ID} not found")
                return False
        else:
            print(f"❌ Failed to get users: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking users: {e}")
        return False

def create_admin_user():
    """Create admin user"""
    print(f"Creating admin user {ADMIN_USER_ID}...")
    
    try:
        response = requests.post(f"{BASE_URL}/make-admin/{ADMIN_USER_ID}", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {data['message']}")
            return True
        else:
            print(f"❌ Failed to create admin user: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return False

def test_admin_access():
    """Test admin access with debug endpoint"""
    print(f"Testing admin access...")
    
    try:
        response = requests.get(
            f"{BASE_URL}/admin/debug-test",
            params={"user_id": ADMIN_USER_ID},
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Admin access working: {data['message']}")
            return True
        else:
            print(f"❌ Admin access failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing admin access: {e}")
        return False

def main():
    print("=== Admin User Setup ===\n")
    
    # Check if backend is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Backend is not running properly")
            sys.exit(1)
        print("✅ Backend is running\n")
    except:
        print("❌ Backend is not running")
        sys.exit(1)
    
    # Check if user exists
    if not check_user_exists():
        print("\nCreating admin user...")
        if not create_admin_user():
            print("❌ Failed to create admin user")
            sys.exit(1)
        print()
    
    # Test admin access
    if test_admin_access():
        print("\n✅ Admin setup complete! You can now test the admin endpoints.")
    else:
        print("\n❌ Admin access test failed")

if __name__ == "__main__":
    main()
