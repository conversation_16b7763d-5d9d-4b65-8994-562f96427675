"""
Complete RAG Pipeline for Phase 3
Implements query → retrieve → augment → generate pipeline as required by development plan
"""

import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

@dataclass
class RAGContext:
    """Context for RAG processing"""
    query: str
    tenant_id: str
    user_id: str
    dataset_id: Optional[str] = None
    top_k: int = 5
    include_citations: bool = True
    llm_provider: str = "openai"
    embedding_provider: str = "openai"
    vector_store: str = "faiss"

@dataclass
class RetrievalResult:
    """Result from vector retrieval"""
    content: str
    metadata: Dict[str, Any]
    score: float
    source_id: str
    chunk_id: str

@dataclass
class RAGResponse:
    """Complete RAG response"""
    answer: str
    sources: List[RetrievalResult]
    citations: List[Dict[str, Any]]
    processing_time_ms: int
    confidence_score: float
    trust_score: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None

class CompleteRAGPipeline:
    """Complete RAG pipeline implementing all Phase 3 requirements"""
    
    def __init__(self):
        self.embedding_service = None
        self.vector_service = None
        self.llm_service = None
        self.prompt_service = None
        self.citation_service = None
        
        # Initialize services
        self._initialize_services()
    
    def _initialize_services(self):
        """Initialize all required services"""
        try:
            # Import services
            from services.embedding_service import create_embedding_service
            from services.vector_storage_complete import complete_vector_storage_service
            from services.llm_integration_complete import complete_llm_service
            from services.prompt_engineering_complete import complete_prompt_service
            from services.citation_extraction_complete import complete_citation_service
            
            # Set default services (will be configured per request)
            self.vector_service = complete_vector_storage_service
            self.llm_service = complete_llm_service
            self.prompt_service = complete_prompt_service
            self.citation_service = complete_citation_service
            
            logger.info("RAG pipeline services initialized")
            
        except ImportError as e:
            logger.error(f"Failed to initialize RAG services: {e}")
            # Services will be initialized on demand
    
    async def process_query(self, context: RAGContext) -> RAGResponse:
        """
        Complete RAG pipeline: Query → Retrieve → Augment → Generate
        """
        start_time = time.time()
        
        try:
            # Step 1: Query preprocessing and optimization
            processed_query = await self._preprocess_query(context)
            
            # Step 2: Retrieve relevant chunks
            retrieval_results = await self._retrieve_chunks(processed_query, context)
            
            # Step 3: Augment with context
            augmented_context = await self._augment_context(processed_query, retrieval_results, context)
            
            # Step 4: Generate response
            response = await self._generate_response(augmented_context, context)
            
            # Step 5: Extract citations
            citations = await self._extract_citations(response, retrieval_results)
            
            # Step 6: Calculate confidence
            confidence = await self._calculate_confidence(response, retrieval_results, context)
            
            processing_time = int((time.time() - start_time) * 1000)
            
            return RAGResponse(
                answer=response,
                sources=retrieval_results,
                citations=citations,
                processing_time_ms=processing_time,
                confidence_score=confidence,
                metadata={
                    "query": processed_query,
                    "retrieval_count": len(retrieval_results),
                    "llm_provider": context.llm_provider,
                    "embedding_provider": context.embedding_provider
                }
            )
            
        except Exception as e:
            logger.error(f"RAG pipeline failed: {e}")
            processing_time = int((time.time() - start_time) * 1000)
            
            return RAGResponse(
                answer=f"I apologize, but I encountered an error processing your query: {str(e)}",
                sources=[],
                citations=[],
                processing_time_ms=processing_time,
                confidence_score=0.0,
                metadata={"error": str(e)}
            )
    
    async def _preprocess_query(self, context: RAGContext) -> str:
        """Preprocess and optimize query"""
        
        query = context.query.strip()
        
        # Basic query cleaning
        if not query:
            raise ValueError("Query cannot be empty")
        
        # Query expansion (simple implementation)
        if len(query.split()) < 3:
            # Add context for very short queries
            query = f"Please provide information about: {query}"
        
        # Query optimization
        optimized_query = await self._optimize_query(query, context)
        
        logger.debug(f"Query preprocessed: '{context.query}' -> '{optimized_query}'")
        return optimized_query
    
    async def _optimize_query(self, query: str, context: RAGContext) -> str:
        """Optimize query for better retrieval"""
        
        # For now, return the query as-is
        # In production, this could include:
        # - Query expansion with synonyms
        # - Intent detection
        # - Entity extraction
        # - Query rewriting
        
        return query
    
    async def _retrieve_chunks(self, query: str, context: RAGContext) -> List[RetrievalResult]:
        """Retrieve relevant chunks from vector store"""
        
        try:
            # Initialize embedding service for this request
            if not self.embedding_service:
                from services.embedding_service import create_embedding_service
                self.embedding_service = create_embedding_service(context.embedding_provider)
            
            # Generate query embedding
            query_embedding = self.embedding_service.generate_embedding(query)
            
            # Get vector store provider
            vector_provider = self.vector_service.get_provider(
                context.vector_store,
                dimension=self.embedding_service.get_dimension()
            )
            
            # Search for similar vectors
            search_results = await vector_provider.search_vectors(
                tenant_id=context.tenant_id,
                query_vector=query_embedding,
                top_k=context.top_k,
                filter_metadata={"dataset_id": context.dataset_id} if context.dataset_id else None
            )
            
            # Convert to RetrievalResult objects
            retrieval_results = []
            for result in search_results:
                retrieval_results.append(RetrievalResult(
                    content=result['metadata'].get('content', ''),
                    metadata=result['metadata'],
                    score=result['score'],
                    source_id=result['metadata'].get('source_id', result['id']),
                    chunk_id=result['id']
                ))
            
            logger.info(f"Retrieved {len(retrieval_results)} chunks for query")
            return retrieval_results
            
        except Exception as e:
            logger.error(f"Chunk retrieval failed: {e}")
            return []
    
    async def _augment_context(
        self, 
        query: str, 
        retrieval_results: List[RetrievalResult], 
        context: RAGContext
    ) -> Dict[str, Any]:
        """Augment query with retrieved context"""
        
        # Combine retrieved content
        retrieved_content = []
        for i, result in enumerate(retrieval_results):
            retrieved_content.append({
                "index": i + 1,
                "content": result.content,
                "source": result.metadata.get('source', 'Unknown'),
                "score": result.score
            })
        
        # Create augmented context
        augmented_context = {
            "query": query,
            "retrieved_content": retrieved_content,
            "context_summary": self._create_context_summary(retrieval_results),
            "source_count": len(retrieval_results),
            "tenant_id": context.tenant_id,
            "user_id": context.user_id
        }
        
        return augmented_context
    
    def _create_context_summary(self, retrieval_results: List[RetrievalResult]) -> str:
        """Create a summary of retrieved context"""
        
        if not retrieval_results:
            return "No relevant context found."
        
        # Simple summary - combine top results
        summary_parts = []
        for i, result in enumerate(retrieval_results[:3]):  # Top 3 results
            source = result.metadata.get('source', 'Unknown')
            summary_parts.append(f"Source {i+1} ({source}): {result.content[:200]}...")
        
        return "\n\n".join(summary_parts)
    
    async def _generate_response(self, augmented_context: Dict[str, Any], context: RAGContext) -> str:
        """Generate response using LLM"""
        
        try:
            # Create dynamic prompt
            prompt = await self.prompt_service.create_rag_prompt(
                query=augmented_context["query"],
                context=augmented_context["retrieved_content"],
                include_citations=context.include_citations
            )
            
            # Generate response using LLM
            response = await self.llm_service.generate_response(
                prompt=prompt,
                provider=context.llm_provider,
                tenant_id=context.tenant_id
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            return f"I apologize, but I couldn't generate a response due to an error: {str(e)}"
    
    async def _extract_citations(
        self, 
        response: str, 
        retrieval_results: List[RetrievalResult]
    ) -> List[Dict[str, Any]]:
        """Extract and validate citations from response"""
        
        try:
            citations = await self.citation_service.extract_citations(
                response=response,
                sources=retrieval_results
            )
            
            return citations
            
        except Exception as e:
            logger.error(f"Citation extraction failed: {e}")
            # Return basic citations based on sources
            return [
                {
                    "source_id": result.source_id,
                    "content": result.content[:100] + "...",
                    "score": result.score,
                    "metadata": result.metadata
                }
                for result in retrieval_results[:3]  # Top 3 sources
            ]
    
    async def _calculate_confidence(
        self, 
        response: str, 
        retrieval_results: List[RetrievalResult], 
        context: RAGContext
    ) -> float:
        """Calculate confidence score for the response"""
        
        try:
            # Basic confidence calculation
            confidence = 0.5  # Base confidence
            
            # Boost confidence based on retrieval quality
            if retrieval_results:
                avg_score = sum(r.score for r in retrieval_results) / len(retrieval_results)
                confidence += avg_score * 0.3
            
            # Boost confidence based on response length (simple heuristic)
            if len(response) > 50:
                confidence += 0.1
            
            # Boost confidence if multiple sources
            if len(retrieval_results) >= 3:
                confidence += 0.1
            
            return min(1.0, confidence)
            
        except Exception as e:
            logger.error(f"Confidence calculation failed: {e}")
            return 0.5

class RAGPipelineManager:
    """Manager for RAG pipeline instances"""
    
    def __init__(self):
        self.pipeline = CompleteRAGPipeline()
    
    async def process_query(
        self,
        query: str,
        tenant_id: str,
        user_id: str,
        dataset_id: Optional[str] = None,
        top_k: int = 5,
        include_citations: bool = True,
        llm_provider: str = "openai",
        embedding_provider: str = "openai",
        vector_store: str = "faiss"
    ) -> RAGResponse:
        """Process a query through the complete RAG pipeline"""
        
        context = RAGContext(
            query=query,
            tenant_id=tenant_id,
            user_id=user_id,
            dataset_id=dataset_id,
            top_k=top_k,
            include_citations=include_citations,
            llm_provider=llm_provider,
            embedding_provider=embedding_provider,
            vector_store=vector_store
        )
        
        return await self.pipeline.process_query(context)
    
    async def process_batch_queries(
        self,
        queries: List[str],
        tenant_id: str,
        user_id: str,
        **kwargs
    ) -> List[RAGResponse]:
        """Process multiple queries in batch"""
        
        tasks = []
        for query in queries:
            task = self.process_query(
                query=query,
                tenant_id=tenant_id,
                user_id=user_id,
                **kwargs
            )
            tasks.append(task)
        
        return await asyncio.gather(*tasks)

# Global RAG pipeline manager
complete_rag_pipeline = RAGPipelineManager()
