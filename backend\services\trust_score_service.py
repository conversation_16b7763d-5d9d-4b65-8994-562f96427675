import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple
import re
import hashlib
import logging

# Disable non-deterministic sentence transformer for consistency
# We'll use deterministic text-based similarity instead
model = None

class TrustScoreEvaluator:
    def __init__(self, llm_service=None, df=None):
        self.llm_service = llm_service
        self.df = df

    def evaluate_response(self, question: str, answer: str, chart_type: str = None, chart_data: Dict = None) -> Dict[str, Any]:
        """
        Evaluate the trustworthiness of an LLM response

        Args:
            question: The original question asked
            answer: The LLM's answer
            chart_type: The type of chart generated (if any)
            chart_data: The data used for the chart (if any)

        Returns:
            Dict containing trust score and component scores
        """
        # Calculate individual scores
        coherence_score = self.evaluate_coherence(answer)
        factual_score = self.evaluate_factual_accuracy(answer, question)
        reasoning_score = self.evaluate_reasoning_quality(answer)
        data_alignment = self.evaluate_data_alignment(answer, chart_data)

        # Weighted combination with a baseline boost
        trust_score = (
            0.25 * coherence_score +
            0.30 * factual_score +
            0.25 * reasoning_score +
            0.20 * data_alignment
        )

        # Apply a small boost to ensure more reasonable scores
        trust_score = min(1.0, trust_score + 0.1)

        # Generate explanation for the score
        explanation = self.generate_score_explanation(
            coherence_score, factual_score, reasoning_score, data_alignment
        )

        return {
            "trust_score": float(trust_score),
            "explanation": explanation,
            "component_scores": {
                "coherence": float(coherence_score),
                "factual_accuracy": float(factual_score),
                "reasoning_quality": float(reasoning_score),
                "data_alignment": float(data_alignment)
            }
        }

    def evaluate_coherence(self, answer: str) -> float:
        """
        Evaluate the coherence of the answer

        Args:
            answer: The LLM's answer

        Returns:
            Coherence score between 0 and 1
        """
        # Split answer into sentences
        sentences = self._split_into_sentences(answer)

        if len(sentences) <= 1:
            return 0.9  # Default score for very short answers

        # Calculate semantic similarity between adjacent sentences
        coherence_scores = []
        for i in range(len(sentences) - 1):
            similarity = self._calculate_semantic_similarity(sentences[i], sentences[i+1])
            coherence_scores.append(similarity)

        # Check for logical contradictions
        contradiction_penalty = self._detect_contradictions(sentences)

        # Average coherence score with contradiction penalty
        avg_coherence = sum(coherence_scores) / len(coherence_scores) if coherence_scores else 0.5
        return avg_coherence * (1 - contradiction_penalty)

    def evaluate_factual_accuracy(self, answer: str, question: str) -> float:
        """
        Evaluate the factual accuracy of the answer

        Args:
            answer: The LLM's answer
            question: The original question

        Returns:
            Factual accuracy score between 0 and 1
        """
        # Extract numerical claims from the answer
        numerical_claims = self._extract_numerical_claims(answer)

        if not numerical_claims or self.df is None:
            return 0.85  # Default score when we can't verify numerical claims

        # Verify numerical claims against the dataset
        verification_scores = []
        for claim in numerical_claims:
            verification_score = self._verify_numerical_claim(claim)
            verification_scores.append(verification_score)

        # Average verification score
        return sum(verification_scores) / len(verification_scores) if verification_scores else 0.7

    def evaluate_reasoning_quality(self, answer: str) -> float:
        """
        Evaluate the quality of reasoning in the answer

        Args:
            answer: The LLM's answer

        Returns:
            Reasoning quality score between 0 and 1
        """
        # Check for reasoning indicators
        reasoning_indicators = [
            "because", "therefore", "thus", "since", "as a result",
            "this suggests", "this indicates", "this means", "based on",
            "analysis shows", "data indicates", "we can see", "this implies"
        ]

        # Count reasoning indicators
        indicator_count = sum(1 for indicator in reasoning_indicators if indicator.lower() in answer.lower())

        # Check for step-by-step explanation
        has_steps = bool(re.search(r'(first|1st|step 1|to begin).*?(second|2nd|step 2|next).*?(third|3rd|step 3|finally)',
                                  answer, re.IGNORECASE | re.DOTALL))

        # Check for uncertainty acknowledgment
        acknowledges_uncertainty = bool(re.search(r'(might|may|could|possibly|approximately|around|about|estimate|uncertain)',
                                               answer, re.IGNORECASE))

        # Calculate reasoning score with a higher baseline
        reasoning_score = min(1.0, (
            0.7 +  # Start with a higher baseline
            min(0.2, indicator_count * 0.05) +  # Max 0.2 for indicators
            (0.05 if has_steps else 0) +  # 0.05 for step-by-step
            (0.05 if acknowledges_uncertainty else 0)  # 0.05 for uncertainty
        ))

        return reasoning_score

    def evaluate_data_alignment(self, answer: str, chart_data: Dict = None) -> float:
        """
        Evaluate how well the answer aligns with the data

        Args:
            answer: The LLM's answer
            chart_data: The data used for the chart (if any)

        Returns:
            Data alignment score between 0 and 1
        """
        if chart_data is None or self.df is None:
            return 0.85  # Default score when we can't verify against chart data

        # Extract column names mentioned in the answer
        df_columns = set(self.df.columns)
        mentioned_columns = set()

        for col in df_columns:
            if col.lower() in answer.lower():
                mentioned_columns.add(col)

        # Check if chart data columns are mentioned in the answer
        chart_columns = set()
        if chart_data and isinstance(chart_data, dict):
            # Handle 'x' column
            if 'x' in chart_data:
                if isinstance(chart_data['x'], list):
                    # Handle list of column names
                    for col in chart_data['x']:
                        if isinstance(col, str) and col in df_columns:
                            chart_columns.add(col)
                elif chart_data['x'] in df_columns:
                    chart_columns.add(chart_data['x'])

            # Handle 'y' column
            if 'y' in chart_data:
                if isinstance(chart_data['y'], list):
                    # Check if it's a list of column names or data points
                    if chart_data['y'] and isinstance(chart_data['y'][0], list):
                        # This is a list of data points, not column names
                        pass
                    else:
                        # This is a list of column names
                        for col in chart_data['y']:
                            if isinstance(col, str) and col in df_columns:
                                chart_columns.add(col)
                elif chart_data['y'] in df_columns:
                    chart_columns.add(chart_data['y'])

            # Handle 'group' column
            if 'group' in chart_data:
                if isinstance(chart_data['group'], list):
                    # Handle list of column names
                    for col in chart_data['group']:
                        if isinstance(col, str) and col in df_columns:
                            chart_columns.add(col)
                elif chart_data['group'] in df_columns:
                    chart_columns.add(chart_data['group'])

        # Calculate alignment score
        if not chart_columns:
            column_alignment = len(mentioned_columns) / len(df_columns) if df_columns else 0.5
        else:
            column_alignment = len(mentioned_columns.intersection(chart_columns)) / len(chart_columns) if chart_columns else 0.5

        # Check if answer mentions data ranges or distributions
        mentions_ranges = bool(re.search(r'(range|between|from|minimum|maximum|min|max|highest|lowest)', answer, re.IGNORECASE))
        mentions_distribution = bool(re.search(r'(average|mean|median|mode|distribution|variance|standard deviation|percentile)',
                                            answer, re.IGNORECASE))

        # Calculate data alignment score with higher defaults
        data_alignment = (
            0.6 * column_alignment +
            0.2 * (1.0 if mentions_ranges else 0.7) +  # Higher default (0.7 instead of 0.5)
            0.2 * (1.0 if mentions_distribution else 0.7)  # Higher default (0.7 instead of 0.5)
        )

        return data_alignment

    def generate_score_explanation(self, coherence: float, factual: float, reasoning: float, alignment: float) -> str:
        """
        Generate a human-readable explanation for the trust score

        Args:
            coherence: Coherence score
            factual: Factual accuracy score
            reasoning: Reasoning quality score
            alignment: Data alignment score

        Returns:
            Explanation string
        """
        # Overall assessment
        overall = (coherence + factual + reasoning + alignment) / 4

        if overall >= 0.75:  # Lower threshold from 0.8 to 0.75
            overall_assessment = "This response appears highly trustworthy."
        elif overall >= 0.5:  # Lower threshold from 0.6 to 0.5
            overall_assessment = "This response appears moderately trustworthy."
        else:
            overall_assessment = "This response may have reliability issues."

        # Component assessments with lower thresholds
        component_explanations = []

        if coherence < 0.5:  # Lower threshold from 0.6 to 0.5
            component_explanations.append("The response lacks coherence.")

        if factual < 0.5:  # Lower threshold from 0.6 to 0.5
            component_explanations.append("Some factual claims may be inaccurate.")

        if reasoning < 0.5:  # Lower threshold from 0.6 to 0.5
            component_explanations.append("The reasoning process could be more thorough.")

        if alignment < 0.5:  # Lower threshold from 0.6 to 0.5
            component_explanations.append("The answer may not fully align with the dataset.")

        # Combine explanations
        if component_explanations:
            explanation = f"{overall_assessment} {' '.join(component_explanations)}"
        else:
            explanation = f"{overall_assessment} The response demonstrates good coherence, factual accuracy, reasoning, and alignment with the data."

        return explanation

    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        # Simple sentence splitting
        sentences = re.split(r'(?<=[.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]

    def _calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate deterministic text similarity between two texts"""
        try:
            # Use deterministic text-based similarity instead of embeddings
            return self._calculate_deterministic_text_similarity(text1, text2)
        except Exception as e:
            logging.warning(f"Error calculating text similarity: {str(e)}")
            return 0.85

    def _calculate_deterministic_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate deterministic similarity based on text overlap"""
        # Normalize texts
        text1_clean = re.sub(r'[^\w\s]', '', text1.lower()).strip()
        text2_clean = re.sub(r'[^\w\s]', '', text2.lower()).strip()

        # Split into words
        words1 = set(text1_clean.split())
        words2 = set(text2_clean.split())

        if not words1 or not words2:
            return 0.5

        # Calculate Jaccard similarity (deterministic)
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        if union == 0:
            return 0.5

        jaccard_similarity = intersection / union

        # Scale to a reasonable range (0.3 to 1.0)
        scaled_similarity = 0.3 + (jaccard_similarity * 0.7)

        return min(1.0, scaled_similarity)

    def _detect_contradictions(self, sentences: List[str]) -> float:
        """Detect logical contradictions in sentences"""
        # Simple contradiction detection based on negation patterns
        contradiction_indicators = [
            (r'is', r'is not'),
            (r'are', r'are not'),
            (r'will', r'will not'),
            (r'can', r'cannot'),
            (r'has', r'has not'),
            (r'have', r'have not'),
            (r'increase', r'decrease'),
            (r'higher', r'lower'),
            (r'more', r'less'),
            (r'positive', r'negative')
        ]

        contradiction_count = 0
        for i in range(len(sentences)):
            for j in range(i+1, len(sentences)):
                for pos, neg in contradiction_indicators:
                    if (re.search(r'\b' + pos + r'\b', sentences[i], re.IGNORECASE) and
                        re.search(r'\b' + neg + r'\b', sentences[j], re.IGNORECASE)):
                        contradiction_count += 1
                    if (re.search(r'\b' + neg + r'\b', sentences[i], re.IGNORECASE) and
                        re.search(r'\b' + pos + r'\b', sentences[j], re.IGNORECASE)):
                        contradiction_count += 1

        # Calculate contradiction penalty (0 to 0.5)
        return min(0.5, contradiction_count * 0.1)

    def _extract_numerical_claims(self, text: str) -> List[Tuple[str, float]]:
        """Extract numerical claims from text"""
        # Pattern to match numbers with optional units and context
        pattern = r'(\d+(?:\.\d+)?(?:\s*%|\s*percent|\s*dollars|\s*\$)?)'

        # Find all matches
        matches = re.finditer(pattern, text)

        # Extract matches with surrounding context
        claims = []
        for match in matches:
            start = max(0, match.start() - 50)
            end = min(len(text), match.end() + 50)
            context = text[start:end]
            value_str = match.group(1)

            # Convert to numeric value
            value = float(re.sub(r'[^0-9.]', '', value_str))

            claims.append((context, value))

        return claims

    def _verify_numerical_claim(self, claim: Tuple[str, float]) -> float:
        """Verify a numerical claim against the dataset"""
        context, value = claim

        if self.df is None:
            return 0.7

        # Try to identify which column the claim is about
        max_similarity = 0
        most_similar_col = None

        for col in self.df.columns:
            if col.lower() in context.lower():
                similarity = 1.0
                most_similar_col = col
                break

            similarity = self._calculate_semantic_similarity(col, context)
            if similarity > max_similarity:
                max_similarity = similarity
                most_similar_col = col

        if most_similar_col is None or max_similarity < 0.3:
            return 0.85  # Can't confidently identify which column

        # Check if the column is numeric
        if not pd.api.types.is_numeric_dtype(self.df[most_similar_col]):
            return 0.85  # Non-numeric column

        # Calculate statistics for the column
        try:
            col_min = self.df[most_similar_col].min()
            col_max = self.df[most_similar_col].max()
            col_mean = self.df[most_similar_col].mean()
            col_median = self.df[most_similar_col].median()

            # Check if the claimed value is within a reasonable range
            if col_min <= value <= col_max:
                # Exact match with a statistic
                if abs(value - col_mean) < 0.01 or abs(value - col_median) < 0.01:
                    return 1.0

                # Within range but not a key statistic
                normalized_distance = min(
                    abs(value - col_mean) / (col_max - col_min) if col_max > col_min else 1,
                    abs(value - col_median) / (col_max - col_min) if col_max > col_min else 1
                )

                return 1.0 - min(0.5, normalized_distance)
            else:
                # Outside the range but not too far
                # Calculate how far outside the range it is
                if value < col_min:
                    outside_factor = min(1.0, (col_min - value) / (col_max - col_min) if col_max > col_min else 1)
                else:  # value > col_max
                    outside_factor = min(1.0, (value - col_max) / (col_max - col_min) if col_max > col_min else 1)

                # Less severe penalty for being outside range
                return max(0.6, 0.8 - (outside_factor * 0.2))
        except Exception as e:
            logging.warning(f"Error verifying numerical claim: {str(e)}")
            return 0.85


def generate_chain_of_thought(question: str, df: pd.DataFrame, llm_service) -> Dict[str, Any]:
    """
    Generate a chain of thought reasoning process for answering a question

    Args:
        question: The question to answer
        df: The dataset
        llm_service: The LLM service to use

    Returns:
        Dict containing the reasoning steps and final answer
    """
    # Create a prompt that encourages step-by-step reasoning
    cot_prompt = f"""
    You are analyzing data from a dataset. Please think through this step by step:

    1. First, understand what information is being requested: {question}
    2. Identify the relevant columns and data points needed
    3. Perform the necessary calculations or analysis
    4. Explain your reasoning for each step
    5. Provide your final answer

    Dataset information:
    - Columns: {df.columns.tolist()}
    - Data types: {dict(df.dtypes.astype(str))}
    - Sample data (first 3 rows): {df.head(3).to_dict()}

    Format your response as follows:
    STEP 1: [Your reasoning for step 1]
    STEP 2: [Your reasoning for step 2]
    ...
    FINAL ANSWER: [Your final answer]
    """

    # Get response from LLM
    response = llm_service.process_query_with_prompt(cot_prompt, df)

    # Extract reasoning steps and final answer
    steps = []
    final_answer = ""

    # Parse the response to extract steps and final answer
    step_pattern = r'STEP\s+\d+:\s+(.*?)(?=STEP\s+\d+:|FINAL ANSWER:|$)'
    final_answer_pattern = r'FINAL ANSWER:\s+(.*?)$'

    step_matches = re.finditer(step_pattern, response, re.DOTALL)
    for match in step_matches:
        steps.append(match.group(1).strip())

    final_match = re.search(final_answer_pattern, response, re.DOTALL)
    if final_match:
        final_answer = final_match.group(1).strip()
    else:
        final_answer = response  # Fallback if pattern not found

    return {
        "reasoning_steps": steps,
        "final_answer": final_answer,
        "full_response": response
    }
