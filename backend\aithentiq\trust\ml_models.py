"""
Advanced ML Models for Trust Score Components
"""

import numpy as np
import pickle
import logging
from typing import Dict, List, Any, Optional, Tuple
from sklearn.ensemble import RandomForestRegressor, GradientBoostingClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
import os
from datetime import datetime

from ..config import trust_config


class QATRegressor:
    """Question-Answer-Topic Relevance Regressor"""
    
    def __init__(self):
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.is_trained = False
        self.logger = logging.getLogger(__name__)
        
        # Try to load pre-trained model
        self._load_model()
    
    def _extract_features(self, query: str, answer: str) -> np.ndarray:
        """Extract features for QAT scoring"""
        features = []
        
        # Text similarity features
        combined_text = f"{query} {answer}"
        query_words = set(query.lower().split())
        answer_words = set(answer.lower().split())
        
        # Word overlap ratio
        overlap = len(query_words.intersection(answer_words))
        total_unique = len(query_words.union(answer_words))
        overlap_ratio = overlap / max(1, total_unique)
        features.append(overlap_ratio)
        
        # Length features
        features.append(len(query.split()) / 100)  # Normalized query length
        features.append(len(answer.split()) / 500)  # Normalized answer length
        features.append(len(answer) / len(query) if len(query) > 0 else 0)  # Length ratio
        
        # Linguistic features
        features.append(len(re.findall(r'\b\d+\.?\d*\b', answer)) / 10)  # Number density
        features.append(len(re.findall(r'[.!?]', answer)) / 20)  # Sentence density
        
        # Question type features
        question_words = ['what', 'how', 'why', 'when', 'where', 'who', 'which']
        has_question_word = any(word in query.lower() for word in question_words)
        features.append(float(has_question_word))
        
        # Answer quality indicators
        certainty_words = ['exactly', 'precisely', 'specifically', 'clearly', 'definitely']
        uncertainty_words = ['might', 'maybe', 'possibly', 'perhaps', 'probably']
        
        certainty_count = sum(1 for word in certainty_words if word in answer.lower())
        uncertainty_count = sum(1 for word in uncertainty_words if word in answer.lower())
        
        features.append(certainty_count / 10)
        features.append(uncertainty_count / 10)
        
        # Semantic similarity (simplified)
        try:
            if hasattr(self, 'vectorizer') and self.vectorizer:
                query_vec = self.vectorizer.transform([query])
                answer_vec = self.vectorizer.transform([answer])
                similarity = cosine_similarity(query_vec, answer_vec)[0][0]
                features.append(similarity)
            else:
                features.append(0.5)  # Default similarity
        except:
            features.append(0.5)
        
        return np.array(features).reshape(1, -1)
    
    def predict(self, query: str, answer: str) -> float:
        """Predict QAT relevance score"""
        try:
            if not self.is_trained:
                # Use heuristic scoring if model not trained
                return self._heuristic_score(query, answer)
            
            features = self._extract_features(query, answer)
            score = self.model.predict(features)[0]
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            self.logger.warning(f"QAT prediction failed: {e}")
            return self._heuristic_score(query, answer)
    
    def _heuristic_score(self, query: str, answer: str) -> float:
        """Fallback heuristic scoring"""
        query_words = set(query.lower().split())
        answer_words = set(answer.lower().split())
        
        # Word overlap
        overlap = len(query_words.intersection(answer_words))
        total_unique = len(query_words.union(answer_words))
        overlap_score = overlap / max(1, total_unique)
        
        # Answer completeness
        completeness_score = min(1.0, len(answer) / 200)
        
        # Combine scores
        return 0.6 * overlap_score + 0.4 * completeness_score
    
    def train(self, training_data: List[Dict[str, Any]]) -> None:
        """Train the QAT regressor"""
        try:
            if len(training_data) < 10:
                self.logger.warning("Insufficient training data for QAT regressor")
                return
            
            # Prepare training data
            X = []
            y = []
            texts = []
            
            for item in training_data:
                query = item['query']
                answer = item['answer']
                score = item['relevance_score']  # Ground truth relevance
                
                texts.append(f"{query} {answer}")
                y.append(score)
            
            # Fit vectorizer
            self.vectorizer.fit(texts)
            
            # Extract features
            for item in training_data:
                features = self._extract_features(item['query'], item['answer'])
                X.append(features[0])
            
            X = np.array(X)
            y = np.array(y)
            
            # Train model
            self.model.fit(X, y)
            self.is_trained = True
            
            # Save model
            self._save_model()
            
            self.logger.info(f"QAT regressor trained on {len(training_data)} samples")
            
        except Exception as e:
            self.logger.error(f"QAT training failed: {e}")
    
    def _save_model(self) -> None:
        """Save trained model"""
        try:
            model_dir = "models"
            os.makedirs(model_dir, exist_ok=True)
            
            model_data = {
                'model': self.model,
                'vectorizer': self.vectorizer,
                'is_trained': self.is_trained,
                'trained_at': datetime.utcnow().isoformat()
            }
            
            with open(os.path.join(model_dir, 'qat_regressor.pkl'), 'wb') as f:
                pickle.dump(model_data, f)
                
        except Exception as e:
            self.logger.error(f"Failed to save QAT model: {e}")
    
    def _load_model(self) -> None:
        """Load pre-trained model"""
        try:
            model_path = os.path.join("models", "qat_regressor.pkl")
            if os.path.exists(model_path):
                with open(model_path, 'rb') as f:
                    model_data = pickle.load(f)
                
                self.model = model_data['model']
                self.vectorizer = model_data['vectorizer']
                self.is_trained = model_data['is_trained']
                
                self.logger.info("QAT regressor loaded successfully")
            
        except Exception as e:
            self.logger.warning(f"Failed to load QAT model: {e}")


class RefusalDetector:
    """Binary classifier to detect when model should have refused to answer"""
    
    def __init__(self):
        self.model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        self.vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
        self.is_trained = False
        self.logger = logging.getLogger(__name__)
        
        # Refusal patterns
        self.refusal_patterns = [
            r"i don't know",
            r"i cannot",
            r"i'm not sure",
            r"insufficient information",
            r"cannot determine",
            r"unable to answer",
            r"not enough data",
            r"unclear from the data",
            r"cannot be determined",
            r"insufficient context"
        ]
        
        # Hallucination indicators
        self.hallucination_patterns = [
            r"according to my knowledge",
            r"based on my training",
            r"i believe",
            r"in my opinion",
            r"generally speaking",
            r"typically",
            r"usually",
            r"from what i understand"
        ]
        
        # Try to load pre-trained model
        self._load_model()
    
    def _extract_features(self, answer: str, context: Dict[str, Any]) -> np.ndarray:
        """Extract features for refusal detection"""
        features = []
        
        answer_lower = answer.lower()
        
        # Refusal pattern features
        refusal_count = sum(1 for pattern in self.refusal_patterns 
                          if re.search(pattern, answer_lower))
        features.append(refusal_count / len(self.refusal_patterns))
        
        # Hallucination pattern features
        hallucination_count = sum(1 for pattern in self.hallucination_patterns 
                                if re.search(pattern, answer_lower))
        features.append(hallucination_count / len(self.hallucination_patterns))
        
        # Uncertainty indicators
        uncertainty_words = ['might', 'maybe', 'possibly', 'perhaps', 'probably', 
                           'seems', 'appears', 'suggests', 'likely']
        uncertainty_count = sum(1 for word in uncertainty_words if word in answer_lower)
        features.append(uncertainty_count / 20)
        
        # Source availability
        sources = context.get('sources', [])
        features.append(len(sources) / 10)  # Normalized source count
        
        # Answer length (very short or very long might indicate issues)
        answer_length = len(answer.split())
        features.append(answer_length / 500)  # Normalized length
        
        # Specificity indicators
        specific_patterns = [r'\b\d+\.?\d*\b', r'\$\d+', r'\d+%', r'\b\d{4}\b']
        specificity_count = sum(len(re.findall(pattern, answer)) for pattern in specific_patterns)
        features.append(specificity_count / 10)
        
        # Question complexity (from context)
        query = context.get('query', '')
        query_complexity = len(query.split()) / 50
        features.append(query_complexity)
        
        return np.array(features).reshape(1, -1)
    
    def predict(self, answer: str, context: Dict[str, Any]) -> float:
        """Predict refusal probability"""
        try:
            if not self.is_trained:
                return self._heuristic_refusal_score(answer, context)
            
            features = self._extract_features(answer, context)
            refusal_prob = self.model.predict_proba(features)[0][1]  # Probability of class 1 (should refuse)
            return max(0.0, min(1.0, refusal_prob))
            
        except Exception as e:
            self.logger.warning(f"Refusal detection failed: {e}")
            return self._heuristic_refusal_score(answer, context)
    
    def _heuristic_refusal_score(self, answer: str, context: Dict[str, Any]) -> float:
        """Fallback heuristic refusal scoring"""
        answer_lower = answer.lower()
        
        # Check for explicit refusal patterns
        refusal_indicators = sum(1 for pattern in self.refusal_patterns 
                               if re.search(pattern, answer_lower))
        
        # Check for hallucination patterns
        hallucination_indicators = sum(1 for pattern in self.hallucination_patterns 
                                     if re.search(pattern, answer_lower))
        
        # Combine indicators
        refusal_score = min(1.0, (refusal_indicators * 0.3) + (hallucination_indicators * 0.2))
        
        return refusal_score
    
    def train(self, training_data: List[Dict[str, Any]]) -> None:
        """Train the refusal detector"""
        try:
            if len(training_data) < 20:
                self.logger.warning("Insufficient training data for refusal detector")
                return
            
            # Prepare training data
            X = []
            y = []
            
            for item in training_data:
                answer = item['answer']
                context = item.get('context', {})
                should_refuse = item['should_refuse']  # Ground truth label
                
                features = self._extract_features(answer, context)
                X.append(features[0])
                y.append(int(should_refuse))
            
            X = np.array(X)
            y = np.array(y)
            
            # Train model
            self.model.fit(X, y)
            self.is_trained = True
            
            # Save model
            self._save_model()
            
            self.logger.info(f"Refusal detector trained on {len(training_data)} samples")
            
        except Exception as e:
            self.logger.error(f"Refusal detector training failed: {e}")
    
    def _save_model(self) -> None:
        """Save trained model"""
        try:
            model_dir = "models"
            os.makedirs(model_dir, exist_ok=True)
            
            model_data = {
                'model': self.model,
                'vectorizer': self.vectorizer,
                'is_trained': self.is_trained,
                'trained_at': datetime.utcnow().isoformat()
            }
            
            with open(os.path.join(model_dir, 'refusal_detector.pkl'), 'wb') as f:
                pickle.dump(model_data, f)
                
        except Exception as e:
            self.logger.error(f"Failed to save refusal detector: {e}")
    
    def _load_model(self) -> None:
        """Load pre-trained model"""
        try:
            model_path = os.path.join("models", "refusal_detector.pkl")
            if os.path.exists(model_path):
                with open(model_path, 'rb') as f:
                    model_data = pickle.load(f)
                
                self.model = model_data['model']
                self.vectorizer = model_data['vectorizer']
                self.is_trained = model_data['is_trained']
                
                self.logger.info("Refusal detector loaded successfully")
            
        except Exception as e:
            self.logger.warning(f"Failed to load refusal detector: {e}")


class ConfidenceCalibrator:
    """Temperature scaling for confidence calibration"""
    
    def __init__(self):
        self.temperature = 1.0
        self.is_calibrated = False
        self.logger = logging.getLogger(__name__)
    
    def calibrate(self, predictions: np.ndarray, labels: np.ndarray) -> None:
        """Calibrate confidence using temperature scaling"""
        try:
            from scipy.optimize import minimize_scalar
            
            def negative_log_likelihood(temp):
                calibrated_probs = self._apply_temperature(predictions, temp)
                # Avoid log(0) by adding small epsilon
                epsilon = 1e-15
                calibrated_probs = np.clip(calibrated_probs, epsilon, 1 - epsilon)
                
                # Binary cross-entropy loss
                loss = -np.mean(labels * np.log(calibrated_probs) + 
                              (1 - labels) * np.log(1 - calibrated_probs))
                return loss
            
            # Find optimal temperature
            result = minimize_scalar(negative_log_likelihood, bounds=(0.1, 10.0), method='bounded')
            self.temperature = result.x
            self.is_calibrated = True
            
            self.logger.info(f"Confidence calibrated with temperature: {self.temperature:.3f}")
            
        except Exception as e:
            self.logger.error(f"Confidence calibration failed: {e}")
            self.temperature = 1.0
    
    def _apply_temperature(self, predictions: np.ndarray, temperature: float) -> np.ndarray:
        """Apply temperature scaling to predictions"""
        return 1 / (1 + np.exp(-np.log(predictions / (1 - predictions)) / temperature))
    
    def calibrate_score(self, score: float) -> Tuple[float, Tuple[float, float]]:
        """Apply calibration and return confidence interval"""
        if not self.is_calibrated:
            # Return uncalibrated score with wide confidence interval
            return score, (max(0.0, score - 0.2), min(1.0, score + 0.2))
        
        # Apply temperature scaling
        calibrated_score = self._apply_temperature(np.array([score]), self.temperature)[0]
        
        # Estimate confidence interval (simplified)
        uncertainty = 0.1 / self.temperature  # Higher temperature = more uncertainty
        lower_bound = max(0.0, calibrated_score - uncertainty)
        upper_bound = min(1.0, calibrated_score + uncertainty)
        
        return calibrated_score, (lower_bound, upper_bound)
