"""
🎯 PRODUCTION-GRADE QUERY ENGINE
Bulletproof system that handles ALL Excel/Data Science scenarios
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from difflib import SequenceMatcher
import logging


@dataclass
class QueryResult:
    answer: str
    value: Union[float, int, None]
    confidence: float
    operation: str
    target_column: str
    filter_column: str
    filter_value: str
    rows_matched: int
    total_rows: int
    debug: Dict[str, Any]


class ProductionQueryEngine:
    """🎯 PRODUCTION-GRADE QUERY ENGINE"""
    
    def __init__(self):
        # Operation keywords with comprehensive coverage
        self.operations = {
            'sum': ['sum', 'total', 'add', 'aggregate'],
            'average': ['average', 'avg', 'mean'],
            'count': ['count', 'number', 'how many', 'quantity'],
            'max': ['max', 'maximum', 'highest', 'largest', 'biggest'],
            'min': ['min', 'minimum', 'lowest', 'smallest', 'least'],
            'median': ['median', 'middle'],
            'std': ['std', 'stdev', 'standard deviation'],
            'compare': ['compare', 'vs', 'versus', 'which', 'difference'],
            'percentage': ['percentage', 'percent', '%', 'proportion'],
            'breakdown': ['breakdown', 'group by', 'by', 'split by'],
            'superlative': ['highest', 'lowest', 'best', 'worst', 'most', 'least', 'top', 'bottom'],
            'ranking': ['rank', 'ranking', 'order', 'sort', 'top'],
            'business': ['focus', 'should', 'recommend', 'strategy', 'efficiency', 'roi', 'analysis']
        }
        
        # Value column indicators
        self.value_indicators = [
            'cost', 'price', 'amount', 'value', 'total', 'revenue', 'sales', 
            'salary', 'income', 'expense', 'fee', 'charge', 'payment'
        ]
        
        # Condition column indicators  
        self.condition_indicators = [
            'diagnosis', 'disease', 'condition', 'type', 'category', 'status',
            'department', 'region', 'location', 'group', 'class'
        ]
    
    async def process_query(self, df: pd.DataFrame, query: str) -> QueryResult:
        """🎯 MAIN PROCESSING METHOD"""
        
        debug = {'original_query': query, 'steps': []}
        
        try:
            # STEP 1: Parse operation
            operation = self._detect_operation(query)
            debug['steps'].append(f"Operation detected: {operation}")
            
            # STEP 2: Parse condition/filter
            filter_info = self._extract_filter(query, df)
            debug['steps'].append(f"Filter: {filter_info}")
            
            # STEP 3: Find target column
            target_col = self._find_target_column(df, operation, query)
            debug['steps'].append(f"Target column: {target_col}")
            
            # STEP 4: Handle special operations
            if operation in ['compare', 'percentage', 'breakdown', 'superlative', 'ranking', 'business']:
                return await self._handle_special_operations(df, operation, query, debug)

            # STEP 5: Apply filter
            filtered_df = self._apply_filter(df, filter_info)
            debug['steps'].append(f"Filtered: {len(filtered_df)}/{len(df)} rows")

            # STEP 6: Check for non-existent conditions
            if filter_info.get('value') and len(filtered_df) == 0:
                return QueryResult(
                    answer=f"No records found for '{filter_info['value']}'",
                    value=0,
                    confidence=0.95,
                    operation=operation,
                    target_column=target_col or 'N/A',
                    filter_column=filter_info.get('column', 'N/A'),
                    filter_value=filter_info.get('value', 'N/A'),
                    rows_matched=0,
                    total_rows=len(df),
                    debug=debug
                )

            # STEP 7: Perform operation
            result_value = self._execute_operation(filtered_df, target_col, operation)
            debug['steps'].append(f"Result: {result_value}")

            # STEP 8: Format answer
            answer = self._format_answer(operation, target_col, filter_info, result_value)
            
            return QueryResult(
                answer=answer,
                value=result_value,
                confidence=0.95,
                operation=operation,
                target_column=target_col or 'N/A',
                filter_column=filter_info.get('column', 'N/A'),
                filter_value=filter_info.get('value', 'N/A'),
                rows_matched=len(filtered_df),
                total_rows=len(df),
                debug=debug
            )
            
        except Exception as e:
            debug['error'] = str(e)
            return QueryResult(
                answer=f"Error: {str(e)}",
                value=None,
                confidence=0.0,
                operation='error',
                target_column='N/A',
                filter_column='N/A', 
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )
    
    def _detect_operation(self, query: str) -> str:
        """🔍 ROBUST OPERATION DETECTION"""
        query_lower = query.lower()

        # Priority detection for new operations
        if any(word in query_lower for word in ['highest', 'lowest', 'best', 'worst', 'most expensive', 'least expensive']):
            return 'superlative'

        if any(word in query_lower for word in ['rank', 'ranking', 'order', 'sort', 'top 3', 'top 5']):
            return 'ranking'

        if any(word in query_lower for word in ['focus', 'should', 'recommend', 'strategy', 'efficiency', 'roi']):
            return 'business'

        if any(word in query_lower for word in ['vs', 'versus', 'compare', 'which', 'difference']):
            return 'compare'

        if any(word in query_lower for word in ['percentage', 'percent', '%', 'proportion']):
            return 'percentage'

        if any(word in query_lower for word in ['breakdown', 'group by', 'by diagnosis', 'split by']):
            return 'breakdown'

        # Check each operation type
        for op, keywords in self.operations.items():
            if any(keyword in query_lower for keyword in keywords):
                return op

        # Default based on query structure
        if any(word in query_lower for word in ['how many', 'count', 'number']):
            return 'count'

        return 'sum'  # Default
    
    def _extract_filter(self, query: str, df: pd.DataFrame) -> Dict[str, Any]:
        """🎯 EXTRACT FILTER CONDITIONS"""

        if not query:
            return {'column': None, 'value': None, 'matches': 0}

        query_lower = query.lower()

        # Extract words more carefully to preserve actual conditions
        words = query_lower.split()

        # Remove operation words but keep track of position
        operation_words = []
        for op_list in self.operations.values():
            operation_words.extend(op_list)

        # Remove stop words but NOT single letters that could be conditions
        stop_words = ['of', 'for', 'where', 'with', 'the', 'all', 'patients', 'treatment', 'cost', 'expenses', 'cases', 'total', 'sum', 'count', 'average']

        # Keep words that are:
        # 1. Not operation words
        # 2. Not stop words
        # 3. Either longer than 1 character OR single letters (potential conditions like 'A', 'B')
        filtered_words = []
        for word in words:
            word = word.strip()
            if (word not in operation_words and
                word not in stop_words and
                len(word) > 0 and
                not word.isdigit()):  # Don't use numbers as conditions
                filtered_words.append(word)

        words = filtered_words

        # Find best matching condition - PRIORITIZE CONDITION COLUMNS
        best_match = {'column': None, 'value': None, 'matches': 0}

        # First, try to find matches in condition-specific columns (diagnosis, category, etc.)
        condition_columns = []
        for col in df.columns:
            if df[col].dtype == 'object' and any(indicator in col.lower() for indicator in self.condition_indicators):
                condition_columns.append(col)

        # If no condition columns found, use all object columns
        if not condition_columns:
            condition_columns = df.select_dtypes(include=['object']).columns.tolist()

        for word in words:
            # Skip generic words and column names that shouldn't be used as filters
            skip_words = ['patient', 'patients', 'all', 'total', 'cost', 'treatment', 'expenses', 'value', 'price', 'amount', 'revenue']

            # Also skip if word matches any column name (to avoid using target column as filter)
            column_names = [col.lower() for col in df.columns]

            if word in skip_words or word in column_names:
                continue

            for col in condition_columns:
                try:
                    matches = df[col].astype(str).str.contains(word, case=False, na=False).sum()

                    # Prioritize condition columns and meaningful matches
                    priority_score = matches
                    if any(indicator in col.lower() for indicator in self.condition_indicators):
                        priority_score *= 10  # Boost condition columns

                    if priority_score > best_match['matches']:
                        best_match = {'column': col, 'value': word, 'matches': int(matches)}
                except:
                    continue

        # If no matches found but we have a potential condition word, still return it
        # This allows the system to detect non-existent conditions
        if best_match['matches'] == 0 and len(words) > 0:
            # Look for the most likely condition word
            condition_word = None
            for word in words:
                if len(word) > 2 and word not in ['cost', 'treatment', 'patient']:
                    condition_word = word
                    break

            if condition_word:
                # Find the most likely condition column
                condition_col = None
                for col in df.columns:
                    if df[col].dtype == 'object' and any(indicator in col.lower() for indicator in self.condition_indicators):
                        condition_col = col
                        break

                if not condition_col:
                    # Use first text column
                    text_cols = df.select_dtypes(include=['object']).columns
                    condition_col = text_cols[0] if len(text_cols) > 0 else None

                if condition_col:
                    best_match = {'column': condition_col, 'value': condition_word, 'matches': 0}

        return best_match
    
    def _find_target_column(self, df: pd.DataFrame, operation: str, query: str) -> Optional[str]:
        """🔍 SMART TARGET COLUMN DETECTION"""

        # For count operations, no target column needed
        if operation == 'count':
            return None

        if not query:
            return None

        query_lower = query.lower()
        numeric_cols = df.select_dtypes(include=[np.number]).columns

        if len(numeric_cols) == 0:
            return None

        # Strategy 1: Look for value indicators in query
        for indicator in self.value_indicators:
            if indicator in query_lower:
                for col in numeric_cols:
                    if indicator in col.lower():
                        return col

        # Strategy 2: Fuzzy match with column names
        best_col = None
        best_score = 0

        for col in numeric_cols:
            # Check if any word in query matches column name
            for word in query_lower.split():
                if len(word) > 2:
                    try:
                        score = SequenceMatcher(None, word, col.lower()).ratio()
                        if score > best_score and score > 0.4:
                            best_score = score
                            best_col = col
                    except:
                        continue

        if best_col:
            return best_col

        # Strategy 3: Prioritize common value columns
        for indicator in self.value_indicators:
            for col in numeric_cols:
                if indicator in col.lower():
                    return col

        # Strategy 4: First numeric column
        return numeric_cols[0] if len(numeric_cols) > 0 else None
    
    def _apply_filter(self, df: pd.DataFrame, filter_info: Dict) -> pd.DataFrame:
        """🎯 APPLY FILTER CONDITIONS"""
        
        if not filter_info.get('column') or not filter_info.get('value'):
            return df
        
        col = filter_info['column']
        val = filter_info['value']
        
        try:
            return df[df[col].astype(str).str.contains(val, case=False, na=False)]
        except:
            return df
    
    def _execute_operation(self, df: pd.DataFrame, target_col: str, operation: str) -> Union[float, int]:
        """⚡ EXECUTE OPERATIONS"""
        
        if len(df) == 0:
            return 0
        
        if operation == 'count':
            return len(df)
        
        if not target_col or target_col not in df.columns:
            if operation == 'count':
                return len(df)
            return 0
        
        data = df[target_col].dropna()
        
        if len(data) == 0:
            return 0
        
        if operation == 'sum':
            return float(data.sum())
        elif operation == 'average':
            return float(data.mean())
        elif operation == 'max':
            return float(data.max())
        elif operation == 'min':
            return float(data.min())
        elif operation == 'median':
            return float(data.median())
        elif operation == 'std':
            return float(data.std())
        else:
            return float(data.sum())
    
    def _format_answer(self, operation: str, target_col: str, filter_info: Dict, value: Union[float, int]) -> str:
        """📝 FORMAT ANSWER"""

        if value is None:
            return "No result"

        # Format number
        try:
            if isinstance(value, float):
                if value.is_integer():
                    formatted = f"{int(value):,}"
                else:
                    formatted = f"{value:,.2f}"
            else:
                formatted = f"{value:,}"
        except:
            formatted = str(value)

        # Build answer
        if operation == 'count':
            if filter_info and filter_info.get('value'):
                return f"Count where {filter_info['column']} contains '{filter_info['value']}': {formatted}"
            else:
                return f"Total count: {formatted}"
        else:
            op_name = operation.title()
            if filter_info and filter_info.get('value') and target_col:
                return f"{op_name} of {target_col} where {filter_info['column']} contains '{filter_info['value']}': {formatted}"
            elif target_col:
                return f"{op_name} of {target_col}: {formatted}"
            else:
                return f"{op_name}: {formatted}"

    async def _handle_special_operations(self, df: pd.DataFrame, operation: str, query: str, debug: Dict) -> QueryResult:
        """🎯 HANDLE SPECIAL OPERATIONS - Compare, Percentage, Breakdown"""

        if operation == 'compare':
            return await self._handle_comparison(df, query, debug)
        elif operation == 'percentage':
            return await self._handle_percentage(df, query, debug)
        elif operation == 'breakdown':
            return await self._handle_breakdown(df, query, debug)
        elif operation == 'superlative':
            return await self._handle_superlative(df, query, debug)
        elif operation == 'ranking':
            return await self._handle_ranking(df, query, debug)
        elif operation == 'business':
            return await self._handle_business(df, query, debug)
        else:
            raise ValueError(f"Unknown special operation: {operation}")

    async def _handle_comparison(self, df: pd.DataFrame, query: str, debug: Dict) -> QueryResult:
        """🔍 HANDLE COMPARISON QUERIES"""

        query_lower = query.lower()

        # Extract conditions to compare
        conditions = []

        # Enhanced condition extraction for multi-condition comparisons
        import re

        # Look for specific condition combinations
        if 'covid-19' in query_lower and 'flu' in query_lower and 'diabetes' in query_lower:
            conditions = ['covid-19', 'flu', 'diabetes']
        elif 'covid-19' in query_lower and 'flu' in query_lower:
            conditions = ['covid-19', 'flu']
        elif 'diabetes' in query_lower and 'hypertension' in query_lower:
            conditions = ['diabetes', 'hypertension']
        else:
            # Try to extract from various patterns
            patterns = [
                # Multi-condition: "X and Y and Z"
                r'(\w+(?:-\w+)*)\s+and\s+(\w+(?:-\w+)*)\s+and\s+(\w+(?:-\w+)*)',
                # Two conditions: "X vs Y", "X and Y"
                r'(\w+(?:-\w+)*)\s+(?:vs|versus|and)\s+(\w+(?:-\w+)*)',
                r'(\w+(?:-\w+)*)\s+or\s+(\w+(?:-\w+)*)'
            ]

            for pattern in patterns:
                match = re.search(pattern, query_lower)
                if match:
                    conditions = [g for g in match.groups() if g]
                    break

        # Handle "all other" comparisons
        if 'all other' in query_lower or 'vs all' in query_lower:
            # Find the main condition and compare against all others
            main_condition = None
            for word in query_lower.split():
                if word in ['covid-19', 'flu', 'diabetes', 'hypertension']:
                    main_condition = word
                    break

            if main_condition:
                # Get all unique conditions except the main one
                group_col = None
                for col in df.columns:
                    if df[col].dtype == 'object' and col.lower() in ['diagnosis', 'category', 'type']:
                        group_col = col
                        break

                if group_col:
                    all_conditions = df[group_col].unique()
                    other_conditions = [c for c in all_conditions if main_condition.lower() not in c.lower()]
                    conditions = [main_condition] + list(other_conditions)[:3]  # Limit to avoid too many

        if len(conditions) < 2:
            return QueryResult(
                answer="Could not identify two conditions to compare",
                value=None,
                confidence=0.3,
                operation='compare',
                target_column='N/A',
                filter_column='N/A',
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )

        # Find target column (cost by default)
        target_col = self._find_target_column(df, 'sum', query)
        if not target_col:
            target_col = df.select_dtypes(include=[np.number]).columns[0]

        # Calculate for each condition
        results = {}
        for condition in conditions:
            filter_info = {'column': None, 'value': condition, 'matches': 0}

            # Find best matching column
            for col in df.columns:
                if df[col].dtype == 'object':
                    matches = df[col].astype(str).str.contains(condition, case=False, na=False).sum()
                    if matches > filter_info['matches']:
                        filter_info = {'column': col, 'value': condition, 'matches': matches}

            if filter_info['column']:
                filtered_df = df[df[filter_info['column']].astype(str).str.contains(condition, case=False, na=False)]

                # Determine operation from query
                if 'average' in query_lower or 'mean' in query_lower:
                    value = filtered_df[target_col].mean() if len(filtered_df) > 0 else 0
                    op_name = 'Average'
                elif 'count' in query_lower:
                    value = len(filtered_df)
                    op_name = 'Count'
                else:
                    value = filtered_df[target_col].sum() if len(filtered_df) > 0 else 0
                    op_name = 'Total'

                results[condition] = {
                    'value': value,
                    'count': len(filtered_df),
                    'operation': op_name
                }

        # Format comparison result - enhanced for multiple conditions
        if len(results) >= 2:
            op_name = list(results.values())[0]['operation']

            # Sort results by value for ranking
            sorted_results = sorted(results.items(), key=lambda x: x[1]['value'], reverse=True)

            if len(results) == 2:
                # Two-way comparison
                cond1, cond2 = list(results.keys())
                val1, val2 = results[cond1]['value'], results[cond2]['value']

                if val1 > val2:
                    winner = cond1
                    difference = val1 - val2
                else:
                    winner = cond2
                    difference = val2 - val1

                answer = f"{op_name} comparison: {cond1.title()}: {val1:,.2f}, {cond2.title()}: {val2:,.2f}. {winner.title()} is higher by {difference:,.2f}"
                result_value = difference

            else:
                # Multi-way comparison
                comparison_text = []
                for i, (condition, data) in enumerate(sorted_results[:5], 1):  # Top 5
                    comparison_text.append(f"{i}. {condition.title()}: {data['value']:,.2f}")

                winner = sorted_results[0][0]
                winner_value = sorted_results[0][1]['value']

                answer = f"{op_name} comparison ranking: " + ", ".join(comparison_text)
                result_value = winner_value

            return QueryResult(
                answer=answer,
                value=result_value,
                confidence=0.95,
                operation='compare',
                target_column=target_col,
                filter_column='Multiple',
                filter_value=" vs ".join(list(results.keys())[:3]),
                rows_matched=sum(r['count'] for r in results.values()),
                total_rows=len(df),
                debug={**debug, 'comparison_results': results}
            )

        return QueryResult(
            answer="Comparison failed",
            value=None,
            confidence=0.0,
            operation='compare',
            target_column='N/A',
            filter_column='N/A',
            filter_value='N/A',
            rows_matched=0,
            total_rows=len(df),
            debug=debug
        )

    async def _handle_percentage(self, df: pd.DataFrame, query: str, debug: Dict) -> QueryResult:
        """📊 HANDLE PERCENTAGE QUERIES"""

        query_lower = query.lower()

        # Determine if this is cost percentage or patient percentage
        is_cost_percentage = any(word in query_lower for word in ['cost', 'revenue', 'total', 'value', 'amount'])

        # Extract condition for percentage calculation
        filter_info = self._extract_filter(query, df)

        if not filter_info.get('value'):
            # Handle "percentage breakdown" queries
            if 'breakdown' in query_lower:
                return await self._handle_percentage_breakdown(df, query, debug)

            return QueryResult(
                answer="Could not identify condition for percentage calculation",
                value=None,
                confidence=0.3,
                operation='percentage',
                target_column='N/A',
                filter_column='N/A',
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )

        # Apply filter
        filtered_df = self._apply_filter(df, filter_info)

        if is_cost_percentage:
            # Calculate cost percentage
            target_col = self._find_target_column(df, 'sum', query)
            if target_col:
                total_cost = df[target_col].sum()
                filtered_cost = filtered_df[target_col].sum() if len(filtered_df) > 0 else 0
                percentage = (filtered_cost / total_cost * 100) if total_cost > 0 else 0

                answer = f"{filter_info['value']} represents {percentage:.1f}% of total cost (${filtered_cost:,.2f} out of ${total_cost:,.2f})"
            else:
                return QueryResult(
                    answer="Could not find cost column for percentage calculation",
                    value=None,
                    confidence=0.3,
                    operation='percentage',
                    target_column='N/A',
                    filter_column='N/A',
                    filter_value='N/A',
                    rows_matched=0,
                    total_rows=len(df),
                    debug=debug
                )
        else:
            # Calculate patient/count percentage
            total_count = len(df)
            filtered_count = len(filtered_df)
            percentage = (filtered_count / total_count * 100) if total_count > 0 else 0

            answer = f"{filtered_count} out of {total_count} patients have {filter_info['value']} ({percentage:.1f}%)"

        return QueryResult(
            answer=answer,
            value=percentage,
            confidence=0.95,
            operation='percentage',
            target_column=target_col if is_cost_percentage else 'N/A',
            filter_column=filter_info.get('column', 'N/A'),
            filter_value=filter_info.get('value', 'N/A'),
            rows_matched=len(filtered_df),
            total_rows=len(df),
            debug=debug
        )

    async def _handle_breakdown(self, df: pd.DataFrame, query: str, debug: Dict) -> QueryResult:
        """📋 HANDLE BREAKDOWN/GROUPING QUERIES"""

        query_lower = query.lower()

        # Find grouping column (usually categorical)
        group_col = None
        for col in df.columns:
            if df[col].dtype == 'object' and col.lower() in ['diagnosis', 'category', 'type', 'status']:
                group_col = col
                break

        if not group_col:
            # Use first categorical column
            categorical_cols = df.select_dtypes(include=['object']).columns
            if len(categorical_cols) > 0:
                group_col = categorical_cols[0]

        if not group_col:
            return QueryResult(
                answer="No suitable grouping column found",
                value=None,
                confidence=0.3,
                operation='breakdown',
                target_column='N/A',
                filter_column='N/A',
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )

        # Find target column for aggregation
        target_col = self._find_target_column(df, 'sum', query)

        # Determine operation - enhanced for multiple stats
        if 'min and max' in query_lower or 'minimum and maximum' in query_lower:
            # Min and max breakdown
            if target_col:
                min_vals = df.groupby(group_col)[target_col].min().to_dict()
                max_vals = df.groupby(group_col)[target_col].max().to_dict()
                breakdown = {}
                for category in min_vals.keys():
                    breakdown[category] = f"Min: {min_vals[category]:,.2f}, Max: {max_vals[category]:,.2f}"
                op_name = 'Min/Max'
            else:
                breakdown = df[group_col].value_counts().to_dict()
                op_name = 'Count'
        elif 'count' in query_lower:
            # Count breakdown
            breakdown = df[group_col].value_counts().to_dict()
            op_name = 'Count'
        elif target_col:
            # Value breakdown
            if 'average' in query_lower:
                breakdown = df.groupby(group_col)[target_col].mean().to_dict()
                op_name = 'Average'
            elif 'max' in query_lower or 'maximum' in query_lower:
                breakdown = df.groupby(group_col)[target_col].max().to_dict()
                op_name = 'Maximum'
            elif 'min' in query_lower or 'minimum' in query_lower:
                breakdown = df.groupby(group_col)[target_col].min().to_dict()
                op_name = 'Minimum'
            else:
                breakdown = df.groupby(group_col)[target_col].sum().to_dict()
                op_name = 'Total'
        else:
            # Default to count
            breakdown = df[group_col].value_counts().to_dict()
            op_name = 'Count'

        # Format breakdown result
        breakdown_text = []
        for category, value in breakdown.items():
            if isinstance(value, float):
                breakdown_text.append(f"{category}: {value:,.2f}")
            else:
                breakdown_text.append(f"{category}: {value:,}")

        answer = f"{op_name} breakdown by {group_col}: " + ", ".join(breakdown_text[:5])
        if len(breakdown_text) > 5:
            answer += f" (and {len(breakdown_text) - 5} more)"

        return QueryResult(
            answer=answer,
            value=sum(breakdown.values()) if breakdown else 0,
            confidence=0.95,
            operation='breakdown',
            target_column=target_col or group_col,
            filter_column=group_col,
            filter_value='All categories',
            rows_matched=len(df),
            total_rows=len(df),
            debug={**debug, 'breakdown_results': breakdown}
        )

    async def _handle_percentage_breakdown(self, df: pd.DataFrame, query: str, debug: Dict) -> QueryResult:
        """📊 HANDLE PERCENTAGE BREAKDOWN QUERIES"""

        # Find grouping column
        group_col = None
        for col in df.columns:
            if df[col].dtype == 'object' and col.lower() in ['diagnosis', 'category', 'type', 'status']:
                group_col = col
                break

        if not group_col:
            categorical_cols = df.select_dtypes(include=['object']).columns
            if len(categorical_cols) > 0:
                group_col = categorical_cols[0]

        if not group_col:
            return QueryResult(
                answer="No suitable grouping column found for percentage breakdown",
                value=None,
                confidence=0.3,
                operation='percentage',
                target_column='N/A',
                filter_column='N/A',
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )

        # Calculate percentage breakdown
        total_count = len(df)
        breakdown = df[group_col].value_counts()
        percentage_breakdown = {}

        for category, count in breakdown.items():
            percentage = (count / total_count * 100) if total_count > 0 else 0
            percentage_breakdown[category] = percentage

        # Format result
        breakdown_text = []
        for category, percentage in percentage_breakdown.items():
            breakdown_text.append(f"{category}: {percentage:.1f}%")

        answer = f"Percentage breakdown by {group_col}: " + ", ".join(breakdown_text[:5])
        if len(breakdown_text) > 5:
            answer += f" (and {len(breakdown_text) - 5} more)"

        return QueryResult(
            answer=answer,
            value=sum(percentage_breakdown.values()),
            confidence=0.95,
            operation='percentage',
            target_column=group_col,
            filter_column=group_col,
            filter_value='All categories',
            rows_matched=len(df),
            total_rows=len(df),
            debug={**debug, 'percentage_breakdown': percentage_breakdown}
        )

    async def _handle_superlative(self, df: pd.DataFrame, query: str, debug: Dict) -> QueryResult:
        """🏆 HANDLE SUPERLATIVE QUERIES - highest, lowest, best, worst"""

        query_lower = query.lower()

        # Determine operation type
        is_highest = any(word in query_lower for word in ['highest', 'most', 'best', 'maximum', 'max'])
        is_lowest = any(word in query_lower for word in ['lowest', 'least', 'worst', 'minimum', 'min'])

        # Find grouping column (diagnosis, category, etc.)
        group_col = None
        for col in df.columns:
            if df[col].dtype == 'object' and col.lower() in ['diagnosis', 'category', 'type', 'status']:
                group_col = col
                break

        if not group_col:
            categorical_cols = df.select_dtypes(include=['object']).columns
            if len(categorical_cols) > 0:
                group_col = categorical_cols[0]

        # Find target column
        target_col = self._find_target_column(df, 'sum', query)

        if not group_col or not target_col:
            return QueryResult(
                answer="Could not identify columns for superlative analysis",
                value=None,
                confidence=0.3,
                operation='superlative',
                target_column='N/A',
                filter_column='N/A',
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )

        # Determine aggregation method
        if 'average' in query_lower or 'mean' in query_lower:
            grouped = df.groupby(group_col)[target_col].mean()
            op_name = 'average'
        elif 'count' in query_lower:
            grouped = df.groupby(group_col).size()
            op_name = 'count'
        else:
            grouped = df.groupby(group_col)[target_col].sum()
            op_name = 'total'

        # Find superlative
        if is_highest:
            winner = grouped.idxmax()
            winner_value = grouped.max()
            direction = 'highest'
        else:
            winner = grouped.idxmin()
            winner_value = grouped.min()
            direction = 'lowest'

        if isinstance(winner_value, float):
            answer = f"{winner} has the {direction} {op_name} {target_col}: {winner_value:,.2f}"
        else:
            answer = f"{winner} has the {direction} {op_name} {target_col}: {winner_value:,}"

        return QueryResult(
            answer=answer,
            value=float(winner_value) if pd.notna(winner_value) else 0,
            confidence=0.95,
            operation='superlative',
            target_column=target_col,
            filter_column=group_col,
            filter_value=str(winner),
            rows_matched=len(df[df[group_col] == winner]),
            total_rows=len(df),
            debug={**debug, 'superlative_results': grouped.to_dict()}
        )

    async def _handle_ranking(self, df: pd.DataFrame, query: str, debug: Dict) -> QueryResult:
        """📊 HANDLE RANKING QUERIES - rank, top N, order by"""

        query_lower = query.lower()

        # Extract top N if specified
        import re
        top_n_match = re.search(r'top\s+(\d+)', query_lower)
        top_n = int(top_n_match.group(1)) if top_n_match else 5  # Default to top 5

        # Find grouping column
        group_col = None
        for col in df.columns:
            if df[col].dtype == 'object' and col.lower() in ['diagnosis', 'category', 'type', 'status']:
                group_col = col
                break

        if not group_col:
            categorical_cols = df.select_dtypes(include=['object']).columns
            if len(categorical_cols) > 0:
                group_col = categorical_cols[0]

        # Find target column
        target_col = self._find_target_column(df, 'sum', query)

        if not group_col:
            return QueryResult(
                answer="Could not identify grouping column for ranking",
                value=None,
                confidence=0.3,
                operation='ranking',
                target_column='N/A',
                filter_column='N/A',
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )

        # Determine aggregation method
        if 'count' in query_lower or 'patient' in query_lower:
            grouped = df.groupby(group_col).size()
            op_name = 'patient count'
        elif target_col and ('average' in query_lower or 'mean' in query_lower):
            grouped = df.groupby(group_col)[target_col].mean()
            op_name = f'average {target_col}'
        elif target_col:
            grouped = df.groupby(group_col)[target_col].sum()
            op_name = f'total {target_col}'
        else:
            grouped = df.groupby(group_col).size()
            op_name = 'count'

        # Sort and get top N
        sorted_results = grouped.sort_values(ascending=False).head(top_n)

        # Format ranking
        ranking_text = []
        for i, (category, value) in enumerate(sorted_results.items(), 1):
            if isinstance(value, float):
                ranking_text.append(f"{i}. {category}: {value:,.2f}")
            else:
                ranking_text.append(f"{i}. {category}: {value:,}")

        answer = f"Top {top_n} {group_col} by {op_name}: " + ", ".join(ranking_text)

        return QueryResult(
            answer=answer,
            value=float(sorted_results.iloc[0]) if len(sorted_results) > 0 else 0,
            confidence=0.95,
            operation='ranking',
            target_column=target_col or group_col,
            filter_column=group_col,
            filter_value=f'Top {top_n}',
            rows_matched=len(df),
            total_rows=len(df),
            debug={**debug, 'ranking_results': sorted_results.to_dict()}
        )

    async def _handle_business(self, df: pd.DataFrame, query: str, debug: Dict) -> QueryResult:
        """💼 HANDLE BUSINESS INTELLIGENCE QUERIES"""

        query_lower = query.lower()

        # Find grouping column
        group_col = None
        for col in df.columns:
            if df[col].dtype == 'object' and col.lower() in ['diagnosis', 'category', 'type', 'status']:
                group_col = col
                break

        if not group_col:
            categorical_cols = df.select_dtypes(include=['object']).columns
            if len(categorical_cols) > 0:
                group_col = categorical_cols[0]

        # Find target column
        target_col = self._find_target_column(df, 'sum', query)

        if not group_col or not target_col:
            return QueryResult(
                answer="Could not identify columns for business analysis",
                value=None,
                confidence=0.3,
                operation='business',
                target_column='N/A',
                filter_column='N/A',
                filter_value='N/A',
                rows_matched=0,
                total_rows=len(df),
                debug=debug
            )

        # Business analysis logic
        if 'cost reduction' in query_lower or 'focus' in query_lower:
            # Find highest cost category for cost reduction focus
            total_costs = df.groupby(group_col)[target_col].sum().sort_values(ascending=False)
            top_category = total_costs.index[0]
            top_value = total_costs.iloc[0]

            answer = f"Focus on {top_category} for cost reduction - highest total cost: ${top_value:,.2f}"

        elif 'efficiency' in query_lower or 'roi' in query_lower:
            # Calculate cost per patient for efficiency analysis
            costs = df.groupby(group_col)[target_col].sum()
            counts = df.groupby(group_col).size()
            efficiency = costs / counts

            best_efficiency = efficiency.idxmin()  # Lowest cost per patient
            best_value = efficiency.min()

            answer = f"Best cost efficiency: {best_efficiency} at ${best_value:,.2f} per patient"

        elif 'volume' in query_lower and 'cost' in query_lower:
            # Volume vs cost analysis
            costs = df.groupby(group_col)[target_col].sum()
            counts = df.groupby(group_col).size()

            analysis_text = []
            for category in costs.index:
                cost = costs[category]
                count = counts[category]
                analysis_text.append(f"{category}: {count} patients, ${cost:,.2f} total")

            answer = f"Volume vs Cost analysis: " + ", ".join(analysis_text[:3])

        else:
            # General business analysis - show top performers
            grouped = df.groupby(group_col)[target_col].sum().sort_values(ascending=False)
            top_category = grouped.index[0]
            top_value = grouped.iloc[0]

            answer = f"Business analysis: {top_category} is the top performer with ${top_value:,.2f}"

        return QueryResult(
            answer=answer,
            value=float(top_value) if 'top_value' in locals() else 0,
            confidence=0.90,
            operation='business',
            target_column=target_col,
            filter_column=group_col,
            filter_value='Business Analysis',
            rows_matched=len(df),
            total_rows=len(df),
            debug=debug
        )


# Global instance
production_engine = ProductionQueryEngine()
