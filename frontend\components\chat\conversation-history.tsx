'use client';

import React, { useState, useEffect } from 'react';
import {
  History,
  MessageSquare,
  Clock,
  Database,
  Search,
  Trash2,
  ChevronRight,
  Filter,
  Heart,
  RefreshCw,
  Bookmark,
  ThumbsUp,
  ThumbsDown,
  Copy
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { createApiInstance } from '../../lib/api';


interface Conversation {
  id: string;
  title: string;
  datasetId: number;
  datasetName: string;
  lastMessage: string;
  messageCount: number;
  createdAt: Date;
  updatedAt: Date;
  isImportant: boolean;
  isTemporary?: boolean;
  savedQuery?: any;
}

interface ConversationHistoryProps {
  onSelectConversation: (conversation: Conversation) => void;
  currentConversationId?: string;
  className?: string;
  refreshTrigger?: number; // Add this to trigger refresh from parent
}

export default function ConversationHistory({
  onSelectConversation,
  currentConversationId,
  className = '',
  refreshTrigger = 0
}: ConversationHistoryProps) {
  const { data: session } = useSession();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<Conversation[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterBy, setFilterBy] = useState<'all' | 'saved' | 'temporary' | 'important' | 'recent'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConversations();

    // Listen for conversation creation and save events
    const handleConversationCreated = () => {
      setTimeout(() => loadConversations(), 500);
    };

    const handleConversationSaved = () => {
      setTimeout(() => loadConversations(), 500);
    };

    window.addEventListener('conversationCreated', handleConversationCreated);
    window.addEventListener('conversationSaved', handleConversationSaved);

    return () => {
      window.removeEventListener('conversationCreated', handleConversationCreated);
      window.removeEventListener('conversationSaved', handleConversationSaved);
    };
  }, [session, refreshTrigger]);

  useEffect(() => {
    filterConversations();
  }, [conversations, searchQuery, filterBy]);

  const loadConversations = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Load temporary conversations from localStorage
      const localConversations = JSON.parse(localStorage.getItem('rag_conversations') || '[]');

      const tempConversations = localConversations.map((conv: any) => ({
        id: conv.id,
        title: conv.title,
        datasetId: conv.datasetId,
        datasetName: conv.datasetName,
        lastMessage: conv.lastMessage,
        messageCount: conv.messageCount,
        createdAt: new Date(conv.createdAt),
        updatedAt: new Date(conv.updatedAt),
        isImportant: conv.isImportant || false,
        isTemporary: true // Mark as temporary
      }));

      // Load saved queries from database if session available
      let savedConversations: any[] = [];
      if (session) {
        try {
          const userWithApiKey = session?.user as any;
          if (userWithApiKey?.apiKey) {
            const sessionApi = createApiInstance(session);
            console.log('🔍 Calling saved queries API...');
            const response = await sessionApi.get('/api/v1/saved-queries/');
            console.log('🔍 Saved queries API response:', response);

            if (response && response.data) {
              console.log('✅ Loaded saved queries from API:', response.data);
              savedConversations = response.data.map((query: any) => ({
                id: `saved-${query.id}`,
                title: query.name || query.question.substring(0, 50) + '...',
                datasetId: query.dataset_id,
                datasetName: query.dataset_name || `Dataset ${query.dataset_id}`,
                lastMessage: query.answer.substring(0, 100) + '...',
                messageCount: 2, // Question + Answer
                createdAt: new Date(query.created_at),
                updatedAt: new Date(query.updated_at || query.created_at),
                isImportant: true, // All saved queries are important
                isTemporary: false, // Mark as permanent
                savedQuery: query // Store full query data
              }));
              console.log('✅ Processed saved conversations:', savedConversations);
            }
          }
        } catch (apiError) {
          console.error('Failed to load saved queries:', apiError);
          console.error('API Error details:', (apiError as any).response?.data);
        }
      }

      // Combine and sort by date (saved first, then temporary)
      // Only show temporary conversations in history (saved queries go to Saved Queries tab)
      const allConversations = [...tempConversations]
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

      setConversations(allConversations);
    } catch (error) {
      console.error('Failed to load conversations:', error);
      setConversations([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filterConversations = () => {
    let filtered = [...conversations];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(conv => 
        conv.title.toLowerCase().includes(query) ||
        conv.datasetName.toLowerCase().includes(query) ||
        conv.lastMessage.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    switch (filterBy) {
      case 'saved':
        filtered = filtered.filter(conv => !conv.isTemporary);
        break;
      case 'temporary':
        filtered = filtered.filter(conv => conv.isTemporary);
        break;
      case 'important':
        filtered = filtered.filter(conv => conv.isImportant);
        break;
      case 'recent':
        filtered = filtered.filter(conv => {
          const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
          return conv.updatedAt > dayAgo;
        });
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    // Sort by most recent first
    filtered.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

    setFilteredConversations(filtered);
  };

  const saveTemporaryConversation = async (conversation: Conversation, event: React.MouseEvent) => {
    event.stopPropagation();

    if (!conversation.isTemporary || !session) return;

    try {
      const sessionApi = createApiInstance(session);

      // Extract the last Q&A from localStorage conversation
      const localConversations = JSON.parse(localStorage.getItem('rag_conversations') || '[]');
      const localConv = localConversations.find((c: any) => c.id === conversation.id);

      if (!localConv || !localConv.messages || localConv.messages.length < 2) {
        console.error('No messages found in temporary conversation');
        return;
      }

      const userMessage = localConv.messages.find((m: any) => m.role === 'user');
      const assistantMessage = localConv.messages.find((m: any) => m.role === 'assistant');

      if (!userMessage || !assistantMessage) {
        console.error('Missing user or assistant message');
        return;
      }

      // Save to database
      await sessionApi.post('/api/v1/saved-queries/', {
        question: userMessage.content,
        answer: assistantMessage.content,
        dataset_id: conversation.datasetId,
        name: conversation.title,
        description: `Saved from temporary chat about ${conversation.datasetName}`,
        tags: `dataset:${conversation.datasetName}`
      });

      // Refresh conversations to show the saved version
      setTimeout(() => loadConversations(), 500);

    } catch (error) {
      console.error('Failed to save temporary conversation:', error);
    }
  };

  const handleLike = (conversation: Conversation, event: React.MouseEvent) => {
    event.stopPropagation();
    console.log('Liked conversation:', conversation.title);
    // TODO: Implement like functionality
  };

  const handleDislike = (conversation: Conversation, event: React.MouseEvent) => {
    event.stopPropagation();
    console.log('Disliked conversation:', conversation.title);
    // TODO: Implement dislike functionality
  };

  const copyMessage = async (conversation: Conversation, event: React.MouseEvent) => {
    event.stopPropagation();

    try {
      let textToCopy = '';

      if (conversation.isTemporary) {
        // Get from localStorage
        const localConversations = JSON.parse(localStorage.getItem('rag_conversations') || '[]');
        const localConv = localConversations.find((c: any) => c.id === conversation.id);

        if (localConv && localConv.messages) {
          const userMessage = localConv.messages.find((m: any) => m.role === 'user');
          const assistantMessage = localConv.messages.find((m: any) => m.role === 'assistant');

          if (userMessage && assistantMessage) {
            textToCopy = `Q: ${userMessage.content}\n\nA: ${assistantMessage.content}`;
          }
        }
      } else {
        // Get from saved query
        if (conversation.savedQuery) {
          textToCopy = `Q: ${conversation.savedQuery.question}\n\nA: ${conversation.savedQuery.answer}`;
        }
      }

      if (textToCopy) {
        await navigator.clipboard.writeText(textToCopy);
        console.log('Message copied to clipboard');
      }
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const toggleImportant = (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation();

    const updated = conversations.map(conv =>
      conv.id === conversationId
        ? { ...conv, isImportant: !conv.isImportant }
        : conv
    );

    setConversations(updated);
    localStorage.setItem('rag_conversations', JSON.stringify(updated));
  };

  const deleteConversation = (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (confirm('Are you sure you want to delete this conversation?')) {
      const updated = conversations.filter(conv => conv.id !== conversationId);
      setConversations(updated);
      localStorage.setItem('rag_conversations', JSON.stringify(updated));
    }
  };

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };



  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/2"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 ${className}`}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <History className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-gray-900">Conversation History</h3>
          </div>
          <button
            onClick={loadConversations}
            disabled={isLoading}
            className="p-1 text-gray-400 hover:text-blue-600 rounded"
            title="Refresh conversations"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search conversations..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value as any)}
            className="text-sm border border-gray-300 rounded px-2 py-1"
          >
            <option value="all">All</option>
            <option value="saved">Saved</option>
            <option value="temporary">Temporary</option>
            <option value="important">Important</option>
            <option value="recent">Recent</option>
          </select>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <div className="flex items-center justify-between text-red-700">
            <span className="text-sm">{error}</span>
            <div className="flex items-center space-x-2">
              {error.includes('sign out') ? (
                <button
                  onClick={() => {
                    import('next-auth/react').then(({ signOut }) => signOut());
                  }}
                  className="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                >
                  Sign Out
                </button>
              ) : (
                <button
                  onClick={loadConversations}
                  className="text-xs underline hover:no-underline"
                >
                  Retry
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Conversations List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              {searchQuery || filterBy !== 'all'
                ? 'No conversations match your criteria'
                : 'No conversations yet'
              }
            </p>
            {!searchQuery && filterBy === 'all' && (
              <p className="text-xs text-gray-400 mt-2">
                Start a chat with a dataset to see conversations here
              </p>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => onSelectConversation(conversation)}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                  currentConversationId === conversation.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {conversation.title}
                      </h4>
                      {!conversation.isTemporary && (
                        <div className="px-1.5 py-0.5 bg-green-100 text-green-700 text-xs rounded font-medium">
                          SAVED
                        </div>
                      )}
                      {conversation.isTemporary && (
                        <div className="px-1.5 py-0.5 bg-gray-100 text-gray-600 text-xs rounded">
                          TEMP
                        </div>
                      )}

                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <Database className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600 truncate">
                        {conversation.datasetName}
                      </span>
                    </div>
                    
                    <p className="text-xs text-gray-500 line-clamp-2 mb-2">
                      {conversation.lastMessage}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-400">
                      <div className="flex items-center space-x-2">
                        <MessageSquare className="h-3 w-3" />
                        <span>{conversation.messageCount} messages</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatRelativeTime(conversation.updatedAt)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1 ml-2">
                    {/* Like Button */}
                    <button
                      onClick={(e) => handleLike(conversation, e)}
                      className="p-1 text-gray-400 hover:text-green-500 rounded"
                      title="Like this response"
                    >
                      <ThumbsUp className="h-3 w-3" />
                    </button>

                    {/* Dislike Button */}
                    <button
                      onClick={(e) => handleDislike(conversation, e)}
                      className="p-1 text-gray-400 hover:text-red-500 rounded"
                      title="Dislike this response"
                    >
                      <ThumbsDown className="h-3 w-3" />
                    </button>

                    {/* Mark Important Button */}
                    <button
                      onClick={(e) => toggleImportant(conversation.id, e)}
                      className="p-1 text-gray-400 hover:text-red-500 rounded"
                      title={conversation.isImportant ? "Remove from important" : "Mark as important"}
                    >
                      <Heart className={`h-3 w-3 ${conversation.isImportant ? 'fill-current text-red-500' : ''}`} />
                    </button>

                    {/* Save to Database Button */}
                    {conversation.isTemporary ? (
                      <button
                        onClick={(e) => saveTemporaryConversation(conversation, e)}
                        className="p-1 text-gray-400 hover:text-green-600 rounded"
                        title="Save this Q&A permanently"
                      >
                        <Bookmark className="h-3 w-3" />
                      </button>
                    ) : (
                      <div className="p-1" title="Saved permanently">
                        <Bookmark className="h-3 w-3 text-green-600 fill-current" />
                      </div>
                    )}

                    {/* Copy Message Button */}
                    <button
                      onClick={(e) => copyMessage(conversation, e)}
                      className="p-1 text-gray-400 hover:text-blue-500 rounded"
                      title="Copy message"
                    >
                      <Copy className="h-3 w-3" />
                    </button>

                    {/* Delete Button */}
                    <button
                      onClick={(e) => deleteConversation(conversation.id, e)}
                      className="p-1 text-gray-400 hover:text-red-500 rounded"
                      title="Delete conversation"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>

                    <ChevronRight className="h-3 w-3 text-gray-400" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
