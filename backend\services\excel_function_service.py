import pandas as pd
import numpy as np
import re
from typing import Dict, Any, List, Union, Callable
import logging
from datetime import datetime, timedelta

class ExcelFunctionService:
    """
    Service for executing Excel-like functions on pandas DataFrames
    """

    def __init__(self, df: pd.DataFrame):
        """
        Initialize the Excel function service

        Args:
            df: The pandas DataFrame to operate on
        """
        self.df = df
        self.functions = self._register_functions()

    def _register_functions(self) -> Dict[str, Callable]:
        """
        Register all supported Excel functions

        Returns:
            Dictionary mapping function names to their implementations
        """
        return {
            # Basic math functions
            "SUM": self.excel_sum,
            "AVERAGE": self.excel_average,
            "COUNT": self.excel_count,
            "MAX": self.excel_max,
            "MIN": self.excel_min,
            "ROUND": self.excel_round,
            "SUMIF": self.excel_sumif,
            "COUNTIF": self.excel_countif,
            "AVERAGEIF": self.excel_averageif,

            # Statistical functions
            "STDEV": self.excel_stdev,
            "MEDIAN": self.excel_median,
            "PERCENTILE": self.excel_percentile,
            "CORREL": self.excel_correlation,
            "VAR": self.excel_variance,
            "SKEW": self.excel_skew,
            "KURT": self.excel_kurtosis,
            "QUARTILE": self.excel_quartile,
            "MODE": self.excel_mode,
            "ZSCORE": self.excel_zscore,

            # Logical functions
            "IF": self.excel_if,
            "AND": self.excel_and,
            "OR": self.excel_or,
            "NOT": self.excel_not,
            "IFERROR": self.excel_iferror,
            "IFNA": self.excel_ifna,

            # Lookup functions
            "VLOOKUP": self.excel_vlookup,
            "HLOOKUP": self.excel_hlookup,
            "INDEX": self.excel_index,
            "MATCH": self.excel_match,

            # Text functions
            "CONCATENATE": self.excel_concatenate,
            "LEFT": self.excel_left,
            "RIGHT": self.excel_right,
            "MID": self.excel_mid,
            "LEN": self.excel_len,
            "FIND": self.excel_find,
            "REPLACE": self.excel_replace,
            "PROPER": self.excel_proper,
            "TRIM": self.excel_trim,
            "SUBSTITUTE": self.excel_substitute,
            "UPPER": self.excel_upper,
            "LOWER": self.excel_lower,
            "TEXT": self.excel_text,

            # Date functions
            "YEAR": self.excel_year,
            "MONTH": self.excel_month,
            "DAY": self.excel_day,
            "TODAY": self.excel_today,

            # Financial functions
            "NPV": self.excel_npv,
            "PMT": self.excel_pmt,

            # Advanced analytics
            "TREND": self.excel_trend,
            "FORECAST": self.excel_forecast,
            "GROWTH": self.excel_growth,
            "RANK": self.excel_rank
        }

    def execute_formula(self, formula: str) -> Any:
        """
        Parse and execute an Excel-like formula

        Args:
            formula: The Excel-like formula to execute

        Returns:
            The result of the formula execution
        """
        try:
            # Remove '=' if present at the beginning
            if formula.startswith('='):
                formula = formula[1:]

            # Parse the formula and execute it
            result = self._parse_formula(formula)
            return result

        except Exception as e:
            logging.error(f"Error executing formula '{formula}': {str(e)}")
            return f"Error: {str(e)}"

    def _parse_formula(self, formula: str) -> Any:
        """
        Parse an Excel-like formula and execute it

        Args:
            formula: The formula to parse

        Returns:
            The result of the formula execution
        """
        # Handle nested functions
        if '(' in formula:
            # Find the innermost function call
            pattern = r'([A-Z]+)\(([^()]*(?:\([^()]*\)[^()]*)*)\)'
            match = re.search(pattern, formula)

            if match:
                func_name = match.group(1)
                args_str = match.group(2)

                # Parse arguments
                args = self._parse_arguments(args_str)

                # Execute the function
                if func_name in self.functions:
                    result = self.functions[func_name](*args)

                    # Replace the function call with its result in the original formula
                    new_formula = formula.replace(match.group(0), str(result))

                    # Continue parsing if there are more functions
                    if '(' in new_formula:
                        return self._parse_formula(new_formula)
                    else:
                        # Evaluate any remaining arithmetic operations
                        return self._evaluate_arithmetic(new_formula)
                else:
                    raise ValueError(f"Unknown function: {func_name}")
            else:
                # No function calls, just evaluate arithmetic
                return self._evaluate_arithmetic(formula)
        else:
            # No function calls, just evaluate arithmetic
            return self._evaluate_arithmetic(formula)

    def _parse_arguments(self, args_str: str) -> List[Any]:
        """
        Parse function arguments

        Args:
            args_str: String containing comma-separated arguments

        Returns:
            List of parsed arguments
        """
        args = []

        # Handle empty arguments
        if not args_str.strip():
            return args

        # Split by commas, but respect nested functions
        current_arg = ""
        paren_level = 0

        for char in args_str:
            if char == '(' and paren_level == 0:
                current_arg += char
                paren_level += 1
            elif char == '(' and paren_level > 0:
                current_arg += char
                paren_level += 1
            elif char == ')' and paren_level > 1:
                current_arg += char
                paren_level -= 1
            elif char == ')' and paren_level == 1:
                current_arg += char
                paren_level -= 1
            elif char == ',' and paren_level == 0:
                args.append(self._parse_argument(current_arg.strip()))
                current_arg = ""
            else:
                current_arg += char

        # Add the last argument
        if current_arg:
            args.append(self._parse_argument(current_arg.strip()))

        return args

    def _parse_argument(self, arg: str) -> Any:
        """
        Parse a single argument

        Args:
            arg: The argument to parse

        Returns:
            The parsed argument value
        """
        # Check if it's a nested function
        if '(' in arg and ')' in arg:
            return self._parse_formula(arg)

        # Check if it's a column reference
        if arg in self.df.columns:
            return self.df[arg].values

        # Check if it's a number
        try:
            if '.' in arg:
                return float(arg)
            else:
                return int(arg)
        except ValueError:
            pass

        # Check if it's a string (remove quotes)
        if (arg.startswith('"') and arg.endswith('"')) or (arg.startswith("'") and arg.endswith("'")):
            return arg[1:-1]

        # Return as is (might be a boolean or other value)
        return arg

    def _evaluate_arithmetic(self, expression: str) -> Any:
        """
        Evaluate arithmetic expressions

        Args:
            expression: The arithmetic expression to evaluate

        Returns:
            The result of the evaluation
        """
        # Handle column references
        for col in self.df.columns:
            if col in expression:
                # Replace column name with its average value for simple arithmetic
                avg_value = self.df[col].mean()
                expression = expression.replace(col, str(avg_value))

        # Safely evaluate the expression
        try:
            # Use eval with restricted globals
            return eval(expression, {"__builtins__": {}}, {"np": np})
        except Exception as e:
            logging.error(f"Error evaluating expression '{expression}': {str(e)}")
            return expression

    # Basic math functions
    def excel_sum(self, *args) -> float:
        """SUM function: Returns the sum of values"""
        total = 0
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                total += np.sum(arg)
            elif isinstance(arg, (int, float)):
                total += arg
        return float(total)

    def excel_average(self, *args) -> float:
        """AVERAGE function: Returns the average of values"""
        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)):
                values.append(arg)

        if not values:
            return 0
        return float(np.mean(values))

    def excel_count(self, *args) -> int:
        """COUNT function: Returns the count of numeric values"""
        count = 0
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                count += np.sum([1 for x in arg if isinstance(x, (int, float)) and not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                count += 1
        return count

    def excel_max(self, *args) -> float:
        """MAX function: Returns the maximum value"""
        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if not values:
            return 0
        return float(np.max(values))

    def excel_min(self, *args) -> float:
        """MIN function: Returns the minimum value"""
        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if not values:
            return 0
        return float(np.min(values))

    def excel_round(self, number, decimals=0) -> float:
        """ROUND function: Rounds a number to a specified number of decimals"""
        if isinstance(number, (list, np.ndarray)):
            number = np.mean(number)
        return round(float(number), int(decimals))

    # Statistical functions
    def excel_stdev(self, *args) -> float:
        """STDEV function: Returns the standard deviation of values"""
        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if len(values) <= 1:
            return 0
        return float(np.std(values, ddof=1))

    def excel_median(self, *args) -> float:
        """MEDIAN function: Returns the median of values"""
        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if not values:
            return 0
        return float(np.median(values))

    def excel_percentile(self, array, percentile) -> float:
        """PERCENTILE function: Returns the k-th percentile of values"""
        if isinstance(array, (list, np.ndarray)):
            values = [x for x in array if not pd.isna(x)]
        else:
            values = [array]

        if not values:
            return 0

        k = float(percentile)
        if k < 0 or k > 1:
            raise ValueError("Percentile must be between 0 and 1")

        return float(np.percentile(values, k * 100))

    def excel_correlation(self, array1, array2) -> float:
        """CORREL function: Returns the correlation coefficient of two arrays"""
        if isinstance(array1, (list, np.ndarray)) and isinstance(array2, (list, np.ndarray)):
            # Filter out NaN values and ensure arrays have the same length
            mask = ~(np.isnan(array1) | np.isnan(array2))
            array1_filtered = np.array(array1)[mask]
            array2_filtered = np.array(array2)[mask]

            if len(array1_filtered) <= 1 or len(array2_filtered) <= 1:
                return 0

            return float(np.corrcoef(array1_filtered, array2_filtered)[0, 1])
        else:
            return 0

    # Logical functions
    def excel_if(self, condition, value_if_true, value_if_false) -> Any:
        """IF function: Returns one value if a condition is true and another value if it's false"""
        if isinstance(condition, str):
            # Evaluate the condition
            try:
                condition_result = eval(condition, {"__builtins__": {}}, {"np": np})
            except:
                condition_result = False
        else:
            condition_result = bool(condition)

        return value_if_true if condition_result else value_if_false

    def excel_and(self, *args) -> bool:
        """AND function: Returns TRUE if all arguments are TRUE"""
        return all(bool(arg) for arg in args)

    def excel_or(self, *args) -> bool:
        """OR function: Returns TRUE if any argument is TRUE"""
        return any(bool(arg) for arg in args)

    def excel_not(self, logical) -> bool:
        """NOT function: Reverses the value of its argument"""
        return not bool(logical)

    # Lookup functions
    def excel_vlookup(self, lookup_value, table_array, col_index, approximate_match=False) -> Any:
        """VLOOKUP function: Looks for a value in the first column of a table and returns a value in the same row from a column you specify"""
        if not isinstance(table_array, pd.DataFrame):
            if isinstance(table_array, str) and table_array in self.df.columns:
                # If table_array is a column name, use the entire DataFrame
                table_array = self.df
            else:
                raise ValueError("Table array must be a DataFrame or a column name")

        # Convert col_index to integer
        col_index = int(col_index)

        # Get the lookup column (first column)
        lookup_column = table_array.iloc[:, 0]

        if approximate_match:
            # Find the closest match
            idx = np.abs(lookup_column.values - lookup_value).argmin()
        else:
            # Find exact match
            matches = lookup_column[lookup_column == lookup_value].index
            if len(matches) == 0:
                return "N/A"
            idx = matches[0]

        # Return the value from the specified column
        if col_index <= len(table_array.columns):
            return table_array.iloc[idx, col_index - 1]
        else:
            return "N/A"

    def excel_hlookup(self, lookup_value, table_array, row_index, approximate_match=False) -> Any:
        """HLOOKUP function: Looks for a value in the top row of a table and returns a value in the same column from a row you specify"""
        if not isinstance(table_array, pd.DataFrame):
            if isinstance(table_array, str) and table_array in self.df.columns:
                # If table_array is a column name, use the entire DataFrame
                table_array = self.df
            else:
                raise ValueError("Table array must be a DataFrame or a column name")

        # Convert row_index to integer
        row_index = int(row_index)

        # Get the lookup row (first row)
        lookup_row = table_array.iloc[0, :]

        if approximate_match:
            # Find the closest match
            idx = np.abs(lookup_row.values - lookup_value).argmin()
        else:
            # Find exact match
            matches = lookup_row[lookup_row == lookup_value].index
            if len(matches) == 0:
                return "N/A"
            idx = matches[0]

        # Return the value from the specified row
        if row_index <= len(table_array):
            return table_array.iloc[row_index - 1, idx]
        else:
            return "N/A"

    def excel_index(self, array, row_num, col_num=None) -> Any:
        """INDEX function: Returns a value from a table based on the row and column numbers"""
        if isinstance(array, str) and array in self.df.columns:
            # If array is a column name, use the entire DataFrame
            array = self.df

        if not isinstance(array, pd.DataFrame):
            if isinstance(array, (list, np.ndarray)):
                # Convert to DataFrame
                array = pd.DataFrame(array)
            else:
                raise ValueError("Array must be a DataFrame, array, or column name")

        # Convert indices to integers
        row_num = int(row_num)

        if col_num is None:
            # Return the entire row
            if row_num <= len(array):
                return array.iloc[row_num - 1, :].values
            else:
                return "N/A"
        else:
            col_num = int(col_num)
            # Return the specific cell
            if row_num <= len(array) and col_num <= len(array.columns):
                return array.iloc[row_num - 1, col_num - 1]
            else:
                return "N/A"

    def excel_match(self, lookup_value, lookup_array, match_type=1) -> int:
        """MATCH function: Returns the position of a value in an array"""
        if isinstance(lookup_array, str) and lookup_array in self.df.columns:
            # If lookup_array is a column name, use that column
            lookup_array = self.df[lookup_array].values

        if not isinstance(lookup_array, (list, np.ndarray)):
            raise ValueError("Lookup array must be an array or column name")

        # Convert match_type to integer
        match_type = int(match_type)

        # Convert lookup_array to numpy array
        lookup_array = np.array(lookup_array)

        if match_type == 0:
            # Exact match
            matches = np.where(lookup_array == lookup_value)[0]
            if len(matches) == 0:
                return "N/A"
            return int(matches[0]) + 1  # 1-based index

        elif match_type == 1:
            # Largest value less than or equal to lookup_value
            if not np.all(np.diff(lookup_array) >= 0):
                raise ValueError("Array must be in ascending order for match_type=1")

            matches = np.where(lookup_array <= lookup_value)[0]
            if len(matches) == 0:
                return "N/A"
            return int(matches[-1]) + 1  # 1-based index

        elif match_type == -1:
            # Smallest value greater than or equal to lookup_value
            if not np.all(np.diff(lookup_array) <= 0):
                raise ValueError("Array must be in descending order for match_type=-1")

            matches = np.where(lookup_array >= lookup_value)[0]
            if len(matches) == 0:
                return "N/A"
            return int(matches[-1]) + 1  # 1-based index

        else:
            raise ValueError("match_type must be -1, 0, or 1")

    # Text functions
    def excel_concatenate(self, *args) -> str:
        """CONCATENATE function: Joins text strings into one string"""
        return "".join(str(arg) for arg in args)

    def excel_left(self, text, num_chars) -> str:
        """LEFT function: Returns the specified number of characters from the start of a text string"""
        text = str(text)
        num_chars = int(num_chars)
        return text[:num_chars]

    def excel_right(self, text, num_chars) -> str:
        """RIGHT function: Returns the specified number of characters from the end of a text string"""
        text = str(text)
        num_chars = int(num_chars)
        return text[-num_chars:]

    def excel_mid(self, text, start_num, num_chars) -> str:
        """MID function: Returns a specific number of characters from a text string, starting at the position you specify"""
        text = str(text)
        start_num = int(start_num)
        num_chars = int(num_chars)
        return text[start_num-1:start_num-1+num_chars]

    def excel_len(self, text) -> int:
        """LEN function: Returns the number of characters in a text string"""
        return len(str(text))

    def excel_find(self, find_text, within_text, start_num=1) -> int:
        """FIND function: Returns the position of a string within another string (case-sensitive)"""
        find_text = str(find_text)
        within_text = str(within_text)
        start_num = int(start_num)

        pos = within_text.find(find_text, start_num - 1)
        if pos == -1:
            return "N/A"
        return pos + 1  # 1-based index

    def excel_replace(self, old_text, start_num, num_chars, new_text) -> str:
        """REPLACE function: Replaces characters within a string"""
        old_text = str(old_text)
        start_num = int(start_num)
        num_chars = int(num_chars)
        new_text = str(new_text)

        return old_text[:start_num-1] + new_text + old_text[start_num-1+num_chars:]

    # Date functions
    def excel_year(self, date) -> int:
        """YEAR function: Returns the year component of a date"""
        if isinstance(date, str):
            try:
                date = pd.to_datetime(date)
            except:
                return "N/A"

        if pd.isna(date):
            return "N/A"

        return date.year

    def excel_month(self, date) -> int:
        """MONTH function: Returns the month component of a date"""
        if isinstance(date, str):
            try:
                date = pd.to_datetime(date)
            except:
                return "N/A"

        if pd.isna(date):
            return "N/A"

        return date.month

    def excel_day(self, date) -> int:
        """DAY function: Returns the day component of a date"""
        if isinstance(date, str):
            try:
                date = pd.to_datetime(date)
            except:
                return "N/A"

        if pd.isna(date):
            return "N/A"

        return date.day

    def excel_today(self) -> pd.Timestamp:
        """TODAY function: Returns the current date"""
        return pd.Timestamp.today().normalize()

    # Advanced analytics
    def excel_trend(self, known_y, known_x=None, new_x=None, const=True) -> Union[float, np.ndarray]:
        """TREND function: Returns values along a linear trend"""
        if isinstance(known_y, str) and known_y in self.df.columns:
            known_y = self.df[known_y].values

        if not isinstance(known_y, (list, np.ndarray)):
            raise ValueError("known_y must be an array or column name")

        # Convert to numpy arrays
        known_y = np.array(known_y, dtype=float)

        # Handle missing known_x
        if known_x is None:
            known_x = np.arange(len(known_y))
        elif isinstance(known_x, str) and known_x in self.df.columns:
            known_x = self.df[known_x].values

        known_x = np.array(known_x, dtype=float)

        # Handle missing new_x
        if new_x is None:
            new_x = known_x
        elif isinstance(new_x, str) and new_x in self.df.columns:
            new_x = self.df[new_x].values

        new_x = np.array(new_x, dtype=float)

        # Reshape arrays
        known_x = known_x.reshape(-1, 1)
        new_x = new_x.reshape(-1, 1)

        # Add constant term if requested
        if const:
            known_x = np.hstack([np.ones((known_x.shape[0], 1)), known_x])
            new_x = np.hstack([np.ones((new_x.shape[0], 1)), new_x])

        # Calculate coefficients using least squares
        try:
            coeffs = np.linalg.lstsq(known_x, known_y, rcond=None)[0]

            # Calculate trend values
            trend = np.dot(new_x, coeffs)

            if len(trend) == 1:
                return float(trend[0])
            else:
                return trend
        except:
            return "N/A"

    def excel_forecast(self, x, known_y, known_x) -> float:
        """FORECAST function: Predicts a future value based on existing values"""
        if isinstance(known_y, str) and known_y in self.df.columns:
            known_y = self.df[known_y].values

        if isinstance(known_x, str) and known_x in self.df.columns:
            known_x = self.df[known_x].values

        if not isinstance(known_y, (list, np.ndarray)) or not isinstance(known_x, (list, np.ndarray)):
            raise ValueError("known_y and known_x must be arrays or column names")

        # Convert to numpy arrays
        known_y = np.array(known_y, dtype=float)
        known_x = np.array(known_x, dtype=float)
        x = float(x)

        # Calculate forecast using linear regression
        try:
            slope = np.cov(known_x, known_y)[0, 1] / np.var(known_x)
            intercept = np.mean(known_y) - slope * np.mean(known_x)

            return float(slope * x + intercept)
        except:
            return "N/A"

    def excel_growth(self, known_y, known_x=None, new_x=None, const=True) -> Union[float, np.ndarray]:
        """GROWTH function: Returns values along an exponential trend"""
        if isinstance(known_y, str) and known_y in self.df.columns:
            known_y = self.df[known_y].values

        if not isinstance(known_y, (list, np.ndarray)):
            raise ValueError("known_y must be an array or column name")

        # Convert to numpy arrays and ensure positive values
        known_y = np.array(known_y, dtype=float)

        # Handle negative or zero values
        if np.any(known_y <= 0):
            return "N/A"  # Can't take log of negative or zero values

        # Take log of y values
        log_y = np.log(known_y)

        # Handle missing known_x
        if known_x is None:
            known_x = np.arange(len(known_y))
        elif isinstance(known_x, str) and known_x in self.df.columns:
            known_x = self.df[known_x].values

        known_x = np.array(known_x, dtype=float)

        # Handle missing new_x
        if new_x is None:
            new_x = known_x
        elif isinstance(new_x, str) and new_x in self.df.columns:
            new_x = self.df[new_x].values

        new_x = np.array(new_x, dtype=float)

        # Reshape arrays
        known_x = known_x.reshape(-1, 1)
        new_x = new_x.reshape(-1, 1)

        # Add constant term if requested
        if const:
            known_x = np.hstack([np.ones((known_x.shape[0], 1)), known_x])
            new_x = np.hstack([np.ones((new_x.shape[0], 1)), new_x])

        # Calculate coefficients using least squares
        try:
            coeffs = np.linalg.lstsq(known_x, log_y, rcond=None)[0]

            # Calculate trend values and exponentiate
            log_trend = np.dot(new_x, coeffs)
            trend = np.exp(log_trend)

            if len(trend) == 1:
                return float(trend[0])
            else:
                return trend
        except:
            return "N/A"

    def excel_rank(self, number, ref, order=0) -> int:
        """RANK function: Returns the rank of a number in a list of numbers"""
        if isinstance(ref, str) and ref in self.df.columns:
            ref = self.df[ref].values

        if not isinstance(ref, (list, np.ndarray)):
            raise ValueError("ref must be an array or column name")

        # Convert to numpy array
        ref = np.array(ref, dtype=float)
        number = float(number)
        order = int(order)

        # Sort values
        sorted_ref = np.sort(ref)
        if order != 0:
            # Descending order
            sorted_ref = sorted_ref[::-1]

        # Find rank
        rank = np.where(sorted_ref == number)[0]
        if len(rank) == 0:
            return "N/A"

        # Return 1-based rank
        return int(rank[0]) + 1

    # Financial functions
    def excel_npv(self, rate, *cash_flows) -> float:
        """NPV function: Calculates the net present value of an investment"""
        rate = float(rate)
        npv = 0

        for i, cf in enumerate(cash_flows):
            if isinstance(cf, (list, np.ndarray)):
                for j, val in enumerate(cf):
                    if pd.notna(val) and isinstance(val, (int, float)):
                        npv += val / ((1 + rate) ** (i + j + 1))
            elif pd.notna(cf) and isinstance(cf, (int, float)):
                npv += cf / ((1 + rate) ** (i + 1))

        return float(npv)

    def excel_pmt(self, rate, nper, pv, fv=0, type=0) -> float:
        """PMT function: Calculates the payment for a loan"""
        rate = float(rate)
        nper = float(nper)
        pv = float(pv)
        fv = float(fv)
        type = int(type)

        # Handle edge case
        if rate == 0:
            return -(pv + fv) / nper

        # Calculate payment
        pvif = (1 + rate) ** nper
        pmt = rate / (pvif - 1) * -(pv * pvif + fv)

        # Adjust for payments at beginning of period
        if type == 1:
            pmt = pmt / (1 + rate)

        return float(pmt)

    # Text functions
    def excel_proper(self, text) -> str:
        """PROPER function: Capitalizes the first letter of each word"""
        if not isinstance(text, str):
            text = str(text)

        return ' '.join(word.capitalize() for word in text.split())

    def excel_trim(self, text) -> str:
        """TRIM function: Removes extra spaces from text"""
        if not isinstance(text, str):
            text = str(text)

        # Replace multiple spaces with a single space
        return ' '.join(text.split())

    def excel_substitute(self, text, old_text, new_text, instance_num=None) -> str:
        """SUBSTITUTE function: Replaces occurrences of a text string"""
        if not isinstance(text, str):
            text = str(text)

        if not isinstance(old_text, str):
            old_text = str(old_text)

        if not isinstance(new_text, str):
            new_text = str(new_text)

        # If instance_num is provided, replace only that instance
        if instance_num is not None:
            instance_num = int(instance_num)
            if instance_num <= 0:
                return text

            parts = text.split(old_text)
            if instance_num >= len(parts):
                return text

            result = old_text.join(parts[:instance_num])
            result += new_text + old_text.join(parts[instance_num:])
            return result

        # Otherwise replace all instances
        return text.replace(old_text, new_text)

    # Conditional functions
    def excel_sumif(self, range_array, criteria, sum_range=None) -> float:
        """SUMIF function: Sums values that meet criteria"""
        if sum_range is None:
            sum_range = range_array

        # Convert to numpy arrays
        range_array = np.array(range_array, dtype=object)
        sum_range = np.array(sum_range, dtype=float)

        # Ensure arrays have the same shape
        if range_array.shape != sum_range.shape:
            raise ValueError("Range and sum_range must have the same shape")

        # Parse criteria
        if isinstance(criteria, str):
            if criteria.startswith('='):
                criteria = criteria[1:]  # Remove equals sign

            # Handle comparison operators
            if any(op in criteria for op in ['>', '<', '=']):
                # Extract operator and value
                for op in ['>=', '<=', '<>', '=', '>', '<']:
                    if op in criteria:
                        value = criteria.split(op)[1].strip()
                        try:
                            value = float(value)
                        except ValueError:
                            # Keep as string if not a number
                            pass

                        # Create comparison function
                        if op == '=':
                            compare = lambda x: x == value
                        elif op == '>':
                            compare = lambda x: x > value
                        elif op == '<':
                            compare = lambda x: x < value
                        elif op == '>=':
                            compare = lambda x: x >= value
                        elif op == '<=':
                            compare = lambda x: x <= value
                        elif op == '<>':
                            compare = lambda x: x != value

                        # Apply comparison
                        mask = np.array([compare(x) if pd.notna(x) else False for x in range_array])
                        return float(np.sum(sum_range[mask]))

            # Handle wildcard criteria
            if '*' in criteria or '?' in criteria:
                # Convert Excel wildcards to regex
                pattern = criteria.replace('*', '.*').replace('?', '.')
                regex = re.compile(f"^{pattern}$")

                # Apply regex matching
                mask = np.array([bool(regex.match(str(x))) if pd.notna(x) else False for x in range_array])
                return float(np.sum(sum_range[mask]))

            # Handle exact match with string
            mask = range_array == criteria
            return float(np.sum(sum_range[mask]))

        # Handle numeric criteria
        if isinstance(criteria, (int, float)):
            mask = range_array == criteria
            return float(np.sum(sum_range[mask]))

        return 0.0

    def excel_countif(self, range_array, criteria) -> int:
        """COUNTIF function: Counts cells that meet criteria"""
        # Convert to numpy array
        range_array = np.array(range_array, dtype=object)

        # Parse criteria
        if isinstance(criteria, str):
            if criteria.startswith('='):
                criteria = criteria[1:]  # Remove equals sign

            # Handle comparison operators
            if any(op in criteria for op in ['>', '<', '=']):
                # Extract operator and value
                for op in ['>=', '<=', '<>', '=', '>', '<']:
                    if op in criteria:
                        value = criteria.split(op)[1].strip()
                        try:
                            value = float(value)
                        except ValueError:
                            # Keep as string if not a number
                            pass

                        # Create comparison function
                        if op == '=':
                            compare = lambda x: x == value
                        elif op == '>':
                            compare = lambda x: x > value
                        elif op == '<':
                            compare = lambda x: x < value
                        elif op == '>=':
                            compare = lambda x: x >= value
                        elif op == '<=':
                            compare = lambda x: x <= value
                        elif op == '<>':
                            compare = lambda x: x != value

                        # Apply comparison
                        count = sum(1 for x in range_array if pd.notna(x) and compare(x))
                        return count

            # Handle wildcard criteria
            if '*' in criteria or '?' in criteria:
                # Convert Excel wildcards to regex
                pattern = criteria.replace('*', '.*').replace('?', '.')
                regex = re.compile(f"^{pattern}$")

                # Apply regex matching
                count = sum(1 for x in range_array if pd.notna(x) and bool(regex.match(str(x))))
                return count

            # Handle exact match with string
            return np.sum(range_array == criteria)

        # Handle numeric criteria
        if isinstance(criteria, (int, float)):
            return np.sum(range_array == criteria)

        return 0

    def excel_averageif(self, range_array, criteria, average_range=None) -> float:
        """AVERAGEIF function: Averages values that meet criteria"""
        if average_range is None:
            average_range = range_array

        # Use SUMIF to get the sum of values that meet criteria
        sum_result = self.excel_sumif(range_array, criteria, average_range)

        # Use COUNTIF to get the count of values that meet criteria
        count_result = self.excel_countif(range_array, criteria)

        # Avoid division by zero
        if count_result == 0:
            return 0.0

        return sum_result / count_result

    def excel_variance(self, *args) -> float:
        """VAR function: Calculates the variance of a sample"""
        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if len(values) <= 1:
            return 0
        return float(np.var(values, ddof=1))

    def excel_skew(self, *args) -> float:
        """SKEW function: Calculates the skewness of a distribution"""
        from scipy import stats

        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if len(values) <= 2:
            return 0
        return float(stats.skew(values))

    def excel_kurtosis(self, *args) -> float:
        """KURT function: Calculates the kurtosis of a dataset"""
        from scipy import stats

        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if len(values) <= 3:
            return 0
        return float(stats.kurtosis(values))

    def excel_quartile(self, array, quart) -> float:
        """QUARTILE function: Returns the quartile of a dataset"""
        if isinstance(array, (list, np.ndarray)):
            values = [x for x in array if not pd.isna(x)]
        else:
            values = [array]

        if not values:
            return 0

        quart = int(quart)
        if quart < 0 or quart > 4:
            raise ValueError("Quartile must be between 0 and 4")

        # Map quartile number to percentile
        percentile = quart * 25
        return float(np.percentile(values, percentile))

    def excel_mode(self, *args) -> float:
        """MODE function: Returns the most frequently occurring value"""
        from scipy import stats

        values = []
        for arg in args:
            if isinstance(arg, (list, np.ndarray)):
                values.extend([x for x in arg if not pd.isna(x)])
            elif isinstance(arg, (int, float)) and not pd.isna(arg):
                values.append(arg)

        if not values:
            return 0

        # Find the mode
        mode_result = stats.mode(values)

        # scipy.stats.mode returns a ModeResult object
        # In newer versions, it has a mode attribute
        if hasattr(mode_result, 'mode'):
            return float(mode_result.mode[0])
        # In older versions, the first element is the mode
        else:
            return float(mode_result[0][0])

    def excel_zscore(self, x, array) -> float:
        """ZSCORE function: Returns the z-score for a value in a distribution"""
        if isinstance(array, (list, np.ndarray)):
            values = [val for val in array if not pd.isna(val)]
        else:
            values = [array]

        if not values:
            return 0

        x = float(x)
        mean = np.mean(values)
        std = np.std(values, ddof=1)

        if std == 0:
            return 0

        return float((x - mean) / std)

    def excel_iferror(self, value, value_if_error) -> Any:
        """IFERROR function: Returns a value if an expression errors, otherwise returns the expression result"""
        try:
            # Try to evaluate the value
            if isinstance(value, str) and value.startswith('='):
                result = self.execute_formula(value)
            else:
                result = value

            # Check if result is an error
            if isinstance(result, str) and (result == "N/A" or result.startswith("Error")):
                return value_if_error

            return result
        except:
            return value_if_error

    def excel_ifna(self, value, value_if_na) -> Any:
        """IFNA function: Returns a value if the expression is #N/A, otherwise returns the expression result"""
        try:
            # Try to evaluate the value
            if isinstance(value, str) and value.startswith('='):
                result = self.execute_formula(value)
            else:
                result = value

            # Check if result is N/A
            if result == "N/A":
                return value_if_na

            return result
        except:
            return value_if_na

    def excel_upper(self, text) -> str:
        """UPPER function: Converts text to uppercase"""
        if not isinstance(text, str):
            text = str(text)

        return text.upper()

    def excel_lower(self, text) -> str:
        """LOWER function: Converts text to lowercase"""
        if not isinstance(text, str):
            text = str(text)

        return text.lower()

    def excel_text(self, value, format_text) -> str:
        """TEXT function: Formats a number and converts it to text"""
        # This is a simplified version that handles only basic formats
        if not isinstance(value, (int, float)):
            try:
                value = float(value)
            except:
                return str(value)

        # Handle basic format strings
        if format_text == "0":
            return str(int(value))
        elif format_text == "0.00":
            return f"{value:.2f}"
        elif format_text == "0.0000":
            return f"{value:.4f}"
        elif format_text == "#,##0":
            return f"{int(value):,}"
        elif format_text == "#,##0.00":
            return f"{value:,.2f}"
        elif format_text == "0%":
            return f"{int(value * 100)}%"
        elif format_text == "0.00%":
            return f"{value * 100:.2f}%"
        elif format_text == "$#,##0.00":
            return f"${value:,.2f}"
        else:
            # Default to string representation
            return str(value)
