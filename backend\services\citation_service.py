"""
Professional Citation and Source Verification Service
Handles citation tracking, verification, and compliance for production RAG systems
"""

import json
import time
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from models import DocumentChunk, SourceAttribution, Query, User


class CitationService:
    """
    Production-grade citation tracking and verification service
    Implements enterprise requirements for source attribution and compliance
    """
    
    def __init__(self):
        self.confidence_threshold = 0.7
        self.hallucination_keywords = [
            'i think', 'i believe', 'probably', 'maybe', 'might be',
            'it seems', 'appears to', 'could be', 'possibly'
        ]
    
    def create_source_attribution(self, query_id: int, chunk_id: int, 
                                relevance_score: float, quoted_text: str,
                                search_method: str, rank_position: int,
                                db: Session) -> Dict[str, Any]:
        """
        Create a comprehensive source attribution record with verification
        """
        try:
            # Get chunk for context
            chunk = db.query(DocumentChunk).filter(DocumentChunk.id == chunk_id).first()
            if not chunk:
                return {"error": "Chunk not found"}
            
            # Calculate confidence score based on multiple factors
            confidence_score = self._calculate_confidence_score(
                relevance_score, quoted_text, chunk.text, search_method
            )
            
            # Extract context around quoted text
            context_before, context_after = self._extract_context(
                chunk.text, quoted_text
            )
            
            # Create attribution record
            attribution = SourceAttribution(
                query_id=query_id,
                chunk_id=chunk_id,
                relevance_score=relevance_score,
                confidence_score=confidence_score,
                rank_position=rank_position,
                quoted_text=quoted_text,
                context_before=context_before,
                context_after=context_after,
                search_method=search_method,
                search_query="",  # Will be filled from query
                human_verified=False,
                verification_notes=None
            )
            
            db.add(attribution)
            db.commit()
            db.refresh(attribution)
            
            return {
                "success": True,
                "attribution_id": attribution.id,
                "confidence_score": confidence_score,
                "verification_required": confidence_score < self.confidence_threshold
            }
            
        except Exception as e:
            db.rollback()
            return {"error": f"Failed to create attribution: {str(e)}"}
    
    def verify_citation_accuracy(self, attribution_id: int, user_id: str,
                                verification_notes: str, is_accurate: bool,
                                db: Session) -> Dict[str, Any]:
        """
        Human verification of citation accuracy for compliance
        """
        try:
            attribution = db.query(SourceAttribution).filter(
                SourceAttribution.id == attribution_id
            ).first()
            
            if not attribution:
                return {"error": "Attribution not found"}
            
            # Update verification status
            attribution.human_verified = True
            attribution.verification_notes = json.dumps({
                "verified_by": user_id,
                "verified_at": datetime.now(timezone.utc).isoformat(),
                "is_accurate": is_accurate,
                "notes": verification_notes,
                "original_confidence": attribution.confidence_score
            })
            
            # Adjust confidence based on verification
            if is_accurate:
                attribution.confidence_score = min(1.0, attribution.confidence_score + 0.1)
            else:
                attribution.confidence_score = max(0.0, attribution.confidence_score - 0.2)
            
            db.commit()
            
            return {
                "success": True,
                "verified": True,
                "new_confidence": attribution.confidence_score
            }
            
        except Exception as e:
            db.rollback()
            return {"error": f"Failed to verify citation: {str(e)}"}
    
    def detect_hallucination_indicators(self, response_text: str,
                                      source_chunks: List[str]) -> Dict[str, Any]:
        """
        Detect potential hallucination in AI responses
        """
        indicators = {
            "uncertainty_phrases": 0,
            "unsupported_claims": 0,
            "confidence_level": "high",
            "risk_score": 0.0,
            "details": []
        }

        # Ensure response_text is a string
        if not isinstance(response_text, str):
            response_text = str(response_text)

        response_lower = response_text.lower()
        
        # Check for uncertainty phrases
        for phrase in self.hallucination_keywords:
            if phrase in response_lower:
                indicators["uncertainty_phrases"] += 1
                indicators["details"].append(f"Uncertainty phrase detected: '{phrase}'")
        
        # Check for claims not supported by sources
        response_sentences = self._split_sentences(response_text)
        source_text = " ".join(source_chunks).lower()
        
        for sentence in response_sentences:
            if len(sentence.split()) > 5:  # Only check substantial sentences
                key_terms = self._extract_key_terms(sentence)
                supported = any(term.lower() in source_text for term in key_terms)
                
                if not supported and not any(phrase in sentence.lower() for phrase in self.hallucination_keywords):
                    indicators["unsupported_claims"] += 1
                    indicators["details"].append(f"Potentially unsupported claim: '{sentence[:100]}...'")
        
        # Calculate risk score
        risk_score = (
            indicators["uncertainty_phrases"] * 0.1 +
            indicators["unsupported_claims"] * 0.3
        )
        
        indicators["risk_score"] = min(1.0, risk_score)
        
        # Determine confidence level
        if risk_score > 0.7:
            indicators["confidence_level"] = "low"
        elif risk_score > 0.3:
            indicators["confidence_level"] = "medium"
        else:
            indicators["confidence_level"] = "high"
        
        return indicators
    
    def generate_audit_trail(self, query_id: int, db: Session) -> Dict[str, Any]:
        """
        Generate comprehensive audit trail for compliance
        """
        try:
            # Get query details
            query = db.query(Query).filter(Query.id == query_id).first()
            if not query:
                return {"error": "Query not found"}
            
            # Get all attributions for this query
            attributions = db.query(SourceAttribution).filter(
                SourceAttribution.query_id == query_id
            ).order_by(SourceAttribution.rank_position).all()
            
            # Build audit trail
            audit_trail = {
                "query_id": query_id,
                "query_text": query.question,
                "user_id": query.user_id,
                "timestamp": query.created_at.isoformat() if query.created_at else None,
                "dataset_id": query.dataset_id,
                "total_sources": len(attributions),
                "verified_sources": sum(1 for a in attributions if a.human_verified),
                "average_confidence": sum(a.confidence_score for a in attributions) / len(attributions) if attributions else 0,
                "sources": []
            }
            
            for attribution in attributions:
                # Get chunk details
                chunk = db.query(DocumentChunk).filter(
                    DocumentChunk.id == attribution.chunk_id
                ).first()
                
                source_info = {
                    "attribution_id": attribution.id,
                    "rank": attribution.rank_position,
                    "relevance_score": attribution.relevance_score,
                    "confidence_score": attribution.confidence_score,
                    "search_method": attribution.search_method,
                    "quoted_text": attribution.quoted_text,
                    "human_verified": attribution.human_verified,
                    "verification_notes": attribution.verification_notes,
                    "source_document": chunk.source_document if chunk else None,
                    "page_number": chunk.page_number if chunk else None,
                    "line_range": f"{chunk.line_start}-{chunk.line_end}" if chunk and chunk.line_start else None,
                    "section": chunk.section_title if chunk else None
                }
                
                audit_trail["sources"].append(source_info)
            
            return audit_trail
            
        except Exception as e:
            return {"error": f"Failed to generate audit trail: {str(e)}"}
    
    def get_compliance_report(self, dataset_id: int, start_date: datetime,
                            end_date: datetime, db: Session) -> Dict[str, Any]:
        """
        Generate compliance report for regulatory requirements
        """
        try:
            # Get queries in date range
            queries = db.query(Query).filter(
                and_(
                    Query.dataset_id == dataset_id,
                    Query.created_at >= start_date,
                    Query.created_at <= end_date
                )
            ).all()
            
            if not queries:
                return {"error": "No queries found in date range"}
            
            query_ids = [q.id for q in queries]
            
            # Get attribution statistics
            attribution_stats = db.query(
                func.count(SourceAttribution.id).label('total_attributions'),
                func.count(SourceAttribution.id).filter(SourceAttribution.human_verified == True).label('verified_attributions'),
                func.avg(SourceAttribution.confidence_score).label('avg_confidence'),
                func.count(SourceAttribution.id).filter(SourceAttribution.confidence_score < 0.7).label('low_confidence_attributions')
            ).filter(SourceAttribution.query_id.in_(query_ids)).first()
            
            # Calculate compliance metrics
            total_queries = len(queries)
            queries_with_sources = db.query(Query.id).filter(
                and_(
                    Query.id.in_(query_ids),
                    Query.id.in_(
                        db.query(SourceAttribution.query_id).filter(
                            SourceAttribution.query_id.in_(query_ids)
                        )
                    )
                )
            ).count()
            
            compliance_report = {
                "dataset_id": dataset_id,
                "report_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "query_metrics": {
                    "total_queries": total_queries,
                    "queries_with_sources": queries_with_sources,
                    "source_attribution_rate": (queries_with_sources / total_queries * 100) if total_queries > 0 else 0
                },
                "attribution_metrics": {
                    "total_attributions": attribution_stats.total_attributions or 0,
                    "verified_attributions": attribution_stats.verified_attributions or 0,
                    "verification_rate": ((attribution_stats.verified_attributions or 0) / (attribution_stats.total_attributions or 1) * 100),
                    "average_confidence": round(attribution_stats.avg_confidence or 0, 3),
                    "low_confidence_count": attribution_stats.low_confidence_attributions or 0
                },
                "compliance_status": "compliant" if (queries_with_sources / total_queries) > 0.95 else "needs_attention",
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            return compliance_report
            
        except Exception as e:
            return {"error": f"Failed to generate compliance report: {str(e)}"}
    
    def _calculate_confidence_score(self, relevance_score: float, quoted_text: str,
                                  source_text: str, search_method: str) -> float:
        """Calculate confidence score based on multiple factors"""
        confidence = relevance_score
        
        # Boost confidence for exact matches
        if quoted_text.lower() in source_text.lower():
            confidence += 0.1
        
        # Adjust based on search method
        if search_method == 'semantic':
            confidence += 0.05
        elif search_method == 'hybrid':
            confidence += 0.1
        
        # Penalize very short quotes
        if len(quoted_text.split()) < 5:
            confidence -= 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def _extract_context(self, source_text: str, quoted_text: str,
                        context_chars: int = 200) -> Tuple[str, str]:
        """Extract context before and after quoted text"""
        try:
            start_pos = source_text.lower().find(quoted_text.lower())
            if start_pos == -1:
                return "", ""
            
            context_start = max(0, start_pos - context_chars)
            context_end = min(len(source_text), start_pos + len(quoted_text) + context_chars)
            
            context_before = source_text[context_start:start_pos].strip()
            context_after = source_text[start_pos + len(quoted_text):context_end].strip()
            
            return context_before, context_after
            
        except Exception:
            return "", ""
    
    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        import re
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _extract_key_terms(self, sentence: str) -> List[str]:
        """Extract key terms from a sentence"""
        import re
        # Simple extraction - can be enhanced with NLP
        words = re.findall(r'\b\w+\b', sentence.lower())
        # Filter out common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'}
        return [word for word in words if word not in stop_words and len(word) > 3]
