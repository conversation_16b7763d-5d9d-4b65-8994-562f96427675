#!/bin/bash

# AIthentiq Database Setup Script
# This script helps you set up the database for the predictive analytics system

echo "🚀 AIthentiq Database Setup Script"
echo "=================================="
echo ""

# Check if PostgreSQL is available
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL (psql) is not installed or not in PATH"
    echo "Please install PostgreSQL and try again"
    exit 1
fi

# Default values
DEFAULT_HOST="localhost"
DEFAULT_PORT="5432"
DEFAULT_DATABASE="aithentiq"
DEFAULT_USER="postgres"

echo "📋 Database Configuration"
echo "========================"

# Get database connection details
read -p "Database host [$DEFAULT_HOST]: " DB_HOST
DB_HOST=${DB_HOST:-$DEFAULT_HOST}

read -p "Database port [$DEFAULT_PORT]: " DB_PORT
DB_PORT=${DB_PORT:-$DEFAULT_PORT}

read -p "Database name [$DEFAULT_DATABASE]: " DB_NAME
DB_NAME=${DB_NAME:-$DEFAULT_DATABASE}

read -p "Database user [$DEFAULT_USER]: " DB_USER
DB_USER=${DB_USER:-$DEFAULT_USER}

read -s -p "Database password: " DB_PASSWORD
echo ""
echo ""

# Construct connection string
export PGPASSWORD="$DB_PASSWORD"

echo "🔗 Testing database connection..."
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
    echo "✅ Database connection successful!"
else
    echo "❌ Failed to connect to database"
    echo "Please check your connection details and try again"
    exit 1
fi

echo ""
echo "📊 Running database migration..."
echo "==============================="

# Run the migration script
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f database_migration.sql; then
    echo ""
    echo "✅ Database migration completed successfully!"
    echo ""
    echo "🎉 Your AIthentiq database is now ready for predictive analytics!"
    echo ""
    echo "📋 Summary of created tables:"
    echo "  • predictive_models - Store trained ML models"
    echo "  • predictive_predictions - Store prediction results"
    echo "  • time_series_forecasts - Store forecasting results"
    echo "  • anomaly_detections - Store anomaly detection results"
    echo "  • background_jobs - Handle async processing"
    echo ""
    echo "🔧 Next steps:"
    echo "  1. Update your backend .env file with database credentials"
    echo "  2. Start your backend server: cd backend && uvicorn main:app --reload --port 8000"
    echo "  3. Start your frontend server: cd frontend && npm run dev"
    echo "  4. Visit http://localhost:3000/dashboard/predictive to test the features"
    echo ""
else
    echo ""
    echo "❌ Database migration failed!"
    echo "Please check the error messages above and try again"
    exit 1
fi

# Clean up
unset PGPASSWORD

echo "🔒 Database setup completed securely!"
