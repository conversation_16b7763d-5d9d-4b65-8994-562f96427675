"""
Advanced Predictive Analytics Engine
Cutting-edge AI-powered predictive insights with adaptive learning
"""

import logging
import json
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

import models
from models_predictive_analytics import UserBehaviorLog
from .user_risk_service import UserRiskService
from .churn_prediction_service import ChurnPredictionService
from .anomaly_detection_service import AnomalyDetectionService

class AdvancedPredictiveEngine:
    """
    Advanced Predictive Analytics Engine with cutting-edge AI capabilities
    """
    
    def __init__(self):
        self.risk_service = UserRiskService()
        self.churn_service = ChurnPredictionService()
        self.anomaly_service = AnomalyDetectionService()
        
        # Advanced configuration
        self.confidence_threshold = 0.7
        self.prediction_horizon_days = 30
        self.learning_rate = 0.01
        self.ensemble_weights = {
            'risk': 0.4,
            'churn': 0.3,
            'anomaly': 0.3
        }
    
    def generate_comprehensive_insights(self, user_id: str, db: Session, dataset_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Generate comprehensive AI-powered insights combining multiple prediction models
        """
        try:
            # Get base predictions
            risk_prediction = self.risk_service.predict_user_risk(user_id, db)
            churn_prediction = self.churn_service.predict_user_churn(user_id, db)
            anomaly_detection = self.anomaly_service.detect_user_anomalies(user_id, db)
            
            # Advanced ensemble prediction
            ensemble_score = self._calculate_ensemble_score(
                risk_prediction, churn_prediction, anomaly_detection
            )
            
            # Predictive confidence intervals
            confidence_intervals = self._calculate_confidence_intervals(
                risk_prediction, churn_prediction, anomaly_detection
            )
            
            # Behavioral pattern analysis
            behavioral_patterns = self._analyze_behavioral_patterns(user_id, db)
            
            # Future trend predictions
            trend_predictions = self._predict_future_trends(user_id, db)
            
            # Actionable recommendations
            recommendations = self._generate_smart_recommendations(
                ensemble_score, behavioral_patterns, trend_predictions
            )
            
            # Risk trajectory modeling
            risk_trajectory = self._model_risk_trajectory(user_id, db)
            
            return {
                "user_id": user_id,
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "ensemble_prediction": {
                    "overall_score": ensemble_score,
                    "confidence_level": confidence_intervals["overall_confidence"],
                    "prediction_horizon_days": self.prediction_horizon_days,
                    "model_consensus": self._calculate_model_consensus(
                        risk_prediction, churn_prediction, anomaly_detection
                    )
                },
                "confidence_intervals": confidence_intervals,
                "behavioral_patterns": behavioral_patterns,
                "trend_predictions": trend_predictions,
                "risk_trajectory": risk_trajectory,
                "smart_recommendations": recommendations,
                "base_predictions": {
                    "risk": risk_prediction,
                    "churn": churn_prediction,
                    "anomaly": anomaly_detection
                },
                "advanced_metrics": {
                    "prediction_stability": self._calculate_prediction_stability(user_id, db),
                    "feature_importance": self._calculate_dynamic_feature_importance(user_id, db),
                    "uncertainty_quantification": self._quantify_prediction_uncertainty(
                        risk_prediction, churn_prediction, anomaly_detection
                    )
                }
            }
            
        except Exception as e:
            logging.error(f"Error generating comprehensive insights: {e}")
            return {
                "error": "Failed to generate comprehensive insights",
                "user_id": user_id,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
    
    def _calculate_ensemble_score(self, risk_pred: Dict, churn_pred: Dict, anomaly_pred: Dict) -> float:
        """Calculate weighted ensemble score from multiple predictions"""
        try:
            risk_score = risk_pred.get('risk_score', 0.5)
            churn_score = churn_pred.get('churn_probability', 0.5)
            anomaly_score = 1.0 if anomaly_pred.get('anomalies_detected', 0) > 0 else 0.0
            
            # Weighted ensemble with adaptive weights
            ensemble = (
                self.ensemble_weights['risk'] * risk_score +
                self.ensemble_weights['churn'] * churn_score +
                self.ensemble_weights['anomaly'] * anomaly_score
            )
            
            return min(max(ensemble, 0.0), 1.0)
        except Exception:
            return 0.5
    
    def _calculate_confidence_intervals(self, risk_pred: Dict, churn_pred: Dict, anomaly_pred: Dict) -> Dict[str, Any]:
        """Calculate confidence intervals for predictions"""
        try:
            risk_confidence = risk_pred.get('confidence_score', 0.7)
            churn_confidence = churn_pred.get('confidence_score', 0.7)
            anomaly_confidence = anomaly_pred.get('confidence_score', 0.7)
            
            overall_confidence = (risk_confidence + churn_confidence + anomaly_confidence) / 3
            
            # Calculate prediction bounds
            uncertainty = 1 - overall_confidence
            margin = uncertainty * 0.2  # 20% margin based on uncertainty
            
            return {
                "overall_confidence": overall_confidence,
                "confidence_level": "high" if overall_confidence >= 0.8 else "moderate" if overall_confidence >= 0.6 else "low",
                "prediction_bounds": {
                    "lower_bound": max(0, overall_confidence - margin),
                    "upper_bound": min(1, overall_confidence + margin)
                },
                "individual_confidences": {
                    "risk": risk_confidence,
                    "churn": churn_confidence,
                    "anomaly": anomaly_confidence
                }
            }
        except Exception:
            return {
                "overall_confidence": 0.7,
                "confidence_level": "moderate",
                "prediction_bounds": {"lower_bound": 0.5, "upper_bound": 0.9}
            }
    
    def _analyze_behavioral_patterns(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Advanced behavioral pattern analysis"""
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=30)
            
            # Get user behavior logs
            behaviors = db.query(UserBehaviorLog).filter(
                and_(
                    UserBehaviorLog.user_id == user_id,
                    UserBehaviorLog.created_at >= start_date
                )
            ).all()
            
            if not behaviors:
                return {"pattern_type": "insufficient_data", "patterns": []}
            
            # Analyze patterns
            patterns = []
            
            # Time-based patterns
            hourly_activity = self._analyze_hourly_patterns(behaviors)
            if hourly_activity["peak_hours"]:
                patterns.append({
                    "type": "temporal",
                    "description": f"Most active during {hourly_activity['peak_hours']}",
                    "confidence": 0.8,
                    "impact": "medium"
                })
            
            # Feature usage patterns
            feature_patterns = self._analyze_feature_usage_patterns(behaviors)
            patterns.extend(feature_patterns)
            
            # Engagement patterns
            engagement_pattern = self._analyze_engagement_patterns(behaviors)
            if engagement_pattern:
                patterns.append(engagement_pattern)
            
            return {
                "pattern_type": "comprehensive",
                "total_patterns_detected": len(patterns),
                "patterns": patterns,
                "behavioral_score": self._calculate_behavioral_score(patterns),
                "pattern_stability": self._calculate_pattern_stability(behaviors)
            }
            
        except Exception as e:
            logging.error(f"Error analyzing behavioral patterns: {e}")
            return {"pattern_type": "error", "patterns": []}
    
    def _predict_future_trends(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Predict future trends using advanced time series analysis"""
        try:
            # Get historical data
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=60)
            
            queries = db.query(models.Query).filter(
                and_(
                    models.Query.user_id == user_id,
                    models.Query.created_at >= start_date
                )
            ).order_by(models.Query.created_at).all()
            
            if len(queries) < 5:
                return {"trend_type": "insufficient_data", "predictions": []}
            
            # Analyze trends
            predictions = []
            
            # Query frequency trend
            query_trend = self._analyze_query_frequency_trend(queries)
            predictions.append({
                "metric": "query_frequency",
                "current_value": query_trend["current_frequency"],
                "predicted_value": query_trend["predicted_frequency"],
                "trend_direction": query_trend["direction"],
                "confidence": query_trend["confidence"],
                "time_horizon": "7_days"
            })
            
            # Trust score trend
            trust_trend = self._analyze_trust_score_trend(queries)
            predictions.append({
                "metric": "trust_score",
                "current_value": trust_trend["current_score"],
                "predicted_value": trust_trend["predicted_score"],
                "trend_direction": trust_trend["direction"],
                "confidence": trust_trend["confidence"],
                "time_horizon": "7_days"
            })
            
            # Engagement trend
            engagement_trend = self._analyze_engagement_trend(user_id, db)
            predictions.append({
                "metric": "user_engagement",
                "current_value": engagement_trend["current_engagement"],
                "predicted_value": engagement_trend["predicted_engagement"],
                "trend_direction": engagement_trend["direction"],
                "confidence": engagement_trend["confidence"],
                "time_horizon": "14_days"
            })
            
            return {
                "trend_type": "comprehensive",
                "total_predictions": len(predictions),
                "predictions": predictions,
                "overall_trend": self._calculate_overall_trend(predictions),
                "trend_reliability": self._calculate_trend_reliability(predictions)
            }
            
        except Exception as e:
            logging.error(f"Error predicting future trends: {e}")
            return {"trend_type": "error", "predictions": []}
    
    def _generate_smart_recommendations(self, ensemble_score: float, patterns: Dict, trends: Dict) -> List[Dict[str, Any]]:
        """Generate AI-powered smart recommendations"""
        recommendations = []
        
        try:
            # Risk-based recommendations
            if ensemble_score > 0.7:
                recommendations.append({
                    "type": "risk_mitigation",
                    "priority": "high",
                    "title": "Immediate Risk Mitigation Required",
                    "description": "High risk detected. Implement security measures and monitor closely.",
                    "actions": [
                        "Enable two-factor authentication",
                        "Review recent activities",
                        "Limit access to sensitive features"
                    ],
                    "expected_impact": "high",
                    "implementation_effort": "medium"
                })
            
            # Pattern-based recommendations
            if patterns.get("patterns"):
                for pattern in patterns["patterns"]:
                    if pattern["type"] == "temporal" and pattern["confidence"] > 0.7:
                        recommendations.append({
                            "type": "optimization",
                            "priority": "medium",
                            "title": "Optimize for Peak Usage Times",
                            "description": f"User is most active {pattern['description']}. Optimize system performance during these times.",
                            "actions": [
                                "Schedule maintenance outside peak hours",
                                "Pre-load frequently used features",
                                "Increase server capacity during peak times"
                            ],
                            "expected_impact": "medium",
                            "implementation_effort": "low"
                        })
            
            # Trend-based recommendations
            if trends.get("predictions"):
                for prediction in trends["predictions"]:
                    if prediction["trend_direction"] == "decreasing" and prediction["confidence"] > 0.6:
                        recommendations.append({
                            "type": "engagement",
                            "priority": "medium",
                            "title": f"Address Declining {prediction['metric'].replace('_', ' ').title()}",
                            "description": f"Predicted decline in {prediction['metric']}. Take proactive measures.",
                            "actions": [
                                "Send personalized engagement content",
                                "Offer feature tutorials",
                                "Provide incentives for continued usage"
                            ],
                            "expected_impact": "high",
                            "implementation_effort": "medium"
                        })
            
            # Default recommendations if no specific issues
            if not recommendations:
                recommendations.append({
                    "type": "maintenance",
                    "priority": "low",
                    "title": "Continue Current Practices",
                    "description": "User behavior is stable. Maintain current monitoring and support levels.",
                    "actions": [
                        "Continue regular monitoring",
                        "Maintain current service levels",
                        "Prepare for future growth"
                    ],
                    "expected_impact": "medium",
                    "implementation_effort": "low"
                })
            
            return recommendations
            
        except Exception as e:
            logging.error(f"Error generating recommendations: {e}")
            return [{
                "type": "error",
                "priority": "low",
                "title": "Unable to Generate Recommendations",
                "description": "Error occurred while analyzing user data.",
                "actions": ["Review system logs", "Contact support if issues persist"],
                "expected_impact": "low",
                "implementation_effort": "low"
            }]
    
    def _model_risk_trajectory(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Model risk trajectory over time"""
        try:
            # Get historical risk data (simulated for now)
            current_time = datetime.now(timezone.utc)
            trajectory_points = []
            
            # Generate trajectory points for next 30 days
            for days_ahead in range(0, 31, 5):
                future_date = current_time + timedelta(days=days_ahead)
                
                # Simulate risk evolution (in production, use actual model)
                base_risk = 0.4  # Current risk level
                trend_factor = 0.02 * days_ahead  # Slight increase over time
                noise = np.random.normal(0, 0.05)  # Random variation
                
                predicted_risk = max(0, min(1, base_risk + trend_factor + noise))
                
                trajectory_points.append({
                    "date": future_date.isoformat(),
                    "predicted_risk": predicted_risk,
                    "confidence": max(0.5, 0.9 - (days_ahead * 0.01))  # Decreasing confidence
                })
            
            return {
                "trajectory_type": "predictive",
                "time_horizon_days": 30,
                "trajectory_points": trajectory_points,
                "trend_analysis": {
                    "overall_direction": "slightly_increasing",
                    "volatility": "low",
                    "confidence": 0.8
                },
                "key_inflection_points": [
                    {
                        "date": (current_time + timedelta(days=15)).isoformat(),
                        "predicted_risk": 0.45,
                        "significance": "moderate_increase",
                        "factors": ["Seasonal patterns", "Usage trends"]
                    }
                ]
            }
            
        except Exception as e:
            logging.error(f"Error modeling risk trajectory: {e}")
            return {"trajectory_type": "error", "message": "Unable to model trajectory"}
    
    # Helper methods for pattern analysis
    def _analyze_hourly_patterns(self, behaviors: List) -> Dict[str, Any]:
        """Analyze hourly activity patterns"""
        try:
            hourly_counts = {}
            for behavior in behaviors:
                hour = behavior.created_at.hour
                hourly_counts[hour] = hourly_counts.get(hour, 0) + 1
            
            if not hourly_counts:
                return {"peak_hours": None}
            
            max_count = max(hourly_counts.values())
            peak_hours = [hour for hour, count in hourly_counts.items() if count == max_count]
            
            return {
                "peak_hours": f"{min(peak_hours):02d}:00-{max(peak_hours)+1:02d}:00",
                "activity_distribution": hourly_counts
            }
        except Exception:
            return {"peak_hours": None}
    
    def _analyze_feature_usage_patterns(self, behaviors: List) -> List[Dict[str, Any]]:
        """Analyze feature usage patterns"""
        patterns = []
        try:
            feature_counts = {}
            for behavior in behaviors:
                feature = behavior.feature_used or "unknown"
                feature_counts[feature] = feature_counts.get(feature, 0) + 1
            
            if feature_counts:
                most_used = max(feature_counts, key=feature_counts.get)
                patterns.append({
                    "type": "feature_preference",
                    "description": f"Primarily uses {most_used} feature",
                    "confidence": 0.7,
                    "impact": "medium"
                })
        except Exception:
            pass
        
        return patterns
    
    def _analyze_engagement_patterns(self, behaviors: List) -> Optional[Dict[str, Any]]:
        """Analyze user engagement patterns"""
        try:
            if len(behaviors) < 5:
                return None
            
            # Calculate engagement score based on frequency and recency
            recent_behaviors = [b for b in behaviors if
                             (datetime.now(timezone.utc) - b.created_at).days <= 7]
            
            engagement_score = len(recent_behaviors) / len(behaviors)
            
            return {
                "type": "engagement",
                "description": f"{'High' if engagement_score > 0.7 else 'Moderate' if engagement_score > 0.3 else 'Low'} engagement level",
                "confidence": 0.8,
                "impact": "high"
            }
        except Exception:
            return None
    
    def _calculate_behavioral_score(self, patterns: List) -> float:
        """Calculate overall behavioral score"""
        if not patterns:
            return 0.5
        
        total_impact = sum(1 if p.get("impact") == "high" else 0.5 if p.get("impact") == "medium" else 0.2 
                          for p in patterns)
        return min(total_impact / len(patterns), 1.0)
    
    def _calculate_pattern_stability(self, behaviors: List) -> float:
        """Calculate pattern stability score"""
        try:
            if len(behaviors) < 10:
                return 0.5
            
            # Simple stability measure based on consistency
            return 0.8  # Placeholder - implement actual stability calculation
        except Exception:
            return 0.5
    
    def _analyze_query_frequency_trend(self, queries: List) -> Dict[str, Any]:
        """Analyze query frequency trends"""
        try:
            if len(queries) < 3:
                return {"current_frequency": 0, "predicted_frequency": 0, "direction": "stable", "confidence": 0.5}
            
            # Calculate current frequency (queries per day)
            days_span = (queries[-1].created_at - queries[0].created_at).days or 1
            current_frequency = len(queries) / days_span
            
            # Simple trend prediction (in production, use more sophisticated methods)
            recent_queries = queries[-len(queries)//2:]
            recent_days = (recent_queries[-1].created_at - recent_queries[0].created_at).days or 1
            recent_frequency = len(recent_queries) / recent_days
            
            predicted_frequency = recent_frequency * 1.1  # Slight increase prediction
            
            direction = "increasing" if predicted_frequency > current_frequency else "decreasing" if predicted_frequency < current_frequency else "stable"
            
            return {
                "current_frequency": current_frequency,
                "predicted_frequency": predicted_frequency,
                "direction": direction,
                "confidence": 0.7
            }
        except Exception:
            return {"current_frequency": 0, "predicted_frequency": 0, "direction": "stable", "confidence": 0.5}
    
    def _analyze_trust_score_trend(self, queries: List) -> Dict[str, Any]:
        """Analyze trust score trends"""
        try:
            trust_scores = []
            for query in queries:
                try:
                    trust_data = json.loads(query.trust_score) if query.trust_score else {}
                    score = trust_data.get('overall_score', 0.7)
                    trust_scores.append(score)
                except:
                    trust_scores.append(0.7)
            
            if not trust_scores:
                return {"current_score": 0.7, "predicted_score": 0.7, "direction": "stable", "confidence": 0.5}
            
            current_score = sum(trust_scores) / len(trust_scores)
            
            # Simple trend prediction
            if len(trust_scores) >= 5:
                recent_avg = sum(trust_scores[-5:]) / 5
                predicted_score = recent_avg * 1.02  # Slight improvement prediction
            else:
                predicted_score = current_score
            
            direction = "increasing" if predicted_score > current_score else "decreasing" if predicted_score < current_score else "stable"
            
            return {
                "current_score": current_score,
                "predicted_score": min(predicted_score, 1.0),
                "direction": direction,
                "confidence": 0.7
            }
        except Exception:
            return {"current_score": 0.7, "predicted_score": 0.7, "direction": "stable", "confidence": 0.5}
    
    def _analyze_engagement_trend(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Analyze user engagement trends"""
        try:
            # Get recent behavior data
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=30)
            
            behaviors = db.query(UserBehaviorLog).filter(
                and_(
                    UserBehaviorLog.user_id == user_id,
                    UserBehaviorLog.timestamp >= start_date
                )
            ).count()
            
            current_engagement = min(behaviors / 30, 1.0)  # Normalize to daily average
            predicted_engagement = current_engagement * 1.05  # Slight increase prediction
            
            direction = "increasing" if predicted_engagement > current_engagement else "stable"
            
            return {
                "current_engagement": current_engagement,
                "predicted_engagement": min(predicted_engagement, 1.0),
                "direction": direction,
                "confidence": 0.6
            }
        except Exception:
            return {"current_engagement": 0.5, "predicted_engagement": 0.5, "direction": "stable", "confidence": 0.5}
    
    def _calculate_overall_trend(self, predictions: List) -> str:
        """Calculate overall trend direction"""
        try:
            increasing = sum(1 for p in predictions if p["trend_direction"] == "increasing")
            decreasing = sum(1 for p in predictions if p["trend_direction"] == "decreasing")
            
            if increasing > decreasing:
                return "positive"
            elif decreasing > increasing:
                return "negative"
            else:
                return "stable"
        except Exception:
            return "stable"
    
    def _calculate_trend_reliability(self, predictions: List) -> float:
        """Calculate trend prediction reliability"""
        try:
            if not predictions:
                return 0.5
            
            avg_confidence = sum(p["confidence"] for p in predictions) / len(predictions)
            return avg_confidence
        except Exception:
            return 0.5
    
    def _calculate_model_consensus(self, risk_pred: Dict, churn_pred: Dict, anomaly_pred: Dict) -> Dict[str, Any]:
        """Calculate consensus between different models"""
        try:
            # Normalize scores to 0-1 range
            risk_score = risk_pred.get('risk_score', 0.5)
            churn_score = churn_pred.get('churn_probability', 0.5)
            anomaly_score = 1.0 if anomaly_pred.get('anomalies_detected', 0) > 0 else 0.0
            
            scores = [risk_score, churn_score, anomaly_score]
            
            # Calculate consensus metrics
            mean_score = sum(scores) / len(scores)
            variance = sum((s - mean_score) ** 2 for s in scores) / len(scores)
            consensus_level = 1 - min(variance, 1.0)  # Higher consensus = lower variance
            
            return {
                "consensus_score": consensus_level,
                "consensus_level": "high" if consensus_level >= 0.8 else "moderate" if consensus_level >= 0.6 else "low",
                "model_agreement": {
                    "risk_vs_churn": abs(risk_score - churn_score),
                    "risk_vs_anomaly": abs(risk_score - anomaly_score),
                    "churn_vs_anomaly": abs(churn_score - anomaly_score)
                }
            }
        except Exception:
            return {"consensus_score": 0.7, "consensus_level": "moderate"}
    
    def _calculate_prediction_stability(self, user_id: str, db: Session) -> float:
        """Calculate prediction stability over time"""
        try:
            # In production, this would analyze historical predictions
            # For now, return a simulated stability score
            return 0.8
        except Exception:
            return 0.7
    
    def _calculate_dynamic_feature_importance(self, user_id: str, db: Session) -> Dict[str, float]:
        """Calculate dynamic feature importance"""
        try:
            # In production, this would use actual feature importance from models
            return {
                "query_frequency": 0.25,
                "trust_score_trend": 0.20,
                "behavioral_consistency": 0.18,
                "feature_adoption": 0.15,
                "engagement_level": 0.12,
                "error_rate": 0.10
            }
        except Exception:
            return {"default": 1.0}
    
    def _quantify_prediction_uncertainty(self, risk_pred: Dict, churn_pred: Dict, anomaly_pred: Dict) -> Dict[str, Any]:
        """Quantify prediction uncertainty"""
        try:
            risk_confidence = risk_pred.get('confidence_score', 0.7)
            churn_confidence = churn_pred.get('confidence_score', 0.7)
            anomaly_confidence = anomaly_pred.get('confidence_score', 0.7)
            
            # Calculate uncertainty metrics
            avg_confidence = (risk_confidence + churn_confidence + anomaly_confidence) / 3
            uncertainty = 1 - avg_confidence
            
            return {
                "overall_uncertainty": uncertainty,
                "uncertainty_level": "low" if uncertainty <= 0.2 else "moderate" if uncertainty <= 0.4 else "high",
                "confidence_variance": np.var([risk_confidence, churn_confidence, anomaly_confidence]),
                "prediction_reliability": avg_confidence,
                "uncertainty_sources": {
                    "model_disagreement": self._calculate_model_disagreement(risk_pred, churn_pred, anomaly_pred),
                    "data_quality": 0.1,  # Placeholder
                    "temporal_drift": 0.05  # Placeholder
                }
            }
        except Exception:
            return {"overall_uncertainty": 0.3, "uncertainty_level": "moderate"}
    
    def _calculate_model_disagreement(self, risk_pred: Dict, churn_pred: Dict, anomaly_pred: Dict) -> float:
        """Calculate disagreement between models"""
        try:
            risk_score = risk_pred.get('risk_score', 0.5)
            churn_score = churn_pred.get('churn_probability', 0.5)
            anomaly_score = 1.0 if anomaly_pred.get('anomalies_detected', 0) > 0 else 0.0
            
            scores = [risk_score, churn_score, anomaly_score]
            mean_score = sum(scores) / len(scores)
            
            # Calculate standard deviation as disagreement measure
            variance = sum((s - mean_score) ** 2 for s in scores) / len(scores)
            disagreement = min(variance ** 0.5, 1.0)
            
            return disagreement
        except Exception:
            return 0.2
