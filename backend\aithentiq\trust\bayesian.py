"""
Bayesian Trust Score Updates with User and Topic Priors
"""

import numpy as np
import json
import logging
from typing import Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict

from ..config import trust_config
from .database_persistence import trust_db_manager


@dataclass
class UserPrior:
    """Beta distribution parameters for user-specific trust"""
    alpha: float
    beta: float
    last_updated: datetime
    total_interactions: int = 0
    
    @property
    def mean(self) -> float:
        """Posterior mean of Beta distribution"""
        return self.alpha / (self.alpha + self.beta)
    
    @property
    def variance(self) -> float:
        """Posterior variance of Beta distribution"""
        total = self.alpha + self.beta
        return (self.alpha * self.beta) / (total * total * (total + 1))
    
    @property
    def confidence_interval(self, confidence: float = 0.95) -> Tuple[float, float]:
        """Confidence interval for the posterior"""
        # Approximate using normal distribution for large alpha + beta
        mean = self.mean
        std = np.sqrt(self.variance)
        z_score = 1.96 if confidence == 0.95 else 2.576  # 95% or 99%
        
        lower = max(0.0, mean - z_score * std)
        upper = min(1.0, mean + z_score * std)
        
        return (lower, upper)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization"""
        return {
            "alpha": self.alpha,
            "beta": self.beta,
            "last_updated": self.last_updated.isoformat(),
            "total_interactions": self.total_interactions,
            "mean": self.mean,
            "variance": self.variance
        }


@dataclass
class TopicPrior:
    """Beta distribution parameters for topic-specific trust"""
    alpha: float
    beta: float
    last_updated: datetime
    total_interactions: int = 0
    topic_keywords: Optional[list] = None
    
    @property
    def mean(self) -> float:
        return self.alpha / (self.alpha + self.beta)
    
    @property
    def variance(self) -> float:
        total = self.alpha + self.beta
        return (self.alpha * self.beta) / (total * total * (total + 1))
    
    def to_dict(self) -> Dict:
        return {
            "alpha": self.alpha,
            "beta": self.beta,
            "last_updated": self.last_updated.isoformat(),
            "total_interactions": self.total_interactions,
            "topic_keywords": self.topic_keywords,
            "mean": self.mean,
            "variance": self.variance
        }


class BayesianTrustUpdater:
    """Bayesian Trust Score Updates with User and Topic Learning"""
    
    def __init__(self, config: Optional[Dict] = None, db_session=None):
        self.config = config or trust_config
        self.logger = logging.getLogger(__name__)
        self.db_session = db_session

        # In-memory cache with database persistence
        self.user_priors: Dict[str, UserPrior] = {}
        self.topic_priors: Dict[str, TopicPrior] = {}

        # Default prior parameters
        self.default_user_alpha = self.config.user_prior_alpha
        self.default_user_beta = self.config.user_prior_beta
        self.default_topic_alpha = self.config.topic_prior_alpha
        self.default_topic_beta = self.config.topic_prior_beta

        # Fusion weights
        self.base_weight = self.config.base_trust_weight
        self.user_weight = self.config.user_posterior_weight
        self.topic_weight = self.config.topic_posterior_weight
    
    def get_user_prior(self, user_id: str) -> UserPrior:
        """Get or create user prior with database persistence"""
        if user_id not in self.user_priors:
            # Try to load from database first
            if self.db_session:
                db_prior = trust_db_manager.load_user_prior(self.db_session, user_id)
                if db_prior:
                    self.user_priors[user_id] = db_prior
                    return db_prior

            # Create new prior if not found in database
            self.user_priors[user_id] = UserPrior(
                alpha=self.default_user_alpha,
                beta=self.default_user_beta,
                last_updated=datetime.now(datetime.timezone.utc)
            )
        return self.user_priors[user_id]
    
    def get_topic_prior(self, topic: str) -> TopicPrior:
        """Get or create topic prior"""
        if topic not in self.topic_priors:
            self.topic_priors[topic] = TopicPrior(
                alpha=self.default_topic_alpha,
                beta=self.default_topic_beta,
                last_updated=datetime.utcnow(),
                topic_keywords=self._extract_topic_keywords(topic)
            )
        return self.topic_priors[topic]
    
    def update_posterior(self, user_id: str, topic: str, predicted_trust: float, 
                        actual_correctness: bool, feedback_weight: float = 1.0) -> None:
        """
        Update Bayesian posteriors based on feedback
        
        Args:
            user_id: User identifier
            topic: Topic/domain identifier
            predicted_trust: Trust score that was predicted
            actual_correctness: Whether the answer was actually correct
            feedback_weight: Weight for this feedback (0-1)
        """
        try:
            # Get current priors
            user_prior = self.get_user_prior(user_id)
            topic_prior = self.get_topic_prior(topic)
            
            # Update user prior
            if actual_correctness:
                # Positive feedback - increase alpha proportional to predicted trust
                user_prior.alpha += predicted_trust * feedback_weight
            else:
                # Negative feedback - increase beta proportional to (1 - predicted trust)
                user_prior.beta += (1.0 - predicted_trust) * feedback_weight
            
            user_prior.total_interactions += 1
            user_prior.last_updated = datetime.utcnow()
            
            # Update topic prior (similar logic)
            if actual_correctness:
                topic_prior.alpha += predicted_trust * feedback_weight
            else:
                topic_prior.beta += (1.0 - predicted_trust) * feedback_weight
            
            topic_prior.total_interactions += 1
            topic_prior.last_updated = datetime.utcnow()
            
            self.logger.info(f"Updated priors for user {user_id}, topic {topic}")
            
        except Exception as e:
            self.logger.error(f"Failed to update posteriors: {str(e)}")
    
    def get_posterior_trust(self, user_id: str, topic: str, base_trust: float) -> Dict[str, float]:
        """
        Compute posterior trust score combining base score with user/topic priors
        
        Args:
            user_id: User identifier
            topic: Topic/domain identifier  
            base_trust: Base trust score from core computation
            
        Returns:
            Dictionary with posterior trust and component contributions
        """
        try:
            # Get priors
            user_prior = self.get_user_prior(user_id)
            topic_prior = self.get_topic_prior(topic)
            
            # Get posterior means
            user_posterior = user_prior.mean
            topic_posterior = topic_prior.mean
            
            # Weighted combination
            posterior_trust = (
                self.base_weight * base_trust +
                self.user_weight * user_posterior +
                self.topic_weight * topic_posterior
            )
            
            # Ensure valid range
            posterior_trust = max(0.0, min(1.0, posterior_trust))
            
            return {
                "posterior_trust": posterior_trust,
                "base_trust": base_trust,
                "user_posterior": user_posterior,
                "topic_posterior": topic_posterior,
                "user_confidence": 1.0 / (1.0 + user_prior.variance),
                "topic_confidence": 1.0 / (1.0 + topic_prior.variance),
                "user_interactions": user_prior.total_interactions,
                "topic_interactions": topic_prior.total_interactions
            }
            
        except Exception as e:
            self.logger.error(f"Failed to compute posterior trust: {str(e)}")
            return {
                "posterior_trust": base_trust,
                "base_trust": base_trust,
                "user_posterior": 0.5,
                "topic_posterior": 0.5,
                "user_confidence": 0.0,
                "topic_confidence": 0.0,
                "user_interactions": 0,
                "topic_interactions": 0
            }
    
    def get_user_trust_profile(self, user_id: str) -> Dict[str, any]:
        """Get comprehensive trust profile for a user"""
        user_prior = self.get_user_prior(user_id)
        
        # Calculate trust level
        mean_trust = user_prior.mean
        if mean_trust >= 0.8:
            trust_level = "Expert"
        elif mean_trust >= 0.6:
            trust_level = "Experienced"
        elif mean_trust >= 0.4:
            trust_level = "Novice"
        else:
            trust_level = "Learning"
        
        return {
            "user_id": user_id,
            "trust_level": trust_level,
            "mean_trust": mean_trust,
            "confidence_interval": user_prior.confidence_interval,
            "total_interactions": user_prior.total_interactions,
            "last_updated": user_prior.last_updated.isoformat(),
            "variance": user_prior.variance
        }
    
    def get_topic_trust_profile(self, topic: str) -> Dict[str, any]:
        """Get comprehensive trust profile for a topic"""
        topic_prior = self.get_topic_prior(topic)
        
        return {
            "topic": topic,
            "mean_trust": topic_prior.mean,
            "confidence_interval": topic_prior.confidence_interval,
            "total_interactions": topic_prior.total_interactions,
            "last_updated": topic_prior.last_updated.isoformat(),
            "variance": topic_prior.variance,
            "keywords": topic_prior.topic_keywords
        }
    
    def _extract_topic_keywords(self, topic: str) -> list:
        """Extract keywords from topic string"""
        # Simple keyword extraction - would use NLP in production
        import re
        
        # Remove common words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        words = re.findall(r'\b\w+\b', topic.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords[:10]  # Limit to top 10 keywords
    
    def export_priors(self) -> Dict[str, any]:
        """Export all priors for backup/analysis"""
        return {
            "user_priors": {uid: prior.to_dict() for uid, prior in self.user_priors.items()},
            "topic_priors": {topic: prior.to_dict() for topic, prior in self.topic_priors.items()},
            "export_timestamp": datetime.utcnow().isoformat(),
            "config": {
                "default_user_alpha": self.default_user_alpha,
                "default_user_beta": self.default_user_beta,
                "default_topic_alpha": self.default_topic_alpha,
                "default_topic_beta": self.default_topic_beta
            }
        }
    
    def import_priors(self, data: Dict[str, any]) -> None:
        """Import priors from backup"""
        try:
            # Import user priors
            for uid, prior_data in data.get("user_priors", {}).items():
                self.user_priors[uid] = UserPrior(
                    alpha=prior_data["alpha"],
                    beta=prior_data["beta"],
                    last_updated=datetime.fromisoformat(prior_data["last_updated"]),
                    total_interactions=prior_data.get("total_interactions", 0)
                )
            
            # Import topic priors
            for topic, prior_data in data.get("topic_priors", {}).items():
                self.topic_priors[topic] = TopicPrior(
                    alpha=prior_data["alpha"],
                    beta=prior_data["beta"],
                    last_updated=datetime.fromisoformat(prior_data["last_updated"]),
                    total_interactions=prior_data.get("total_interactions", 0),
                    topic_keywords=prior_data.get("topic_keywords")
                )
            
            self.logger.info(f"Imported {len(self.user_priors)} user priors and {len(self.topic_priors)} topic priors")
            
        except Exception as e:
            self.logger.error(f"Failed to import priors: {str(e)}")
    
    def reset_user_prior(self, user_id: str) -> None:
        """Reset user prior to default values"""
        if user_id in self.user_priors:
            del self.user_priors[user_id]
        self.logger.info(f"Reset prior for user {user_id}")
    
    def reset_topic_prior(self, topic: str) -> None:
        """Reset topic prior to default values"""
        if topic in self.topic_priors:
            del self.topic_priors[topic]
        self.logger.info(f"Reset prior for topic {topic}")
