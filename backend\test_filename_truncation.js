// Test filename truncation function
function truncateFileName(filename, maxLength = 10) {
    // Remove extension since we show it separately
    const lastDotIndex = filename.lastIndexOf('.');
    const nameWithoutExt = lastDotIndex !== -1 ? filename.substring(0, lastDotIndex) : filename;

    if (nameWithoutExt.length <= maxLength) return nameWithoutExt;

    // Just truncate the name without extension and add dots
    return nameWithoutExt.substring(0, maxLength) + '...';
}

// Test cases
const testFiles = [
    "Screenshot_20221109_082721.png",
    "Test IMAGE File.png", 
    "Very Long Document Name With Many Details.pdf",
    "Short.txt",
    "Aviva-Senior Project Manager cover letter.docx",
    "Test MD File.md",
    "Another_Very_Long_Filename_That_Should_Be_Truncated.xlsx"
];

console.log("🧪 Testing filename truncation (max 10 chars, no extension):");
console.log("=" * 50);

testFiles.forEach(filename => {
    const truncated = truncateFileName(filename, 10);
    console.log(`Original: ${filename}`);
    console.log(`Truncated: ${truncated} (${truncated.length} chars)`);
    console.log("---");
});
