# 🚀 AIthentiq GitHub Push Guide

## ⚠️ CRITICAL: Files/Folders to NEVER Upload

### 🔒 **SECURITY FILES (NEVER COMMIT)**
```
❌ .env
❌ .env.local
❌ backend/.env
❌ frontend/.env.local
❌ Any file containing API keys, passwords, or secrets
```

### 🗄️ **DATABASES (NEVER COMMIT)**
```
❌ backend/aithentiq.db
❌ backend/askdata.db
❌ *.sqlite
❌ *.db files
```

### 📁 **LARGE FOLDERS (NEVER COMMIT)**
```
❌ backend/venv/           # Virtual environment (HUGE)
❌ backend/venv_new/       # Backup virtual environment
❌ frontend/node_modules/  # Node dependencies (HUGE)
❌ backend/datasets/       # Data files (can be large)
❌ backend/__pycache__/    # Python cache
```

### 🔧 **SYSTEM & CACHE FILES**
```
❌ .DS_Store              # macOS system files
❌ Thumbs.db              # Windows thumbnails
❌ *.log                  # Log files
❌ *.tmp                  # Temporary files
❌ .vscode/               # IDE settings
❌ .idea/                 # PyCharm settings
```

## ✅ **Files You SHOULD Upload**

### 📋 **Configuration Files**
```
✅ .gitignore
✅ README.md
✅ package.json (root)
✅ frontend/package.json
✅ backend/requirements.txt
✅ docker-compose.yml
✅ Dockerfile files
```

### 🔧 **Source Code**
```
✅ frontend/app/
✅ frontend/components/
✅ frontend/lib/
✅ backend/main.py
✅ backend/models.py
✅ backend/schemas.py
✅ backend/routers/
✅ backend/services/
```

### 📖 **Documentation**
```
✅ README.md
✅ STARTUP_GUIDE.md
✅ Any .md documentation files
```

### ⚙️ **Configuration**
```
✅ frontend/next.config.js
✅ frontend/tailwind.config.js
✅ frontend/tsconfig.json
✅ backend/alembic.ini
```

## 🛡️ **Security Checklist Before Push**

### 1. **Remove Sensitive Data**
```bash
# Check for API keys in files
grep -r "sk-" . --exclude-dir=venv --exclude-dir=node_modules
grep -r "pk_" . --exclude-dir=venv --exclude-dir=node_modules
grep -r "whsec_" . --exclude-dir=venv --exclude-dir=node_modules
```

### 2. **Create Example Environment Files**
Create `.env.example` files instead of real `.env` files:

**backend/.env.example:**
```env
# Database configuration
DATABASE_URL=sqlite:///./aithentiq.db

# OpenAI API configuration
OPENAI_API_KEY=your_openai_api_key_here

# Stripe configuration
STRIPE_API_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Server configuration
PORT=8000
HOST=0.0.0.0
```

**frontend/.env.example:**
```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# API URL
NEXT_PUBLIC_API_URL=http://localhost:8000

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
NEXT_PUBLIC_PRO_PRICE_ID=your_price_id
```

## 📝 **Step-by-Step Push Instructions**

### 1. **Initialize Git Repository**
```bash
# Navigate to your project root
cd C:\Users\<USER>\Documents\augment-projects\AIthentiq

# Initialize git (if not already done)
git init

# Add the .gitignore file first
git add .gitignore
git commit -m "Add comprehensive .gitignore"
```

### 2. **Create Example Environment Files**
```bash
# Create example files (without real secrets)
cp backend/.env backend/.env.example
cp frontend/.env.local frontend/.env.example

# Edit the example files to remove real API keys
# Replace with placeholder text like "your_api_key_here"
```

### 3. **Stage and Commit Files**
```bash
# Add all files (gitignore will exclude sensitive ones)
git add .

# Check what will be committed
git status

# Commit with descriptive message
git commit -m "Initial commit: AIthentiq - AI-powered data insights platform"
```

### 4. **Create GitHub Repository**
1. Go to https://github.com
2. Click "New repository"
3. Name: `AIthentiq` or `aithentiq`
4. Description: "AI-powered data insights platform with natural language interface"
5. Make it **Private** initially (recommended)
6. Don't initialize with README (you already have one)

### 5. **Connect and Push**
```bash
# Add GitHub remote (replace YOUR_USERNAME)
git remote add origin https://github.com/YOUR_USERNAME/AIthentiq.git

# Push to GitHub
git branch -M main
git push -u origin main
```

## 🔍 **Final Verification**

After pushing, verify on GitHub that:
- ❌ No `.env` files are visible
- ❌ No `venv/` or `node_modules/` folders
- ❌ No `.db` files
- ❌ No API keys in any files
- ✅ Source code is present
- ✅ README.md displays correctly
- ✅ .gitignore is working

## 🚨 **If You Accidentally Commit Secrets**

If you accidentally commit API keys or secrets:

```bash
# Remove from history (DANGEROUS - only if just committed)
git reset --hard HEAD~1

# Or use git filter-branch to remove from entire history
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch backend/.env' \
--prune-empty --tag-name-filter cat -- --all

# Force push (only if repository is private and you're the only user)
git push --force-with-lease origin main
```

## 📚 **Recommended Repository Structure**

Your final GitHub repository should look like:
```
AIthentiq/
├── .gitignore
├── README.md
├── STARTUP_GUIDE.md
├── package.json
├── docker-compose.yml
├── backend/
│   ├── .env.example
│   ├── requirements.txt
│   ├── main.py
│   ├── models.py
│   ├── routers/
│   └── services/
└── frontend/
    ├── .env.example
    ├── package.json
    ├── app/
    ├── components/
    └── lib/
```

## 🎯 **Best Practices**

1. **Always use .env.example files** instead of real .env files
2. **Keep the repository private** until you're sure no secrets are exposed
3. **Use GitHub Secrets** for deployment environment variables
4. **Regularly audit your repository** for accidentally committed secrets
5. **Use branch protection rules** for important branches

---

**Remember: Once something is pushed to GitHub, consider it public forever, even if you delete it later!**
