#!/usr/bin/env python3
"""
Clear all feedback data from the database
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.database import get_db
import backend.models as models

def clear_feedback_data():
    """Clear all feedback records from the database"""
    print("🧹 Clearing all feedback data...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Count existing records
        existing_count = db.query(models.Feedback).count()
        print(f"📊 Found {existing_count} existing feedback records")
        
        if existing_count == 0:
            print("✅ No feedback records to delete")
            return
        
        # Delete all feedback records
        deleted_count = db.query(models.Feedback).delete()
        db.commit()
        
        print(f"✅ Successfully deleted {deleted_count} feedback records")
        
        # Verify deletion
        remaining = db.query(models.Feedback).count()
        print(f"✅ Remaining feedback records: {remaining}")
        
        if remaining == 0:
            print("🎯 Database cleaned successfully!")
        else:
            print(f"⚠️ Warning: {remaining} records still remain")
            
    except Exception as e:
        print(f"❌ Error clearing feedback: {e}")
        db.rollback()
        return False
    finally:
        db.close()
    
    return True

if __name__ == "__main__":
    success = clear_feedback_data()
    
    if success:
        print("\n🎉 FEEDBACK DATA CLEARED!")
        print("="*40)
        print("✅ All feedback records have been deleted")
        print("✅ Database is now clean")
        print("✅ Ready for fresh testing")
        print("\n🔧 Next steps:")
        print("1. Open: http://localhost:3000/dashboard")
        print("2. Ask a question")
        print("3. Test rating functionality")
        print("4. Check admin panel: http://localhost:3000/admin")
    else:
        print("\n❌ Failed to clear feedback data")
        print("Please check the error messages above")
