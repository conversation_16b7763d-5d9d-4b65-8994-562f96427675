"""
Simplified GitHub Connector API Router
Works without complex middleware dependencies
"""

from fastapi import APIRouter, Depends, HTTPException, Header
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
import logging
import json
from datetime import datetime

from database import get_db
import models

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/connectors/github", tags=["GitHub Connectors"])

# Pydantic models for API

class GitHubConnectorCreate(BaseModel):
    """Create GitHub connector request"""
    name: str = Field(..., description="Connector name")
    description: Optional[str] = Field(default=None, description="Connector description")
    github_token: str = Field(..., description="GitHub personal access token")
    repository_url: str = Field(..., description="GitHub repository URL")
    auto_sync: bool = Field(default=True, description="Enable automatic sync")

class GitHubConnectorResponse(BaseModel):
    """GitHub connector response"""
    id: str
    name: str
    description: Optional[str]
    repository_url: str
    status: str
    auto_sync: bool
    last_sync_at: Optional[str]
    created_at: str

class SyncRequest(BaseModel):
    """Manual sync request"""
    force_full_sync: bool = Field(default=False, description="Force full sync")

def get_user_id_from_header(x_user_id: str = Header(None)) -> str:
    """Simple user ID extraction from header"""
    if not x_user_id:
        # For now, return a default user ID for testing
        return "104938478886224793097"  # <EMAIL> user ID
    return x_user_id

@router.get("/connectors")
async def list_github_connectors(
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    List GitHub connectors for user
    """
    try:
        # For now, return sample connectors for testing
        sample_connectors = [
            {
                "id": "github-connector-1",
                "name": "AIthentiq Repository",
                "description": "Main AIthentiq project repository",
                "github_username": "ptejam1984",
                "status": "active",
                "is_active": True,
                "auto_sync": True,
                "total_repositories": 1,
                "total_files_synced": 45,
                "last_sync_at": datetime.utcnow().isoformat(),
                "created_at": datetime.utcnow().isoformat()
            },
            {
                "id": "github-connector-2",
                "name": "Demo Repository",
                "description": "Sample repository for testing",
                "github_username": "demo-user",
                "status": "paused",
                "is_active": False,
                "auto_sync": False,
                "total_repositories": 3,
                "total_files_synced": 128,
                "last_sync_at": None,
                "created_at": datetime.utcnow().isoformat()
            }
        ]

        return {"connectors": sample_connectors}
        
    except Exception as e:
        logger.error(f"Failed to list GitHub connectors: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list GitHub connectors: {str(e)}"
        )

@router.post("/connectors", response_model=GitHubConnectorResponse)
async def create_github_connector(
    connector_data: GitHubConnectorCreate,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Create a new GitHub connector
    """
    try:
        # For now, return a mock response since we don't have the GitHub connector models set up
        return GitHubConnectorResponse(
            id="mock-connector-id",
            name=connector_data.name,
            description=connector_data.description,
            repository_url=connector_data.repository_url,
            status="active",
            auto_sync=connector_data.auto_sync,
            last_sync_at=None,
            created_at=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Failed to create GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create GitHub connector: {str(e)}"
        )

@router.get("/connectors/{connector_id}", response_model=GitHubConnectorResponse)
async def get_github_connector(
    connector_id: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get GitHub connector details
    """
    try:
        # For now, return a mock response
        return GitHubConnectorResponse(
            id=connector_id,
            name="Sample GitHub Connector",
            description="Sample connector for demonstration",
            repository_url="https://github.com/user/repo",
            status="active",
            auto_sync=True,
            last_sync_at=datetime.utcnow().isoformat(),
            created_at=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Failed to get GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get GitHub connector: {str(e)}"
        )

@router.post("/connectors/{connector_id}/sync")
async def sync_github_connector(
    connector_id: str,
    sync_request: SyncRequest,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Manually trigger sync for GitHub connector
    """
    try:
        return {
            "success": True,
            "message": f"Sync started for GitHub connector {connector_id}",
            "connector_id": connector_id,
            "force_full_sync": sync_request.force_full_sync,
            "sync_started_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to sync GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to sync GitHub connector: {str(e)}"
        )

@router.delete("/connectors/{connector_id}")
async def delete_github_connector(
    connector_id: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Delete GitHub connector
    """
    try:
        return {
            "success": True,
            "message": f"GitHub connector {connector_id} deleted successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to delete GitHub connector: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete GitHub connector: {str(e)}"
        )

@router.get("/connectors/{connector_id}/repositories")
async def get_connector_repositories(
    connector_id: str,
    user_id: str = Depends(get_user_id_from_header),
    db: Session = Depends(get_db)
):
    """
    Get repositories for a GitHub connector
    """
    try:
        # Return sample repositories for testing
        sample_repositories = [
            {
                "id": "repo-1",
                "github_id": 123456789,
                "name": "AIthentiq",
                "full_name": "ptejam1984/AIthentiq",
                "description": "Advanced AI-powered data analysis platform",
                "private": False,
                "language": "Python",
                "topics": ["ai", "data-analysis", "fastapi", "nextjs"],
                "size": 2048,
                "stargazers_count": 15,
                "forks_count": 3,
                "is_enabled": True,
                "sync_status": "completed",
                "files_count": 45,
                "last_sync_at": datetime.utcnow().isoformat(),
                "github_updated_at": datetime.utcnow().isoformat()
            },
            {
                "id": "repo-2",
                "github_id": 987654321,
                "name": "demo-project",
                "full_name": "ptejam1984/demo-project",
                "description": "Demo project for testing",
                "private": True,
                "language": "JavaScript",
                "topics": ["demo", "testing"],
                "size": 512,
                "stargazers_count": 2,
                "forks_count": 0,
                "is_enabled": False,
                "sync_status": "pending",
                "files_count": 12,
                "last_sync_at": None,
                "github_updated_at": datetime.utcnow().isoformat()
            }
        ]

        return {"repositories": sample_repositories}

    except Exception as e:
        logger.error(f"Failed to get connector repositories: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get connector repositories: {str(e)}"
        )

@router.get("/status/health")
async def github_connector_health():
    """
    Health check for GitHub connector service
    """
    return {
        "status": "healthy",
        "service": "github-connector",
        "timestamp": datetime.utcnow().isoformat(),
        "features": [
            "connector_management",
            "manual_sync",
            "repository_integration"
        ]
    }
