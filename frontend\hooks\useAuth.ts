'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import api from '@/lib/api';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  subscription_status: string;
}

export function useAuth() {
  const { data: session, status } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') return;

    if (status === 'unauthenticated') {
      setUser(null);
      setLoading(false);
      return;
    }

    if (session?.user?.email) {
      fetchUserDetails();
    }
  }, [session, status]);

  const fetchUserDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use actual user ID from session (not email)
      const userId = (session?.user as any)?.id || '';
      console.log('🔍 Frontend Debug - Session user:', session?.user);
      console.log('🔍 Frontend Debug - Using userId:', userId);

      const response = await api.get(`/users/${userId}`);
      console.log('🔍 Frontend Debug - User response:', response.data);
      setUser(response.data);
    } catch (err: any) {
      console.error('Error fetching user details:', err);
      setError(err.response?.data?.detail || 'Failed to fetch user details');
      
      // If user doesn't exist, they might need to be created
      if (err.response?.status === 404) {
        console.log('User not found, they may need to upload a dataset first to auto-create account');
      }
    } finally {
      setLoading(false);
    }
  };

  const isAdmin = () => {
    return user?.role === 'admin';
  };

  const isAuthenticated = () => {
    return status === 'authenticated' && !!session;
  };

  const getUserId = () => {
    return (session?.user as any)?.id || '';
  };

  return {
    user,
    loading,
    error,
    isAdmin,
    isAuthenticated,
    getUserId,
    session,
    status,
    refetch: fetchUserDetails
  };
}
