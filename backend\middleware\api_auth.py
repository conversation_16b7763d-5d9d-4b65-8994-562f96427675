from fastapi import Request, HTTPException, status, Depends
from sqlalchemy.orm import Session
from datetime import datetime
from typing import Optional

from database import get_db
import models

async def get_api_key(request: Request, db: Session = Depends(get_db)) -> Optional[models.ApiKey]:
    """
    Get and validate API key from request headers
    """
    # Check if the request is to the API endpoints
    if not request.url.path.startswith("/api/v1"):
        return None

    # Get API key from header
    api_key = request.headers.get("X-API-Key")
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is required"
        )

    # Handle temporary API keys (from frontend fallback)
    if api_key.startswith("temp-key-"):
        user_id = api_key.replace("temp-key-", "")

        # Check if user exists
        user = db.query(models.User).filter(models.User.id == user_id).first()
        if not user:
            # Create user if doesn't exist
            user = models.User(
                id=user_id,
                email=f"{user_id}@oauth.local",
                name="OAuth User",
                role="user",
                subscription_status="free"
            )
            db.add(user)
            db.commit()
            db.refresh(user)

        # Check if API key exists for this user
        db_api_key = db.query(models.ApiKey).filter(models.ApiKey.user_id == user_id).first()
        if not db_api_key:
            # Create API key
            db_api_key = models.ApiKey(
                user_id=user_id,
                name="Auto-generated API Key",
                is_active=True
            )
            db.add(db_api_key)
            db.commit()
            db.refresh(db_api_key)

        return db_api_key

    # Validate regular API key
    db_api_key = db.query(models.ApiKey).filter(models.ApiKey.key == api_key).first()
    if not db_api_key or not db_api_key.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or inactive API key"
        )

    # Update last used timestamp
    db_api_key.last_used = datetime.now()
    db.commit()

    return db_api_key

async def get_current_user_from_api_key(api_key: models.ApiKey = Depends(get_api_key), db: Session = Depends(get_db)) -> models.User:
    """
    Get current user from API key
    """
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is required"
        )

    # Get user
    user = db.query(models.User).filter(models.User.id == api_key.user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return user
