#!/usr/bin/env python3
"""
Migration script to add embedding progress tracking columns to datasets table
"""

import sqlite3
import os

def add_progress_columns():
    """Add embedding_progress and embedding_current_step columns to datasets table"""
    
    # Database file path
    db_path = "./aithentiq.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(datasets)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add embedding_progress column if it doesn't exist
        if 'embedding_progress' not in columns:
            cursor.execute("ALTER TABLE datasets ADD COLUMN embedding_progress INTEGER DEFAULT 0")
            print("✅ Added embedding_progress column")
        else:
            print("ℹ️ embedding_progress column already exists")
        
        # Add embedding_current_step column if it doesn't exist
        if 'embedding_current_step' not in columns:
            cursor.execute("ALTER TABLE datasets ADD COLUMN embedding_current_step TEXT")
            print("✅ Added embedding_current_step column")
        else:
            print("ℹ️ embedding_current_step column already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify the columns were added
        cursor.execute("PRAGMA table_info(datasets)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'embedding_progress' in columns and 'embedding_current_step' in columns:
            print("✅ Migration completed successfully")
            
            # Update existing datasets with default progress values
            cursor.execute("""
                UPDATE datasets 
                SET embedding_progress = CASE 
                    WHEN embedding_status = 'completed' THEN 100
                    WHEN embedding_status = 'processing' THEN 50
                    WHEN embedding_status = 'failed' THEN 0
                    ELSE 0
                END,
                embedding_current_step = CASE 
                    WHEN embedding_status = 'completed' THEN 'Embeddings ready'
                    WHEN embedding_status = 'processing' THEN 'Creating embeddings...'
                    WHEN embedding_status = 'failed' THEN 'Failed'
                    ELSE 'Waiting to start'
                END
                WHERE embedding_progress IS NULL OR embedding_current_step IS NULL
            """)
            conn.commit()
            print("✅ Updated existing datasets with progress values")
            
            return True
        else:
            print("❌ Migration verification failed")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔄 Starting migration to add embedding progress columns...")
    success = add_progress_columns()
    if success:
        print("🎉 Migration completed successfully!")
    else:
        print("💥 Migration failed!")
