#!/usr/bin/env python3
"""
Fix the queries table by adding missing columns
"""

import sqlite3
import os

def fix_queries_table():
    db_path = 'aithentiq.db'
    
    if not os.path.exists(db_path):
        print(f"Database {db_path} not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current table structure
        cursor.execute('PRAGMA table_info(queries)')
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        
        print("Current queries table columns:")
        for col in existing_columns:
            print(f"  - {col}")
        
        # Add missing columns if they don't exist
        columns_to_add = [
            ('query_name', 'TEXT'),
            ('is_bookmarked', 'BOOLEAN DEFAULT 0'),
            ('tags', 'TEXT')
        ]
        
        for col_name, col_type in columns_to_add:
            if col_name not in existing_columns:
                print(f"\nAdding column: {col_name} ({col_type})")
                cursor.execute(f'ALTER TABLE queries ADD COLUMN {col_name} {col_type}')
                print(f"✅ Added {col_name}")
            else:
                print(f"✅ Column {col_name} already exists")
        
        conn.commit()
        
        # Verify the changes
        cursor.execute('PRAGMA table_info(queries)')
        columns = cursor.fetchall()
        print("\nUpdated queries table columns:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        print("\n✅ Database update completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating database: {e}")
        return False

if __name__ == "__main__":
    fix_queries_table()
