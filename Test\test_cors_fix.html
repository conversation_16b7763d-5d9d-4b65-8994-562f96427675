<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>Testing CORS Fix</h1>
    <button onclick="testAPI()">Test API Call</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:8001/api/v1/trust-comparison/methods');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3>✅ SUCCESS!</h3>
                    <p>CORS is working correctly.</p>
                    <p>Found ${data.methods.length} trust score methods:</p>
                    <ul>
                        ${data.methods.map(method => `<li>${method.name}</li>`).join('')}
                    </ul>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ ERROR!</h3>
                    <p>CORS issue still exists:</p>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
