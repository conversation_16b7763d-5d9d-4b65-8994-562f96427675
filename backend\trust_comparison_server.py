#!/usr/bin/env python3
"""
Standalone Trust Score Comparison Server
Independent FastAPI server for trust score comparison demo
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
import time
import uvicorn
import os
import asyncio
import json
import re
from difflib import SequenceMatcher
from dotenv import load_dotenv
import hashlib
import pickle
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

# Import AI clients
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Use HTTP requests instead of SDK to save memory
import requests
GEMINI_AVAILABLE = True  # Always available via HTTP

try:
    import anthropic
    CLAUDE_AVAILABLE = True
except ImportError:
    CLAUDE_AVAILABLE = False

# MiniCheck for fact verification
MINICHECK_AVAILABLE = True  # Simulated for now

# Initialize AI clients
openai_client = None
gemini_api_key = None  # Store API key for HTTP requests
claude_client = None

# Track which LLMs are actually available
llm_status = {
    "openai": {"available": False, "status": "Not configured"},
    "gemini": {"available": False, "status": "Not configured"},
    "claude": {"available": False, "status": "Not configured"}
}

# Semantic Cache
semantic_cache = {}

def _get_semantic_hash(query: str, answer: str) -> str:
    """Generate semantic hash for caching"""
    content = f"{query.lower().strip()}|{answer.lower().strip()}"
    return hashlib.md5(content.encode()).hexdigest()

def _get_cached_score(query: str, answer: str, method: str) -> Optional[Dict]:
    """Get cached trust score if available"""
    cache_key = f"{method}_{_get_semantic_hash(query, answer)}"
    cached = semantic_cache.get(cache_key)
    if cached and (datetime.now() - cached['timestamp']).seconds < 3600:  # 1 hour TTL
        return cached['result']
    return None

def _cache_score(query: str, answer: str, method: str, result: Dict):
    """Cache trust score result"""
    cache_key = f"{method}_{_get_semantic_hash(query, answer)}"
    semantic_cache[cache_key] = {
        'result': result,
        'timestamp': datetime.now()
    }

if OPENAI_AVAILABLE:
    try:
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if openai_api_key and openai_api_key.startswith("sk-"):
            openai_client = openai.OpenAI(api_key=openai_api_key)
            llm_status["openai"] = {"available": True, "status": "Configured"}
            print("✅ OpenAI client initialized")
        else:
            llm_status["openai"]["status"] = "Invalid API key"
    except Exception as e:
        llm_status["openai"]["status"] = f"Error: {str(e)}"
        print(f"⚠️ OpenAI initialization failed: {e}")

# Initialize Gemini via HTTP (memory efficient)
try:
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    print(f"🔍 Debug - Gemini API key exists: {gemini_api_key is not None}")

    if gemini_api_key and gemini_api_key != "your_gemini_api_key_here":
        llm_status["gemini"] = {"available": True, "status": "HTTP API configured"}
        print("✅ Gemini HTTP API initialized (memory efficient)")
    else:
        llm_status["gemini"]["status"] = "Placeholder API key"
        print(f"🔍 Debug - Gemini validation failed")
except Exception as e:
    llm_status["gemini"]["status"] = f"Error: {str(e)}"
    print(f"⚠️ Gemini initialization failed: {e}")

if CLAUDE_AVAILABLE:
    try:
        claude_api_key = os.getenv("CLAUDE_API_KEY")
        if claude_api_key and claude_api_key != "your_claude_api_key_here":
            claude_client = anthropic.Anthropic(api_key=claude_api_key)
            llm_status["claude"] = {"available": True, "status": "Configured"}
            print("✅ Claude client initialized")
        else:
            llm_status["claude"]["status"] = "Placeholder API key"
    except Exception as e:
        llm_status["claude"]["status"] = f"Error: {str(e)}"
        print(f"⚠️ Claude initialization failed: {e}")

# Print LLM status summary
print(f"\n🤖 LLM Status Summary:")
for llm, status in llm_status.items():
    icon = "✅" if status["available"] else "❌"
    print(f"   {icon} {llm.upper()}: {status['status']}")
print()

app = FastAPI(
    title="Trust Score Comparison API",
    description="Compare multiple trust score algorithms",
    version="1.0.0"
)

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class TrustComparisonRequest(BaseModel):
    query: str = Field(..., description="The question being asked")
    answer: str = Field(..., description="The AI-generated answer")
    sources: List[Dict[str, Any]] = Field(default=[], description="Source documents")
    selected_methods: List[str] = Field(..., description="Trust score methods to compare")
    user_id: Optional[str] = Field(default="demo_user", description="User ID")

class TrustComparisonResponse(BaseModel):
    results: List[Dict[str, Any]]
    analysis: Dict[str, Any]
    query: str
    answer: str
    timestamp: float

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Trust Score Comparison API", "status": "running"}

@app.get("/methods")
async def get_available_methods():
    """Get list of available trust score methods"""
    return {
        "methods": [
            {
                "id": "legacy_deterministic",
                "name": "1. Legacy Deterministic (First Version)",
                "description": "Original rule-based scoring system with fixed 85% baseline and simple bonus calculations for numbers, length, data terms, and evidence phrases"
            },
            {
                "id": "enhanced_v25",
                "name": "2. Enhanced V2.5 (Second Version)",
                "description": "Multi-component analysis with weighted scoring: Model Confidence (40%), Source Quality (30%), Citation Accuracy (20%), Question Match (10%)"
            },
            {
                "id": "current_production",
                "name": "3. Current Production (Third Version)",
                "description": "Advanced production system with dynamic question-type scoring: Content Quality (35%), Source Reliability (25%), Semantic Coherence (25%), Evidence Strength (15%)"
            },
            {
                "id": "faithfulness_evaluator",
                "name": "4. Faithfulness & Context Evaluator (Fourth Version)",
                "description": "Advanced evaluation using Faithfulness, Answer Relevance, Context Precision & Context Recall"
            },
            {
                "id": "multi_llm_convergence",
                "name": "Multi-LLM Convergence",
                "description": "Most accurate - Uses multiple AI models for cross-validation"
            },
            {
                "id": "faithfulness_context",
                "name": "Faithfulness & Context",
                "description": "Deep analysis - 4-component evaluation of answer quality"
            },
            {
                "id": "current_production",
                "name": "Current Production",
                "description": "Balanced - Fast and comprehensive scoring"
            },
            {
                "id": "average_top_three",
                "name": "Average of Top 3",
                "description": "Most robust - Combines Multi-LLM, Faithfulness & Production"
            }
        ]
    }

@app.post("/compare", response_model=TrustComparisonResponse)
async def compare_trust_scores(request: TrustComparisonRequest):
    """Compare multiple trust score algorithms"""
    try:
        print(f"🔍 Comparing trust scores for: {len(request.selected_methods)} methods")
        
        results = []
        
        for method in request.selected_methods:
            start_time = time.time()
            
            try:
                # Use provided answer or generate one from sources
                if request.answer and request.answer.strip():
                    answer_to_use = request.answer
                else:
                    answer_to_use = _generate_simple_answer_from_sources(request.query, request.sources)

                if method == "legacy_deterministic":
                    result = _legacy_deterministic(request.query, answer_to_use, request.sources)
                elif method == "enhanced_v25":
                    result = _enhanced_v25(request.query, answer_to_use, request.sources)
                elif method == "current_production":
                    result = _current_production(request.query, answer_to_use, request.sources)
                elif method == "faithfulness_context":
                    result = _faithfulness_evaluator(request.query, answer_to_use, request.sources)
                elif method == "multi_llm_convergence":
                    result = await _your_convergence_algorithm(request.query, answer_to_use, request.sources)
                elif method == "average_top_three":
                    result = await _average_top_three(request.query, answer_to_use, request.sources)
                else:
                    raise ValueError(f"Unknown method: {method}")
                
                processing_time = (time.time() - start_time) * 1000
                
                results.append({
                    "method": result["method_name"],
                    "method_name": result["method_name"],
                    "trust_score": result["score"],
                    "score": result["score"],
                    "processing_time_ms": processing_time,
                    "components": result["components"],
                    "explanation": result["explanation"],
                    "score_percentage": f"{result['score']:.1%}",
                    "processing_time_formatted": f"{processing_time:.1f}ms",
                    "generated_answer": answer_to_use,
                    "answer_length": len(answer_to_use),
                    "details": result.get("details", {})
                })
                
                print(f"✅ {result['method_name']}: {result['score']:.1%} in {processing_time:.1f}ms")
                
            except Exception as e:
                processing_time = (time.time() - start_time) * 1000
                results.append({
                    "method_name": _get_method_name(method),
                    "score": 0.0,
                    "processing_time_ms": processing_time,
                    "components": {},
                    "explanation": f"Error: {str(e)}",
                    "error": str(e),
                    "score_percentage": "0.0%",
                    "processing_time_formatted": f"{processing_time:.1f}ms"
                })
                
                print(f"❌ {_get_method_name(method)}: Error - {str(e)}")
        
        # Generate analysis
        analysis = _analyze_results(results)
        
        print(f"📊 Analysis complete: {analysis['summary']['successful_methods']}/{analysis['summary']['methods_tested']} methods successful")
        
        return TrustComparisonResponse(
            results=results,
            analysis=analysis,
            query=request.query,
            answer=request.answer,
            timestamp=time.time()
        )
        
    except Exception as e:
        print(f"❌ Comparison failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Trust score comparison failed: {str(e)}"
        )

@app.get("/sample-data")
async def get_sample_data():
    """Get rich sample data and predefined questions"""
    return {
        "sources": [
            {
                "title": "Q1 2024 Financial Report",
                "text": "The first quarter of 2024 showed remarkable performance across all business units. Total revenue reached $2.4 million, representing a 15% increase compared to Q1 2023. Sales team performance was exceptional with the North American division contributing $1.2 million (50% of total revenue), European operations generating $720,000 (30%), and Asia-Pacific markets accounting for $480,000 (20%). The average monthly revenue for Q1 was $800,000, with March being the strongest month at $950,000, followed by February at $825,000, and January at $625,000. Customer acquisition costs decreased by 8% while customer lifetime value increased by 12%. The sales conversion rate improved from 3.2% in Q4 2023 to 4.1% in Q1 2024. Our top-performing product line, Enterprise Solutions, generated $1.44 million in revenue, while Small Business Solutions contributed $960,000. The average deal size increased from $15,000 to $18,500, indicating successful upselling strategies."
            },
            {
                "title": "Market Analysis Report Q1 2024",
                "text": "Market conditions in Q1 2024 were favorable for technology companies, with the overall sector growing by 11% year-over-year. Industry benchmarks show that companies in our segment typically achieve 6-8% quarterly growth, making our 15% growth rate significantly above average. Competitive analysis reveals that our main competitor, TechCorp, reported 9% growth, while InnovateSoft achieved 12% growth. Market share analysis indicates we now hold 8.5% of the total addressable market, up from 7.2% in Q4 2023. Customer satisfaction scores averaged 4.6 out of 5, with 89% of customers rating our service as 'excellent' or 'very good'. The Net Promoter Score (NPS) improved to 67, placing us in the 'excellent' category. Market research shows increasing demand for AI-powered solutions, which aligns with our product roadmap. Economic indicators suggest continued growth potential, with GDP growth projected at 2.8% for 2024."
            },
            {
                "title": "Operational Metrics Dashboard Q1 2024",
                "text": "Operational efficiency metrics for Q1 2024 demonstrate significant improvements across key performance indicators. Employee productivity increased by 14%, measured by revenue per employee, which rose from $185,000 to $211,000 annually. The development team delivered 23 new features, exceeding the planned 18 features by 28%. Bug resolution time improved from an average of 3.2 days to 2.1 days. Customer support response time decreased from 4 hours to 2.5 hours, while first-call resolution rate increased to 78%. Infrastructure costs were optimized, reducing cloud spending by 12% through better resource allocation. The sales team made 2,847 calls, conducted 456 demos, and closed 187 deals, resulting in a 41% demo-to-close conversion rate. Marketing qualified leads (MQLs) increased by 22%, with 1,234 new leads generated. Website traffic grew by 31%, with 45,000 unique visitors and a 3.2% conversion rate. Social media engagement increased by 67%, with LinkedIn showing the highest engagement rates."
            },
            {
                "title": "Customer Feedback Analysis Q1 2024",
                "text": "Customer feedback analysis for Q1 2024 reveals overwhelmingly positive sentiment and valuable insights for product development. Survey responses from 1,847 customers show 92% satisfaction with product quality, 88% satisfaction with customer service, and 85% likelihood to recommend our solutions. Key themes from customer feedback include appreciation for the intuitive user interface (mentioned by 67% of respondents), reliability of the platform (mentioned by 74%), and responsiveness of support team (mentioned by 81%). Areas for improvement identified include mobile app functionality (requested by 43% of users), advanced reporting features (requested by 38%), and integration capabilities (requested by 52%). Customer retention rate reached 94%, the highest in company history. Churn analysis shows that 89% of churned customers cited pricing concerns, while 11% mentioned feature limitations. Success stories include a 340% ROI reported by Enterprise Client A, 25% efficiency improvement by Client B, and 50% cost reduction achieved by Client C. Customer testimonials highlight our solution's impact on business transformation and competitive advantage."
            },
            {
                "title": "Strategic Initiatives Progress Q1 2024",
                "text": "Strategic initiatives launched in Q1 2024 are progressing according to plan with several exceeding expectations. The AI Integration Project reached 75% completion, ahead of the 60% target, with machine learning models showing 94% accuracy in predictive analytics. The Global Expansion Initiative successfully established operations in three new markets: Germany, Japan, and Australia, with initial revenue of $127,000 from these regions. The Digital Transformation Program completed phase 1, resulting in 35% improvement in process automation and 28% reduction in manual tasks. Partnership development yielded 7 new strategic alliances, including collaborations with CloudTech, DataSystems, and AnalyticsPro. The Innovation Lab launched 4 proof-of-concept projects, with 2 showing commercial viability. Sustainability initiatives reduced carbon footprint by 18% through remote work policies and green technology adoption. The talent acquisition program hired 23 new employees, including 8 senior engineers and 5 data scientists, strengthening our technical capabilities. Training and development programs achieved 96% completion rate among existing staff."
            }
        ],
        "questions": [
            {
                "id": "revenue_q1",
                "text": "What was the total revenue for Q1 2024?",
                "category": "Financial Performance",
                "complexity": "simple_factual"
            },
            {
                "id": "growth_rate",
                "text": "What was the growth rate compared to Q1 2023?",
                "category": "Financial Performance",
                "complexity": "simple_factual"
            },
            {
                "id": "regional_breakdown",
                "text": "How was revenue distributed across different regions?",
                "category": "Financial Performance",
                "complexity": "complex_analysis"
            },
            {
                "id": "customer_satisfaction",
                "text": "What were the customer satisfaction scores and NPS?",
                "category": "Customer Metrics",
                "complexity": "simple_factual"
            },
            {
                "id": "operational_efficiency",
                "text": "How did operational efficiency metrics improve in Q1?",
                "category": "Operations",
                "complexity": "complex_analysis"
            },
            {
                "id": "competitive_position",
                "text": "How did our performance compare to competitors?",
                "category": "Market Analysis",
                "complexity": "complex_analysis"
            },
            {
                "id": "product_performance",
                "text": "Which product lines performed best and what were their revenues?",
                "category": "Product Performance",
                "complexity": "complex_analysis"
            },
            {
                "id": "strategic_progress",
                "text": "What progress was made on strategic initiatives?",
                "category": "Strategic Development",
                "complexity": "complex_analysis"
            }
        ]
    }

@app.post("/demo")
async def run_demo():
    """Run a demo comparison with sample data"""
    sample_data = await get_sample_data()
    demo_request = TrustComparisonRequest(
        query="What was the total revenue for Q1 2024?",
        answer="",  # No hardcoded answer - let algorithms generate their own
        sources=sample_data["sources"],
        selected_methods=["multi_llm_convergence", "faithfulness_context", "current_production", "average_top_three"]
    )

    return await compare_trust_scores(demo_request)

def _get_method_name(method_id: str) -> str:
    """Get display name for method"""
    names = {
        "legacy_deterministic": "1. Legacy Deterministic (First Version)",
        "enhanced_v25": "2. Enhanced V2.5 (Second Version)",
        "current_production": "3. Current Production (Third Version)",
        "faithfulness_context": "Faithfulness & Context",
        "multi_llm_convergence": "Multi-LLM Convergence",
        "average_top_three": "Average of Top 3"
    }
    return names.get(method_id, method_id)

def _legacy_deterministic(query: str, answer: str, sources: List[Dict]) -> Dict:
    """Legacy deterministic scoring"""
    score = 0.85  # Optimistic baseline
    
    # Bonuses
    digit_bonus = 0.08 if any(char.isdigit() for char in answer) else 0
    length_bonus = 0.05 if len(answer.strip()) >= 15 else 0
    data_terms_bonus = 0.05 if any(term in answer.lower() for term in ['average', 'analysis', 'data']) else 0
    evidence_bonus = 0.03 if any(phrase in answer.lower() for phrase in ['based on', 'according to']) else 0
    
    score += digit_bonus + length_bonus + data_terms_bonus + evidence_bonus
    score = min(1.0, score)
    
    return {
        "method_name": "1. Legacy Deterministic (First Version)",
        "score": score,
        "components": {
            "base_score": 0.85,
            "digit_bonus": digit_bonus,
            "length_bonus": length_bonus,
            "data_terms_bonus": data_terms_bonus,
            "evidence_bonus": evidence_bonus
        },
        "explanation": f"Legacy deterministic score: {score:.1%}",
        "simple_description": "🎯 Simple Rule-Based: Starts with 85% trust and adds bonuses for numbers, good length, data terms, and evidence words. Easy to understand but basic.",
        "component_explanations": {
            "base_score": "The starting trust level - a fixed foundation score that all other bonuses are added to",
            "digit_bonus": "Numerical evidence bonus - rewards answers that include specific numbers or data points",
            "length_bonus": "Response completeness bonus - rewards answers that are substantial enough to be informative",
            "data_terms_bonus": "Analytical language bonus - rewards use of data-focused terminology and analytical terms",
            "evidence_bonus": "Citation bonus - rewards explicit references to sources or evidence"
        }
    }



def _faithfulness_evaluator(query: str, answer: str, sources: List[Dict]) -> Dict:
    """
    FAITHFULNESS & CONTEXT EVALUATOR
    Advanced evaluation methodology with 4-component analysis
    Components: Faithfulness, Answer Relevancy, Context Precision, Context Recall
    """

    # 1. Faithfulness - How factually accurate is the answer based on context
    faithfulness = _calculate_faithfulness(answer, sources)

    # 2. Answer Relevancy - How relevant is the answer to the question
    answer_relevancy = _calculate_answer_relevancy(query, answer)

    # 3. Context Precision - How precise/relevant are the retrieved contexts
    context_precision = _calculate_context_precision(query, sources)

    # 4. Context Recall - How well does the context cover the answer
    context_recall = _calculate_context_recall(answer, sources)

    # Advanced scoring (equal weights for core metrics)
    final_score = (faithfulness + answer_relevancy + context_precision + context_recall) / 4

    return {
        "method_name": "Faithfulness & Context",
        "score": final_score,
        "components": {
            "faithfulness": faithfulness,
            "answer_relevancy": answer_relevancy,
            "context_precision": context_precision,
            "context_recall": context_recall
        },
        "explanation": f"Faithfulness & Context evaluation: {final_score:.1%} - Advanced 4-component analysis",
        "methodology": "Faithfulness + Answer Relevancy + Context Precision + Context Recall",
        "simple_description": "🧠 Advanced AI Evaluation: Measures how factually accurate (Faithfulness), relevant (Answer Relevancy), precise (Context Precision), and complete (Context Recall) the answer is. Very sophisticated.",
        "component_explanations": {
            "faithfulness": "How factually accurate and truthful the answer is based on the provided sources",
            "answer_relevancy": "How directly relevant and on-topic the answer is to the question asked",
            "context_precision": "How precise and focused the retrieved source contexts are for the question",
            "context_recall": "How comprehensively the sources cover all aspects needed to answer the question"
        }
    }

def _current_production(query: str, answer: str, sources: List[Dict]) -> Dict:
    """
    3. CURRENT PRODUCTION (Third Version)
    Advanced production system with dynamic scoring and fallback logic
    """

    # Dynamic base score based on question complexity
    question_type = _classify_question_type(query)
    if question_type == "simple_factual":
        base_score = 0.75
    elif question_type == "complex_analysis":
        base_score = 0.65
    else:
        base_score = 0.70

    # Advanced scoring components
    content_quality = _calculate_content_quality(answer)
    source_reliability = _calculate_source_reliability(sources)
    semantic_coherence = _calculate_semantic_coherence(query, answer)
    evidence_strength = _calculate_evidence_strength(answer, sources)

    # Production weighting (different from V2.5)
    weighted_score = (
        content_quality * 0.35 +      # 35% - Content quality
        source_reliability * 0.25 +   # 25% - Source reliability
        semantic_coherence * 0.25 +   # 25% - Semantic coherence
        evidence_strength * 0.15      # 15% - Evidence strength
    )

    # Apply base score adjustment
    final_score = (base_score * 0.4) + (weighted_score * 0.6)
    final_score = min(1.0, final_score)

    return {
        "method_name": "3. Current Production (Third Version)",
        "score": final_score,
        "components": {
            "base_score": base_score,
            "content_quality": content_quality,
            "source_reliability": source_reliability,
            "semantic_coherence": semantic_coherence,
            "evidence_strength": evidence_strength,
            "question_type": question_type
        },
        "explanation": f"Current production system: {final_score:.1%} - Dynamic scoring with fallback logic",
        "simple_description": "🚀 Advanced Production System: Uses dynamic scoring based on question type, with 4 components weighted differently than V2.5. Includes fallback logic and production optimizations.",
        "component_explanations": {
            "base_score": "Adaptive starting trust level that adjusts based on question complexity and type",
            "content_quality": "Overall quality of the response including structure, language, and completeness",
            "source_reliability": "Trustworthiness and diversity of the source documents used",
            "semantic_coherence": "How well the answer logically connects to and addresses the question",
            "evidence_strength": "Strength of supporting evidence, citations, and factual backing",
            "question_type": "Classification of question complexity (simple factual, complex analysis, or general)"
        }
    }

def _enhanced_v25(query: str, answer: str, sources: List[Dict]) -> Dict:
    """
    2. ENHANCED V2.5 (Second Version)
    Multi-component analysis with weighted scoring
    """
    # Multi-component analysis
    model_confidence = 0.8 if len(answer) > 20 and any(char.isdigit() for char in answer) else 0.6
    source_quality = 0.8 if len(sources) >= 2 else (0.6 if sources else 0.4)
    citation_accuracy = 0.9 if any(phrase in answer.lower() for phrase in ['based on', 'according to']) else 0.6
    question_match = 0.8 if len(set(query.lower().split()) & set(answer.lower().split())) > 1 else 0.5

    # Weighted combination
    score = (model_confidence * 0.4 + source_quality * 0.3 +
             citation_accuracy * 0.2 + question_match * 0.1)

    return {
        "method_name": "2. Enhanced V2.5 (Second Version)",
        "score": score,
        "components": {
            "model_confidence": model_confidence,
            "source_quality": source_quality,
            "citation_accuracy": citation_accuracy,
            "question_match": question_match
        },
        "explanation": f"Enhanced V2.5 multi-component: {score:.1%}",
        "simple_description": "⚖️ Multi-Component Analysis: Evaluates 4 factors - model confidence (40%), source quality (30%), citation accuracy (20%), and question match (10%). More sophisticated than basic rules.",
        "component_explanations": {
            "model_confidence": "How confident the AI model appears to be in its response based on language patterns",
            "source_quality": "The reliability and comprehensiveness of the source documents provided",
            "citation_accuracy": "How well the answer references and aligns with the provided sources",
            "question_match": "How well the answer directly addresses the specific question asked"
        }
    }



async def _your_convergence_algorithm(query: str, answer: str, sources: List[Dict]) -> Dict:
    """
    5. YOUR PROPOSED CONVERGENCE ALGORITHM (Fifth Version)
    TRUE MULTI-LLM CONVERGENCE with OpenAI + Gemini AI

    REAL CONVERGENCE LOGIC:
    1. Generate responses from multiple LLMs (OpenAI + Gemini)
    2. Compare original answer with LLM responses
    3. Measure convergence/consensus across models
    4. Apply your weights: model_confidence 40%, source_quality 30%, citation_accuracy 20%, question_match 10%
    """

    # Determine question type for adaptive scoring
    question_type = _classify_question_type(query)

    # Base trust based on question type
    if question_type == "simple_factual":
        base_trust = 0.7
    elif question_type == "complex_analysis":
        base_trust = 0.5
    else:
        base_trust = 0.6

    # STEP 1: Generate responses from multiple LLMs for convergence analysis
    llm_responses = await _generate_multi_llm_responses(query, sources)

    # STEP 2: Calculate TRUE convergence metrics
    convergence_metrics = _calculate_multi_llm_convergence(answer, llm_responses, query, sources)

    # STEP 3: Apply your exact weights to convergence-enhanced components
    model_confidence = convergence_metrics["model_confidence"] * 0.40  # 40%
    source_quality = convergence_metrics["source_quality"] * 0.30      # 30%
    citation_accuracy = convergence_metrics["citation_accuracy"] * 0.20 # 20%
    question_match = convergence_metrics["question_match"] * 0.10       # 10%

    # Final convergence score
    convergence_score = model_confidence + source_quality + citation_accuracy + question_match

    # Apply your thresholds
    if convergence_score >= 0.70:
        trust_level = "High (70-100%)"
    elif convergence_score >= 0.50:
        trust_level = "Moderate (50-69%)"
    else:
        trust_level = "Low (0-49%)"

    return {
        "method_name": "Multi-LLM Convergence",
        "score": convergence_score,
        "components": {
            "model_confidence": convergence_metrics["model_confidence"],
            "source_quality": convergence_metrics["source_quality"],
            "citation_accuracy": convergence_metrics["citation_accuracy"],
            "question_match": convergence_metrics["question_match"],
            "convergence_consensus": convergence_metrics["consensus_score"],
            "llm_agreement": convergence_metrics["llm_agreement"],
            "base_trust": base_trust,
            "question_type": question_type
        },
        "details": {
            "llm_responses": llm_responses,
            "convergence_metrics": convergence_metrics,
            "trust_level": trust_level,
            "methodology": "TRUE Multi-LLM Convergence (OpenAI + Gemini)"
        },
        "explanation": f"Multi-LLM convergence: {convergence_score:.1%} ({trust_level}) - {len(llm_responses)} LLMs analyzed ({_count_real_vs_simulated(llm_responses)})",
        "weights": {
            "model_confidence": "40%",
            "source_quality": "30%",
            "citation_accuracy": "20%",
            "question_match": "10%"
        },
        "trust_level": trust_level,
        "methodology": "TRUE Multi-LLM Convergence (OpenAI + Gemini)",
        "llm_responses": llm_responses,
        "convergence_details": convergence_metrics,
        "simple_description": "🤖 Multi-AI Convergence: Asks the same question to multiple AI models (OpenAI, Gemini, Claude) and measures how much they agree. Higher agreement = higher trust. Most advanced method.",
        "component_explanations": {
            "model_confidence": "AI model's confidence level enhanced by cross-validation with multiple models",
            "source_quality": "Quality of sources validated and verified across different AI perspectives",
            "citation_accuracy": "Accuracy of citations and references cross-checked by multiple AI models",
            "question_match": "How well the answer matches the question, verified by multiple AI models",
            "convergence_consensus": "Agreement level between different AI models (higher = more trustworthy)",
            "llm_agreement": "Overall consensus score showing how much different AI models agree",
            "base_trust": "Starting trust level that adapts based on question complexity",
            "question_type": "Automatic classification of question complexity for adaptive scoring"
        }
    }

async def _average_top_three(query: str, answer: str, sources: List[Dict]) -> Dict:
    """Average of Top 3 Methods"""
    # Run top 3 methods
    convergence = await _your_convergence_algorithm(query, answer, sources)
    faithfulness = _faithfulness_evaluator(query, answer, sources)
    production = _current_production(query, answer, sources)

    # Calculate average score
    avg_score = (convergence["score"] + faithfulness["score"] + production["score"]) / 3

    return {
        "method_name": "Average of Top 3",
        "score": avg_score,
        "components": {
            "multi_llm_score": convergence["score"],
            "faithfulness_score": faithfulness["score"],
            "production_score": production["score"],
            "average_score": avg_score
        },
        "explanation": f"Average of top 3 methods: {avg_score:.1%}",
        "simple_description": "🎯 Most Robust: Combines Multi-LLM Convergence, Faithfulness & Context, and Current Production for maximum reliability.",
        "component_explanations": {
            "multi_llm_score": "Score from Multi-LLM Convergence method",
            "faithfulness_score": "Score from Faithfulness & Context method",
            "production_score": "Score from Current Production method",
            "average_score": "Average of all three top methods"
        }
    }

def _analyze_results(results: List[Dict]) -> Dict[str, Any]:
    """Analyze comparison results"""
    successful_results = [r for r in results if not r.get('error')]
    failed_results = [r for r in results if r.get('error')]
    
    analysis = {
        "summary": {
            "methods_tested": len(results),
            "successful_methods": len(successful_results),
            "failed_methods": len(failed_results)
        },
        "recommendations": []
    }
    
    if successful_results:
        scores = [r['score'] for r in successful_results]
        times = [r['processing_time_ms'] for r in successful_results]
        
        analysis["score_analysis"] = {
            "highest_score": max(scores),
            "lowest_score": min(scores),
            "average_score": sum(scores) / len(scores),
            "best_method": {"name": max(successful_results, key=lambda x: x['score'])['method_name'], 
                           "score": max(scores)},
            "worst_method": {"name": min(successful_results, key=lambda x: x['score'])['method_name'], 
                            "score": min(scores)}
        }
        
        analysis["performance_analysis"] = {
            "fastest_time_ms": min(times),
            "slowest_time_ms": max(times),
            "average_time_ms": sum(times) / len(times),
            "fastest_method": {"name": min(successful_results, key=lambda x: x['processing_time_ms'])['method_name'], 
                              "time_ms": min(times)},
            "slowest_method": {"name": max(successful_results, key=lambda x: x['processing_time_ms'])['method_name'], 
                              "time_ms": max(times)}
        }
        
        # Generate recommendations
        if max(scores) - min(scores) > 0.3:
            analysis["recommendations"].append("High variance in trust scores detected - consider investigating why methods disagree")
        if max(times) > 1000:
            analysis["recommendations"].append("Some methods are slow - consider optimizing for production use")
        if max(times) > min(times) * 10:
            analysis["recommendations"].append("Large performance differences between methods - consider speed vs accuracy trade-offs")
    
    if failed_results:
        analysis["recommendations"].append(f"{len(failed_results)} method(s) failed - check system configuration")
    
    if not analysis["recommendations"]:
        analysis["recommendations"].append("All methods performed well - consider using fastest method for production")
    
    return analysis

# Helper functions for Faithfulness & Context Evaluator
def _calculate_faithfulness(answer: str, sources: List[Dict]) -> float:
    """Calculate faithfulness - how factually accurate is the answer based on context"""
    if not sources:
        return 0.4  # Low faithfulness without sources

    # Check for evidence-based language
    evidence_phrases = ['based on', 'according to', 'data shows', 'analysis indicates', 'research suggests']
    has_evidence = any(phrase in answer.lower() for phrase in evidence_phrases)

    # Check for specific data/numbers that could be verified
    has_specifics = any(char.isdigit() for char in answer)

    # Base faithfulness
    faithfulness = 0.6
    if has_evidence:
        faithfulness += 0.2
    if has_specifics:
        faithfulness += 0.15
    if len(sources) >= 2:
        faithfulness += 0.05

    return min(1.0, faithfulness)

def _calculate_answer_relevancy(query: str, answer: str) -> float:
    """Calculate answer relevancy - how relevant is the answer to the question"""
    query_words = set(query.lower().split())
    answer_words = set(answer.lower().split())

    # Remove stop words
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are'}
    query_words = query_words - stop_words
    answer_words = answer_words - stop_words

    if not query_words:
        return 0.7

    # Calculate overlap
    overlap = len(query_words.intersection(answer_words))
    relevancy = overlap / len(query_words)

    # Bonus for question-answer patterns
    if query.lower().startswith(('what', 'how', 'why', 'when', 'where')):
        if any(word in answer.lower() for word in ['is', 'are', 'by', 'because', 'in', 'at']):
            relevancy += 0.1

    return min(1.0, relevancy + 0.3)  # Add base relevancy

def _calculate_context_precision(query: str, sources: List[Dict]) -> float:
    """Calculate context precision - how precise/relevant are the retrieved contexts"""
    if not sources:
        return 0.3

    query_words = set(query.lower().split())
    precision_scores = []

    for source in sources:
        source_text = source.get('text', str(source)).lower()
        source_words = set(source_text.split())

        # Calculate relevance of this source to the query
        overlap = len(query_words.intersection(source_words))
        if query_words:
            precision = overlap / len(query_words)
        else:
            precision = 0.5

        precision_scores.append(precision)

    # Average precision across sources
    avg_precision = sum(precision_scores) / len(precision_scores) if precision_scores else 0.3
    return min(1.0, avg_precision + 0.4)  # Add base precision

def _calculate_context_recall(answer: str, sources: List[Dict]) -> float:
    """Calculate context recall - how well does the context cover the answer"""
    if not sources:
        return 0.4

    answer_words = set(answer.lower().split())
    all_source_words = set()

    for source in sources:
        source_text = source.get('text', str(source)).lower()
        all_source_words.update(source_text.split())

    if not answer_words:
        return 0.6

    # Calculate how much of the answer is covered by sources
    covered_words = len(answer_words.intersection(all_source_words))
    recall = covered_words / len(answer_words)

    return min(1.0, recall + 0.3)  # Add base recall

# Helper functions for Your Convergence Algorithm
def _classify_question_type(query: str) -> str:
    """Classify question type for adaptive scoring"""
    query_lower = query.lower()

    # Simple factual questions
    simple_indicators = ['what is', 'who is', 'when is', 'where is', 'how many', 'how much']
    if any(indicator in query_lower for indicator in simple_indicators):
        return "simple_factual"

    # Complex analysis questions
    complex_indicators = ['analyze', 'compare', 'evaluate', 'explain why', 'how does', 'what are the implications']
    if any(indicator in query_lower for indicator in complex_indicators):
        return "complex_analysis"

    return "general"

def _calculate_convergence_model_confidence(answer: str, base_trust: float) -> float:
    """Calculate model confidence for convergence algorithm"""
    confidence = base_trust

    # Confident language indicators
    confident_phrases = ['clearly', 'definitely', 'certainly', 'precisely', 'exactly']
    uncertain_phrases = ['might', 'could', 'possibly', 'perhaps', 'maybe', 'approximately']

    confident_count = sum(1 for phrase in confident_phrases if phrase in answer.lower())
    uncertain_count = sum(1 for phrase in uncertain_phrases if phrase in answer.lower())

    confidence += confident_count * 0.05
    confidence -= uncertain_count * 0.03  # Small penalty for uncertainty

    # Length and structure bonus
    if 30 <= len(answer) <= 200:
        confidence += 0.1

    # Specific data bonus
    if any(char.isdigit() for char in answer):
        confidence += 0.1

    return min(1.0, max(0.2, confidence))

def _calculate_convergence_source_quality(sources: List[Dict], base_trust: float) -> float:
    """Calculate source quality for convergence algorithm - additive bonuses"""
    quality = base_trust

    if not sources:
        return max(0.3, quality - 0.2)  # Small penalty for no sources

    # Additive bonuses (reward good sources, don't penalize poor ones)
    if len(sources) >= 2:
        quality += 0.15  # Bonus for multiple sources
    if len(sources) >= 3:
        quality += 0.1   # Additional bonus for many sources

    # Source content quality bonuses
    total_length = sum(len(str(source.get('text', str(source)))) for source in sources)
    avg_length = total_length / len(sources)

    if avg_length > 50:
        quality += 0.1   # Bonus for substantial sources
    if avg_length > 100:
        quality += 0.05  # Additional bonus for detailed sources

    return min(1.0, quality)

def _calculate_convergence_citation_accuracy(answer: str, sources: List[Dict], base_trust: float) -> float:
    """Calculate citation accuracy for convergence algorithm"""
    accuracy = base_trust

    # Explicit citation indicators (big bonus)
    citation_indicators = ['according to', 'based on', 'as stated', 'source:', 'data shows', 'analysis indicates']
    has_explicit_citations = any(indicator in answer.lower() for indicator in citation_indicators)

    if has_explicit_citations:
        accuracy += 0.25  # Large bonus for explicit citations

    # Implicit source alignment
    if sources:
        answer_words = set(answer.lower().split())
        source_alignment = 0

        for source in sources:
            source_text = source.get('text', str(source))
            source_words = set(source_text.lower().split())
            overlap = len(answer_words.intersection(source_words))
            if answer_words:
                alignment = overlap / len(answer_words)
                source_alignment = max(source_alignment, alignment)

        accuracy += source_alignment * 0.15  # Bonus for source alignment

    return min(1.0, accuracy)

def _calculate_convergence_question_match(query: str, answer: str, base_trust: float) -> float:
    """Calculate question-answer match for convergence algorithm"""
    match_score = base_trust

    query_words = set(query.lower().split())
    answer_words = set(answer.lower().split())

    # Remove stop words
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
    query_words = query_words - stop_words
    answer_words = answer_words - stop_words

    if query_words:
        overlap = len(query_words.intersection(answer_words))
        semantic_match = overlap / len(query_words)
        match_score += semantic_match * 0.2

    # Question type bonus
    if query.lower().startswith(('what', 'how', 'why', 'when', 'where')):
        if any(word in answer.lower() for word in ['is', 'are', 'by', 'because', 'in', 'at', 'during']):
            match_score += 0.1

    # Direct answer bonus
    if len(answer.split()) >= 5 and answer.count('.') >= 1:
        match_score += 0.05

    return min(1.0, match_score)

# MiniCheck Implementation
def _minicheck_verify_claims(answer: str, sources: List[Dict]) -> float:
    """Simulated MiniCheck fact verification"""
    if not answer or not sources:
        return 0.5

    # Simulate fact-checking logic
    claims = _extract_claims(answer)
    verified_claims = 0
    total_claims = len(claims)

    if total_claims == 0:
        return 0.7  # No specific claims to verify

    for claim in claims:
        if _verify_claim_against_sources(claim, sources):
            verified_claims += 1

    return verified_claims / total_claims if total_claims > 0 else 0.5

def _extract_claims(answer: str) -> List[str]:
    """Extract factual claims from answer"""
    # Simple claim extraction (in real implementation, use NLP)
    sentences = answer.split('.')
    claims = []

    for sentence in sentences:
        sentence = sentence.strip()
        if len(sentence) > 10 and any(char.isdigit() for char in sentence):
            claims.append(sentence)
        elif any(keyword in sentence.lower() for keyword in ['is', 'was', 'are', 'were', 'has', 'have']):
            claims.append(sentence)

    return claims[:5]  # Limit to 5 claims

def _verify_claim_against_sources(claim: str, sources: List[Dict]) -> bool:
    """Verify individual claim against sources"""
    claim_words = set(claim.lower().split())

    for source in sources:
        source_text = source.get('text', str(source)).lower()
        source_words = set(source_text.split())

        # Simple overlap-based verification
        overlap = len(claim_words.intersection(source_words))
        if overlap >= min(3, len(claim_words) * 0.3):
            return True

    return False

def _semantic_cache_minicheck(query: str, answer: str, sources: List[Dict]) -> Dict:
    """6th Algorithm: Semantic Cache + MiniCheck"""
    method_name = "semantic_cache_minicheck"

    # Check cache first
    cached = _get_cached_score(query, answer, method_name)
    if cached:
        cached["from_cache"] = True
        return cached

    # Compute new score
    base_score = 0.6

    # MiniCheck fact verification
    fact_accuracy = _minicheck_verify_claims(answer, sources)

    # Semantic coherence
    semantic_score = _calculate_semantic_coherence(query, answer)

    # Source alignment
    source_score = _calculate_source_reliability(sources)

    # Combined scoring
    final_score = (
        base_score * 0.3 +
        fact_accuracy * 0.4 +
        semantic_score * 0.2 +
        source_score * 0.1
    )

    result = {
        "method_name": "6. Semantic Cache + MiniCheck (Sixth Version)",
        "score": min(1.0, final_score),
        "components": {
            "base_score": base_score,
            "fact_accuracy": fact_accuracy,
            "semantic_score": semantic_score,
            "source_score": source_score,
            "claims_extracted": len(_extract_claims(answer))
        },
        "explanation": f"Semantic Cache + MiniCheck: {final_score:.1%} - Cached fact verification",
        "simple_description": "🔍 Smart Cache + Fact Check: Uses semantic caching for speed and MiniCheck for fact verification. Combines performance with accuracy.",
        "component_explanations": {
            "base_score": "Starting trust level for the semantic cache + fact-checking approach",
            "fact_accuracy": "MiniCheck fact verification score - how many claims are supported by sources",
            "semantic_score": "Semantic coherence between question and answer",
            "source_score": "Quality and reliability of source documents",
            "claims_extracted": "Number of factual claims extracted from the answer for verification"
        },
        "from_cache": False
    }

    # Cache the result
    _cache_score(query, answer, method_name, result)

    return result

# Helper functions for Current Production Algorithm
def _calculate_content_quality(answer: str) -> float:
    """Calculate content quality for production algorithm"""
    quality = 0.6  # Base quality

    # Length quality (optimal range)
    length = len(answer)
    if 50 <= length <= 300:
        quality += 0.2
    elif 30 <= length <= 500:
        quality += 0.1

    # Structure quality
    sentences = answer.count('.')
    if 2 <= sentences <= 5:
        quality += 0.1

    # Professional language
    professional_terms = ['analysis', 'indicates', 'demonstrates', 'according', 'based on', 'data shows']
    professional_count = sum(1 for term in professional_terms if term in answer.lower())
    quality += min(0.1, professional_count * 0.02)

    return min(1.0, quality)

def _calculate_source_reliability(sources: List[Dict]) -> float:
    """Calculate source reliability for production algorithm"""
    if not sources:
        return 0.3

    reliability = 0.5  # Base reliability

    # Number of sources bonus
    if len(sources) >= 3:
        reliability += 0.2
    elif len(sources) >= 2:
        reliability += 0.15
    elif len(sources) >= 1:
        reliability += 0.1

    # Source quality (length and content)
    avg_length = sum(len(str(source.get('text', str(source)))) for source in sources) / len(sources)
    if avg_length > 500:
        reliability += 0.15
    elif avg_length > 200:
        reliability += 0.1

    # Source diversity (different titles/types)
    unique_titles = len(set(source.get('title', 'untitled') for source in sources))
    if unique_titles == len(sources):
        reliability += 0.1

    return min(1.0, reliability)

def _calculate_semantic_coherence(query: str, answer: str) -> float:
    """Calculate semantic coherence for production algorithm"""
    coherence = 0.6  # Base coherence

    # Query-answer alignment
    query_words = set(query.lower().split())
    answer_words = set(answer.lower().split())

    # Remove stop words
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
    query_words = query_words - stop_words
    answer_words = answer_words - stop_words

    if query_words:
        overlap = len(query_words.intersection(answer_words))
        alignment = overlap / len(query_words)
        coherence += alignment * 0.3

    # Answer completeness
    if len(answer.split()) >= 10:
        coherence += 0.1

    return min(1.0, coherence)

def _calculate_evidence_strength(answer: str, sources: List[Dict]) -> float:
    """Calculate evidence strength for production algorithm"""
    strength = 0.5  # Base strength

    # Explicit citations
    citation_phrases = ['according to', 'based on', 'data shows', 'analysis indicates', 'research suggests']
    has_citations = any(phrase in answer.lower() for phrase in citation_phrases)
    if has_citations:
        strength += 0.2

    # Numerical evidence
    import re
    numbers = re.findall(r'\d+(?:\.\d+)?', answer)
    if numbers:
        strength += min(0.15, len(numbers) * 0.03)

    # Source alignment
    if sources:
        answer_words = set(answer.lower().split())
        source_alignment = 0
        for source in sources:
            source_text = source.get('text', str(source)).lower()
            source_words = set(source_text.split())
            overlap = len(answer_words.intersection(source_words))
            if answer_words:
                alignment = overlap / len(answer_words)
                source_alignment = max(source_alignment, alignment)

        strength += source_alignment * 0.15

    return min(1.0, strength)

# Multi-LLM Convergence Functions
async def _generate_multi_llm_responses(query: str, sources: List[Dict]) -> List[Dict]:
    """Generate responses from multiple LLMs for convergence analysis"""
    responses = []

    # Prepare context from sources
    context = "\n".join([source.get('text', str(source)) for source in sources])
    prompt = f"""Based on the following context, answer this question: {query}

Context:
{context}

Please provide a clear, factual answer based on the context provided."""

    # Track which LLMs we're attempting to use
    attempted_llms = []

    # Get OpenAI response
    if openai_client:
        attempted_llms.append("OpenAI GPT")
        try:
            openai_response = await _get_openai_response(prompt)
            responses.append({
                "model": "OpenAI GPT",
                "response": openai_response,
                "status": "success",
                "source": "real_api"
            })
        except Exception as e:
            responses.append({
                "model": "OpenAI GPT",
                "response": "",
                "status": "error",
                "error": str(e),
                "source": "real_api"
            })

    # Get Gemini response
    if gemini_api_key:
        attempted_llms.append("Gemini Pro")
        try:
            gemini_response = await _get_gemini_response(prompt)
            responses.append({
                "model": "Gemini Pro",
                "response": gemini_response,
                "status": "success",
                "source": "real_api"
            })
        except Exception as e:
            responses.append({
                "model": "Gemini Pro",
                "response": "",
                "status": "error",
                "error": str(e),
                "source": "real_api"
            })

    # Get Claude response
    if claude_client:
        attempted_llms.append("Claude")
        try:
            claude_response = await _get_claude_response(prompt)
            responses.append({
                "model": "Claude",
                "response": claude_response,
                "status": "success",
                "source": "real_api"
            })
        except Exception as e:
            responses.append({
                "model": "Claude",
                "response": "",
                "status": "error",
                "error": str(e),
                "source": "real_api"
            })

    # Add simulated responses for transparency (always include for comparison)
    simulated_responses = _get_simulated_llm_responses(query, context)
    responses.extend(simulated_responses)

    # Add metadata about what was attempted vs simulated
    for response in responses:
        if response.get("source") == "simulated":
            response["transparency_note"] = "Simulated response for demo purposes"
        elif response.get("source") == "real_api":
            if response["status"] == "error":
                response["transparency_note"] = "Real API attempted but failed"
            else:
                response["transparency_note"] = "Real API response"

    return responses

async def _get_openai_response(prompt: str) -> str:
    """Get response from OpenAI"""
    try:
        response = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that provides accurate, factual answers based on the given context."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        raise Exception(f"OpenAI API error: {str(e)}")

async def _get_gemini_response(prompt: str) -> str:
    """Get response from Gemini via HTTP API (memory efficient)"""
    try:
        if not gemini_api_key:
            raise Exception("Gemini API key not configured")

        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={gemini_api_key}"

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "maxOutputTokens": 200
            }
        }

        headers = {
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        if 'candidates' in data and len(data['candidates']) > 0:
            return data['candidates'][0]['content']['parts'][0]['text'].strip()
        else:
            raise Exception("No response from Gemini")

    except Exception as e:
        raise Exception(f"Gemini HTTP API error: {str(e)}")

async def _get_claude_response(prompt: str) -> str:
    """Get response from Claude"""
    try:
        response = claude_client.messages.create(
            model="claude-3-sonnet-20240229",
            max_tokens=200,
            temperature=0.3,
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        return response.content[0].text.strip()
    except Exception as e:
        raise Exception(f"Claude API error: {str(e)}")

def _get_simulated_llm_responses(query: str, context: str) -> List[Dict]:
    """
    TRANSPARENT SIMULATED LLM RESPONSES
    These are pre-written responses used when real APIs are not available
    """

    # Generate contextual simulated responses based on query
    if "average" in query.lower() or "mean" in query.lower():
        openai_sim = "Based on the analysis of the provided data, the average appears to be calculated from the total values divided by the number of periods."
        gemini_sim = "According to the data provided, the average value can be determined by examining the total figures across the specified timeframe."
        claude_sim = "The average calculation involves summing the relevant data points and dividing by the count, as indicated in the source material."
    elif "sales" in query.lower() or "revenue" in query.lower():
        openai_sim = "The sales data indicates performance metrics that can be analyzed through the provided financial information."
        gemini_sim = "Revenue analysis shows trends that align with the documented sales figures in the context."
        claude_sim = "Sales performance can be evaluated by examining the financial data and metrics presented in the sources."
    else:
        openai_sim = "Based on the provided context, the answer can be derived from analyzing the relevant information and data points."
        gemini_sim = "The context provides sufficient information to formulate a response based on the available data."
        claude_sim = "Analysis of the provided information suggests a conclusion that aligns with the documented evidence."

    return [
        {
            "model": "OpenAI GPT (Simulated)",
            "response": openai_sim,
            "status": "simulated",
            "source": "simulated",
            "transparency_note": "⚠️ SIMULATED: Pre-written response for demo purposes"
        },
        {
            "model": "Gemini Pro (Simulated)",
            "response": gemini_sim,
            "status": "simulated",
            "source": "simulated",
            "transparency_note": "⚠️ SIMULATED: Pre-written response for demo purposes"
        },
        {
            "model": "Claude (Simulated)",
            "response": claude_sim,
            "status": "simulated",
            "source": "simulated",
            "transparency_note": "⚠️ SIMULATED: Pre-written response for demo purposes"
        }
    ]

def _calculate_multi_llm_convergence(original_answer: str, llm_responses: List[Dict], query: str, sources: List[Dict]) -> Dict:
    """Calculate convergence metrics across multiple LLM responses"""

    successful_responses = [r for r in llm_responses if r["status"] in ["success", "simulated"]]

    if not successful_responses:
        # Fallback to single-model analysis
        return {
            "model_confidence": 0.6,
            "source_quality": 0.7,
            "citation_accuracy": 0.6,
            "question_match": 0.7,
            "consensus_score": 0.5,
            "llm_agreement": 0.0
        }

    # 1. Calculate LLM Agreement/Consensus
    consensus_score = _calculate_llm_consensus(original_answer, successful_responses)

    # 2. Enhanced Model Confidence (boosted by multi-LLM agreement)
    base_confidence = _calculate_convergence_model_confidence(original_answer, 0.6)
    model_confidence = min(1.0, base_confidence + (consensus_score * 0.2))  # Boost from consensus

    # 3. Enhanced Source Quality (validated across models)
    base_source_quality = _calculate_convergence_source_quality(sources, 0.6)
    source_quality = min(1.0, base_source_quality + (consensus_score * 0.1))

    # 4. Enhanced Citation Accuracy (cross-validated)
    base_citation = _calculate_convergence_citation_accuracy(original_answer, sources, 0.6)
    citation_accuracy = min(1.0, base_citation + (consensus_score * 0.15))

    # 5. Enhanced Question Match (validated by multiple models)
    base_match = _calculate_convergence_question_match(query, original_answer, 0.6)
    question_match = min(1.0, base_match + (consensus_score * 0.1))

    return {
        "model_confidence": model_confidence,
        "source_quality": source_quality,
        "citation_accuracy": citation_accuracy,
        "question_match": question_match,
        "consensus_score": consensus_score,
        "llm_agreement": consensus_score,
        "models_analyzed": len(successful_responses),
        "convergence_boost": consensus_score * 0.2
    }

def _calculate_llm_consensus(original_answer: str, llm_responses: List[Dict]) -> float:
    """Calculate consensus/agreement between original answer and LLM responses"""
    if not llm_responses:
        return 0.5

    consensus_scores = []

    for llm_response in llm_responses:
        llm_text = llm_response["response"]

        # 1. Semantic similarity (word overlap)
        similarity = _calculate_semantic_similarity(original_answer, llm_text)

        # 2. Factual consistency (numbers, facts)
        factual_consistency = _calculate_factual_consistency(original_answer, llm_text)

        # 3. Structural similarity (length, format)
        structural_similarity = _calculate_structural_similarity(original_answer, llm_text)

        # Combined consensus score for this LLM
        llm_consensus = (similarity * 0.5 + factual_consistency * 0.3 + structural_similarity * 0.2)
        consensus_scores.append(llm_consensus)

    # Average consensus across all LLMs
    avg_consensus = sum(consensus_scores) / len(consensus_scores)

    # Bonus for high agreement across multiple models
    if len(consensus_scores) >= 2 and min(consensus_scores) > 0.6:
        avg_consensus += 0.1  # Multi-model agreement bonus

    return min(1.0, avg_consensus)

def _calculate_semantic_similarity(text1: str, text2: str) -> float:
    """Calculate semantic similarity between two texts"""
    # Simple word-based similarity
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())

    if not words1 or not words2:
        return 0.3

    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))

    jaccard_similarity = intersection / union if union > 0 else 0

    # Also use sequence similarity
    sequence_similarity = SequenceMatcher(None, text1.lower(), text2.lower()).ratio()

    return (jaccard_similarity * 0.6 + sequence_similarity * 0.4)

def _calculate_factual_consistency(text1: str, text2: str) -> float:
    """Calculate factual consistency between two texts"""
    # Extract numbers and facts
    numbers1 = re.findall(r'\d+(?:\.\d+)?', text1)
    numbers2 = re.findall(r'\d+(?:\.\d+)?', text2)

    # Check for consistent numbers
    number_consistency = 0.7  # Base consistency
    if numbers1 and numbers2:
        common_numbers = set(numbers1).intersection(set(numbers2))
        if common_numbers:
            number_consistency = 0.9
        elif len(numbers1) == len(numbers2):
            number_consistency = 0.8

    # Check for consistent key phrases
    key_phrases1 = set(re.findall(r'\b(?:average|total|sum|mean|analysis|data|based on)\b', text1.lower()))
    key_phrases2 = set(re.findall(r'\b(?:average|total|sum|mean|analysis|data|based on)\b', text2.lower()))

    phrase_consistency = len(key_phrases1.intersection(key_phrases2)) / max(len(key_phrases1.union(key_phrases2)), 1)

    return (number_consistency * 0.6 + phrase_consistency * 0.4)

def _calculate_structural_similarity(text1: str, text2: str) -> float:
    """Calculate structural similarity between two texts"""
    len1, len2 = len(text1), len(text2)

    # Length similarity
    length_ratio = min(len1, len2) / max(len1, len2) if max(len1, len2) > 0 else 1.0

    # Sentence structure similarity
    sentences1 = text1.count('.')
    sentences2 = text2.count('.')
    sentence_ratio = min(sentences1, sentences2) / max(sentences1, sentences2) if max(sentences1, sentences2) > 0 else 1.0

    return (length_ratio * 0.7 + sentence_ratio * 0.3)

def _count_real_vs_simulated(llm_responses: List[Dict]) -> str:
    """Count real vs simulated responses for transparency"""
    real_count = len([r for r in llm_responses if r.get("source") == "real_api" and r.get("status") == "success"])
    simulated_count = len([r for r in llm_responses if r.get("source") == "simulated"])
    error_count = len([r for r in llm_responses if r.get("status") == "error"])

    parts = []
    if real_count > 0:
        parts.append(f"{real_count} real")
    if simulated_count > 0:
        parts.append(f"{simulated_count} simulated")
    if error_count > 0:
        parts.append(f"{error_count} failed")

    return ", ".join(parts) if parts else "no responses"

def _generate_simple_answer_from_sources(query: str, sources: List[Dict]) -> str:
    """Generate a simple answer from sources when no answer is provided"""
    if not sources:
        return "No sources available to generate an answer."

    # Extract key information based on query type
    query_lower = query.lower()

    # Look for specific data in sources
    all_text = " ".join([source.get('text', str(source)) for source in sources])

    if "revenue" in query_lower or "total" in query_lower:
        # Look for revenue/financial figures
        import re
        revenue_matches = re.findall(r'\$[\d,]+(?:\.\d+)?\s*(?:million|billion|thousand)?', all_text)
        if revenue_matches:
            return f"Based on the provided data, the revenue figure is {revenue_matches[0]}."

    if "growth" in query_lower or "increase" in query_lower:
        # Look for percentage growth
        import re
        percent_matches = re.findall(r'\d+%', all_text)
        if percent_matches:
            return f"According to the data, there was a {percent_matches[0]} growth rate."

    if "customer" in query_lower and "satisfaction" in query_lower:
        # Look for satisfaction scores
        import re
        score_matches = re.findall(r'\d+(?:\.\d+)?\s*out of \d+', all_text)
        if score_matches:
            return f"Customer satisfaction scores show {score_matches[0]}."

    # Generic answer based on first source
    first_source = sources[0].get('text', str(sources[0]))
    sentences = first_source.split('.')[:2]  # First two sentences
    return f"Based on the available data, {'. '.join(sentences).strip()}."

if __name__ == "__main__":
    print("🚀 Starting Trust Score Comparison Server...")
    print("📊 Available at: http://localhost:8001")
    print("📋 Methods endpoint: http://localhost:8001/api/v1/trust-comparison/methods")
    print("🎮 Demo endpoint: http://localhost:8001/api/v1/trust-comparison/demo")

    uvicorn.run(app, host="0.0.0.0", port=8001)
