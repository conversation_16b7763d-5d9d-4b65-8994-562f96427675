{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_OPTIONS='--max-old-space-size=4096' next build", "start": "next start", "test": "jest", "build:railway": "npm ci --only=production && npm run build"}, "dependencies": {"@auth/pg-adapter": "^1.9.1", "@geist-ui/core": "^2.3.8", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@stripe/stripe-js": "^4.8.0", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.511.0", "next": "^14.2.29", "next-auth": "^4.24.11", "pg": "^8.16.0", "plotly.js": "^3.0.1", "plotly.js-dist-min": "^2.35.2", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.9", "react-hook-form": "^7.54.0", "react-plotly.js": "^2.6.0", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.15", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/plotly.js": "^2.29.4", "@types/react": "18.3.23", "@types/react-dom": "^18.3.1", "@types/react-plotly.js": "^2.6.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "5.8.3"}}