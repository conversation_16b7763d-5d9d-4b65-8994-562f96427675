"""
Vector Storage Service for AIthentiq
Supports FAISS, Pinecone, and Weaviate with tenant isolation
"""

import os
import json
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from abc import ABC, abstractmethod
from pathlib import Path
import pickle

logger = logging.getLogger(__name__)

class VectorStore(ABC):
    """Abstract base class for vector stores"""
    
    @abstractmethod
    def add_vectors(
        self, 
        vectors: List[List[float]], 
        metadata: List[Dict[str, Any]], 
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Add vectors to the store"""
        pass
    
    @abstractmethod
    def search(
        self, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors"""
        pass
    
    @abstractmethod
    def delete_vectors(self, ids: List[str]) -> bool:
        """Delete vectors by IDs"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics"""
        pass

class FAISSVectorStore(VectorStore):
    """FAISS-based vector store with tenant isolation"""
    
    def __init__(self, tenant_id: str, dimension: int = 1536, index_type: str = "flat"):
        self.tenant_id = tenant_id
        self.dimension = dimension
        self.index_type = index_type
        
        # Create tenant-specific storage directory
        self.storage_dir = Path(f"vector_storage/faiss/{tenant_id}")
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        self.index_path = self.storage_dir / "index.faiss"
        self.metadata_path = self.storage_dir / "metadata.json"
        
        # Initialize FAISS
        self._init_faiss()
        
        # Load existing data
        self._load_index()
    
    def _init_faiss(self):
        """Initialize FAISS index"""
        try:
            import faiss
            
            if self.index_type == "flat":
                self.index = faiss.IndexFlatIP(self.dimension)  # Inner product (cosine similarity)
            elif self.index_type == "ivf":
                quantizer = faiss.IndexFlatIP(self.dimension)
                self.index = faiss.IndexIVFFlat(quantizer, self.dimension, 100)
            else:
                self.index = faiss.IndexFlatIP(self.dimension)
            
            self.metadata_store = {}
            self.id_to_index = {}
            self.index_to_id = {}
            self.next_index = 0
            
            logger.info(f"FAISS index initialized for tenant {self.tenant_id}")
            
        except ImportError:
            raise ImportError("FAISS is required for FAISSVectorStore")
    
    def _load_index(self):
        """Load existing index and metadata"""
        try:
            import faiss
            
            if self.index_path.exists():
                self.index = faiss.read_index(str(self.index_path))
                logger.info(f"Loaded FAISS index for tenant {self.tenant_id}")
            
            if self.metadata_path.exists():
                with open(self.metadata_path, 'r') as f:
                    data = json.load(f)
                    self.metadata_store = data.get('metadata', {})
                    self.id_to_index = data.get('id_to_index', {})
                    self.index_to_id = {v: k for k, v in self.id_to_index.items()}
                    self.next_index = data.get('next_index', 0)
                logger.info(f"Loaded metadata for tenant {self.tenant_id}")
                
        except Exception as e:
            logger.error(f"Failed to load index: {e}")
    
    def _save_index(self):
        """Save index and metadata to disk"""
        try:
            import faiss
            
            # Save FAISS index
            faiss.write_index(self.index, str(self.index_path))
            
            # Save metadata
            metadata_data = {
                'metadata': self.metadata_store,
                'id_to_index': self.id_to_index,
                'next_index': self.next_index,
                'tenant_id': self.tenant_id,
                'dimension': self.dimension
            }
            
            with open(self.metadata_path, 'w') as f:
                json.dump(metadata_data, f, indent=2)
            
            logger.debug(f"Saved index for tenant {self.tenant_id}")
            
        except Exception as e:
            logger.error(f"Failed to save index: {e}")
    
    def add_vectors(
        self, 
        vectors: List[List[float]], 
        metadata: List[Dict[str, Any]], 
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Add vectors to FAISS index"""
        try:
            if not vectors:
                return []
            
            # Convert to numpy array
            vector_array = np.array(vectors, dtype=np.float32)
            
            # Normalize vectors for cosine similarity
            norms = np.linalg.norm(vector_array, axis=1, keepdims=True)
            vector_array = vector_array / norms
            
            # Generate IDs if not provided
            if ids is None:
                ids = [f"{self.tenant_id}_{self.next_index + i}" for i in range(len(vectors))]
            
            # Add to FAISS index
            start_index = self.next_index
            self.index.add(vector_array)
            
            # Store metadata and mappings
            for i, (vector_id, meta) in enumerate(zip(ids, metadata)):
                index_pos = start_index + i
                self.id_to_index[vector_id] = index_pos
                self.index_to_id[index_pos] = vector_id
                self.metadata_store[vector_id] = meta
            
            self.next_index += len(vectors)
            
            # Save to disk
            self._save_index()
            
            logger.info(f"Added {len(vectors)} vectors for tenant {self.tenant_id}")
            return ids
            
        except Exception as e:
            logger.error(f"Failed to add vectors: {e}")
            raise
    
    def search(
        self, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors"""
        try:
            if self.index.ntotal == 0:
                return []
            
            # Normalize query vector
            query_array = np.array([query_vector], dtype=np.float32)
            query_array = query_array / np.linalg.norm(query_array)
            
            # Search FAISS index
            scores, indices = self.index.search(query_array, min(top_k, self.index.ntotal))
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx == -1:  # FAISS returns -1 for invalid indices
                    continue
                
                vector_id = self.index_to_id.get(idx)
                if not vector_id:
                    continue
                
                metadata = self.metadata_store.get(vector_id, {})
                
                # Apply metadata filtering
                if filter_metadata:
                    if not self._matches_filter(metadata, filter_metadata):
                        continue
                
                results.append({
                    'id': vector_id,
                    'score': float(score),
                    'metadata': metadata
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def _matches_filter(self, metadata: Dict[str, Any], filter_metadata: Dict[str, Any]) -> bool:
        """Check if metadata matches filter criteria"""
        for key, value in filter_metadata.items():
            if key not in metadata:
                return False
            if metadata[key] != value:
                return False
        return True
    
    def delete_vectors(self, ids: List[str]) -> bool:
        """Delete vectors by IDs (FAISS doesn't support deletion, so we mark as deleted)"""
        try:
            for vector_id in ids:
                if vector_id in self.metadata_store:
                    # Mark as deleted in metadata
                    self.metadata_store[vector_id]['deleted'] = True
            
            self._save_index()
            logger.info(f"Marked {len(ids)} vectors as deleted for tenant {self.tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get FAISS index statistics"""
        active_vectors = sum(1 for meta in self.metadata_store.values() if not meta.get('deleted', False))
        
        return {
            'total_vectors': self.index.ntotal,
            'active_vectors': active_vectors,
            'deleted_vectors': self.index.ntotal - active_vectors,
            'dimension': self.dimension,
            'index_type': self.index_type,
            'tenant_id': self.tenant_id,
            'storage_size_mb': self._get_storage_size()
        }
    
    def _get_storage_size(self) -> float:
        """Get storage size in MB"""
        try:
            total_size = 0
            for file_path in self.storage_dir.glob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            return total_size / (1024 * 1024)
        except:
            return 0.0

class PineconeVectorStore(VectorStore):
    """Pinecone-based vector store with tenant isolation"""
    
    def __init__(self, tenant_id: str, dimension: int = 1536):
        self.tenant_id = tenant_id
        self.dimension = dimension
        self.index_name = f"aithentiq-{tenant_id}"
        
        # Initialize Pinecone
        self._init_pinecone()
    
    def _init_pinecone(self):
        """Initialize Pinecone client"""
        try:
            import pinecone
            
            api_key = os.getenv("PINECONE_API_KEY")
            environment = os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp")
            
            if not api_key:
                raise ValueError("PINECONE_API_KEY environment variable is required")
            
            pinecone.init(api_key=api_key, environment=environment)
            
            # Create index if it doesn't exist
            if self.index_name not in pinecone.list_indexes():
                pinecone.create_index(
                    name=self.index_name,
                    dimension=self.dimension,
                    metric="cosine"
                )
            
            self.index = pinecone.Index(self.index_name)
            logger.info(f"Pinecone index initialized for tenant {self.tenant_id}")
            
        except ImportError:
            raise ImportError("pinecone-client is required for PineconeVectorStore")
    
    def add_vectors(
        self, 
        vectors: List[List[float]], 
        metadata: List[Dict[str, Any]], 
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Add vectors to Pinecone index"""
        try:
            if not vectors:
                return []
            
            # Generate IDs if not provided
            if ids is None:
                ids = [f"{self.tenant_id}_{i}" for i in range(len(vectors))]
            
            # Prepare upsert data
            upsert_data = []
            for vector_id, vector, meta in zip(ids, vectors, metadata):
                # Add tenant_id to metadata for filtering
                meta_with_tenant = {**meta, 'tenant_id': self.tenant_id}
                upsert_data.append((vector_id, vector, meta_with_tenant))
            
            # Upsert to Pinecone
            self.index.upsert(vectors=upsert_data)
            
            logger.info(f"Added {len(vectors)} vectors to Pinecone for tenant {self.tenant_id}")
            return ids
            
        except Exception as e:
            logger.error(f"Failed to add vectors to Pinecone: {e}")
            raise
    
    def search(
        self, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search Pinecone index"""
        try:
            # Add tenant filter
            filter_dict = {'tenant_id': self.tenant_id}
            if filter_metadata:
                filter_dict.update(filter_metadata)
            
            # Query Pinecone
            response = self.index.query(
                vector=query_vector,
                top_k=top_k,
                filter=filter_dict,
                include_metadata=True
            )
            
            results = []
            for match in response.matches:
                results.append({
                    'id': match.id,
                    'score': match.score,
                    'metadata': match.metadata
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Pinecone search failed: {e}")
            return []
    
    def delete_vectors(self, ids: List[str]) -> bool:
        """Delete vectors from Pinecone"""
        try:
            self.index.delete(ids=ids)
            logger.info(f"Deleted {len(ids)} vectors from Pinecone for tenant {self.tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors from Pinecone: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Pinecone index statistics"""
        try:
            stats = self.index.describe_index_stats()
            return {
                'total_vectors': stats.total_vector_count,
                'dimension': self.dimension,
                'tenant_id': self.tenant_id,
                'index_name': self.index_name
            }
        except Exception as e:
            logger.error(f"Failed to get Pinecone stats: {e}")
            return {}

class WeaviateVectorStore(VectorStore):
    """Weaviate-based vector store with tenant isolation"""
    
    def __init__(self, tenant_id: str, dimension: int = 1536):
        self.tenant_id = tenant_id
        self.dimension = dimension
        self.class_name = f"AIthentiq_{tenant_id.replace('-', '_')}"
        
        # Initialize Weaviate
        self._init_weaviate()
    
    def _init_weaviate(self):
        """Initialize Weaviate client"""
        try:
            import weaviate
            
            url = os.getenv("WEAVIATE_URL", "http://localhost:8080")
            api_key = os.getenv("WEAVIATE_API_KEY")
            
            if api_key:
                auth_config = weaviate.AuthApiKey(api_key=api_key)
                self.client = weaviate.Client(url=url, auth_client_secret=auth_config)
            else:
                self.client = weaviate.Client(url=url)
            
            # Create class schema if it doesn't exist
            self._create_schema()
            
            logger.info(f"Weaviate client initialized for tenant {self.tenant_id}")
            
        except ImportError:
            raise ImportError("weaviate-client is required for WeaviateVectorStore")
    
    def _create_schema(self):
        """Create Weaviate class schema"""
        try:
            # Check if class exists
            if self.client.schema.exists(self.class_name):
                return
            
            # Define class schema
            class_schema = {
                "class": self.class_name,
                "description": f"AIthentiq documents for tenant {self.tenant_id}",
                "vectorizer": "none",  # We provide our own vectors
                "properties": [
                    {
                        "name": "content",
                        "dataType": ["text"],
                        "description": "Document content"
                    },
                    {
                        "name": "metadata",
                        "dataType": ["object"],
                        "description": "Document metadata"
                    },
                    {
                        "name": "tenant_id",
                        "dataType": ["string"],
                        "description": "Tenant identifier"
                    }
                ]
            }
            
            self.client.schema.create_class(class_schema)
            logger.info(f"Created Weaviate class {self.class_name}")
            
        except Exception as e:
            logger.error(f"Failed to create Weaviate schema: {e}")
    
    def add_vectors(
        self, 
        vectors: List[List[float]], 
        metadata: List[Dict[str, Any]], 
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """Add vectors to Weaviate"""
        try:
            if not vectors:
                return []
            
            # Generate IDs if not provided
            if ids is None:
                ids = [f"{self.tenant_id}_{i}" for i in range(len(vectors))]
            
            # Add objects to Weaviate
            with self.client.batch as batch:
                for vector_id, vector, meta in zip(ids, vectors, metadata):
                    properties = {
                        "content": meta.get("text", ""),
                        "metadata": meta,
                        "tenant_id": self.tenant_id
                    }
                    
                    batch.add_data_object(
                        data_object=properties,
                        class_name=self.class_name,
                        uuid=vector_id,
                        vector=vector
                    )
            
            logger.info(f"Added {len(vectors)} vectors to Weaviate for tenant {self.tenant_id}")
            return ids
            
        except Exception as e:
            logger.error(f"Failed to add vectors to Weaviate: {e}")
            raise
    
    def search(
        self, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search Weaviate index"""
        try:
            # Build query
            query = self.client.query.get(self.class_name, ["content", "metadata"]) \
                .with_near_vector({"vector": query_vector}) \
                .with_limit(top_k) \
                .with_where({
                    "path": ["tenant_id"],
                    "operator": "Equal",
                    "valueString": self.tenant_id
                })
            
            # Add additional filters
            if filter_metadata:
                for key, value in filter_metadata.items():
                    query = query.with_where({
                        "path": [f"metadata.{key}"],
                        "operator": "Equal",
                        "valueString": str(value)
                    })
            
            # Execute query
            response = query.do()
            
            results = []
            if "data" in response and "Get" in response["data"]:
                for item in response["data"]["Get"][self.class_name]:
                    results.append({
                        'id': item.get('_additional', {}).get('id'),
                        'score': item.get('_additional', {}).get('distance', 0),
                        'metadata': item.get('metadata', {})
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Weaviate search failed: {e}")
            return []
    
    def delete_vectors(self, ids: List[str]) -> bool:
        """Delete vectors from Weaviate"""
        try:
            for vector_id in ids:
                self.client.data_object.delete(vector_id, class_name=self.class_name)
            
            logger.info(f"Deleted {len(ids)} vectors from Weaviate for tenant {self.tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors from Weaviate: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Weaviate statistics"""
        try:
            # Get object count
            response = self.client.query.aggregate(self.class_name) \
                .with_meta_count() \
                .with_where({
                    "path": ["tenant_id"],
                    "operator": "Equal",
                    "valueString": self.tenant_id
                }).do()
            
            count = 0
            if "data" in response and "Aggregate" in response["data"]:
                count = response["data"]["Aggregate"][self.class_name][0]["meta"]["count"]
            
            return {
                'total_vectors': count,
                'dimension': self.dimension,
                'tenant_id': self.tenant_id,
                'class_name': self.class_name
            }
            
        except Exception as e:
            logger.error(f"Failed to get Weaviate stats: {e}")
            return {}

class VectorStorageService:
    """
    Unified vector storage service supporting multiple backends
    """
    
    def __init__(self):
        self.stores = {}  # tenant_id -> VectorStore
    
    def get_vector_store(
        self, 
        tenant_id: str, 
        store_type: str = "faiss",
        dimension: int = 1536
    ) -> VectorStore:
        """Get or create vector store for tenant"""
        store_key = f"{tenant_id}_{store_type}"
        
        if store_key not in self.stores:
            if store_type == "faiss":
                self.stores[store_key] = FAISSVectorStore(tenant_id, dimension)
            elif store_type == "pinecone":
                self.stores[store_key] = PineconeVectorStore(tenant_id, dimension)
            elif store_type == "weaviate":
                self.stores[store_key] = WeaviateVectorStore(tenant_id, dimension)
            else:
                raise ValueError(f"Unsupported vector store type: {store_type}")
        
        return self.stores[store_key]
    
    def get_available_stores(self) -> List[str]:
        """Get list of available vector store types"""
        available = ["faiss"]  # FAISS is always available
        
        # Check Pinecone
        if os.getenv("PINECONE_API_KEY"):
            try:
                import pinecone
                available.append("pinecone")
            except ImportError:
                pass
        
        # Check Weaviate
        try:
            import weaviate
            available.append("weaviate")
        except ImportError:
            pass
        
        return available

# Global vector storage service instance
vector_storage_service = VectorStorageService()
