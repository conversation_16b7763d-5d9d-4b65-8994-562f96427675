"use client";

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  RefreshCw, 
  Plus, 
  Settings, 
  Trash2, 
  GitBranch, 
  Cloud,
  Folder,
  Database,
  Globe,
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  ExternalLink,
  Sync,
  Play,
  Pause,
  FileText,
  Users,
  HardDrive
} from 'lucide-react';
import { createApiInstance } from '@/lib/api';

interface DataConnector {
  id: string;
  name: string;
  connector_type: string;
  description?: string;
  status: 'active' | 'paused' | 'error' | 'disconnected';
  auto_sync_enabled: boolean;
  sync_frequency: string;
  last_sync_at?: string;
  next_sync_at?: string;
  sync_count: number;
  last_error?: string;
  created_at: string;
  config?: any;
}

const CONNECTOR_TYPES = {
  github: {
    name: 'GitHub',
    icon: GitBranch,
    color: 'text-gray-900',
    bgColor: 'bg-gray-100',
    description: 'Git repositories and source code'
  },
  sharepoint: {
    name: 'SharePoint',
    icon: Cloud,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    description: 'SharePoint sites and document libraries'
  },
  onedrive: {
    name: 'OneDrive',
    icon: Cloud,
    color: 'text-blue-500',
    bgColor: 'bg-blue-50',
    description: 'OneDrive files and folders'
  },
  google_drive: {
    name: 'Google Drive',
    icon: HardDrive,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    description: 'Google Drive documents and files'
  },
  dropbox: {
    name: 'Dropbox',
    icon: Cloud,
    color: 'text-blue-700',
    bgColor: 'bg-blue-100',
    description: 'Dropbox files and shared folders'
  },
  network_folder: {
    name: 'Network Folder',
    icon: Folder,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    description: 'Local network drives and shared folders'
  },
  database: {
    name: 'Database',
    icon: Database,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    description: 'SQL databases and data warehouses'
  },
  api: {
    name: 'API',
    icon: Globe,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
    description: 'REST APIs and web services'
  },
  file_upload: {
    name: 'File Upload',
    icon: FileText,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    description: 'Manually uploaded documents'
  }
};

export default function UniversalConnectorManagement() {
  const { data: session } = useSession();
  const [connectors, setConnectors] = useState<DataConnector[]>([]);
  const [selectedConnector, setSelectedConnector] = useState<DataConnector | null>(null);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState<{ [key: string]: boolean }>({});
  const [error, setError] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<string>('all');

  useEffect(() => {
    if (session) {
      loadConnectors();
    }
  }, [session]);

  const loadConnectors = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.get('/connectors');
      setConnectors(response.data.connectors || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load connectors');
    } finally {
      setLoading(false);
    }
  };

  const handleSyncConnector = async (connectorId: string) => {
    if (!session) return;

    setSyncing(prev => ({ ...prev, [connectorId]: true }));

    try {
      const api = createApiInstance(session);
      
      // Universal sync endpoint for all connector types
      const response = await api.post(`/connectors/${connectorId}/sync`, {
        force_full_sync: false
      });

      if (response.data.success) {
        alert(`✅ Sync started successfully`);
        await loadConnectors();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Sync failed';
      alert(`❌ Sync failed: ${errorMessage}`);
    } finally {
      setSyncing(prev => ({ ...prev, [connectorId]: false }));
    }
  };

  const toggleConnectorStatus = async (connectorId: string, currentStatus: string) => {
    if (!session) return;

    try {
      const api = createApiInstance(session);
      const newStatus = currentStatus === 'active' ? 'paused' : 'active';
      
      await api.put(`/connectors/${connectorId}`, {
        status: newStatus
      });

      await loadConnectors();
    } catch (err) {
      alert(`Failed to update connector: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const getConnectorTypeInfo = (type: string) => {
    return CONNECTOR_TYPES[type as keyof typeof CONNECTOR_TYPES] || {
      name: type.charAt(0).toUpperCase() + type.slice(1),
      icon: Database,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      description: 'Data connector'
    };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'paused':
        return <Pause className="h-5 w-5 text-yellow-500" />;
      case 'disconnected':
        return <XCircle className="h-5 w-5 text-gray-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const getFilteredConnectors = () => {
    if (filterType === 'all') return connectors;
    return connectors.filter(connector => connector.connector_type === filterType);
  };

  const getConnectorTypeCounts = () => {
    const counts: { [key: string]: number } = { all: connectors.length };
    connectors.forEach(connector => {
      counts[connector.connector_type] = (counts[connector.connector_type] || 0) + 1;
    });
    return counts;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  const typeCounts = getConnectorTypeCounts();
  const filteredConnectors = getFilteredConnectors();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Database className="h-8 w-8 text-gray-700" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Data Connectors</h2>
            <p className="text-sm text-gray-600">Manage all data source integrations and sync operations</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadConnectors}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="h-4 w-4" />
            <span>Add Connector</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <XCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Connector Type Filter */}
      <div className="bg-white rounded-lg shadow border p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter by Type</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilterType('all')}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              filterType === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All ({typeCounts.all || 0})
          </button>
          {Object.entries(CONNECTOR_TYPES).map(([type, info]) => {
            const count = typeCounts[type] || 0;
            if (count === 0) return null;
            
            const Icon = info.icon;
            return (
              <button
                key={type}
                onClick={() => setFilterType(type)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filterType === type
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{info.name} ({count})</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Connectors Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredConnectors.map((connector) => {
          const typeInfo = getConnectorTypeInfo(connector.connector_type);
          const Icon = typeInfo.icon;
          
          return (
            <div
              key={connector.id}
              className="bg-white rounded-lg shadow border hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${typeInfo.bgColor}`}>
                      <Icon className={`h-6 w-6 ${typeInfo.color}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{connector.name}</h3>
                      <p className="text-sm text-gray-600">{typeInfo.name}</p>
                    </div>
                  </div>
                  {getStatusIcon(connector.status)}
                </div>

                {connector.description && (
                  <p className="text-sm text-gray-600 mb-4">{connector.description}</p>
                )}

                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className="font-medium capitalize">{connector.status}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Auto Sync:</span>
                    <span className="font-medium">{connector.auto_sync_enabled ? 'Enabled' : 'Disabled'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Frequency:</span>
                    <span className="font-medium capitalize">{connector.sync_frequency}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Syncs:</span>
                    <span className="font-medium">{connector.sync_count}</span>
                  </div>
                </div>

                <div className="text-xs text-gray-500 mb-4">
                  <div>Last sync: {formatDate(connector.last_sync_at)}</div>
                  {connector.next_sync_at && (
                    <div>Next sync: {formatDate(connector.next_sync_at)}</div>
                  )}
                </div>

                {connector.last_error && (
                  <div className="bg-red-50 border border-red-200 rounded p-2 mb-4">
                    <p className="text-xs text-red-700">{connector.last_error}</p>
                  </div>
                )}

                <div className="flex space-x-2">
                  {/* Universal Sync Button */}
                  <button
                    onClick={() => handleSyncConnector(connector.id)}
                    disabled={syncing[connector.id] || connector.status === 'disconnected'}
                    className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Sync Now"
                  >
                    {syncing[connector.id] ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Sync className="h-4 w-4" />
                    )}
                    <span className="text-sm">
                      {syncing[connector.id] ? 'Syncing...' : 'Sync Now'}
                    </span>
                  </button>

                  {/* Toggle Status */}
                  <button
                    onClick={() => toggleConnectorStatus(connector.id, connector.status)}
                    disabled={connector.status === 'disconnected'}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                    title={connector.status === 'active' ? 'Pause' : 'Resume'}
                  >
                    {connector.status === 'active' ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </button>

                  {/* Settings */}
                  <button
                    onClick={() => setSelectedConnector(connector)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Settings"
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredConnectors.length === 0 && (
        <div className="text-center py-12">
          <Database className="h-16 w-16 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {filterType === 'all' ? 'No Connectors Found' : `No ${CONNECTOR_TYPES[filterType as keyof typeof CONNECTOR_TYPES]?.name || filterType} Connectors`}
          </h3>
          <p className="text-gray-600 mb-4">
            {filterType === 'all' 
              ? 'Get started by adding your first data connector.'
              : 'No connectors of this type have been configured yet.'
            }
          </p>
          <button className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="h-4 w-4" />
            <span>Add Connector</span>
          </button>
        </div>
      )}

      {/* Bulk Actions */}
      {filteredConnectors.length > 0 && (
        <div className="bg-white rounded-lg shadow border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Bulk Actions</h3>
          <div className="flex space-x-3">
            <button
              onClick={() => {
                filteredConnectors.forEach(connector => {
                  if (connector.status === 'active') {
                    handleSyncConnector(connector.id);
                  }
                });
              }}
              disabled={Object.values(syncing).some(Boolean)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              <Sync className="h-4 w-4" />
              <span>Sync All Active</span>
            </button>
            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <Pause className="h-4 w-4" />
              <span>Pause All</span>
            </button>
            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              <Play className="h-4 w-4" />
              <span>Resume All</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
