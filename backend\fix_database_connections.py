#!/usr/bin/env python3
"""
Fix database connection issues and optimize database performance
"""

import sqlite3
import sys
import os

def fix_database_connections():
    """Fix database connection issues"""
    
    db_path = "aithentiq.db"
    print(f"Fixing database connections for: {db_path}")
    
    conn = sqlite3.connect(db_path, timeout=30.0)
    cursor = conn.cursor()
    
    try:
        # Enable WAL mode for better concurrency
        cursor.execute("PRAGMA journal_mode=WAL")
        
        # Set synchronous mode to NORMAL for better performance
        cursor.execute("PRAGMA synchronous=NORMAL")
        
        # Set cache size to 64MB
        cursor.execute("PRAGMA cache_size=16384")
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys=ON")
        
        # Set busy timeout to 30 seconds
        cursor.execute("PRAGMA busy_timeout=30000")
        
        # Optimize database
        cursor.execute("VACUUM")
        cursor.execute("ANALYZE")
        
        conn.commit()
        print("✅ Database optimizations applied successfully")
        
        # Test basic operations
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM datasets")
        dataset_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM conversations")
        conversation_count = cursor.fetchone()[0]
        
        print(f"✅ Database test successful: {user_count} users, {dataset_count} datasets, {conversation_count} conversations")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def create_indexes():
    """Create indexes for better performance"""
    
    db_path = "aithentiq.db"
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create indexes for better query performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_queries_user_id ON queries(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_queries_dataset_id ON queries(dataset_id)",
            "CREATE INDEX IF NOT EXISTS idx_queries_created_at ON queries(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_datasets_user_id ON datasets(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_conversations_dataset_id ON conversations(dataset_id)",
            # "CREATE INDEX IF NOT EXISTS idx_conversation_messages_conversation_id ON conversation_messages(conversation_id)",
            "CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_api_keys_key ON api_keys(key)",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
            
        conn.commit()
        print("✅ Database indexes created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error creating indexes: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success1 = fix_database_connections()
    success2 = create_indexes()
    sys.exit(0 if (success1 and success2) else 1)
