"""
Task Workers for AIthentiq Background Processing
Handles document processing, embedding generation, and connector sync
"""

import os
import json
import logging
from typing import Dict, Any
from datetime import datetime

# Import task queue backends
try:
    from celery import Celery
    CELERY_AVAILABLE = True
except ImportError:
    CELERY_AVAILABLE = False

from database import SessionLocal
from models_multitenant import TenantDataset, TenantDocumentChunk, DataConnector
from middleware.tenant_isolation import tenant_scope
from services.task_queue_service import TaskType, TaskStatus

logger = logging.getLogger(__name__)

# Celery app instance (if available)
if CELERY_AVAILABLE:
    celery_app = Celery('aithentiq_tasks')

def get_task_processor(task_type: str):
    """
    Get the appropriate task processor for the task type
    """
    processors = {
        TaskType.DOCUMENT_PROCESSING.value: process_document_task,
        TaskType.EMBEDDING_GENERATION.value: process_embedding_task,
        TaskType.CONNECTOR_SYNC.value: process_connector_sync_task,
        TaskType.TRUST_SCORE_CALCULATION.value: process_trust_score_task,
        TaskType.DATA_EXPORT.value: process_data_export_task,
        TaskType.CLEANUP.value: process_cleanup_task,
    }
    
    return processors.get(task_type, process_unknown_task)

# Celery Tasks
if CELERY_AVAILABLE:
    @celery_app.task(bind=True)
    def process_task(self, task_payload: Dict[str, Any]):
        """
        Celery task processor
        """
        task_id = self.request.id
        task_payload["task_id"] = task_id
        
        return execute_task_with_context(task_payload)

# RQ Task Processor
def process_task_rq(task_payload: Dict[str, Any]):
    """
    RQ task processor
    """
    # RQ job ID is available through the job context
    import rq
    job = rq.get_current_job()
    task_payload["task_id"] = job.id if job else "unknown"
    
    return execute_task_with_context(task_payload)

# Synchronous Task Processor
def execute_task_sync(task_payload: Dict[str, Any]):
    """
    Synchronous task processor (fallback)
    """
    return execute_task_with_context(task_payload)

def execute_task_with_context(task_payload: Dict[str, Any]):
    """
    Execute task with proper tenant context
    """
    tenant_id = task_payload.get("tenant_id")
    user_id = task_payload.get("user_id")
    task_type = task_payload.get("task_type")
    task_id = task_payload.get("task_id", "unknown")
    
    logger.info(f"Starting task {task_id} of type {task_type} for tenant {tenant_id}")
    
    try:
        # Set tenant context for the task
        with tenant_scope(tenant_id, user_id):
            # Get the appropriate processor
            processor = get_task_processor(task_type)
            
            # Execute the task
            result = processor(task_payload)
            
            logger.info(f"Task {task_id} completed successfully")
            return {
                "status": TaskStatus.COMPLETED.value,
                "result": result,
                "completed_at": datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Task {task_id} failed: {str(e)}", exc_info=True)
        return {
            "status": TaskStatus.FAILED.value,
            "error": str(e),
            "failed_at": datetime.utcnow().isoformat()
        }

# Task Processors

def process_document_task(task_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process document upload and parsing
    """
    task_data = task_payload.get("task_data", {})
    dataset_id = task_data.get("dataset_id")
    
    if not dataset_id:
        raise ValueError("Dataset ID is required for document processing")
    
    db = SessionLocal()
    try:
        # Get dataset
        dataset = db.query(TenantDataset).filter(TenantDataset.id == dataset_id).first()
        if not dataset:
            raise ValueError(f"Dataset {dataset_id} not found")
        
        # Update status
        dataset.processing_status = "processing"
        db.commit()
        
        # Process the document based on file type
        if dataset.file_type in ['pdf', 'docx', 'txt', 'md']:
            result = process_text_document(dataset, task_data)
        elif dataset.file_type in ['csv', 'xlsx']:
            result = process_tabular_document(dataset, task_data)
        else:
            raise ValueError(f"Unsupported file type: {dataset.file_type}")
        
        # Update dataset with results
        dataset.processing_status = "completed"
        dataset.word_count = result.get("word_count", 0)
        dataset.character_count = result.get("character_count", 0)
        dataset.chunk_count = result.get("chunk_count", 0)
        db.commit()
        
        return result
        
    except Exception as e:
        # Update dataset status on failure
        if dataset:
            dataset.processing_status = "failed"
            dataset.processing_error = str(e)
            db.commit()
        raise
    finally:
        db.close()

def process_embedding_task(task_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate embeddings for document chunks
    """
    task_data = task_payload.get("task_data", {})
    dataset_id = task_data.get("dataset_id")
    embedding_model = task_data.get("embedding_model", "openai")
    
    if not dataset_id:
        raise ValueError("Dataset ID is required for embedding generation")
    
    db = SessionLocal()
    try:
        # Get dataset
        dataset = db.query(TenantDataset).filter(TenantDataset.id == dataset_id).first()
        if not dataset:
            raise ValueError(f"Dataset {dataset_id} not found")
        
        # Update status
        dataset.embeddings_status = "processing"
        db.commit()
        
        # Get chunks that need embeddings
        chunks = db.query(TenantDocumentChunk).filter(
            TenantDocumentChunk.dataset_id == dataset_id,
            TenantDocumentChunk.embedding_vector.is_(None)
        ).all()
        
        # Generate embeddings
        from services.embedding_service import EmbeddingService
        embedding_service = EmbeddingService(provider=embedding_model)
        
        processed_count = 0
        for chunk in chunks:
            try:
                embedding = embedding_service.generate_embedding(chunk.text)
                chunk.embedding_vector = embedding
                chunk.embedding_model = embedding_model
                processed_count += 1
                
                # Commit in batches
                if processed_count % 10 == 0:
                    db.commit()
                    
            except Exception as e:
                logger.error(f"Failed to generate embedding for chunk {chunk.id}: {e}")
        
        # Final commit
        db.commit()
        
        # Update dataset status
        dataset.embeddings_status = "completed"
        db.commit()
        
        return {
            "processed_chunks": processed_count,
            "total_chunks": len(chunks),
            "embedding_model": embedding_model
        }
        
    except Exception as e:
        # Update dataset status on failure
        if dataset:
            dataset.embeddings_status = "failed"
            db.commit()
        raise
    finally:
        db.close()

def process_connector_sync_task(task_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sync data from external connectors
    """
    task_data = task_payload.get("task_data", {})
    connector_id = task_data.get("connector_id")
    
    if not connector_id:
        raise ValueError("Connector ID is required for sync task")
    
    db = SessionLocal()
    try:
        # Get connector
        connector = db.query(DataConnector).filter(DataConnector.id == connector_id).first()
        if not connector:
            raise ValueError(f"Connector {connector_id} not found")
        
        # Update status
        connector.status = "syncing"
        db.commit()
        
        # Sync based on connector type
        if connector.connector_type == "github":
            result = sync_github_connector(connector, task_data)
        elif connector.connector_type == "sharepoint":
            result = sync_sharepoint_connector(connector, task_data)
        elif connector.connector_type == "onedrive":
            result = sync_onedrive_connector(connector, task_data)
        else:
            raise ValueError(f"Unsupported connector type: {connector.connector_type}")
        
        # Update connector
        connector.status = "active"
        connector.last_sync_at = datetime.utcnow()
        connector.sync_count += 1
        db.commit()
        
        return result
        
    except Exception as e:
        # Update connector status on failure
        if connector:
            connector.status = "error"
            connector.last_error = str(e)
            db.commit()
        raise
    finally:
        db.close()

def process_trust_score_task(task_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate trust scores for queries
    """
    task_data = task_payload.get("task_data", {})
    query_id = task_data.get("query_id")
    trust_method = task_data.get("trust_method", "basic")
    
    # TODO: Implement trust score calculation
    # This would use the trust score service to calculate scores
    
    return {
        "query_id": query_id,
        "trust_method": trust_method,
        "trust_score": 0.85,  # Placeholder
        "calculated_at": datetime.utcnow().isoformat()
    }

def process_data_export_task(task_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Export data in various formats
    """
    task_data = task_payload.get("task_data", {})
    export_type = task_data.get("export_type", "csv")
    dataset_id = task_data.get("dataset_id")
    
    # TODO: Implement data export functionality
    
    return {
        "export_type": export_type,
        "dataset_id": dataset_id,
        "file_path": f"/exports/{dataset_id}.{export_type}",
        "exported_at": datetime.utcnow().isoformat()
    }

def process_cleanup_task(task_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Cleanup old data and temporary files
    """
    task_data = task_payload.get("task_data", {})
    cleanup_type = task_data.get("cleanup_type", "temp_files")
    
    # TODO: Implement cleanup functionality
    
    return {
        "cleanup_type": cleanup_type,
        "files_cleaned": 0,
        "space_freed_mb": 0,
        "cleaned_at": datetime.utcnow().isoformat()
    }

def process_unknown_task(task_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle unknown task types
    """
    task_type = task_payload.get("task_type", "unknown")
    raise ValueError(f"Unknown task type: {task_type}")

# Helper functions for document processing

def process_text_document(dataset: TenantDataset, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process text-based documents (PDF, DOCX, TXT, MD)
    """
    try:
        from services.text_chunking_service import text_chunking_service
        from services.vector_storage_service import vector_storage_service
        from services.embedding_service import create_embedding_service

        # Get processing data
        text_content = task_data.get("text_content", "")
        chunk_strategy = task_data.get("chunk_strategy", "recursive")
        chunk_size = task_data.get("chunk_size", 1000)
        overlap = task_data.get("overlap", 200)

        if not text_content:
            raise ValueError("No text content to process")

        # Chunk the text
        chunks = text_chunking_service.chunk_text(
            text=text_content,
            chunk_strategy=chunk_strategy,
            chunk_size=chunk_size,
            overlap=overlap,
            source_document=dataset.name
        )

        # Store chunks in database
        db = SessionLocal()
        try:
            chunk_records = []
            for chunk in chunks:
                chunk_record = TenantDocumentChunk(
                    tenant_id=dataset.tenant_id,
                    dataset_id=dataset.id,
                    text=chunk.text,
                    text_hash=chunk.text_hash,
                    chunk_index=chunk.chunk_index,
                    source_document=chunk.source_document,
                    chunk_type=chunk.chunk_type.value,
                    word_count=chunk.word_count,
                    char_count=chunk.char_count
                )
                chunk_records.append(chunk_record)
                db.add(chunk_record)

            db.commit()

            # Refresh to get IDs
            for record in chunk_records:
                db.refresh(record)

        finally:
            db.close()

        return {
            "word_count": sum(chunk.word_count for chunk in chunks),
            "character_count": sum(chunk.char_count for chunk in chunks),
            "chunk_count": len(chunks),
            "processing_method": "text_chunking",
            "chunk_strategy": chunk_strategy
        }

    except Exception as e:
        logger.error(f"Text document processing failed: {e}")
        raise

def process_tabular_document(dataset: TenantDataset, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process tabular documents (CSV, XLSX)
    """
    # TODO: Implement tabular document processing
    # This would parse the data and store it appropriately
    
    return {
        "row_count": 100,  # Placeholder
        "column_count": 5,
        "processing_method": "tabular_parsing"
    }

# Helper functions for connector sync

def sync_github_connector(connector: DataConnector, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sync GitHub repositories
    """
    # TODO: Implement GitHub sync
    return {"synced_files": 0, "connector_type": "github"}

def sync_sharepoint_connector(connector: DataConnector, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sync SharePoint documents
    """
    # TODO: Implement SharePoint sync
    return {"synced_files": 0, "connector_type": "sharepoint"}

def sync_onedrive_connector(connector: DataConnector, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sync OneDrive files
    """
    # TODO: Implement OneDrive sync
    return {"synced_files": 0, "connector_type": "onedrive"}
