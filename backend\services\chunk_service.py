"""
Professional Document Chunk Service
Handles creation, storage, and retrieval of document chunks with source attribution
"""

import hashlib
import json
import re
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from models import DocumentChunk, Dataset, SourceAttribution
from services.vector_service import VectorService


class ChunkService:
    """
    Professional service for managing document chunks with source attribution
    """
    
    def __init__(self):
        self.vector_service = VectorService()
    
    def create_chunks_from_document(self, dataset_id: int, chunks_data: List[Dict[str, Any]], 
                                  db: Session) -> Dict[str, Any]:
        """
        Create and store document chunks with professional source attribution
        
        Args:
            dataset_id: The dataset ID
            chunks_data: List of chunk dictionaries with content and metadata
            db: Database session
            
        Returns:
            Dict with creation results and statistics
        """
        try:
            # Verify dataset exists
            dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
            if not dataset:
                return {"error": "Dataset not found"}
            
            created_chunks = []
            skipped_duplicates = 0
            total_words = 0
            total_chars = 0
            
            for idx, chunk_data in enumerate(chunks_data):
                # Extract chunk content and metadata
                text = chunk_data.get('text', '').strip()
                if not text:
                    continue
                
                # Calculate text metrics
                word_count = len(text.split())
                char_count = len(text)
                total_words += word_count
                total_chars += char_count
                
                # Generate text hash for deduplication
                text_hash = self._generate_text_hash(text)
                
                # Check for duplicates
                existing_chunk = db.query(DocumentChunk).filter(
                    and_(
                        DocumentChunk.dataset_id == dataset_id,
                        DocumentChunk.text_hash == text_hash
                    )
                ).first()
                
                if existing_chunk:
                    skipped_duplicates += 1
                    continue
                
                # Create new chunk
                chunk = DocumentChunk(
                    dataset_id=dataset_id,
                    chunk_index=idx,
                    text=text,
                    text_hash=text_hash,
                    
                    # Source attribution
                    source_document=chunk_data.get('source_document'),
                    line_start=chunk_data.get('line_start'),
                    line_end=chunk_data.get('line_end'),
                    char_start=chunk_data.get('char_start'),
                    char_end=chunk_data.get('char_end'),
                    page_number=chunk_data.get('page_number'),
                    section_title=chunk_data.get('section'),
                    section_level=self._extract_section_level(chunk_data.get('section')),
                    
                    # Chunk metadata
                    chunk_type=chunk_data.get('chunk_type', 'paragraph'),
                    word_count=word_count,
                    char_count=char_count,
                    language=self._detect_language(text),
                    
                    # Quality metrics
                    readability_score=self._calculate_readability(text),
                    information_density=self._calculate_information_density(text),
                    
                    # Processing metadata
                    processing_version='2.0'
                )
                
                db.add(chunk)
                created_chunks.append(chunk)
            
            # Commit chunks to database
            db.commit()
            
            # Generate embeddings for all chunks
            embedding_results = self._generate_embeddings_batch(created_chunks)
            
            # Update chunks with embeddings
            for chunk, embedding in zip(created_chunks, embedding_results):
                if embedding:
                    chunk.embedding_vector = embedding.tolist() if hasattr(embedding, 'tolist') else embedding
                    chunk.embedding_model = 'all-MiniLM-L6-v2'
            
            db.commit()
            
            return {
                "success": True,
                "chunks_created": len(created_chunks),
                "chunks_skipped": skipped_duplicates,
                "total_words": total_words,
                "total_characters": total_chars,
                "average_chunk_size": total_words / len(created_chunks) if created_chunks else 0,
                "embeddings_generated": sum(1 for chunk in created_chunks if chunk.embedding_vector)
            }
            
        except Exception as e:
            db.rollback()
            return {"error": f"Failed to create chunks: {str(e)}"}
    
    def search_chunks(self, dataset_id: int, query: str, limit: int = 10,
                     search_method: str = 'hybrid', db: Session = None) -> List[Dict[str, Any]]:
        """
        Professional search across document chunks with source attribution
        """
        try:
            print(f"🔍 Searching dataset {dataset_id} for query: '{query}'")

            # Get chunks from database
            chunks_query = db.query(DocumentChunk).filter(
                DocumentChunk.dataset_id == dataset_id
            ).order_by(DocumentChunk.chunk_index)

            chunks = chunks_query.all()
            print(f"📊 Found {len(chunks)} chunks in dataset {dataset_id}")

            if not chunks:
                print("❌ No chunks found in dataset")
                return []
            
            # Prepare data for vector search
            chunk_texts = [chunk.text for chunk in chunks]
            chunk_embeddings = [chunk.embedding_vector for chunk in chunks if chunk.embedding_vector]
            
            # Use production fallback search (optimized for reliability)
            search_results = self._fallback_text_search(
                query=query,
                chunks=chunks,
                limit=limit
            )

            print(f"🔍 Search results: {len(search_results)} chunks found")
            if search_results:
                print(f"📊 Top result score: {search_results[0].get('score', 0):.4f}")

            # For fallback search, results already have chunk_db_id, so return directly
            if search_results and 'chunk_db_id' in search_results[0]:
                return search_results

            # Enhance results with database chunk information (for vector search)
            enhanced_results = []
            for result in search_results:
                chunk_idx = int(result['chunk_id'].split('_')[1]) if '_' in result['chunk_id'] else 0
                if chunk_idx < len(chunks):
                    chunk = chunks[chunk_idx]
                    enhanced_result = {
                        **result,
                        'chunk_db_id': chunk.id,
                        'readability_score': chunk.readability_score,
                        'information_density': chunk.information_density,
                        'section_level': chunk.section_level,
                        'language': chunk.language,
                        'created_at': chunk.created_at.isoformat() if chunk.created_at else None
                    }
                    enhanced_results.append(enhanced_result)

            return enhanced_results
            
        except Exception as e:
            print(f"Error searching chunks: {e}")
            return []
    
    # get_chunk_context method removed as requested
    
    def _generate_text_hash(self, text: str) -> str:
        """Generate a hash for text deduplication"""
        normalized_text = re.sub(r'\s+', ' ', text.lower().strip())
        return hashlib.md5(normalized_text.encode()).hexdigest()
    
    def _extract_section_level(self, section_title: Optional[str]) -> Optional[int]:
        """Extract section level from title (H1=1, H2=2, etc.)"""
        if not section_title:
            return None
        
        # Look for markdown-style headers
        if section_title.startswith('#'):
            return len(section_title) - len(section_title.lstrip('#'))
        
        return None
    
    def _detect_language(self, text: str) -> str:
        """Simple language detection (can be enhanced with proper library)"""
        # Basic English detection - can be enhanced with langdetect library
        english_indicators = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
        words = text.lower().split()
        english_count = sum(1 for word in words[:50] if word in english_indicators)
        
        return 'en' if english_count > 3 else 'unknown'
    
    def _calculate_readability(self, text: str) -> float:
        """Calculate simple readability score"""
        words = text.split()
        sentences = len(re.split(r'[.!?]+', text))
        
        if not words or not sentences:
            return 0.0
        
        avg_words_per_sentence = len(words) / sentences
        avg_chars_per_word = sum(len(word) for word in words) / len(words)
        
        # Simple readability formula (lower is easier)
        score = (avg_words_per_sentence * 0.39) + (avg_chars_per_word * 11.8) - 15.59
        return max(0.0, min(100.0, score))
    
    def _calculate_information_density(self, text: str) -> float:
        """Calculate information density (unique words / total words)"""
        words = text.lower().split()
        if not words:
            return 0.0
        
        unique_words = set(words)
        return len(unique_words) / len(words)
    
    def _generate_embeddings_batch(self, chunks: List[DocumentChunk]) -> List[Optional[List[float]]]:
        """Generate embeddings for a batch of chunks"""
        if not self.vector_service.embedding_model:
            return [None] * len(chunks)
        
        try:
            texts = [chunk.text for chunk in chunks]
            embeddings = self.vector_service.embedding_model.encode(texts)
            return embeddings
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            return [None] * len(chunks)
    
    def _chunk_to_metadata(self, chunk: DocumentChunk) -> Dict[str, Any]:
        """Convert chunk to metadata dictionary"""
        return {
            'chunk_id': chunk.id,
            'source_document': chunk.source_document,
            'line_start': chunk.line_start,
            'line_end': chunk.line_end,
            'char_start': chunk.char_start,
            'char_end': chunk.char_end,
            'page_number': chunk.page_number,
            'section': chunk.section_title,
            'chunk_type': chunk.chunk_type,
            'word_count': chunk.word_count,
            'char_count': chunk.char_count
        }
    
    def _chunk_to_dict(self, chunk: DocumentChunk) -> Dict[str, Any]:
        """Convert chunk to dictionary representation"""
        return {
            'id': chunk.id,
            'text': chunk.text,
            'source_document': chunk.source_document,
            'line_start': chunk.line_start,
            'line_end': chunk.line_end,
            'char_start': chunk.char_start,  # Add missing field
            'char_end': chunk.char_end,      # Add missing field
            'page_number': chunk.page_number,
            'section_title': chunk.section_title,
            'chunk_type': chunk.chunk_type,
            'word_count': chunk.word_count,
            'readability_score': chunk.readability_score,
            'information_density': chunk.information_density,
            'created_at': chunk.created_at.isoformat() if chunk.created_at else None  # Add missing field
        }

    def _fallback_text_search(self, query: str, chunks: List[DocumentChunk], limit: int) -> List[Dict[str, Any]]:
        """
        Production-level fallback text search when vector search is unavailable
        Uses TF-IDF-like scoring with multiple ranking factors
        """
        import re
        from collections import Counter

        query_terms = set(re.findall(r'\b\w+\b', query.lower()))
        results = []

        print(f"🔍 Query terms: {query_terms}")
        print(f"📊 Processing {len(chunks)} chunks...")

        for i, chunk in enumerate(chunks):
            text_lower = chunk.text.lower()
            text_words = re.findall(r'\b\w+\b', text_lower)
            text_word_set = set(text_words)

            # Calculate multiple scoring factors
            scores = {}

            # 1. Exact phrase matching (highest weight)
            if query.lower() in text_lower:
                scores['exact_phrase'] = 1.0
            else:
                scores['exact_phrase'] = 0.0

            # 2. Term frequency scoring
            term_matches = len(query_terms.intersection(text_word_set))
            scores['term_frequency'] = term_matches / len(query_terms) if query_terms else 0

            # 3. Jaccard similarity
            if text_word_set and query_terms:
                scores['jaccard'] = len(query_terms.intersection(text_word_set)) / len(query_terms.union(text_word_set))
            else:
                scores['jaccard'] = 0

            # 4. Position-based scoring (earlier matches score higher)
            position_score = 0
            for term in query_terms:
                if term in text_lower:
                    position = text_lower.find(term)
                    # Normalize position (earlier = higher score)
                    position_score += max(0, 1 - (position / len(text_lower)))
            scores['position'] = position_score / len(query_terms) if query_terms else 0

            # 5. Section relevance (certain sections are more important)
            section_boost = 1.0
            if chunk.section_title:
                important_sections = ['introduction', 'summary', 'conclusion', 'abstract']
                if any(sec in chunk.section_title.lower() for sec in important_sections):
                    section_boost = 1.2
            scores['section_boost'] = section_boost

            # 6. Chunk type relevance
            type_boost = 1.0
            if chunk.chunk_type in ['header', 'title']:
                type_boost = 1.3
            elif chunk.chunk_type in ['paragraph', 'text']:
                type_boost = 1.0
            elif chunk.chunk_type in ['code', 'table']:
                type_boost = 0.9
            scores['type_boost'] = type_boost

            # Calculate weighted final score
            final_score = (
                scores['exact_phrase'] * 0.4 +
                scores['term_frequency'] * 0.25 +
                scores['jaccard'] * 0.15 +
                scores['position'] * 0.1 +
                (scores['section_boost'] - 1) * 0.05 +
                (scores['type_boost'] - 1) * 0.05
            )

            # Debug: Show scores for first chunk only
            if i == 0:
                print(f"📊 Sample chunk score: {final_score:.6f}")

            # Only include results with meaningful scores (very low threshold for better recall)
            if final_score > 0.001:
                result = {
                    'chunk_id': f"chunk_{chunk.id}",
                    'text': chunk.text,
                    'score': final_score,
                    'document': chunk.source_document,
                    'line_start': chunk.line_start,
                    'line_end': chunk.line_end,
                    'char_start': chunk.char_start,
                    'char_end': chunk.char_end,
                    'page_number': chunk.page_number,
                    'section': chunk.section_title,
                    'chunk_type': chunk.chunk_type,
                    'rank': 0,  # Will be set after sorting
                    'chunk_db_id': chunk.id,
                    'readability_score': chunk.readability_score,
                    'information_density': chunk.information_density,
                    'search_method': 'fallback_text_search',
                    'score_breakdown': scores
                }
                results.append(result)

        # Sort by score (descending) and assign ranks
        results.sort(key=lambda x: x['score'], reverse=True)
        for i, result in enumerate(results[:limit]):
            result['rank'] = i + 1

        print(f"🔍 Final results: {len(results)} chunks with scores above threshold")

        # If no results found, return top chunks anyway (fallback)
        if not results and chunks:
            print("⚠️ No results above threshold, returning top chunks as fallback")
            fallback_results = []
            for i, chunk in enumerate(chunks[:limit]):
                fallback_results.append({
                    'chunk_id': f"chunk_{chunk.id}",
                    'text': chunk.text,
                    'score': 0.1,  # Low but non-zero score
                    'document': chunk.source_document,
                    'line_start': chunk.line_start,
                    'line_end': chunk.line_end,
                    'char_start': chunk.char_start,
                    'char_end': chunk.char_end,
                    'page_number': chunk.page_number,
                    'section': chunk.section_title,
                    'chunk_type': chunk.chunk_type,
                    'rank': i + 1,
                    'chunk_db_id': chunk.id,
                    'readability_score': chunk.readability_score,
                    'information_density': chunk.information_density,
                    'search_method': 'fallback_default',
                    'score_breakdown': {'fallback': True}
                })
            return fallback_results

        return results[:limit]

    def get_chunk_statistics(self, dataset_id: int, db: Session) -> Dict[str, Any]:
        """Get comprehensive statistics about chunks in a dataset"""
        from sqlalchemy import func

        # Basic statistics
        stats = db.query(
            func.count(DocumentChunk.id).label('total_chunks'),
            func.sum(DocumentChunk.word_count).label('total_words'),
            func.sum(DocumentChunk.char_count).label('total_characters'),
            func.avg(DocumentChunk.word_count).label('avg_words_per_chunk'),
            func.avg(DocumentChunk.readability_score).label('avg_readability'),
            func.avg(DocumentChunk.information_density).label('avg_information_density'),
            func.min(DocumentChunk.word_count).label('min_words'),
            func.max(DocumentChunk.word_count).label('max_words')
        ).filter(DocumentChunk.dataset_id == dataset_id).first()

        # Chunk type distribution
        chunk_types = db.query(
            DocumentChunk.chunk_type,
            func.count(DocumentChunk.id).label('count'),
            func.avg(DocumentChunk.word_count).label('avg_words')
        ).filter(
            DocumentChunk.dataset_id == dataset_id
        ).group_by(DocumentChunk.chunk_type).all()

        # Section distribution
        sections = db.query(
            DocumentChunk.section_title,
            func.count(DocumentChunk.id).label('count'),
            func.sum(DocumentChunk.word_count).label('total_words')
        ).filter(
            DocumentChunk.dataset_id == dataset_id,
            DocumentChunk.section_title.isnot(None)
        ).group_by(DocumentChunk.section_title).all()

        # Document distribution
        documents = db.query(
            DocumentChunk.source_document,
            func.count(DocumentChunk.id).label('chunk_count'),
            func.sum(DocumentChunk.word_count).label('word_count'),
            func.min(DocumentChunk.line_start).label('first_line'),
            func.max(DocumentChunk.line_end).label('last_line')
        ).filter(
            DocumentChunk.dataset_id == dataset_id
        ).group_by(DocumentChunk.source_document).all()

        # Quality metrics
        quality_stats = db.query(
            func.count(DocumentChunk.id).filter(DocumentChunk.readability_score > 70).label('high_readability'),
            func.count(DocumentChunk.id).filter(DocumentChunk.information_density > 0.8).label('high_density'),
            func.count(DocumentChunk.id).filter(DocumentChunk.embedding_vector.isnot(None)).label('with_embeddings')
        ).filter(DocumentChunk.dataset_id == dataset_id).first()

        return {
            'basic_stats': {
                'total_chunks': stats.total_chunks or 0,
                'total_words': stats.total_words or 0,
                'total_characters': stats.total_characters or 0,
                'avg_words_per_chunk': round(stats.avg_words_per_chunk or 0, 2),
                'avg_readability_score': round(stats.avg_readability or 0, 2),
                'avg_information_density': round(stats.avg_information_density or 0, 3),
                'min_words_per_chunk': stats.min_words or 0,
                'max_words_per_chunk': stats.max_words or 0
            },
            'chunk_types': [
                {
                    'type': ct.chunk_type,
                    'count': ct.count,
                    'avg_words': round(ct.avg_words or 0, 1)
                }
                for ct in chunk_types
            ],
            'sections': [
                {
                    'section': s.section_title,
                    'chunk_count': s.count,
                    'word_count': s.total_words or 0
                }
                for s in sections
            ],
            'documents': [
                {
                    'document': d.source_document,
                    'chunk_count': d.chunk_count,
                    'word_count': d.word_count or 0,
                    'line_range': f"{d.first_line}-{d.last_line}" if d.first_line and d.last_line else "N/A"
                }
                for d in documents
            ],
            'quality_metrics': {
                'high_readability_chunks': quality_stats.high_readability or 0,
                'high_density_chunks': quality_stats.high_density or 0,
                'chunks_with_embeddings': quality_stats.with_embeddings or 0,
                'readability_percentage': round((quality_stats.high_readability or 0) / (stats.total_chunks or 1) * 100, 1),
                'density_percentage': round((quality_stats.high_density or 0) / (stats.total_chunks or 1) * 100, 1),
                'embedding_percentage': round((quality_stats.with_embeddings or 0) / (stats.total_chunks or 1) * 100, 1)
            }
        }
