'use client'

import React, { useState, useEffect } from 'react'
import { X, FileText, Image, Table } from 'lucide-react'
import { useSession } from 'next-auth/react'
import { createApiInstance } from '@/lib/api'

interface DocumentViewerProps {
  dataset: {
    id: number
    name: string
    file_type?: string
    content_type?: string
    word_count?: number
    character_count?: number
    created_at: string
    columns: string[]
    row_count: number
    processing_status?: string
  }
  isOpen: boolean
  onClose: () => void
}

interface DocumentContent {
  text: string
  metadata: any
  filename: string
}

export default function DocumentViewer({ dataset, isOpen, onClose }: DocumentViewerProps) {
  const [content, setContent] = useState<DocumentContent | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { data: session } = useSession()

  const fetchDocumentContent = async () => {
    if (!dataset || content || !session?.user?.email) return

    setLoading(true)
    setError(null)

    try {
      const api = createApiInstance(session.user.email)
      const response = await api.get(`/datasets/${dataset.id}/content`)
      setContent(response.data)
    } catch (err: any) {
      console.error('Failed to fetch document content:', err)
      setError(err?.response?.data?.detail || err.message || 'Failed to load document')
    } finally {
      setLoading(false)
    }
  }

  const isImageFile = () => {
    const fileType = dataset.file_type?.toLowerCase()
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileType || '')
  }

  const getImageUrl = () => {
    // For now, we'll need to add an endpoint to serve the original image
    // This is a placeholder - we'll need to implement the image serving endpoint
    return `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/datasets/${dataset.id}/image`
  }

  useEffect(() => {
    if (isOpen && !content && !loading && session?.user?.email) {
      fetchDocumentContent()
    }
  }, [isOpen, content, loading, session?.user?.email])

  // Add escape key listener
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  const getFileIcon = () => {
    const fileType = dataset.file_type?.toLowerCase()
    if (['jpg', 'jpeg', 'png'].includes(fileType || '')) {
      return <Image className="w-5 h-5" />
    } else if (['csv', 'xlsx', 'xls'].includes(fileType || '')) {
      return <Table className="w-5 h-5" />
    } else {
      return <FileText className="w-5 h-5" />
    }
  }

  const formatMetadata = (metadata: any) => {
    if (!metadata) return null

    const entries = Object.entries(metadata).filter(([key, value]) =>
      value !== null && value !== undefined && value !== '' &&
      key.toLowerCase() !== 'columns' // Remove columns from metadata display
    )

    return entries.map(([key, value]) => (
      <div key={key} className="flex justify-between py-1">
        <span className="text-gray-600 capitalize">{key.replace(/_/g, ' ')}:</span>
        <span className="text-gray-900 font-medium">
          {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
        </span>
      </div>
    ))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            {getFileIcon()}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{dataset.name}</h2>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  dataset.file_type === 'pdf' ? 'bg-red-100 text-red-800' :
                  dataset.file_type === 'docx' ? 'bg-blue-100 text-blue-800' :
                  dataset.file_type === 'txt' ? 'bg-gray-100 text-gray-800' :
                  dataset.file_type === 'md' ? 'bg-purple-100 text-purple-800' :
                  ['jpg', 'jpeg', 'png'].includes(dataset.file_type || '') ? 'bg-orange-100 text-orange-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {dataset.file_type?.toUpperCase()}
                </span>
                {dataset.content_type === 'document' && (
                  <span>{dataset.word_count || 0} words • {dataset.character_count || 0} characters</span>
                )}
                <span>{new Date(dataset.created_at).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading document...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {content && (
            <div className="space-y-6">
              {/* Image Display for Image Files */}
              {isImageFile() && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Image Preview</h3>
                  <div className="bg-gray-50 rounded-lg p-4 flex justify-center">
                    <div className="relative">
                      <img
                        src={getImageUrl()}
                        alt={dataset.name}
                        className="max-w-full max-h-96 object-contain rounded-lg shadow-sm"
                        onError={(e) => {
                          // If image fails to load, show placeholder
                          const target = e.currentTarget as HTMLImageElement;
                          target.style.display = 'none';
                          const placeholder = target.nextElementSibling as HTMLElement;
                          if (placeholder) placeholder.style.display = 'flex';
                        }}
                      />
                      <div
                        className="hidden flex-col items-center justify-center bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 min-h-[200px]"
                        style={{ display: 'none' }}
                      >
                        <Image className="w-12 h-12 text-gray-400 mb-3" />
                        <p className="text-gray-500 text-center">
                          <span className="font-medium">Image Preview Not Available</span><br />
                          <span className="text-sm">Original image data not stored</span>
                        </p>
                        <div className="mt-3 text-xs text-gray-400">
                          File: {dataset.name}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Document Text or Data Preview */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {isImageFile() ? 'Extracted Text (OCR)' :
                   content.metadata?.preview_data ? 'Data Preview' : 'Content'}
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                  {content.text && content.text.includes('[OCR not available') ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="text-yellow-600 mb-2">⚠️</div>
                        <p className="text-gray-600 font-medium mb-1">OCR Not Available</p>
                        <p className="text-sm text-gray-500">
                          Tesseract OCR engine is not installed on the server.<br />
                          Text extraction from images is currently unavailable.
                        </p>
                      </div>
                    </div>
                  ) : content.metadata?.preview_data ? (
                    // Show tabular data preview for Excel/CSV files
                    <div className="space-y-4">
                      <div className="text-sm text-gray-600 mb-2">
                        Showing first {content.metadata.preview_data.length} rows of {content.metadata.total_rows} total rows
                      </div>
                      <div className="overflow-x-auto">
                        <table className="min-w-full text-xs border border-gray-200">
                          <thead className="bg-gray-100">
                            <tr>
                              {content.metadata.columns?.map((col: string, idx: number) => (
                                <th key={idx} className="px-2 py-1 text-left border-b border-gray-200 font-medium">
                                  {col}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {content.metadata.preview_data.map((row: any, rowIdx: number) => (
                              <tr key={rowIdx} className="hover:bg-gray-50">
                                {content.metadata.columns?.map((col: string, colIdx: number) => (
                                  <td key={colIdx} className="px-2 py-1 border-b border-gray-100 text-gray-800">
                                    {row[col] !== null && row[col] !== undefined ? String(row[col]) : '—'}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ) : (
                    <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                      {content.text || 'No text content available'}
                    </pre>
                  )}
                </div>
              </div>

              {/* Metadata */}
              {content.metadata && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Metadata</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      {formatMetadata(content.metadata)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
