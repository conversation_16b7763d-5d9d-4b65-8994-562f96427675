'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import {
  AlertOctagon,
  Eye,
  TrendingUp,
  Clock,
  Activity,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  BarChart3
} from 'lucide-react';

// Tooltip component for information icons
const InfoTooltip = ({ content, title }: { content: string; title: string }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="relative inline-block">
      <button
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="ml-1 text-gray-400 hover:text-gray-600"
      >
        <Info className="h-4 w-4" />
      </button>
      {showTooltip && (
        <div className="absolute z-10 w-64 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg -top-2 left-6">
          <div className="font-semibold mb-1">{title}</div>
          <div>{content}</div>
          <div className="absolute top-3 -left-1 w-2 h-2 bg-gray-900 rotate-45"></div>
        </div>
      )}
    </div>
  );
};

interface Anomaly {
  type: string;
  metric: string;
  anomaly_value: number;
  baseline_mean?: number;
  baseline_std?: number;
  z_score?: number;
  severity: string;
  day_index?: number;
  description: string;
}

interface AnomalyDetection {
  user_id: string;
  anomaly_score: number;
  overall_severity: string;
  total_anomalies: number;
  anomalies: Anomaly[];
  features_analyzed: string[];
  detection_method: string;
  model_version: string;
  detected_at: string;
}

interface AnomalyTrends {
  user_id: string;
  analysis_period_days: number;
  total_anomalies: number;
  anomaly_types: Record<string, number>;
  severity_distribution: Record<string, number>;
  monthly_trends: Record<string, number>;
  recent_anomalies: Array<{
    type: string;
    severity: string;
    description: string;
    detected_at: string;
    status: string;
  }>;
}

export default function AnomalyDetectionDashboard() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anomalyDetection, setAnomalyDetection] = useState<AnomalyDetection | null>(null);
  const [anomalyTrends, setAnomalyTrends] = useState<AnomalyTrends | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);

  // Create simple axios instance for predictive analytics (no API key needed)
  const api = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
    timeout: 30000,
  });

  useEffect(() => {
    if (session?.user?.id) {
      setSelectedUserId(session.user.id);
      fetchAnomalyData(session.user.id);
    }
  }, [session]);

  const fetchAnomalyData = async (userId: string) => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);

    try {
      // Fetch current anomaly detection
      const detectionResponse = await api.get(`/predictive/anomaly-detection/${userId}`);
      setAnomalyDetection(detectionResponse.data);

      // Fetch anomaly trends
      const trendsResponse = await api.get(`/predictive/anomaly-detection/${userId}/trends`);
      setAnomalyTrends(trendsResponse.data);

    } catch (err: any) {
      console.error('Error fetching anomaly data:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching anomaly data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    if (!selectedUserId) return;
    
    setRefreshing(true);
    await fetchAnomalyData(selectedUserId);
    setRefreshing(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'low': return <Info className="h-4 w-4" />;
      case 'medium': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <AlertOctagon className="h-4 w-4" />;
      case 'critical': return <XCircle className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getAnomalyScoreColor = (score: number) => {
    if (score >= 0.75) return 'text-red-600';
    if (score >= 0.5) return 'text-orange-600';
    if (score >= 0.25) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center text-red-600">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
          <p className="text-lg font-medium">Error Loading Anomaly Data</p>
          <p className="text-sm mt-2">{error}</p>
          <button
            onClick={refreshData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Eye className="h-8 w-8 text-purple-600" />
          <h2 className="text-2xl font-bold text-gray-900">Anomaly Detection</h2>
          <InfoTooltip
            title="Anomaly Detection"
            content="AI system that identifies unusual patterns, behaviors, or events that deviate significantly from normal baselines. Helps detect potential security threats, system issues, or unusual user activities."
          />
        </div>
        <button
          onClick={refreshData}
          disabled={refreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Anomaly Overview */}
      {anomalyDetection && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Anomaly Score Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Anomaly Score</p>
                <p className={`text-3xl font-bold ${getAnomalyScoreColor(anomalyDetection.anomaly_score)}`}>
                  {(anomalyDetection.anomaly_score * 100).toFixed(1)}%
                </p>
              </div>
              <div className={`p-3 rounded-full ${getSeverityColor(anomalyDetection.overall_severity)}`}>
                {getSeverityIcon(anomalyDetection.overall_severity)}
              </div>
            </div>
            <div className="mt-4">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(anomalyDetection.overall_severity)}`}>
                {anomalyDetection.overall_severity.toUpperCase()}
              </span>
            </div>
          </div>

          {/* Total Anomalies Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Anomalies Detected</p>
                <p className="text-3xl font-bold text-purple-600">
                  {anomalyDetection.total_anomalies}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <AlertOctagon className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-gray-500">
                Method: {anomalyDetection.detection_method}
              </p>
            </div>
          </div>

          {/* Features Analyzed Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Features Analyzed</p>
                <p className="text-3xl font-bold text-blue-600">
                  {anomalyDetection.features_analyzed.length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <BarChart3 className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-gray-500">
                Model: {anomalyDetection.model_version}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Current Anomalies */}
      {anomalyDetection && anomalyDetection.anomalies.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Anomalies</h3>
          <div className="space-y-4">
            {anomalyDetection.anomalies.map((anomaly, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(anomaly.severity)}`}>
                        {getSeverityIcon(anomaly.severity)}
                        <span className="ml-1">{anomaly.severity.toUpperCase()}</span>
                      </span>
                      <span className="text-sm font-medium text-gray-900">{anomaly.metric}</span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{anomaly.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Type: {anomaly.type.replace('_', ' ')}</span>
                      {anomaly.z_score && (
                        <span>Z-Score: {anomaly.z_score.toFixed(2)}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Anomaly Trends */}
      {anomalyTrends && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Severity Distribution */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Severity Distribution</h3>
            <div className="space-y-3">
              {Object.entries(anomalyTrends.severity_distribution).map(([severity, count]) => (
                <div key={severity} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(severity)}`}>
                      {getSeverityIcon(severity)}
                      <span className="ml-1">{severity.toUpperCase()}</span>
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Anomaly Types */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Anomaly Types</h3>
            <div className="space-y-3">
              {Object.entries(anomalyTrends.anomaly_types).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{type.replace('_', ' ')}</span>
                  <span className="text-sm font-medium text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Recent Anomalies */}
      {anomalyTrends && anomalyTrends.recent_anomalies.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Anomaly History</h3>
          <div className="space-y-3">
            {anomalyTrends.recent_anomalies.map((anomaly, index) => (
              <div key={index} className="flex items-start space-x-3 py-2 border-b border-gray-100 last:border-b-0">
                <div className={`flex-shrink-0 p-1 rounded-full ${getSeverityColor(anomaly.severity)}`}>
                  {getSeverityIcon(anomaly.severity)}
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{anomaly.description}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(anomaly.detected_at).toLocaleString()} • Status: {anomaly.status}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Detection Details */}
      {anomalyDetection && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Detection Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Detected At:</p>
              <p className="font-medium">{new Date(anomalyDetection.detected_at).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-600">Analysis Period:</p>
              <p className="font-medium">{anomalyTrends?.analysis_period_days || 30} days</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
