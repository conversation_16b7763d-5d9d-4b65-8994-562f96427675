'use client';

import React, { useState } from 'react';
import { 
  Database, 
  FileText, 
  Eye, 
  ExternalLink, 
  Copy, 
  Check,
  ChevronDown,
  ChevronUp,
  Star,
  BookOpen,
  Code,
  Table,
  List,
  Hash
} from 'lucide-react';
// import ContextViewer from './context-viewer';

interface SourceAttribution {
  document: string;
  line_start?: number;
  line_end?: number;
  char_start?: number;
  char_end?: number;
  text: string;
  page_number?: number;
  section?: string;
  chunk_type?: string;
  score?: number;
  rank?: number;
  chunk_db_id?: number;
}

interface SourcePanelProps {
  sources: SourceAttribution[];
  onGetContext?: (chunkId: number) => Promise<any>;
  className?: string;
}

export default function SourcePanel({ sources, onGetContext: _onGetContext, className = '' }: SourcePanelProps) {
  const [expandedSources, setExpandedSources] = useState<Set<number>>(new Set());
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  // const [contextViewer, setContextViewer] = useState<{
  //   isOpen: boolean;
  //   chunkId: number;
  //   documentName: string;
  //   targetText: string;
  // }>({
  //   isOpen: false,
  //   chunkId: 0,
  //   documentName: '',
  //   targetText: ''
  // });

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedSources);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedSources(newExpanded);
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  // const openContextViewer = (source: SourceAttribution, index: number) => {
  //   if (source.chunk_db_id && onGetContext) {
  //     setContextViewer({
  //       isOpen: true,
  //       chunkId: source.chunk_db_id,
  //       documentName: source.document,
  //       targetText: source.text
  //     });
  //   }
  // };

  const getChunkTypeIcon = (type?: string) => {
    switch (type) {
      case 'code':
        return <Code className="h-4 w-4 text-blue-500" />;
      case 'table':
        return <Table className="h-4 w-4 text-green-500" />;
      case 'list':
        return <List className="h-4 w-4 text-purple-500" />;
      case 'header':
        return <Hash className="h-4 w-4 text-orange-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getScoreColor = (score?: number) => {
    if (!score) return 'bg-gray-100 text-gray-700';
    if (score >= 0.8) return 'bg-green-100 text-green-700';
    if (score >= 0.6) return 'bg-yellow-100 text-yellow-700';
    return 'bg-red-100 text-red-700';
  };

  const getScoreLabel = (score?: number) => {
    if (!score) return 'N/A';
    if (score >= 0.8) return 'High';
    if (score >= 0.6) return 'Medium';
    return 'Low';
  };

  if (!sources || sources.length === 0) {
    return null;
  }

  return (
    <>
      <div className={`bg-gradient-to-r from-blue-50/50 to-purple-50/30 rounded-lg border border-blue-200/60 p-4 ${className}`}>
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Database className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800">Source Attribution</h3>
            <p className="text-sm text-gray-600">{sources.length} source{sources.length > 1 ? 's' : ''} found</p>
          </div>
        </div>

        {/* Sources List */}
        <div className="space-y-3">
          {sources.map((source, index) => {
            const isExpanded = expandedSources.has(index);
            const isCopied = copiedIndex === index;
            
            return (
              <div
                key={index}
                className="bg-white/80 rounded-lg border border-gray-200/60 overflow-hidden transition-all duration-200 hover:shadow-md"
              >
                {/* Source Header */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        {getChunkTypeIcon(source.chunk_type)}
                        <h4 className="text-sm font-semibold text-gray-800 truncate">
                          {source.document}
                        </h4>
                        {source.section && (
                          <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                            {source.section}
                          </span>
                        )}
                      </div>
                      
                      {/* Metadata */}
                      <div className="flex items-center space-x-4 text-xs text-gray-600">
                        {source.line_start && source.line_end && (
                          <span className="flex items-center space-x-1">
                            <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                            <span>Lines {source.line_start}-{source.line_end}</span>
                          </span>
                        )}
                        {source.page_number && (
                          <span className="flex items-center space-x-1">
                            <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                            <span>Page {source.page_number}</span>
                          </span>
                        )}
                        {source.chunk_type && (
                          <span className="flex items-center space-x-1">
                            <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                            <span className="capitalize">{source.chunk_type}</span>
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {/* Score and Rank */}
                    <div className="flex items-center space-x-2 ml-3">
                      {source.score && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(source.score)}`}>
                          {getScoreLabel(source.score)} ({(source.score * 100).toFixed(0)}%)
                        </span>
                      )}
                      <span className="text-xs text-gray-500 font-medium">
                        #{source.rank || index + 1}
                      </span>
                    </div>
                  </div>
                  
                  {/* Source Text Preview */}
                  <div className="bg-gray-50/80 rounded-md p-3 border border-gray-200/60">
                    <p className="text-sm text-gray-700 leading-relaxed">
                      "{source.text.length > 150 && !isExpanded 
                        ? `${source.text.substring(0, 150)}...` 
                        : source.text}"
                    </p>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex items-center justify-between mt-3">
                    <div className="flex items-center space-x-2">
                      {source.text.length > 150 && (
                        <button
                          onClick={() => toggleExpanded(index)}
                          className="text-xs text-blue-600 hover:text-blue-800 font-medium flex items-center space-x-1 hover:underline"
                        >
                          <span>{isExpanded ? 'Show less' : 'Show more'}</span>
                          {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                        </button>
                      )}
                      
                      {/* {source.chunk_db_id && onGetContext && (
                        <button
                          onClick={() => openContextViewer(source, index)}
                          className="text-xs text-purple-600 hover:text-purple-800 font-medium flex items-center space-x-1 hover:underline"
                        >
                          <Eye className="h-3 w-3" />
                          <span>View context</span>
                        </button>
                      )} */}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => copyToClipboard(source.text, index)}
                        className="text-xs text-gray-600 hover:text-gray-800 font-medium flex items-center space-x-1 hover:underline"
                      >
                        {isCopied ? (
                          <>
                            <Check className="h-3 w-3 text-green-600" />
                            <span className="text-green-600">Copied!</span>
                          </>
                        ) : (
                          <>
                            <Copy className="h-3 w-3" />
                            <span>Copy</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="mt-4 pt-3 border-t border-gray-200/60">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span>Sources ranked by relevance</span>
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>High confidence</span>
              </span>
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>Medium confidence</span>
              </span>
              <span className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>Low confidence</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Context Viewer Modal */}
      {/* <ContextViewer
        isOpen={contextViewer.isOpen}
        onClose={() => setContextViewer(prev => ({ ...prev, isOpen: false }))}
        chunkId={contextViewer.chunkId}
        documentName={contextViewer.documentName}
        targetText={contextViewer.targetText}
        onGetContext={onGetContext}
      /> */}
    </>
  );
}
