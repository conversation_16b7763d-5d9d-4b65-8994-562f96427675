# OCR Setup Guide for AIthentiq

## 🔍 **Current Status**
AIthentiq now supports image file uploads (JPG, JPEG, PNG) with OCR text extraction capabilities.

## 📋 **What's Working**
- ✅ Image file upload and processing
- ✅ Basic image metadata extraction
- ✅ Graceful fallback when OCR is not available
- ✅ File type detection and validation

## 🚀 **To Enable Full OCR Functionality**

### **Windows Installation**

1. **Download Tesseract**
   - Go to: https://github.com/UB-Mannheim/tesseract/wiki
   - Download the latest Windows installer
   - Run the installer and follow the setup wizard

2. **Add to PATH**
   - Add Tesseract installation directory to your system PATH
   - Default location: `C:\Program Files\Tesseract-OCR`

3. **Verify Installation**
   ```bash
   tesseract --version
   ```

### **Alternative: Using Conda**
```bash
conda install -c conda-forge tesseract
```

### **Alternative: Using Chocolatey**
```bash
choco install tesseract
```

## 🧪 **Testing OCR**

Once Tesseract is installed, restart the backend and test with:

```bash
python test_ocr.py
```

You should see OCR text extraction working with confidence scores.

## 📊 **OCR Features**

### **Supported Image Formats**
- JPG/JPEG
- PNG
- (More formats can be added easily)

### **OCR Capabilities**
- Text extraction from images
- Confidence scoring
- Multiple language support (currently English)
- Automatic image preprocessing

### **Metadata Extracted**
- Image dimensions
- Image format and mode
- OCR confidence scores
- Text block information
- Processing status

## 🔧 **Configuration Options**

### **Language Support**
To add more languages, modify the OCR reader initialization:

```python
# In document_service.py
self.ocr_reader = pytesseract  # Supports multiple languages
```

### **OCR Quality Settings**
You can adjust OCR settings for better accuracy:

```python
# Custom OCR configuration
custom_config = r'--oem 3 --psm 6'
text = pytesseract.image_to_string(image, config=custom_config)
```

## 🚨 **Troubleshooting**

### **Common Issues**

1. **"Tesseract not found"**
   - Ensure Tesseract is installed and in PATH
   - Restart terminal/IDE after installation

2. **Poor OCR Quality**
   - Ensure images have good contrast
   - Try preprocessing images (resize, denoise)
   - Adjust OCR configuration parameters

3. **Memory Issues**
   - Large images may require more memory
   - Consider image resizing for very large files

## 📈 **Performance Tips**

1. **Image Preprocessing**
   - Convert to grayscale for better OCR
   - Increase contrast and resolution
   - Remove noise and artifacts

2. **Batch Processing**
   - Process multiple images efficiently
   - Cache OCR results to avoid reprocessing

3. **Language Models**
   - Download specific language models for better accuracy
   - Use appropriate PSM (Page Segmentation Mode) settings

## 🔮 **Future Enhancements**

- [ ] Support for more image formats (TIFF, BMP, etc.)
- [ ] Advanced image preprocessing
- [ ] Multiple language detection
- [ ] OCR confidence-based filtering
- [ ] Batch image processing
- [ ] OCR result caching
- [ ] Integration with cloud OCR services (Google Vision, AWS Textract)

## 📞 **Support**

If you encounter issues with OCR setup:
1. Check the backend logs for error messages
2. Verify Tesseract installation
3. Test with simple, high-contrast images first
4. Ensure sufficient system memory for large images
