#!/usr/bin/env python3
"""
Verify that logo integration is complete by checking files
"""

import os

def verify_logo_integration():
    """
    Verify that all logo integration steps are complete
    """
    print("🔍 VERIFYING LOGO INTEGRATION")
    print("="*35)
    
    checks_passed = 0
    total_checks = 6
    
    # Check 1: Logo file exists in Logo directory
    print("\n1. 📁 Checking original logo processing...")
    
    original_logo = r"Logo\Logo.jpg"
    new_logo = r"Logo\AIthentiq_Logo.jpg"
    
    if os.path.exists(original_logo):
        print(f"✅ Original logo exists: {original_logo}")
    else:
        print(f"❌ Original logo missing: {original_logo}")
    
    if os.path.exists(new_logo):
        file_size = os.path.getsize(new_logo)
        print(f"✅ AIthentiq logo created: {new_logo}")
        print(f"   File size: {file_size:,} bytes")
        checks_passed += 1
    else:
        print(f"❌ AIthentiq logo missing: {new_logo}")
    
    # Check 2: Logo copied to frontend public directory
    print("\n2. 📂 Checking frontend logo file...")
    
    frontend_logo = r"frontend\public\AIthentiq_Logo.jpg"
    if os.path.exists(frontend_logo):
        file_size = os.path.getsize(frontend_logo)
        print(f"✅ Logo copied to frontend: {frontend_logo}")
        print(f"   File size: {file_size:,} bytes")
        checks_passed += 1
    else:
        print(f"❌ Logo not found in frontend: {frontend_logo}")
    
    # Check 3: Navbar component updated
    print("\n3. 🧭 Checking navbar component...")
    
    navbar_file = r"frontend\components\ui\navbar.tsx"
    if os.path.exists(navbar_file):
        with open(navbar_file, 'r', encoding='utf-8') as f:
            navbar_content = f.read()
        
        if 'AIthentiq_Logo.jpg' in navbar_content:
            print(f"✅ Navbar updated with logo reference")
            checks_passed += 1
        else:
            print(f"❌ Navbar missing logo reference")
            
        if 'alt="AIthentiq Logo"' in navbar_content:
            print(f"✅ Logo alt text found")
        else:
            print(f"⚠️ Logo alt text missing")
    else:
        print(f"❌ Navbar file not found: {navbar_file}")
    
    # Check 4: Home page updated
    print("\n4. 🏠 Checking home page...")
    
    home_file = r"frontend\app\page.tsx"
    if os.path.exists(home_file):
        with open(home_file, 'r', encoding='utf-8') as f:
            home_content = f.read()
        
        if 'AIthentiq_Logo.jpg' in home_content:
            print(f"✅ Home page updated with logo")
            checks_passed += 1
        else:
            print(f"❌ Home page missing logo")
            
        if 'AIthentiq' in home_content and 'AskData' not in home_content:
            print(f"✅ Home page branding updated")
        else:
            print(f"⚠️ Home page branding needs review")
    else:
        print(f"❌ Home page file not found: {home_file}")
    
    # Check 5: Footer component updated
    print("\n5. 🦶 Checking footer component...")
    
    footer_file = r"frontend\components\ui\footer.tsx"
    if os.path.exists(footer_file):
        with open(footer_file, 'r', encoding='utf-8') as f:
            footer_content = f.read()
        
        if 'AIthentiq' in footer_content:
            print(f"✅ Footer updated with AIthentiq branding")
            checks_passed += 1
        else:
            print(f"❌ Footer still has old branding")
            
        if 'AskData.ai' not in footer_content:
            print(f"✅ Old branding removed from footer")
        else:
            print(f"⚠️ Old branding still in footer")
    else:
        print(f"❌ Footer file not found: {footer_file}")
    
    # Check 6: Package.json and dependencies
    print("\n6. 📦 Checking project configuration...")
    
    package_file = r"frontend\package.json"
    if os.path.exists(package_file):
        with open(package_file, 'r', encoding='utf-8') as f:
            package_content = f.read()
        
        if '"next"' in package_content:
            print(f"✅ Next.js dependency found")
            checks_passed += 1
        else:
            print(f"❌ Next.js dependency missing")
    else:
        print(f"❌ Package.json not found: {package_file}")
    
    # Summary
    print(f"\n" + "="*35)
    print(f"📊 INTEGRATION VERIFICATION COMPLETE")
    print("="*35)
    
    print(f"\n✅ CHECKS PASSED: {checks_passed}/{total_checks}")
    
    if checks_passed == total_checks:
        print(f"🎉 ALL CHECKS PASSED!")
        print(f"\n✅ LOGO INTEGRATION COMPLETE:")
        print(f"✅ Logo created and processed")
        print(f"✅ Logo copied to frontend")
        print(f"✅ Navbar updated with logo")
        print(f"✅ Home page updated with logo")
        print(f"✅ Footer branding updated")
        print(f"✅ Project dependencies ready")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Start frontend: cd frontend && npm run dev")
        print(f"2. Open browser: http://localhost:3000")
        print(f"3. Verify logo displays correctly")
        print(f"4. Check navbar, home page, and footer")
        
    elif checks_passed >= 4:
        print(f"✅ MOSTLY COMPLETE!")
        print(f"Minor issues found, but core integration is done.")
        
    else:
        print(f"⚠️ INTEGRATION INCOMPLETE")
        print(f"Please review the failed checks above.")
    
    return checks_passed >= 4

def show_file_structure():
    """Show the current file structure"""
    print(f"\n📁 CURRENT FILE STRUCTURE:")
    print("="*30)
    
    files_to_check = [
        r"Logo\Logo.jpg",
        r"Logo\AIthentiq_Logo.jpg",
        r"frontend\public\AIthentiq_Logo.jpg",
        r"frontend\components\ui\navbar.tsx",
        r"frontend\app\page.tsx",
        r"frontend\components\ui\footer.tsx"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size:,} bytes)")
        else:
            print(f"❌ {file_path} (missing)")

if __name__ == "__main__":
    success = verify_logo_integration()
    show_file_structure()
    
    if success:
        print(f"\n🎯 LOGO INTEGRATION SUCCESSFUL!")
        print(f"Your AIthentiq logo is ready to use!")
    else:
        print(f"\n⚠️ Please complete the remaining integration steps.")
