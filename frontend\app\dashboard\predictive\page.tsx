'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import Navbar from '@/components/ui/navbar';
import Footer from '@/components/ui/footer';
import DashboardNav from '@/components/dashboard/dashboard-nav';
import Forecasting from '@/components/predictive/forecasting';
import ModelTrainer from '@/components/predictive/model-trainer';
import ModelManager from '@/components/predictive/model-manager';
import UserRiskDashboard from '@/components/predictive/user-risk-dashboard';
import ChurnPredictionDashboard from '@/components/predictive/churn-prediction-dashboard';
import AnomalyDetectionDashboard from '@/components/predictive/anomaly-detection-dashboard';
import PredictiveOverviewDashboard from '@/components/predictive/predictive-overview-dashboard';
import AdvancedInsightsDashboard from '@/components/predictive/advanced-insights-dashboard';

interface Dataset {
  id: number;
  name: string;
  columns: string[];
  row_count: number;
  created_at: string;
  embedding_status?: string;
  embedding_created_at?: string;
  embedding_error?: string;
}

export default function PredictivePage() {
  const { data: session, status } = useSession();
  const isLoaded = status !== 'loading';
  const isSignedIn = !!session;
  const user = session?.user;
  const router = useRouter();
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDatasetId, setSelectedDatasetId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredDatasets, setFilteredDatasets] = useState<Dataset[]>([]);

  // Fetch user's datasets
  useEffect(() => {
    if (!isLoaded || !isSignedIn) return;

    const fetchDatasets = async () => {
      try {
        // Use session user ID - no demo fallback
        const userId = user?.id;
        console.log('Fetching datasets for user:', userId);

        const response = await api.get(`/datasets/${userId}`);
        console.log('Datasets response:', response);

        if (response && response.data) {
          setDatasets(response.data);
          setFilteredDatasets(response.data);

          // Select the first dataset by default if available
          if (response.data.length > 0) {
            setSelectedDatasetId(response.data[0].id);
          }
        } else {
          console.error('Invalid response format:', response);
          setError('Invalid response format from server');
        }

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching datasets:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching datasets');
        setLoading(false);
      }
    };

    fetchDatasets();
  }, [isLoaded, isSignedIn, user?.id]);

  // Filter datasets based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredDatasets(datasets);
    } else {
      const filtered = datasets.filter(dataset =>
        dataset.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDatasets(filtered);
    }
  }, [searchQuery, datasets]);

  // Redirect to login if not signed in
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/api/auth/signin');
    }
  }, [isLoaded, isSignedIn, router]);

  // Handle model trained event
  const handleModelTrained = (modelId: string) => {
    setSelectedModelId(modelId);
    setActiveTab('models');
  };

  if (!isLoaded || !isSignedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow py-8">
        <DashboardNav />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-black mb-2">Predictive Analytics</h1>
          <p className="text-md text-black mb-8">Forecast future trends and build predictive models from your data</p>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-8">
            {/* Dataset Selection - Only show if datasets exist */}
            {datasets.length > 0 && (
              <div className="bg-gradient-to-br from-white/95 to-blue-50/90 backdrop-blur-lg rounded-2xl shadow-xl border border-blue-500/20 p-6 relative overflow-hidden">
                {/* Background effects */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent animate-pulse"></div>

                <div className="relative z-10">
                  <h2 className="text-xl font-semibold text-black mb-4">Select Dataset</h2>

                  {/* Search Box */}
                  <div className="mb-6">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        placeholder="Search datasets..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white/80 backdrop-blur-sm placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                      />
                    </div>
                    {searchQuery && (
                      <p className="mt-2 text-sm text-gray-600">
                        Found {filteredDatasets.length} dataset{filteredDatasets.length !== 1 ? 's' : ''} matching "{searchQuery}"
                      </p>
                    )}
                  </div>

                  {/* Dataset Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {filteredDatasets.map(dataset => (
                      <div
                        key={dataset.id}
                        onClick={() => setSelectedDatasetId(dataset.id)}
                        className={`group relative border rounded-xl p-4 cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                          selectedDatasetId === dataset.id
                            ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-glow'
                            : 'border-gray-200/50 bg-white/60 backdrop-blur-sm hover:border-blue-300/50 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:shadow-lg'
                        }`}
                      >
                        <h3 className="font-semibold text-black group-hover:text-blue-600 transition-colors duration-300">
                          {dataset.name.length > 20 ? `${dataset.name.substring(0, 20)}...` : dataset.name}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">{dataset.row_count.toLocaleString()} rows</p>
                        <p className="text-sm text-gray-500">{dataset.columns.length} columns</p>
                        {selectedDatasetId === dataset.id && (
                          <div className="absolute top-2 right-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* No results message */}
                  {searchQuery && filteredDatasets.length === 0 && (
                    <div className="text-center py-8">
                      <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
                      </svg>
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No datasets found</h3>
                      <p className="mt-1 text-sm text-gray-500">Try adjusting your search terms.</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* No Datasets Info - Show as info, not blocker */}
            {datasets.length === 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      <strong>No datasets found.</strong> You can still use Risk Forecasting, Churn Prediction, and Anomaly Detection.
                      Upload a dataset to access Time Series Forecasting and Model Training features.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Analytics Tabs */}
            <div className="bg-gradient-to-br from-white/95 to-blue-50/90 backdrop-blur-lg rounded-2xl shadow-2xl border border-blue-500/20 p-6 relative overflow-hidden">
              {/* Futuristic background effects */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-500/5 to-transparent animate-pulse"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 opacity-60"></div>

              <div className="border-b border-gradient-to-r from-blue-200/50 via-purple-200/50 to-indigo-200/50 mb-6 relative">
                <nav className="-mb-px relative z-10">
                  {/* First Row - 4 tabs */}
                  <div className="grid grid-cols-4 gap-2 mb-2">
                    <button
                      onClick={() => setActiveTab('overview')}
                      className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                        activeTab === 'overview'
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                          : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                      }`}
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Overview
                      </span>
                      {activeTab === 'overview' && (
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                      )}
                    </button>
                    <button
                      onClick={() => setActiveTab('advanced')}
                      className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                        activeTab === 'advanced'
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                          : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                      }`}
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        🧠 Advanced AI Insights
                      </span>
                      {activeTab === 'advanced' && (
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                      )}
                    </button>
                    <button
                      onClick={() => setActiveTab('risk')}
                      className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                        activeTab === 'risk'
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                          : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                      }`}
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        Risk Assessment
                      </span>
                      {activeTab === 'risk' && (
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                      )}
                    </button>
                    <button
                      onClick={() => setActiveTab('churn')}
                      className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                        activeTab === 'churn'
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                          : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                      }`}
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        Churn Prediction
                      </span>
                      {activeTab === 'churn' && (
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                      )}
                    </button>
                  </div>

                  {/* Second Row - 4 tabs */}
                  <div className="grid grid-cols-4 gap-2">
                    <button
                      onClick={() => setActiveTab('anomaly')}
                      className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                        activeTab === 'anomaly'
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                          : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                      }`}
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        Anomaly Detection
                      </span>
                      {activeTab === 'anomaly' && (
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                      )}
                    </button>
                    {selectedDatasetId ? (
                      <>
                        <button
                          onClick={() => setActiveTab('forecasting')}
                          className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                            activeTab === 'forecasting'
                              ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                              : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                          }`}
                        >
                          <span className="relative z-10 flex items-center gap-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                            </svg>
                            Time Series Forecasting
                          </span>
                          {activeTab === 'forecasting' && (
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                          )}
                        </button>
                        <button
                          onClick={() => setActiveTab('train')}
                          className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                            activeTab === 'train'
                              ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                              : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                          }`}
                        >
                          <span className="relative z-10 flex items-center gap-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Train Model
                          </span>
                          {activeTab === 'train' && (
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                          )}
                        </button>
                        <button
                          onClick={() => setActiveTab('models')}
                          className={`group relative py-3 px-4 font-semibold text-sm rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 ${
                            activeTab === 'models'
                              ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-glow border border-blue-400/30'
                              : 'bg-white/60 backdrop-blur-sm text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg'
                          }`}
                        >
                          <span className="relative z-10 flex items-center gap-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            My Models
                          </span>
                          {activeTab === 'models' && (
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                          )}
                        </button>
                      </>
                    ) : (
                      <>
                        {/* Placeholder tabs when no dataset is selected */}
                        <div className="group relative py-3 px-4 font-semibold text-sm rounded-xl bg-gray-100/60 text-gray-400 border border-gray-200/50 flex items-center justify-center gap-2">
                          <span className="flex items-center gap-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                            </svg>
                            Time Series Forecasting
                          </span>
                        </div>
                        <div className="group relative py-3 px-4 font-semibold text-sm rounded-xl bg-gray-100/60 text-gray-400 border border-gray-200/50 flex items-center justify-center gap-2">
                          <span className="flex items-center gap-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Train Model
                          </span>
                        </div>
                        <div className="group relative py-3 px-4 font-semibold text-sm rounded-xl bg-gray-100/60 text-gray-400 border border-gray-200/50 flex items-center justify-center gap-2">
                          <span className="flex items-center gap-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            My Models
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </nav>
              </div>

              {/* Tab Content */}
              <div>
                {activeTab === 'overview' && (
                  <PredictiveOverviewDashboard />
                )}

                {activeTab === 'advanced' && (
                  <AdvancedInsightsDashboard datasetId={selectedDatasetId} />
                )}

                {activeTab === 'risk' && (
                  <UserRiskDashboard />
                )}

                {activeTab === 'churn' && (
                  <ChurnPredictionDashboard />
                )}

                {activeTab === 'anomaly' && (
                  <AnomalyDetectionDashboard />
                )}

                {activeTab === 'forecasting' && selectedDatasetId && (
                  <Forecasting datasetId={selectedDatasetId} />
                )}

                {activeTab === 'train' && selectedDatasetId && (
                  <ModelTrainer
                    datasetId={selectedDatasetId}
                    onModelTrained={handleModelTrained}
                  />
                )}

                {activeTab === 'models' && (
                  <ModelManager
                    selectedModelId={selectedModelId || undefined}
                    onSelectModel={setSelectedModelId}
                  />
                )}
              </div>
            </div>
          </div>
        )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
