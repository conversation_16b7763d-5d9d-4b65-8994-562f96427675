'use client';

import { useState, useEffect } from 'react';
import api from '@/lib/api';

interface RatingComponentProps {
  queryId: number;
  userId: string;
  onRatingSubmitted?: (rating: string) => void;
}

export default function RatingComponent({ queryId, userId, onRatingSubmitted }: RatingComponentProps) {
  const [rating, setRating] = useState<string | null>(null);
  const [comment, setComment] = useState('');
  const [showComment, setShowComment] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [submittedRating, setSubmittedRating] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check for existing feedback when component loads or queryId changes
  useEffect(() => {
    const checkExistingFeedback = async () => {
      try {
        if (!queryId || !userId) {
          // Reset state if no queryId
          setRating(null);
          setSubmittedRating(null);
          setSubmitted(false);
          setComment('');
          setShowComment(false);
          setIsLoading(false);
          return;
        }

        setIsLoading(true);

        // Reset state for new query
        setRating(null);
        setSubmittedRating(null);
        setSubmitted(false);
        setComment('');
        setShowComment(false);

        console.log('Checking feedback for query ID:', queryId);

        const response = await api.get(`/feedback/query/${queryId}`);
        const feedbackList = response.data;

        // Find feedback from current user
        const userFeedback = feedbackList.find((fb: any) => fb.user_id === userId);

        if (userFeedback) {
          setRating(userFeedback.rating);
          setSubmittedRating(userFeedback.rating);
          setSubmitted(true);
          setComment(userFeedback.comment || '');
          console.log('Found existing feedback for query', queryId, ':', userFeedback.rating);
        } else {
          console.log('No existing feedback found for query', queryId);
        }
      } catch (error) {
        console.error('Error checking existing feedback:', error);
        // Reset state on error
        setRating(null);
        setSubmittedRating(null);
        setSubmitted(false);
        setComment('');
        setShowComment(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingFeedback();
  }, [queryId, userId]);

  const handleRating = async (newRating: 'up' | 'down') => {
    if (isSubmitting) return;

    setRating(newRating);

    // If rating down, show comment box
    if (newRating === 'down') {
      setShowComment(true);
      return;
    }

    // Submit immediately for positive rating
    await submitFeedback(newRating, '');
  };

  const submitFeedback = async (ratingValue: string, commentValue: string) => {
    setIsSubmitting(true);

    try {
      const response = await api.post('/feedback', {
        user_id: userId,
        query_id: queryId,
        rating: ratingValue,
        comment: commentValue || null
      });

      if (response.status === 200) {
        setRating(ratingValue);
        setSubmittedRating(ratingValue);
        setSubmitted(true);
        setShowComment(false);
        onRatingSubmitted?.(ratingValue);
        console.log('Feedback submitted successfully:', ratingValue);
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      // Reset on error
      setRating(null);
      setShowComment(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSubmit = async () => {
    if (rating) {
      await submitFeedback(rating, comment);
    }
  };

  const handleCommentCancel = () => {
    setRating(null);
    setComment('');
    setShowComment(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <span>Loading rating...</span>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Was this helpful?</span>

          <button
            disabled
            className={`p-2 rounded-full ${
              submittedRating === 'up'
                ? 'bg-green-100 text-green-600'
                : 'text-gray-300'
            }`}
            title="Thumbs up"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558-.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z" />
            </svg>
          </button>

          <button
            disabled
            className={`p-2 rounded-full ${
              submittedRating === 'down'
                ? 'bg-red-100 text-red-600'
                : 'text-gray-300'
            }`}
            title="Thumbs down"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M15.73 5.25h1.035A7.465 7.465 0 0118 9.375a7.465 7.465 0 01-1.235 4.125h-.148c-.806 0-1.534.446-2.031 1.08a9.04 9.04 0 01-2.861 2.4c-.723.384-1.35.956-1.653 1.715a4.498 4.498 0 00-.322 1.672V21a.75.75 0 01-.75.75 2.25 2.25 0 01-2.25-2.25c0-1.152.26-2.243.723-3.218C7.74 15.724 7.366 15 6.748 15H3.622c-1.026 0-1.945-.694-2.054-1.715A12.134 12.134 0 011.5 12c0-2.848.992-5.464 2.649-7.521C4.537 3.997 5.136 3.75 5.754 3.75H9.77a4.5 4.5 0 011.423.23l3.114 1.04a4.5 4.5 0 001.423.23zM21.669 14.023c.536-1.362.831-2.845.831-4.398 0-1.22-.182-2.398-.52-3.507-.26-.85-1.084-1.368-1.973-1.368H19.1c-.445 0-.72.498-.523.898.591 1.2.924 2.55.924 3.977a8.958 8.958 0 01-1.302 4.666c-.245.403.028.959.5.959h1.053c.832 0 1.612-.453 1.918-1.227z" />
            </svg>
          </button>
        </div>

        <div className="text-sm text-green-600">
          ✅ Thank you for your feedback!
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600">Was this helpful?</span>

        <button
          onClick={() => handleRating('up')}
          disabled={isSubmitting}
          className={`p-2 rounded-full transition-colors ${
            rating === 'up' || submittedRating === 'up'
              ? 'bg-green-100 text-green-600'
              : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
          } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          title="Thumbs up"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558-.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z" />
          </svg>
        </button>

        <button
          onClick={() => handleRating('down')}
          disabled={isSubmitting}
          className={`p-2 rounded-full transition-colors ${
            rating === 'down' || submittedRating === 'down'
              ? 'bg-red-100 text-red-600'
              : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
          } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          title="Thumbs down"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M15.73 5.25h1.035A7.465 7.465 0 0118 9.375a7.465 7.465 0 01-1.235 4.125h-.148c-.806 0-1.534.446-2.031 1.08a9.04 9.04 0 01-2.861 2.4c-.723.384-1.35.956-1.653 1.715a4.498 4.498 0 00-.322 1.672V21a.75.75 0 01-.75.75 2.25 2.25 0 01-2.25-2.25c0-1.152.26-2.243.723-3.218C7.74 15.724 7.366 15 6.748 15H3.622c-1.026 0-1.945-.694-2.054-1.715A12.134 12.134 0 011.5 12c0-2.848.992-5.464 2.649-7.521C4.537 3.997 5.136 3.75 5.754 3.75H9.77a4.5 4.5 0 011.423.23l3.114 1.04a4.5 4.5 0 001.423.23zM21.669 14.023c.536-1.362.831-2.845.831-4.398 0-1.22-.182-2.398-.52-3.507-.26-.85-1.084-1.368-1.973-1.368H19.1c-.445 0-.72.498-.523.898.591 1.2.924 2.55.924 3.977a8.958 8.958 0 01-1.302 4.666c-.245.403.028.959.5.959h1.053c.832 0 1.612-.453 1.918-1.227z" />
          </svg>
        </button>

        {isSubmitting && (
          <div className="text-sm text-gray-500">
            Submitting...
          </div>
        )}
      </div>

      {showComment && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Help us improve (optional):
              </label>
              <textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="What could be better about this answer?"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <button
                onClick={handleCommentCancel}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleCommentSubmit}
                disabled={isSubmitting}
                className="px-4 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
