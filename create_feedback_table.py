import sys
import os
sys.path.append('backend')

from backend.database import engine
import sqlalchemy as sa

# Create feedback table manually
print("Creating feedback table...")

try:
    with engine.connect() as conn:
        conn.execute(sa.text('''
            CREATE TABLE IF NOT EXISTS feedback (
                id INTEGER PRIMARY KEY,
                user_id VARCHAR NOT NULL,
                query_id INTEGER NOT NULL,
                rating VARCHAR NOT NULL,
                comment TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (query_id) REFERENCES queries(id)
            )
        '''))
        conn.commit()
        print('✅ Feedback table created successfully!')
        
        # Check if table exists
        result = conn.execute(sa.text("SELECT name FROM sqlite_master WHERE type='table' AND name='feedback'"))
        if result.fetchone():
            print('✅ Feedback table confirmed to exist!')
        else:
            print('❌ Feedback table not found!')
            
except Exception as e:
    print(f'❌ Error: {e}')
