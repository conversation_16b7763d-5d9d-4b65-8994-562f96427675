"""
Complete Vector Storage Service for Phase 2
Implements tenant isolation for FAISS, Pinecone, Weaviate with index management
"""

import os
import json
import logging
import hashlib
import pickle
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from abc import ABC, abstractmethod
import numpy as np

logger = logging.getLogger(__name__)

class VectorStoreProvider(ABC):
    """Abstract base class for vector store providers"""
    
    @abstractmethod
    async def add_vectors(
        self, 
        tenant_id: str, 
        vectors: List[List[float]], 
        metadata: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Add vectors to the store"""
        pass
    
    @abstractmethod
    async def search_vectors(
        self, 
        tenant_id: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors"""
        pass
    
    @abstractmethod
    async def delete_vectors(
        self, 
        tenant_id: str, 
        ids: List[str]
    ) -> bool:
        """Delete vectors by IDs"""
        pass
    
    @abstractmethod
    async def get_index_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get index statistics"""
        pass
    
    @abstractmethod
    async def create_tenant_index(self, tenant_id: str) -> bool:
        """Create tenant-specific index"""
        pass
    
    @abstractmethod
    async def delete_tenant_index(self, tenant_id: str) -> bool:
        """Delete tenant-specific index"""
        pass

class FAISSVectorStore(VectorStoreProvider):
    """FAISS vector store with tenant isolation"""
    
    def __init__(self, dimension: int = 1536, storage_path: str = "data/faiss_indexes"):
        self.dimension = dimension
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # Import FAISS
        try:
            import faiss
            self.faiss = faiss
        except ImportError:
            raise ImportError("faiss-cpu package is required for FAISS vector store")
        
        # Tenant indexes cache
        self.tenant_indexes = {}
        self.tenant_metadata = {}
    
    def _get_tenant_index_path(self, tenant_id: str) -> Path:
        """Get the file path for tenant's FAISS index"""
        return self.storage_path / f"tenant_{tenant_id}.faiss"
    
    def _get_tenant_metadata_path(self, tenant_id: str) -> Path:
        """Get the file path for tenant's metadata"""
        return self.storage_path / f"tenant_{tenant_id}_metadata.pkl"
    
    async def _load_tenant_index(self, tenant_id: str):
        """Load tenant's FAISS index from disk"""
        index_path = self._get_tenant_index_path(tenant_id)
        metadata_path = self._get_tenant_metadata_path(tenant_id)
        
        if index_path.exists():
            try:
                # Load FAISS index
                index = self.faiss.read_index(str(index_path))
                self.tenant_indexes[tenant_id] = index
                
                # Load metadata
                if metadata_path.exists():
                    with open(metadata_path, 'rb') as f:
                        self.tenant_metadata[tenant_id] = pickle.load(f)
                else:
                    self.tenant_metadata[tenant_id] = {}
                
                logger.info(f"Loaded FAISS index for tenant {tenant_id}: {index.ntotal} vectors")
            except Exception as e:
                logger.error(f"Failed to load FAISS index for tenant {tenant_id}: {e}")
                await self.create_tenant_index(tenant_id)
        else:
            await self.create_tenant_index(tenant_id)
    
    async def _save_tenant_index(self, tenant_id: str):
        """Save tenant's FAISS index to disk"""
        if tenant_id not in self.tenant_indexes:
            return
        
        try:
            index_path = self._get_tenant_index_path(tenant_id)
            metadata_path = self._get_tenant_metadata_path(tenant_id)
            
            # Save FAISS index
            self.faiss.write_index(self.tenant_indexes[tenant_id], str(index_path))
            
            # Save metadata
            with open(metadata_path, 'wb') as f:
                pickle.dump(self.tenant_metadata.get(tenant_id, {}), f)
            
            logger.debug(f"Saved FAISS index for tenant {tenant_id}")
        except Exception as e:
            logger.error(f"Failed to save FAISS index for tenant {tenant_id}: {e}")
    
    async def create_tenant_index(self, tenant_id: str) -> bool:
        """Create tenant-specific FAISS index"""
        try:
            # Create new FAISS index (using IndexFlatIP for cosine similarity)
            index = self.faiss.IndexFlatIP(self.dimension)
            
            # Wrap with IDMap for ID tracking
            index_with_ids = self.faiss.IndexIDMap(index)
            
            self.tenant_indexes[tenant_id] = index_with_ids
            self.tenant_metadata[tenant_id] = {}
            
            await self._save_tenant_index(tenant_id)
            
            logger.info(f"Created FAISS index for tenant {tenant_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to create FAISS index for tenant {tenant_id}: {e}")
            return False
    
    async def add_vectors(
        self, 
        tenant_id: str, 
        vectors: List[List[float]], 
        metadata: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Add vectors to tenant's FAISS index"""
        
        if tenant_id not in self.tenant_indexes:
            await self._load_tenant_index(tenant_id)
        
        try:
            # Convert vectors to numpy array and normalize for cosine similarity
            vectors_array = np.array(vectors, dtype=np.float32)
            
            # Normalize vectors for cosine similarity
            norms = np.linalg.norm(vectors_array, axis=1, keepdims=True)
            vectors_array = vectors_array / norms
            
            # Generate IDs if not provided
            if ids is None:
                ids = [hashlib.md5(f"{tenant_id}_{i}_{datetime.now().isoformat()}".encode()).hexdigest() 
                       for i in range(len(vectors))]
            
            # Convert string IDs to integers for FAISS
            id_mapping = {}
            int_ids = []
            for i, str_id in enumerate(ids):
                int_id = hash(str_id) % (2**31)  # Ensure positive 32-bit integer
                id_mapping[int_id] = str_id
                int_ids.append(int_id)
            
            int_ids_array = np.array(int_ids, dtype=np.int64)
            
            # Add to index
            self.tenant_indexes[tenant_id].add_with_ids(vectors_array, int_ids_array)
            
            # Store metadata
            tenant_metadata = self.tenant_metadata[tenant_id]
            for str_id, meta in zip(ids, metadata):
                tenant_metadata[str_id] = {
                    **meta,
                    'added_at': datetime.now().isoformat(),
                    'vector_id': str_id
                }
            
            # Save to disk
            await self._save_tenant_index(tenant_id)
            
            logger.info(f"Added {len(vectors)} vectors to FAISS index for tenant {tenant_id}")
            
            return {
                'success': True,
                'added_count': len(vectors),
                'total_vectors': self.tenant_indexes[tenant_id].ntotal,
                'ids': ids
            }
            
        except Exception as e:
            logger.error(f"Failed to add vectors to FAISS for tenant {tenant_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def search_vectors(
        self, 
        tenant_id: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors in tenant's index"""
        
        if tenant_id not in self.tenant_indexes:
            await self._load_tenant_index(tenant_id)
        
        try:
            # Normalize query vector
            query_array = np.array([query_vector], dtype=np.float32)
            norm = np.linalg.norm(query_array)
            if norm > 0:
                query_array = query_array / norm
            
            # Search in FAISS index
            scores, indices = self.tenant_indexes[tenant_id].search(query_array, top_k)
            
            # Convert results
            results = []
            tenant_metadata = self.tenant_metadata.get(tenant_id, {})
            
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx == -1:  # No more results
                    break
                
                # Find string ID from integer ID
                str_id = None
                for meta_id, meta in tenant_metadata.items():
                    if hash(meta_id) % (2**31) == idx:
                        str_id = meta_id
                        break
                
                if str_id and str_id in tenant_metadata:
                    metadata = tenant_metadata[str_id]
                    
                    # Apply metadata filtering if specified
                    if filter_metadata:
                        if not all(metadata.get(k) == v for k, v in filter_metadata.items()):
                            continue
                    
                    results.append({
                        'id': str_id,
                        'score': float(score),
                        'metadata': metadata
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search vectors in FAISS for tenant {tenant_id}: {e}")
            return []
    
    async def delete_vectors(self, tenant_id: str, ids: List[str]) -> bool:
        """Delete vectors by IDs (FAISS doesn't support deletion, so we mark as deleted)"""
        
        if tenant_id not in self.tenant_indexes:
            await self._load_tenant_index(tenant_id)
        
        try:
            tenant_metadata = self.tenant_metadata.get(tenant_id, {})
            
            # Mark as deleted in metadata
            for vector_id in ids:
                if vector_id in tenant_metadata:
                    tenant_metadata[vector_id]['deleted'] = True
                    tenant_metadata[vector_id]['deleted_at'] = datetime.now().isoformat()
            
            await self._save_tenant_index(tenant_id)
            
            logger.info(f"Marked {len(ids)} vectors as deleted for tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors from FAISS for tenant {tenant_id}: {e}")
            return False
    
    async def get_index_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get index statistics for tenant"""
        
        if tenant_id not in self.tenant_indexes:
            await self._load_tenant_index(tenant_id)
        
        try:
            index = self.tenant_indexes[tenant_id]
            metadata = self.tenant_metadata.get(tenant_id, {})
            
            # Count active (non-deleted) vectors
            active_count = sum(1 for meta in metadata.values() if not meta.get('deleted', False))
            deleted_count = sum(1 for meta in metadata.values() if meta.get('deleted', False))
            
            return {
                'provider': 'faiss',
                'tenant_id': tenant_id,
                'total_vectors': index.ntotal,
                'active_vectors': active_count,
                'deleted_vectors': deleted_count,
                'dimension': self.dimension,
                'index_type': 'IndexFlatIP',
                'storage_path': str(self._get_tenant_index_path(tenant_id))
            }
            
        except Exception as e:
            logger.error(f"Failed to get FAISS stats for tenant {tenant_id}: {e}")
            return {'error': str(e)}
    
    async def delete_tenant_index(self, tenant_id: str) -> bool:
        """Delete tenant's entire index"""
        try:
            # Remove from memory
            if tenant_id in self.tenant_indexes:
                del self.tenant_indexes[tenant_id]
            if tenant_id in self.tenant_metadata:
                del self.tenant_metadata[tenant_id]
            
            # Remove files
            index_path = self._get_tenant_index_path(tenant_id)
            metadata_path = self._get_tenant_metadata_path(tenant_id)
            
            if index_path.exists():
                index_path.unlink()
            if metadata_path.exists():
                metadata_path.unlink()
            
            logger.info(f"Deleted FAISS index for tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete FAISS index for tenant {tenant_id}: {e}")
            return False

class PineconeVectorStore(VectorStoreProvider):
    """Pinecone vector store with tenant namespaces"""
    
    def __init__(self, index_name: str = "aithentiq", dimension: int = 1536):
        self.index_name = index_name
        self.dimension = dimension
        
        # Import Pinecone
        try:
            import pinecone
            self.pinecone = pinecone
            
            # Initialize Pinecone
            api_key = os.getenv("PINECONE_API_KEY")
            environment = os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp")
            
            if not api_key:
                raise ValueError("PINECONE_API_KEY environment variable is required")
            
            pinecone.init(api_key=api_key, environment=environment)
            
            # Get or create index
            if index_name not in pinecone.list_indexes():
                pinecone.create_index(
                    name=index_name,
                    dimension=dimension,
                    metric="cosine"
                )
            
            self.index = pinecone.Index(index_name)
            
        except ImportError:
            raise ImportError("pinecone-client package is required for Pinecone vector store")
    
    def _get_tenant_namespace(self, tenant_id: str) -> str:
        """Get namespace for tenant"""
        return f"tenant_{tenant_id}"
    
    async def create_tenant_index(self, tenant_id: str) -> bool:
        """Create tenant namespace (Pinecone uses namespaces for isolation)"""
        # Pinecone namespaces are created automatically when vectors are added
        logger.info(f"Tenant namespace will be created automatically for {tenant_id}")
        return True
    
    async def add_vectors(
        self, 
        tenant_id: str, 
        vectors: List[List[float]], 
        metadata: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Add vectors to Pinecone with tenant namespace"""
        
        try:
            namespace = self._get_tenant_namespace(tenant_id)
            
            # Generate IDs if not provided
            if ids is None:
                ids = [f"{tenant_id}_{i}_{datetime.now().timestamp()}" for i in range(len(vectors))]
            
            # Prepare vectors for upsert
            vectors_to_upsert = []
            for i, (vector, meta) in enumerate(zip(vectors, metadata)):
                vectors_to_upsert.append({
                    'id': ids[i],
                    'values': vector,
                    'metadata': {
                        **meta,
                        'tenant_id': tenant_id,
                        'added_at': datetime.now().isoformat()
                    }
                })
            
            # Upsert vectors
            upsert_response = self.index.upsert(
                vectors=vectors_to_upsert,
                namespace=namespace
            )
            
            logger.info(f"Added {len(vectors)} vectors to Pinecone for tenant {tenant_id}")
            
            return {
                'success': True,
                'added_count': upsert_response.upserted_count,
                'ids': ids
            }
            
        except Exception as e:
            logger.error(f"Failed to add vectors to Pinecone for tenant {tenant_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def search_vectors(
        self, 
        tenant_id: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search vectors in Pinecone with tenant namespace"""
        
        try:
            namespace = self._get_tenant_namespace(tenant_id)
            
            # Prepare filter
            filter_dict = {'tenant_id': tenant_id}
            if filter_metadata:
                filter_dict.update(filter_metadata)
            
            # Query Pinecone
            query_response = self.index.query(
                vector=query_vector,
                top_k=top_k,
                namespace=namespace,
                filter=filter_dict,
                include_metadata=True
            )
            
            # Convert results
            results = []
            for match in query_response.matches:
                results.append({
                    'id': match.id,
                    'score': match.score,
                    'metadata': match.metadata
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search vectors in Pinecone for tenant {tenant_id}: {e}")
            return []
    
    async def delete_vectors(self, tenant_id: str, ids: List[str]) -> bool:
        """Delete vectors from Pinecone"""
        
        try:
            namespace = self._get_tenant_namespace(tenant_id)
            
            self.index.delete(ids=ids, namespace=namespace)
            
            logger.info(f"Deleted {len(ids)} vectors from Pinecone for tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors from Pinecone for tenant {tenant_id}: {e}")
            return False
    
    async def get_index_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get Pinecone index statistics"""
        
        try:
            namespace = self._get_tenant_namespace(tenant_id)
            stats = self.index.describe_index_stats()
            
            namespace_stats = stats.namespaces.get(namespace, {})
            
            return {
                'provider': 'pinecone',
                'tenant_id': tenant_id,
                'namespace': namespace,
                'total_vectors': namespace_stats.get('vector_count', 0),
                'dimension': self.dimension,
                'index_name': self.index_name
            }
            
        except Exception as e:
            logger.error(f"Failed to get Pinecone stats for tenant {tenant_id}: {e}")
            return {'error': str(e)}
    
    async def delete_tenant_index(self, tenant_id: str) -> bool:
        """Delete all vectors in tenant namespace"""
        
        try:
            namespace = self._get_tenant_namespace(tenant_id)
            
            # Delete all vectors in namespace
            self.index.delete(delete_all=True, namespace=namespace)
            
            logger.info(f"Deleted all vectors in Pinecone namespace for tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete Pinecone namespace for tenant {tenant_id}: {e}")
            return False

# Global vector storage service
class CompleteVectorStorageService:
    """Complete vector storage service with multiple providers"""
    
    def __init__(self):
        self.providers = {}
        self.default_provider = "faiss"
    
    def get_provider(self, provider_name: str, **kwargs) -> VectorStoreProvider:
        """Get or create vector store provider"""

        if provider_name not in self.providers:
            if provider_name == "faiss":
                dimension = kwargs.get("dimension", 1536)
                storage_path = kwargs.get("storage_path", "data/faiss_indexes")
                self.providers[provider_name] = FAISSVectorStore(dimension, storage_path)
            elif provider_name == "pinecone":
                index_name = kwargs.get("index_name", "aithentiq")
                dimension = kwargs.get("dimension", 1536)
                self.providers[provider_name] = PineconeVectorStore(index_name, dimension)
            elif provider_name == "weaviate":
                url = kwargs.get("url", "http://localhost:8080")
                self.providers[provider_name] = WeaviateVectorStore(url)
            else:
                raise ValueError(f"Unsupported vector store provider: {provider_name}")

        return self.providers[provider_name]

    def get_available_providers(self) -> List[str]:
        """Get list of available vector store providers"""
        providers = ["faiss"]  # FAISS is always available

        # Check Pinecone
        if os.getenv("PINECONE_API_KEY"):
            try:
                import pinecone
                providers.append("pinecone")
            except ImportError:
                pass

        # Check Weaviate
        try:
            import weaviate
            providers.append("weaviate")
        except ImportError:
            pass

        return providers

class WeaviateVectorStore(VectorStoreProvider):
    """Weaviate vector store with tenant-specific classes"""

    def __init__(self, url: str = "http://localhost:8080"):
        self.url = url

        # Import Weaviate
        try:
            import weaviate
            self.weaviate = weaviate

            # Initialize client
            self.client = weaviate.Client(url)

        except ImportError:
            raise ImportError("weaviate-client package is required for Weaviate vector store")

    def _get_tenant_class_name(self, tenant_id: str) -> str:
        """Get class name for tenant"""
        return f"Tenant_{tenant_id.replace('-', '_')}"

    async def create_tenant_index(self, tenant_id: str) -> bool:
        """Create tenant-specific class in Weaviate"""

        try:
            class_name = self._get_tenant_class_name(tenant_id)

            # Check if class already exists
            if self.client.schema.exists(class_name):
                return True

            # Create class schema
            class_schema = {
                "class": class_name,
                "description": f"Vector storage for tenant {tenant_id}",
                "vectorizer": "none",  # We provide our own vectors
                "properties": [
                    {
                        "name": "content",
                        "dataType": ["text"],
                        "description": "The content text"
                    },
                    {
                        "name": "metadata",
                        "dataType": ["object"],
                        "description": "Additional metadata"
                    },
                    {
                        "name": "tenant_id",
                        "dataType": ["string"],
                        "description": "Tenant identifier"
                    }
                ]
            }

            self.client.schema.create_class(class_schema)

            logger.info(f"Created Weaviate class for tenant {tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to create Weaviate class for tenant {tenant_id}: {e}")
            return False

    async def add_vectors(
        self,
        tenant_id: str,
        vectors: List[List[float]],
        metadata: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Add vectors to Weaviate"""

        try:
            class_name = self._get_tenant_class_name(tenant_id)

            # Ensure class exists
            await self.create_tenant_index(tenant_id)

            # Generate IDs if not provided
            if ids is None:
                ids = [f"{tenant_id}_{i}_{datetime.now().timestamp()}" for i in range(len(vectors))]

            # Add objects with vectors
            added_count = 0
            for i, (vector, meta) in enumerate(zip(vectors, metadata)):
                object_data = {
                    "content": meta.get("content", ""),
                    "metadata": meta,
                    "tenant_id": tenant_id
                }

                self.client.data_object.create(
                    data_object=object_data,
                    class_name=class_name,
                    uuid=ids[i],
                    vector=vector
                )
                added_count += 1

            logger.info(f"Added {added_count} vectors to Weaviate for tenant {tenant_id}")

            return {
                'success': True,
                'added_count': added_count,
                'ids': ids
            }

        except Exception as e:
            logger.error(f"Failed to add vectors to Weaviate for tenant {tenant_id}: {e}")
            return {'success': False, 'error': str(e)}

    async def search_vectors(
        self,
        tenant_id: str,
        query_vector: List[float],
        top_k: int = 10,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search vectors in Weaviate"""

        try:
            class_name = self._get_tenant_class_name(tenant_id)

            # Build query
            query = self.client.query.get(class_name, ["content", "metadata", "tenant_id"]) \
                        .with_near_vector({"vector": query_vector}) \
                        .with_limit(top_k) \
                        .with_additional(["certainty", "id"])

            # Add where filter if specified
            if filter_metadata:
                where_filter = {"path": ["tenant_id"], "operator": "Equal", "valueString": tenant_id}
                query = query.with_where(where_filter)

            result = query.do()

            # Convert results
            results = []
            if "data" in result and "Get" in result["data"] and class_name in result["data"]["Get"]:
                for item in result["data"]["Get"][class_name]:
                    results.append({
                        'id': item["_additional"]["id"],
                        'score': item["_additional"]["certainty"],
                        'metadata': item.get("metadata", {})
                    })

            return results

        except Exception as e:
            logger.error(f"Failed to search vectors in Weaviate for tenant {tenant_id}: {e}")
            return []

    async def delete_vectors(self, tenant_id: str, ids: List[str]) -> bool:
        """Delete vectors from Weaviate"""

        try:
            class_name = self._get_tenant_class_name(tenant_id)

            for vector_id in ids:
                self.client.data_object.delete(vector_id, class_name)

            logger.info(f"Deleted {len(ids)} vectors from Weaviate for tenant {tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete vectors from Weaviate for tenant {tenant_id}: {e}")
            return False

    async def get_index_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get Weaviate class statistics"""

        try:
            class_name = self._get_tenant_class_name(tenant_id)

            # Get object count
            result = self.client.query.aggregate(class_name).with_meta_count().do()

            count = 0
            if "data" in result and "Aggregate" in result["data"] and class_name in result["data"]["Aggregate"]:
                count = result["data"]["Aggregate"][class_name][0]["meta"]["count"]

            return {
                'provider': 'weaviate',
                'tenant_id': tenant_id,
                'class_name': class_name,
                'total_vectors': count,
                'url': self.url
            }

        except Exception as e:
            logger.error(f"Failed to get Weaviate stats for tenant {tenant_id}: {e}")
            return {'error': str(e)}

    async def delete_tenant_index(self, tenant_id: str) -> bool:
        """Delete tenant's Weaviate class"""

        try:
            class_name = self._get_tenant_class_name(tenant_id)

            self.client.schema.delete_class(class_name)

            logger.info(f"Deleted Weaviate class for tenant {tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete Weaviate class for tenant {tenant_id}: {e}")
            return False

# Global instance
complete_vector_storage_service = CompleteVectorStorageService()
