"""
Adaptive Learning System for Predictive Analytics
Real-time model improvement based on user feedback and performance metrics
"""

import logging
import json
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

import models
from models_predictive_analytics import UserBehaviorLog

class AdaptiveLearningSystem:
    """
    Advanced Adaptive Learning System that continuously improves predictions
    based on user feedback, performance metrics, and behavioral patterns
    """
    
    def __init__(self):
        self.learning_rate = 0.01
        self.feedback_weight = 0.3
        self.performance_weight = 0.4
        self.temporal_weight = 0.3
        self.min_feedback_threshold = 5
        
        # Model performance tracking
        self.model_performance = {
            'risk_prediction': {'accuracy': 0.85, 'confidence': 0.8, 'feedback_count': 0},
            'churn_prediction': {'accuracy': 0.82, 'confidence': 0.75, 'feedback_count': 0},
            'anomaly_detection': {'accuracy': 0.88, 'confidence': 0.85, 'feedback_count': 0}
        }
        
        # Feature importance weights (adaptive)
        self.feature_weights = {
            'query_frequency': 0.25,
            'trust_score_trend': 0.20,
            'behavioral_consistency': 0.18,
            'feature_adoption': 0.15,
            'engagement_level': 0.12,
            'error_rate': 0.10
        }
    
    def record_prediction_feedback(self, user_id: str, prediction_type: str, 
                                 predicted_value: float, actual_outcome: float,
                                 user_feedback: Optional[str], db: Session) -> Dict[str, Any]:
        """
        Record user feedback and actual outcomes to improve future predictions
        """
        try:
            # Calculate prediction error
            prediction_error = abs(predicted_value - actual_outcome)
            accuracy = 1 - min(prediction_error, 1.0)
            
            # Update model performance
            if prediction_type in self.model_performance:
                current_perf = self.model_performance[prediction_type]
                current_perf['accuracy'] = (
                    current_perf['accuracy'] * 0.9 + accuracy * 0.1
                )
                current_perf['feedback_count'] += 1
                
                # Adjust confidence based on recent performance
                if current_perf['feedback_count'] >= self.min_feedback_threshold:
                    current_perf['confidence'] = min(
                        current_perf['accuracy'] + 0.1, 1.0
                    )
            
            # Process user feedback
            feedback_score = self._process_user_feedback(user_feedback)
            
            # Adaptive feature weight adjustment
            self._adjust_feature_weights(prediction_type, accuracy, feedback_score)
            
            # Store feedback for future learning
            feedback_record = {
                'user_id': user_id,
                'prediction_type': prediction_type,
                'predicted_value': predicted_value,
                'actual_outcome': actual_outcome,
                'prediction_error': prediction_error,
                'accuracy': accuracy,
                'user_feedback': user_feedback,
                'feedback_score': feedback_score,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'model_version': '1.0'
            }
            
            # In production, store this in a dedicated feedback table
            logging.info(f"Recorded feedback for {prediction_type}: {feedback_record}")
            
            return {
                'status': 'success',
                'feedback_recorded': True,
                'prediction_accuracy': accuracy,
                'model_performance': self.model_performance[prediction_type],
                'adjusted_weights': self._get_relevant_weights(prediction_type),
                'learning_impact': self._calculate_learning_impact(accuracy, feedback_score)
            }
            
        except Exception as e:
            logging.error(f"Error recording prediction feedback: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'feedback_recorded': False
            }
    
    def get_adaptive_predictions(self, user_id: str, base_predictions: Dict[str, Any], 
                               db: Session) -> Dict[str, Any]:
        """
        Apply adaptive learning to improve base predictions
        """
        try:
            # Get user's historical feedback patterns
            user_patterns = self._analyze_user_feedback_patterns(user_id, db)
            
            # Apply adaptive adjustments
            adapted_predictions = {}
            
            for pred_type, pred_data in base_predictions.items():
                if pred_type in self.model_performance:
                    # Get base prediction value
                    if pred_type == 'risk':
                        base_value = pred_data.get('risk_score', 0.5)
                    elif pred_type == 'churn':
                        base_value = pred_data.get('churn_probability', 0.5)
                    elif pred_type == 'anomaly':
                        base_value = 1.0 if pred_data.get('anomalies_detected', 0) > 0 else 0.0
                    else:
                        base_value = 0.5
                    
                    # Apply adaptive adjustments
                    adapted_value = self._apply_adaptive_adjustment(
                        pred_type, base_value, user_patterns
                    )
                    
                    # Calculate dynamic confidence
                    dynamic_confidence = self._calculate_dynamic_confidence(
                        pred_type, adapted_value, user_patterns
                    )
                    
                    adapted_predictions[pred_type] = {
                        **pred_data,
                        'original_value': base_value,
                        'adapted_value': adapted_value,
                        'adaptation_factor': adapted_value - base_value,
                        'dynamic_confidence': dynamic_confidence,
                        'model_performance': self.model_performance[pred_type],
                        'learning_metadata': {
                            'user_pattern_influence': user_patterns.get('influence_score', 0.0),
                            'temporal_adjustment': self._get_temporal_adjustment(pred_type),
                            'feature_weight_impact': self._calculate_feature_impact(pred_type)
                        }
                    }
            
            return {
                'adapted_predictions': adapted_predictions,
                'learning_summary': {
                    'total_adaptations': len(adapted_predictions),
                    'average_confidence': np.mean([
                        p['dynamic_confidence'] for p in adapted_predictions.values()
                    ]) if adapted_predictions else 0.7,
                    'learning_effectiveness': self._calculate_learning_effectiveness(),
                    'model_maturity': self._calculate_model_maturity()
                },
                'user_learning_profile': user_patterns,
                'system_performance': self.model_performance
            }
            
        except Exception as e:
            logging.error(f"Error applying adaptive predictions: {e}")
            return {
                'adapted_predictions': base_predictions,
                'error': str(e)
            }
    
    def get_learning_insights(self, db: Session) -> Dict[str, Any]:
        """
        Get insights about the learning system's performance and evolution
        """
        try:
            return {
                'system_status': 'active',
                'learning_metrics': {
                    'total_feedback_received': sum(
                        perf['feedback_count'] for perf in self.model_performance.values()
                    ),
                    'average_model_accuracy': np.mean([
                        perf['accuracy'] for perf in self.model_performance.values()
                    ]) if self.model_performance else 0.8,
                    'average_confidence': np.mean([
                        perf['confidence'] for perf in self.model_performance.values()
                    ]) if self.model_performance else 0.7,
                    'learning_rate': self.learning_rate,
                    'adaptation_strength': self._calculate_adaptation_strength()
                },
                'model_performance': self.model_performance,
                'feature_importance': self.feature_weights,
                'learning_evolution': {
                    'improvement_trend': self._calculate_improvement_trend(),
                    'stability_score': self._calculate_stability_score(),
                    'adaptation_frequency': self._calculate_adaptation_frequency(),
                    'user_satisfaction_trend': self._estimate_user_satisfaction()
                },
                'recommendations': self._generate_learning_recommendations(),
                'next_optimization_cycle': (
                    datetime.now(timezone.utc) + timedelta(hours=24)
                ).isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error getting learning insights: {e}")
            return {
                'system_status': 'error',
                'error': str(e)
            }
    
    def _process_user_feedback(self, feedback: Optional[str]) -> float:
        """Process textual user feedback into numerical score"""
        if not feedback:
            return 0.5  # Neutral
        
        feedback_lower = feedback.lower()
        
        # Positive feedback indicators
        positive_words = ['good', 'accurate', 'helpful', 'correct', 'excellent', 'great']
        negative_words = ['bad', 'wrong', 'inaccurate', 'poor', 'terrible', 'useless']
        
        positive_count = sum(1 for word in positive_words if word in feedback_lower)
        negative_count = sum(1 for word in negative_words if word in feedback_lower)
        
        if positive_count > negative_count:
            return min(0.7 + (positive_count * 0.1), 1.0)
        elif negative_count > positive_count:
            return max(0.3 - (negative_count * 0.1), 0.0)
        else:
            return 0.5
    
    def _adjust_feature_weights(self, prediction_type: str, accuracy: float, 
                              feedback_score: float):
        """Dynamically adjust feature weights based on performance"""
        try:
            # Calculate adjustment factor
            performance_factor = (accuracy + feedback_score) / 2
            adjustment = (performance_factor - 0.5) * self.learning_rate
            
            # Apply small adjustments to maintain stability
            for feature in self.feature_weights:
                if prediction_type in feature or 'general' in feature:
                    current_weight = self.feature_weights[feature]
                    new_weight = current_weight + (adjustment * 0.1)
                    self.feature_weights[feature] = max(0.05, min(0.4, new_weight))
            
            # Normalize weights to sum to 1
            total_weight = sum(self.feature_weights.values())
            if total_weight > 0:
                for feature in self.feature_weights:
                    self.feature_weights[feature] /= total_weight
                    
        except Exception as e:
            logging.error(f"Error adjusting feature weights: {e}")
    
    def _analyze_user_feedback_patterns(self, user_id: str, db: Session) -> Dict[str, Any]:
        """Analyze user's historical feedback patterns"""
        try:
            # In production, query actual feedback data
            # For now, simulate user patterns
            return {
                'feedback_count': np.random.randint(5, 20),
                'average_satisfaction': np.random.uniform(0.6, 0.9),
                'feedback_consistency': np.random.uniform(0.7, 0.95),
                'influence_score': np.random.uniform(0.1, 0.3),
                'learning_responsiveness': np.random.uniform(0.8, 1.0)
            }
        except Exception:
            return {
                'feedback_count': 0,
                'average_satisfaction': 0.7,
                'feedback_consistency': 0.8,
                'influence_score': 0.2,
                'learning_responsiveness': 0.9
            }
    
    def _apply_adaptive_adjustment(self, pred_type: str, base_value: float, 
                                 user_patterns: Dict[str, Any]) -> float:
        """Apply adaptive adjustments to base prediction"""
        try:
            # Get model performance
            model_perf = self.model_performance.get(pred_type, {})
            model_accuracy = model_perf.get('accuracy', 0.8)
            
            # Calculate adjustment factors
            performance_factor = (model_accuracy - 0.5) * 0.1
            user_factor = (user_patterns.get('average_satisfaction', 0.7) - 0.5) * 0.05
            temporal_factor = self._get_temporal_adjustment(pred_type)
            
            # Apply weighted adjustment
            total_adjustment = (
                performance_factor * self.performance_weight +
                user_factor * self.feedback_weight +
                temporal_factor * self.temporal_weight
            )
            
            # Apply adjustment with bounds
            adjusted_value = base_value + total_adjustment
            return max(0.0, min(1.0, adjusted_value))
            
        except Exception:
            return base_value
    
    def _calculate_dynamic_confidence(self, pred_type: str, adapted_value: float,
                                    user_patterns: Dict[str, Any]) -> float:
        """Calculate dynamic confidence based on multiple factors"""
        try:
            # Base confidence from model performance
            base_confidence = self.model_performance.get(pred_type, {}).get('confidence', 0.7)
            
            # Adjust based on user feedback patterns
            user_confidence_boost = user_patterns.get('feedback_consistency', 0.8) * 0.1
            
            # Adjust based on prediction certainty (values closer to 0.5 are less certain)
            certainty_factor = abs(adapted_value - 0.5) * 2  # 0 to 1 scale
            certainty_boost = certainty_factor * 0.1
            
            # Calculate final confidence
            dynamic_confidence = base_confidence + user_confidence_boost + certainty_boost
            return max(0.5, min(1.0, dynamic_confidence))
            
        except Exception:
            return 0.7
    
    def _get_temporal_adjustment(self, pred_type: str) -> float:
        """Get temporal adjustment factor based on time of day, season, etc."""
        try:
            current_hour = datetime.now().hour
            
            # Simple temporal patterns (in production, use more sophisticated analysis)
            if pred_type == 'risk':
                # Higher risk during off-hours
                if current_hour < 6 or current_hour > 22:
                    return 0.05
                return -0.02
            elif pred_type == 'churn':
                # Higher churn risk on weekends
                if datetime.now().weekday() >= 5:
                    return 0.03
                return -0.01
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def _calculate_feature_impact(self, pred_type: str) -> float:
        """Calculate the impact of current feature weights"""
        try:
            relevant_features = [f for f in self.feature_weights.keys() 
                               if pred_type in f or 'general' in f]
            if relevant_features:
                return sum(self.feature_weights[f] for f in relevant_features)
            return sum(self.feature_weights.values()) / len(self.feature_weights)
        except Exception:
            return 0.5
    
    def _calculate_learning_effectiveness(self) -> float:
        """Calculate overall learning system effectiveness"""
        try:
            total_feedback = sum(perf['feedback_count'] for perf in self.model_performance.values())
            avg_accuracy = np.mean([perf['accuracy'] for perf in self.model_performance.values()]) if self.model_performance else 0.8
            
            if total_feedback == 0:
                return 0.5
            
            # Effectiveness based on feedback volume and accuracy
            feedback_factor = min(total_feedback / 100, 1.0)  # Normalize to 0-1
            accuracy_factor = avg_accuracy
            
            return (feedback_factor * 0.3 + accuracy_factor * 0.7)
        except Exception:
            return 0.7
    
    def _calculate_model_maturity(self) -> float:
        """Calculate model maturity based on feedback and stability"""
        try:
            total_feedback = sum(perf['feedback_count'] for perf in self.model_performance.values())
            confidence_variance = np.var([perf['confidence'] for perf in self.model_performance.values()])
            
            # Maturity increases with feedback and decreases with variance
            feedback_maturity = min(total_feedback / 50, 1.0)
            stability_maturity = max(0, 1 - confidence_variance)
            
            return (feedback_maturity * 0.6 + stability_maturity * 0.4)
        except Exception:
            return 0.6
    
    def _calculate_adaptation_strength(self) -> float:
        """Calculate how strongly the system adapts to new information"""
        return self.learning_rate * 10  # Scale for display
    
    def _calculate_improvement_trend(self) -> str:
        """Calculate the overall improvement trend"""
        try:
            avg_accuracy = np.mean([perf['accuracy'] for perf in self.model_performance.values()]) if self.model_performance else 0.8
            if avg_accuracy > 0.85:
                return 'excellent'
            elif avg_accuracy > 0.75:
                return 'good'
            elif avg_accuracy > 0.65:
                return 'moderate'
            else:
                return 'needs_improvement'
        except Exception:
            return 'stable'
    
    def _calculate_stability_score(self) -> float:
        """Calculate system stability score"""
        try:
            confidence_values = [perf['confidence'] for perf in self.model_performance.values()]
            return max(0, 1 - np.var(confidence_values))
        except Exception:
            return 0.8
    
    def _calculate_adaptation_frequency(self) -> str:
        """Calculate how frequently the system adapts"""
        total_feedback = sum(perf['feedback_count'] for perf in self.model_performance.values())
        if total_feedback > 50:
            return 'high'
        elif total_feedback > 20:
            return 'moderate'
        else:
            return 'low'
    
    def _estimate_user_satisfaction(self) -> str:
        """Estimate overall user satisfaction trend"""
        avg_accuracy = np.mean([perf['accuracy'] for perf in self.model_performance.values()]) if self.model_performance else 0.8
        if avg_accuracy > 0.8:
            return 'increasing'
        elif avg_accuracy > 0.7:
            return 'stable'
        else:
            return 'declining'
    
    def _generate_learning_recommendations(self) -> List[Dict[str, Any]]:
        """Generate recommendations for improving the learning system"""
        recommendations = []
        
        try:
            avg_accuracy = np.mean([perf['accuracy'] for perf in self.model_performance.values()]) if self.model_performance else 0.8
            total_feedback = sum(perf['feedback_count'] for perf in self.model_performance.values())
            
            if avg_accuracy < 0.75:
                recommendations.append({
                    'type': 'accuracy_improvement',
                    'priority': 'high',
                    'title': 'Improve Model Accuracy',
                    'description': 'Current accuracy is below optimal threshold',
                    'actions': ['Collect more training data', 'Tune hyperparameters', 'Feature engineering']
                })
            
            if total_feedback < 20:
                recommendations.append({
                    'type': 'feedback_collection',
                    'priority': 'medium',
                    'title': 'Increase Feedback Collection',
                    'description': 'More user feedback needed for better learning',
                    'actions': ['Add feedback prompts', 'Incentivize feedback', 'Simplify feedback process']
                })
            
            if not recommendations:
                recommendations.append({
                    'type': 'optimization',
                    'priority': 'low',
                    'title': 'Continue Optimization',
                    'description': 'System performing well, continue current approach',
                    'actions': ['Monitor performance', 'Fine-tune parameters', 'Expand feature set']
                })
            
            return recommendations
            
        except Exception:
            return [{
                'type': 'maintenance',
                'priority': 'low',
                'title': 'System Maintenance',
                'description': 'Regular system maintenance recommended',
                'actions': ['Check logs', 'Update dependencies', 'Performance review']
            }]
    
    def _get_relevant_weights(self, prediction_type: str) -> Dict[str, float]:
        """Get feature weights relevant to specific prediction type"""
        return {k: v for k, v in self.feature_weights.items() 
                if prediction_type in k.lower() or 'general' in k.lower()}
