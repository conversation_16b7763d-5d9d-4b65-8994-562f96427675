"""
Trust Score Integration Service
Integrates new trust system with existing LLM service
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

try:
    from aithentiq.trust import TrustScoreComputer, BayesianTrustUpdater
    from aithentiq.config import trust_config
    TRUST_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Trust system not available: {e}")
    TRUST_SYSTEM_AVAILABLE = False
    TrustScoreComputer = None
    BayesianTrustUpdater = None
    trust_config = None


class TrustIntegrationService:
    """Service to integrate advanced trust scoring with existing systems"""
    
    def __init__(self):
        if TRUST_SYSTEM_AVAILABLE:
            self.trust_computer = TrustScoreComputer()
            self.bayesian_updater = BayesianTrustUpdater()
        else:
            self.trust_computer = None
            self.bayesian_updater = None
        self.logger = logging.getLogger(__name__)
    
    def compute_enhanced_trust_score(self, query: str, answer: str, user_id: str, 
                                   sources: Optional[List[Dict]] = None,
                                   topic: str = "general") -> Dict[str, Any]:
        """
        Compute enhanced trust score with Bayesian updates
        
        Args:
            query: User question
            answer: AI-generated answer
            user_id: User identifier
            sources: Source documents (for RAG)
            topic: Topic/domain classification
            
        Returns:
            Enhanced trust score with all components
        """
        try:
            # Check if trust system is available
            if not TRUST_SYSTEM_AVAILABLE or not self.trust_computer:
                return self._fallback_trust_score("Trust system not available")

            # Prepare context
            context = {
                "sources": sources or [],
                "user_id": user_id,
                "topic": topic,
                "metadata": {
                    "query_length": len(query),
                    "answer_length": len(answer),
                    "has_sources": bool(sources)
                }
            }
            
            # Compute base trust score
            trust_result = self.trust_computer.compute(
                query=query,
                answer=answer,
                context=context
            )
            
            # Apply Bayesian updates
            posterior_result = self.bayesian_updater.get_posterior_trust(
                user_id=user_id,
                topic=topic,
                base_trust=trust_result.overall_score
            )
            
            # Format for compatibility with existing system
            enhanced_trust_score = {
                "overall_score": posterior_result["posterior_trust"],
                "factors": trust_result.factors,
                "explanation": trust_result.explanation,
                
                # Enhanced components
                "component_scores": trust_result.components.to_dict(),
                "base_score": trust_result.overall_score,
                "bayesian_adjustment": posterior_result["posterior_trust"] - trust_result.overall_score,
                
                # Bayesian information
                "user_posterior": posterior_result["user_posterior"],
                "topic_posterior": posterior_result["topic_posterior"],
                "user_confidence": posterior_result["user_confidence"],
                "topic_confidence": posterior_result["topic_confidence"],
                "user_interactions": posterior_result["user_interactions"],
                "topic_interactions": posterior_result["topic_interactions"],
                
                # Metadata
                "processing_time_ms": trust_result.processing_time_ms,
                "confidence_interval": trust_result.confidence_interval,
                "computed_at": datetime.utcnow().isoformat(),
                "version": "3.0"
            }
            
            return enhanced_trust_score
            
        except Exception as e:
            self.logger.error(f"Enhanced trust score computation failed: {str(e)}")
            return self._fallback_trust_score(str(e))
    
    def process_trust_feedback(self, user_id: str, topic: str, predicted_trust: float,
                             actual_correctness: bool, feedback_weight: float = 1.0) -> bool:
        """
        Process user feedback to update Bayesian priors
        
        Args:
            user_id: User identifier
            topic: Topic/domain
            predicted_trust: Trust score that was predicted
            actual_correctness: Whether the answer was actually correct
            feedback_weight: Weight for this feedback (0-1)
            
        Returns:
            Success status
        """
        try:
            self.bayesian_updater.update_posterior(
                user_id=user_id,
                topic=topic,
                predicted_trust=predicted_trust,
                actual_correctness=actual_correctness,
                feedback_weight=feedback_weight
            )
            
            self.logger.info(f"Processed trust feedback for user {user_id}, topic {topic}")
            return True
            
        except Exception as e:
            self.logger.error(f"Trust feedback processing failed: {str(e)}")
            return False
    
    def get_user_trust_insights(self, user_id: str) -> Dict[str, Any]:
        """Get trust insights for a specific user"""
        try:
            profile = self.bayesian_updater.get_user_trust_profile(user_id)
            
            # Add interpretable insights
            insights = {
                "trust_profile": profile,
                "expertise_level": self._classify_expertise_level(profile["mean_trust"]),
                "reliability_trend": self._assess_reliability_trend(profile),
                "recommendations": self._generate_user_recommendations(profile)
            }
            
            return insights
            
        except Exception as e:
            self.logger.error(f"Failed to get user trust insights: {str(e)}")
            return {"error": str(e)}
    
    def classify_query_topic(self, query: str) -> str:
        """Classify query into topic for Bayesian updates"""
        # Simple keyword-based classification
        # In production, would use ML-based topic classification
        
        query_lower = query.lower()
        
        # Financial/business topics
        if any(word in query_lower for word in ['revenue', 'profit', 'sales', 'cost', 'price', 'financial']):
            return "finance"
        
        # Data analysis topics
        elif any(word in query_lower for word in ['average', 'mean', 'median', 'count', 'sum', 'analysis']):
            return "analytics"
        
        # Customer/marketing topics
        elif any(word in query_lower for word in ['customer', 'user', 'marketing', 'campaign', 'engagement']):
            return "marketing"
        
        # Operations topics
        elif any(word in query_lower for word in ['inventory', 'supply', 'production', 'operations', 'logistics']):
            return "operations"
        
        # HR/people topics
        elif any(word in query_lower for word in ['employee', 'staff', 'hr', 'human', 'team', 'performance']):
            return "hr"
        
        # Default
        else:
            return "general"
    
    def _classify_expertise_level(self, mean_trust: float) -> str:
        """Classify user expertise level based on trust score"""
        if mean_trust >= 0.8:
            return "Expert"
        elif mean_trust >= 0.65:
            return "Advanced"
        elif mean_trust >= 0.5:
            return "Intermediate"
        elif mean_trust >= 0.35:
            return "Beginner"
        else:
            return "Novice"
    
    def _assess_reliability_trend(self, profile: Dict[str, Any]) -> str:
        """Assess user reliability trend"""
        interactions = profile.get("total_interactions", 0)
        mean_trust = profile.get("mean_trust", 0.5)
        variance = profile.get("variance", 0.25)
        
        if interactions < 5:
            return "Insufficient data"
        elif variance < 0.05 and mean_trust > 0.7:
            return "Consistently reliable"
        elif variance < 0.1 and mean_trust > 0.6:
            return "Generally reliable"
        elif variance > 0.2:
            return "Inconsistent"
        else:
            return "Developing"
    
    def _generate_user_recommendations(self, profile: Dict[str, Any]) -> List[str]:
        """Generate personalized recommendations for user"""
        recommendations = []
        
        mean_trust = profile.get("mean_trust", 0.5)
        interactions = profile.get("total_interactions", 0)
        variance = profile.get("variance", 0.25)
        
        if interactions < 10:
            recommendations.append("Continue using the system to build your trust profile")
        
        if mean_trust < 0.5:
            recommendations.append("Consider reviewing AI responses more carefully")
            recommendations.append("Provide feedback to help improve personalization")
        
        if variance > 0.2:
            recommendations.append("Try to be consistent in your domain expertise")
        
        if mean_trust > 0.8:
            recommendations.append("Your expertise is well-recognized by the system")
            recommendations.append("Consider mentoring other users")
        
        return recommendations
    
    def _fallback_trust_score(self, error_msg: str) -> Dict[str, Any]:
        """Generate fallback trust score on computation failure"""
        return {
            "overall_score": 0.5,
            "factors": ["Computation error"],
            "explanation": f"Trust score computation failed: {error_msg}",
            "component_scores": {
                "model_confidence": 0.5,
                "qat_regressor": 0.5,
                "refusal_detector": 0.5,
                "citation_precision": 0.5
            },
            "base_score": 0.5,
            "bayesian_adjustment": 0.0,
            "user_posterior": 0.5,
            "topic_posterior": 0.5,
            "user_confidence": 0.0,
            "topic_confidence": 0.0,
            "user_interactions": 0,
            "topic_interactions": 0,
            "processing_time_ms": 0.0,
            "confidence_interval": None,
            "computed_at": datetime.utcnow().isoformat(),
            "version": "3.0",
            "error": error_msg
        }


# Global service instance
try:
    trust_integration_service = TrustIntegrationService()
except Exception as e:
    print(f"Warning: Failed to initialize trust integration service: {e}")
    trust_integration_service = None
