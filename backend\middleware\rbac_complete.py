"""
Complete Role-Based Access Control (RBAC) System for Phase 1
Implements Admin/User/Guest roles as required by development plan
"""

from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from functools import wraps
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

logger = logging.getLogger(__name__)

class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

class Permission(str, Enum):
    # Dataset permissions
    UPLOAD_DATASETS = "can_upload_datasets"
    VIEW_DATASETS = "can_view_datasets"
    DELETE_DATASETS = "can_delete_datasets"
    MANAGE_DATASETS = "can_manage_datasets"
    
    # Query permissions
    CREATE_QUERIES = "can_create_queries"
    VIEW_QUERIES = "can_view_queries"
    SAVE_QUERIES = "can_save_queries"
    DELETE_QUERIES = "can_delete_queries"
    
    # Connector permissions
    MANAGE_CONNECTORS = "can_manage_connectors"
    VIEW_CONNECTORS = "can_view_connectors"
    
    # Analytics permissions
    VIEW_ANALYTICS = "can_view_analytics"
    EXPORT_ANALYTICS = "can_export_analytics"
    
    # User management permissions
    MANAGE_USERS = "can_manage_users"
    VIEW_USERS = "can_view_users"
    INVITE_USERS = "can_invite_users"
    
    # System permissions
    MANAGE_TENANT = "can_manage_tenant"
    VIEW_SYSTEM_LOGS = "can_view_system_logs"
    MANAGE_BILLING = "can_manage_billing"

class RolePermissions:
    """Define permissions for each role"""
    
    ADMIN_PERMISSIONS = {
        # Full access to everything
        Permission.UPLOAD_DATASETS: True,
        Permission.VIEW_DATASETS: True,
        Permission.DELETE_DATASETS: True,
        Permission.MANAGE_DATASETS: True,
        Permission.CREATE_QUERIES: True,
        Permission.VIEW_QUERIES: True,
        Permission.SAVE_QUERIES: True,
        Permission.DELETE_QUERIES: True,
        Permission.MANAGE_CONNECTORS: True,
        Permission.VIEW_CONNECTORS: True,
        Permission.VIEW_ANALYTICS: True,
        Permission.EXPORT_ANALYTICS: True,
        Permission.MANAGE_USERS: True,
        Permission.VIEW_USERS: True,
        Permission.INVITE_USERS: True,
        Permission.MANAGE_TENANT: True,
        Permission.VIEW_SYSTEM_LOGS: True,
        Permission.MANAGE_BILLING: True,
    }
    
    USER_PERMISSIONS = {
        # Standard user access
        Permission.UPLOAD_DATASETS: True,
        Permission.VIEW_DATASETS: True,
        Permission.DELETE_DATASETS: True,  # Own datasets only
        Permission.MANAGE_DATASETS: True,  # Own datasets only
        Permission.CREATE_QUERIES: True,
        Permission.VIEW_QUERIES: True,     # Own queries only
        Permission.SAVE_QUERIES: True,
        Permission.DELETE_QUERIES: True,   # Own queries only
        Permission.MANAGE_CONNECTORS: False,
        Permission.VIEW_CONNECTORS: True,
        Permission.VIEW_ANALYTICS: False,
        Permission.EXPORT_ANALYTICS: False,
        Permission.MANAGE_USERS: False,
        Permission.VIEW_USERS: False,
        Permission.INVITE_USERS: False,
        Permission.MANAGE_TENANT: False,
        Permission.VIEW_SYSTEM_LOGS: False,
        Permission.MANAGE_BILLING: False,
    }
    
    GUEST_PERMISSIONS = {
        # Limited read-only access
        Permission.UPLOAD_DATASETS: False,
        Permission.VIEW_DATASETS: True,    # Public datasets only
        Permission.DELETE_DATASETS: False,
        Permission.MANAGE_DATASETS: False,
        Permission.CREATE_QUERIES: True,   # Limited queries
        Permission.VIEW_QUERIES: True,     # Own queries only
        Permission.SAVE_QUERIES: False,
        Permission.DELETE_QUERIES: False,
        Permission.MANAGE_CONNECTORS: False,
        Permission.VIEW_CONNECTORS: False,
        Permission.VIEW_ANALYTICS: False,
        Permission.EXPORT_ANALYTICS: False,
        Permission.MANAGE_USERS: False,
        Permission.VIEW_USERS: False,
        Permission.INVITE_USERS: False,
        Permission.MANAGE_TENANT: False,
        Permission.VIEW_SYSTEM_LOGS: False,
        Permission.MANAGE_BILLING: False,
    }
    
    @classmethod
    def get_role_permissions(cls, role: UserRole) -> Dict[Permission, bool]:
        """Get permissions for a specific role"""
        if role == UserRole.ADMIN:
            return cls.ADMIN_PERMISSIONS.copy()
        elif role == UserRole.USER:
            return cls.USER_PERMISSIONS.copy()
        elif role == UserRole.GUEST:
            return cls.GUEST_PERMISSIONS.copy()
        else:
            return {}

class RBACContext:
    """Context for role-based access control"""
    
    def __init__(self, user_id: str, tenant_id: str, role: UserRole, permissions: Dict[str, bool] = None):
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.role = role
        self.permissions = permissions or {}
        
        # Merge role-based permissions with custom permissions
        role_permissions = RolePermissions.get_role_permissions(role)
        self.effective_permissions = {**role_permissions, **self.permissions}
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if user has a specific permission"""
        return self.effective_permissions.get(permission, False)
    
    def require_permission(self, permission: Permission) -> bool:
        """Require a specific permission, raise exception if not granted"""
        if not self.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission.value}' required"
            )
        return True
    
    def can_access_resource(self, resource_owner_id: str, permission: Permission) -> bool:
        """Check if user can access a resource owned by another user"""
        # Admins can access everything
        if self.role == UserRole.ADMIN:
            return True
        
        # Users can access their own resources
        if self.user_id == resource_owner_id:
            return self.has_permission(permission)
        
        # Guests have very limited access
        if self.role == UserRole.GUEST:
            # Only allow viewing public resources
            return permission in [Permission.VIEW_DATASETS, Permission.VIEW_QUERIES]
        
        return False

class RBACManager:
    """Role-Based Access Control Manager"""
    
    def __init__(self):
        self.security = HTTPBearer()
    
    def create_context(self, user_id: str, tenant_id: str, role: str, permissions: Dict[str, bool] = None) -> RBACContext:
        """Create RBAC context for a user"""
        try:
            user_role = UserRole(role)
        except ValueError:
            user_role = UserRole.GUEST  # Default to guest for invalid roles
        
        return RBACContext(user_id, tenant_id, user_role, permissions)
    
    def require_role(self, required_role: UserRole):
        """Decorator to require a specific role"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Extract RBAC context from kwargs or dependency injection
                rbac_context = kwargs.get('rbac_context')
                if not rbac_context:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Authentication required"
                    )
                
                # Check role hierarchy
                role_hierarchy = {
                    UserRole.GUEST: 1,
                    UserRole.USER: 2,
                    UserRole.ADMIN: 3
                }
                
                user_level = role_hierarchy.get(rbac_context.role, 0)
                required_level = role_hierarchy.get(required_role, 3)
                
                if user_level < required_level:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Role '{required_role.value}' or higher required"
                    )
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def require_permission(self, required_permission: Permission):
        """Decorator to require a specific permission"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                rbac_context = kwargs.get('rbac_context')
                if not rbac_context:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Authentication required"
                    )
                
                rbac_context.require_permission(required_permission)
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def require_resource_access(self, permission: Permission, resource_owner_key: str = 'resource_owner_id'):
        """Decorator to require access to a specific resource"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                rbac_context = kwargs.get('rbac_context')
                if not rbac_context:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Authentication required"
                    )
                
                resource_owner_id = kwargs.get(resource_owner_key)
                if not resource_owner_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Resource owner ID required"
                    )
                
                if not rbac_context.can_access_resource(resource_owner_id, permission):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access to this resource denied"
                    )
                
                return await func(*args, **kwargs)
            return wrapper
        return decorator

# Global RBAC manager instance
rbac_manager = RBACManager()

# Convenience decorators
require_admin = rbac_manager.require_role(UserRole.ADMIN)
require_user = rbac_manager.require_role(UserRole.USER)
require_guest = rbac_manager.require_role(UserRole.GUEST)

# Permission decorators
require_upload_datasets = rbac_manager.require_permission(Permission.UPLOAD_DATASETS)
require_manage_users = rbac_manager.require_permission(Permission.MANAGE_USERS)
require_view_analytics = rbac_manager.require_permission(Permission.VIEW_ANALYTICS)
require_manage_connectors = rbac_manager.require_permission(Permission.MANAGE_CONNECTORS)

# Resource access decorators
require_dataset_access = rbac_manager.require_resource_access(Permission.VIEW_DATASETS)
require_query_access = rbac_manager.require_resource_access(Permission.VIEW_QUERIES)

class GuestLimitations:
    """Specific limitations for guest users"""
    
    MAX_QUERIES_PER_DAY = 10
    MAX_QUERY_LENGTH = 500
    ALLOWED_QUERY_TYPES = ["general", "summary"]
    
    @staticmethod
    def check_guest_query_limits(rbac_context: RBACContext, query_text: str, query_type: str) -> bool:
        """Check if guest user can perform this query"""
        if rbac_context.role != UserRole.GUEST:
            return True  # Not a guest, no limitations
        
        # Check query length
        if len(query_text) > GuestLimitations.MAX_QUERY_LENGTH:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Guest users limited to {GuestLimitations.MAX_QUERY_LENGTH} characters per query"
            )
        
        # Check query type
        if query_type not in GuestLimitations.ALLOWED_QUERY_TYPES:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Guest users can only perform: {', '.join(GuestLimitations.ALLOWED_QUERY_TYPES)} queries"
            )
        
        return True

# FastAPI Dependencies
def get_rbac_context(
    user_id: str,
    tenant_id: str,
    role: str,
    permissions: Dict[str, bool] = None
) -> RBACContext:
    """FastAPI dependency to get RBAC context"""
    return rbac_manager.create_context(user_id, tenant_id, role, permissions)

def require_authenticated_user(rbac_context: RBACContext = Depends(get_rbac_context)) -> RBACContext:
    """FastAPI dependency to require authenticated user"""
    if not rbac_context:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return rbac_context

def require_admin_user(rbac_context: RBACContext = Depends(require_authenticated_user)) -> RBACContext:
    """FastAPI dependency to require admin user"""
    if rbac_context.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return rbac_context

def require_user_or_admin(rbac_context: RBACContext = Depends(require_authenticated_user)) -> RBACContext:
    """FastAPI dependency to require user or admin role"""
    if rbac_context.role not in [UserRole.USER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User or admin access required"
        )
    return rbac_context

# Utility functions
def get_user_permissions_summary(role: UserRole, custom_permissions: Dict[str, bool] = None) -> Dict[str, Any]:
    """Get a summary of user permissions for display"""
    base_permissions = RolePermissions.get_role_permissions(role)
    effective_permissions = {**base_permissions, **(custom_permissions or {})}
    
    return {
        "role": role.value,
        "permissions": {perm.value: granted for perm, granted in effective_permissions.items()},
        "limitations": {
            "max_queries_per_day": GuestLimitations.MAX_QUERIES_PER_DAY if role == UserRole.GUEST else None,
            "max_query_length": GuestLimitations.MAX_QUERY_LENGTH if role == UserRole.GUEST else None,
            "allowed_query_types": GuestLimitations.ALLOWED_QUERY_TYPES if role == UserRole.GUEST else None
        }
    }

def validate_role_transition(current_role: UserRole, new_role: UserRole, requester_role: UserRole) -> bool:
    """Validate if a role transition is allowed"""
    # Only admins can change roles
    if requester_role != UserRole.ADMIN:
        return False
    
    # Admins can change any role
    return True
