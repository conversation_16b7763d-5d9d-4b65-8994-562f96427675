import threading
import uuid
import time
from typing import Dict, Any, Callable, List, Optional
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class Job:
    """
    Represents a background job
    """
    def __init__(self, job_id: str, job_type: str, params: Dict[str, Any]):
        self.id = job_id
        self.type = job_type
        self.params = params
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0  # 0-100
        self.result = None
        self.error = None
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.status_message = "Job created"
        self.stages = []
        self.current_stage = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert job to dictionary for API responses"""
        return {
            "id": self.id,
            "type": self.type,
            "status": self.status,
            "progress": self.progress,
            "result": self.result,
            "error": self.error,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "status_message": self.status_message,
            "stages": self.stages,
            "current_stage": self.current_stage
        }

    def update_progress(self, progress: int, status_message: str = None):
        """Update job progress"""
        self.progress = min(max(progress, 0), 100)  # Ensure progress is between 0-100
        if status_message:
            self.status_message = status_message
        logger.info(f"Job {self.id} progress: {self.progress}% - {self.status_message}")

    def set_stage(self, stage: str, progress: int = None):
        """Set the current processing stage"""
        self.current_stage = stage
        if stage not in self.stages:
            self.stages.append(stage)
        if progress is not None:
            self.update_progress(progress)
        logger.info(f"Job {self.id} stage: {stage}")

    def start(self):
        """Mark job as started"""
        self.status = "running"
        self.started_at = datetime.now()
        self.update_progress(0, "Job started")

    def complete(self, result: Any = None):
        """Mark job as completed"""
        self.status = "completed"
        self.completed_at = datetime.now()
        self.result = result
        self.update_progress(100, "Job completed")

    def fail(self, error: str):
        """Mark job as failed"""
        self.status = "failed"
        self.completed_at = datetime.now()
        self.error = error
        self.update_progress(0, f"Job failed: {error}")


class JobQueue:
    """
    Simple in-memory job queue
    """
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(JobQueue, cls).__new__(cls)
            cls._instance.jobs = {}
            cls._instance.job_handlers = {}
            cls._instance.cleanup_thread = None
            cls._instance.cleanup_interval = 3600  # 1 hour
            cls._instance.job_retention = 24  # 24 hours
            cls._instance._start_cleanup_thread()
        return cls._instance

    def _start_cleanup_thread(self):
        """Start a thread to clean up old jobs"""
        def cleanup_worker():
            while True:
                try:
                    self._cleanup_old_jobs()
                except Exception as e:
                    logger.error(f"Error in job cleanup: {str(e)}")
                time.sleep(self.cleanup_interval)

        self.cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self.cleanup_thread.start()

    def _cleanup_old_jobs(self):
        """Remove old completed jobs"""
        now = datetime.now()
        retention_threshold = now - timedelta(hours=self.job_retention)

        jobs_to_remove = []
        for job_id, job in self.jobs.items():
            if job.status in ["completed", "failed"] and job.completed_at and job.completed_at < retention_threshold:
                jobs_to_remove.append(job_id)

        for job_id in jobs_to_remove:
            logger.info(f"Cleaning up old job: {job_id}")
            del self.jobs[job_id]

    def register_handler(self, job_type: str, handler: Callable[[Job], None]):
        """Register a handler for a job type"""
        self.job_handlers[job_type] = handler
        logger.info(f"Registered handler for job type: {job_type}")

    def create_job(self, job_type: str, params: Dict[str, Any]) -> str:
        """Create a new job and return its ID"""
        if job_type not in self.job_handlers:
            raise ValueError(f"No handler registered for job type: {job_type}")

        job_id = str(uuid.uuid4())
        job = Job(job_id, job_type, params)
        self.jobs[job_id] = job

        # Start job in a background thread
        thread = threading.Thread(target=self._run_job, args=(job,), daemon=True)
        thread.start()

        logger.info(f"Created job {job_id} of type {job_type}")
        return job_id

    def _run_job(self, job: Job):
        """Run a job in the background"""
        try:
            # Mark job as started
            job.start()
            logger.info(f"Starting job {job.id} of type {job.type}")

            # Get the handler
            handler = self.job_handlers[job.type]

            # Run the handler with error handling
            try:
                handler(job)

                # If job is still running after handler returns, mark it as completed
                if job.status == "running":
                    logger.warning(f"Job {job.id} handler returned without completing the job. Marking as completed.")
                    job.complete()

            except Exception as e:
                logger.error(f"Error running job {job.id}: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                job.fail(str(e))

        except Exception as outer_e:
            # Handle errors in the job starting process
            logger.error(f"Critical error in job {job.id} execution framework: {str(outer_e)}")
            import traceback
            logger.error(traceback.format_exc())

            # Try to mark the job as failed
            try:
                job.fail(f"Critical error: {str(outer_e)}")
            except:
                logger.error(f"Could not mark job {job.id} as failed")

        finally:
            # Log job completion
            logger.info(f"Job {job.id} finished with status: {job.status}")

    def get_job(self, job_id: str) -> Optional[Job]:
        """Get a job by ID"""
        return self.jobs.get(job_id)

    def get_jobs(self, status: str = None) -> List[Job]:
        """Get all jobs, optionally filtered by status"""
        if status:
            return [job for job in self.jobs.values() if job.status == status]
        return list(self.jobs.values())

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or running job"""
        job = self.get_job(job_id)
        if not job:
            return False

        if job.status in ["pending", "running"]:
            job.status = "failed"
            job.error = "Job cancelled by user"
            job.completed_at = datetime.now()
            job.update_progress(0, "Job cancelled")
            return True

        return False


# Create a global job queue instance
job_queue = JobQueue()
