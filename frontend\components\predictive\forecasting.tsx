'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { createApiInstance, getUserId } from '@/lib/api';
import JobProgress from './JobProgress';

// Import Plotly dynamically to avoid SSR issues
let Plotly: any;
if (typeof window !== 'undefined') {
  import('plotly.js-dist-min').then((module: any) => {
    Plotly = module.default || module;
  }).catch(err => {
    console.error('Failed to load Plotly:', err);
  });
}

interface ForecastingProps {
  datasetId: number;
}

export default function Forecasting({ datasetId }: ForecastingProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [columns, setColumns] = useState<string[]>([]);
  const [dateColumns, setDateColumns] = useState<string[]>([]);
  const [targetColumn, setTargetColumn] = useState<string>('');
  const [dateColumn, setDateColumn] = useState<string>('');
  const [horizon, setHorizon] = useState<number>(10);
  const [frequency, setFrequency] = useState<string>('');
  const [method, setMethod] = useState<string>('auto');
  const [confidenceInterval, setConfidenceInterval] = useState<number>(0.95);
  const [forecastResults, setForecastResults] = useState<any>(null);

  // Background job state
  const [jobId, setJobId] = useState<string | null>(null);
  const [useBackground, setUseBackground] = useState<boolean>(false);

  const chartRef = useRef<HTMLDivElement>(null);

  // Fetch dataset columns
  useEffect(() => {
    if (!datasetId || !session) return;

    const fetchDatasetColumns = async () => {
      try {
        const userId = getUserId(session);
        const api = createApiInstance(session);
        const response = await api.get(`/datasets/${userId}`);
        const datasets = response.data;
        const currentDataset = datasets.find((d: any) => d.id === datasetId);

        if (currentDataset) {
          setColumns(currentDataset.columns);

          // Try to identify date columns
          const possibleDateColumns = currentDataset.columns.filter((col: string) => {
            return col.toLowerCase().includes('date') ||
                   col.toLowerCase().includes('time') ||
                   col.toLowerCase().includes('day') ||
                   col.toLowerCase().includes('month') ||
                   col.toLowerCase().includes('year');
          });

          setDateColumns(possibleDateColumns);

          // Set default columns if available
          if (possibleDateColumns.length > 0) {
            setDateColumn(possibleDateColumns[0]);
          }

          // Try to find a numeric column for target
          const numericColumns = currentDataset.columns.filter((col: string) => {
            return !possibleDateColumns.includes(col);
          });

          if (numericColumns.length > 0) {
            setTargetColumn(numericColumns[0]);
          }
        }
      } catch (err: any) {
        console.error('Error fetching dataset columns:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching dataset columns');
      }
    };

    fetchDatasetColumns();
  }, [datasetId, session]);

  // Generate forecast
  const generateForecast = async () => {
    if (!datasetId || !targetColumn || !dateColumn) {
      setError('Please select both target and date columns');
      return;
    }

    if (!session) {
      setError('Please log in to generate forecasts');
      return;
    }

    setLoading(true);
    setError(null);
    setJobId(null);

    // Show immediate feedback
    toast.info('Starting forecast generation. This may take a moment...', {
      autoClose: 2000
    });

    // Check if we have a cached result (only for non-background jobs)
    if (!useBackground) {
      const cacheKey = `forecast_${datasetId}_${targetColumn}_${dateColumn}_${horizon}_${method}`;
      const cachedResult = localStorage.getItem(cacheKey);

      if (cachedResult) {
        try {
          const forecast = JSON.parse(cachedResult);
          setForecastResults(forecast);
          toast.success('Loaded forecast from cache');
          setLoading(false);
          return;
        } catch (e) {
          console.warn('Error parsing cached forecast:', e);
          // Continue with API request if cache parsing fails
        }
      }
    }

    try {
      // Create an AbortController to handle timeouts manually
      const controller = new AbortController();

      // Much longer timeout - 2 minutes
      const timeoutId = setTimeout(() => controller.abort(), 120000);

      // Show a message about the request being sent
      toast.info('Sending forecast request to server...', {
        autoClose: 3000
      });

      // Set a longer timeout for all requests
      // Background processing requests need more time to initialize the job
      const timeoutMs = 60000; // 60 seconds for all requests

      console.log(`Sending forecast request with background=${useBackground}`);

      const api = createApiInstance(session);
      const response = await api.get(`/predictive/time-series-forecast/${datasetId}`, {
        params: {
          forecast_days: horizon,
          target_column: targetColumn,
          date_column: dateColumn,
          frequency: frequency || undefined,
          method: method,
          confidence_interval: confidenceInterval
        },
        timeout: timeoutMs,
        signal: controller.signal
      });

      // For backward compatibility, also try the old POST endpoint if GET fails
      /* const response = await api.post(`/predictive/forecast/${datasetId}`, {
        target_column: targetColumn,
        date_column: dateColumn,
        horizon: horizon,
        frequency: frequency || undefined,
        method: method,
        confidence_interval: confidenceInterval,
        use_background: useBackground
      }, {
        timeout: timeoutMs,
        signal: controller.signal
      }); */

      clearTimeout(timeoutId);

      // The new endpoint returns forecast data directly
      const forecastData = {
        forecast_points: response.data.forecast_points || [],
        confidence_intervals: response.data.confidence_intervals || {},
        model_metrics: response.data.model_metrics || {},
        trend_analysis: response.data.trend_analysis || {},
        forecast_dates: response.data.forecast_points?.map((p: any) => p.date) || [],
        forecast_values: response.data.forecast_points?.map((p: any) => p.predicted_value) || [],
        historical_dates: [], // Would need to be fetched separately
        historical_values: [], // Would need to be fetched separately
        method: 'time_series'
      };

      // Cache the result
      try {
        const cacheKey = `forecast_${datasetId}_${targetColumn}_${dateColumn}_${horizon}_${method}`;
        localStorage.setItem(cacheKey, JSON.stringify(forecastData));
      } catch (e) {
        console.warn('Error caching forecast:', e);
      }

      setForecastResults(forecastData);
      toast.success('Forecast generated successfully!');

      // Create forecast chart
      setTimeout(() => {
        if (chartRef.current && Plotly && forecastData) {
          const forecast = forecastData;

          // Create base data array with historical and forecast data
          const data: any[] = [
            {
              x: forecast.historical_dates,
              y: forecast.historical_values,
              type: 'scatter',
              mode: 'lines',
              name: 'Historical',
              line: { color: 'blue', width: 2 }
            },
            {
              x: forecast.forecast_dates,
              y: forecast.forecast_values,
              type: 'scatter',
              mode: 'lines',
              name: 'Forecast',
              line: { color: 'red', width: 2, dash: 'dash' }
            }
          ];

          // Add confidence intervals if available
          if (forecast.confidence_intervals?.lower && forecast.confidence_intervals?.upper) {
            data.push({
              x: forecast.forecast_dates,
              y: forecast.confidence_intervals.lower,
              type: 'scatter',
              mode: 'lines',
              name: 'Lower Bound',
              line: { color: 'transparent' },
              showlegend: false
            } as any);

            data.push({
              x: forecast.forecast_dates,
              y: forecast.confidence_intervals.upper,
              type: 'scatter',
              mode: 'lines',
              name: 'Upper Bound',
              line: { color: 'transparent' },
              fill: 'tonexty',
              fillcolor: 'rgba(255, 0, 0, 0.2)',
              showlegend: false
            } as any);
          }

          // Add trend line if available
          if (forecast.trend_analysis?.trend_values) {
            // Add historical trend
            if (forecast.trend_analysis.historical_trend_values) {
              data.push({
                x: forecast.historical_dates,
                y: forecast.trend_analysis.historical_trend_values,
                type: 'scatter',
                mode: 'lines',
                name: 'Historical Trend',
                line: { color: 'green', width: 1.5, dash: 'dot' },
                opacity: 0.7
              });
            }

            // Add forecast trend
            data.push({
              x: forecast.forecast_dates,
              y: forecast.trend_analysis.trend_values,
              type: 'scatter',
              mode: 'lines',
              name: 'Forecast Trend',
              line: { color: 'green', width: 1.5, dash: 'dot' },
              opacity: 0.7
            });
          }

          // Add seasonality component if available
          if (forecast.trend_analysis?.seasonality_values) {
            // Create a separate subplot for seasonality
            const seasonalityData = {
              x: [...forecast.historical_dates, ...forecast.forecast_dates],
              y: forecast.trend_analysis.seasonality_values,
              type: 'scatter',
              mode: 'lines',
              name: 'Seasonality',
              line: { color: 'purple', width: 1.5 },
              yaxis: 'y2'
            };

            data.push(seasonalityData);
          }

          // Create layout with method-specific styling
          let methodColor = 'rgba(255, 0, 0, 0.2)';
          let methodTitle = 'Simple Forecast';

          if (forecast.method === 'arima') {
            methodColor = 'rgba(0, 128, 255, 0.2)';
            methodTitle = 'ARIMA Forecast';
          } else if (forecast.method === 'exponential_smoothing') {
            methodColor = 'rgba(255, 165, 0, 0.2)';
            methodTitle = 'Exponential Smoothing Forecast';
          } else if (forecast.method === 'prophet') {
            methodColor = 'rgba(128, 0, 128, 0.2)';
            methodTitle = 'Prophet Forecast';
          }

          // Update confidence interval color
          if (data.length >= 4) {
            data[3].fillcolor = methodColor;
          }

          // Create layout with potential second y-axis for seasonality
          const layout: any = {
            title: `${methodTitle} for ${targetColumn}`,
            xaxis: {
              title: dateColumn,
              rangeslider: { visible: true },
              rangeselector: {
                buttons: [
                  {
                    count: 1,
                    label: '1m',
                    step: 'month',
                    stepmode: 'backward'
                  },
                  {
                    count: 6,
                    label: '6m',
                    step: 'month',
                    stepmode: 'backward'
                  },
                  {
                    count: 1,
                    label: '1y',
                    step: 'year',
                    stepmode: 'backward'
                  },
                  { step: 'all' }
                ]
              }
            },
            yaxis: {
              title: targetColumn,
              zeroline: true,
              zerolinecolor: '#969696',
              gridcolor: '#D3D3D3'
            },
            yaxis2: {
              title: 'Seasonality',
              titlefont: { color: 'purple' },
              tickfont: { color: 'purple' },
              overlaying: 'y',
              side: 'right',
              showgrid: false
            },
            hovermode: 'closest',
            legend: { orientation: 'h', y: -0.2 },
            margin: { l: 60, r: 60, t: 50, b: 50 },
            plot_bgcolor: '#f8f9fa',
            paper_bgcolor: '#f8f9fa',
            annotations: []
          };

          // Add accuracy metrics as annotations if available
          if (forecast.model_metrics) {
            const metrics = forecast.model_metrics;
            if (metrics.mape !== null && metrics.mape !== undefined) {
              layout.annotations.push({
                x: 0.02,
                y: 0.98,
                xref: 'paper',
                yref: 'paper',
                text: `MAPE: ${formatNumber(metrics.mape)}%`,
                showarrow: false,
                font: { size: 12 },
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: '#d3d3d3',
                borderwidth: 1,
                borderpad: 4
              });
            }
          }

          Plotly.newPlot(chartRef.current, data, layout);
        }
      }, 100);
    } catch (err: any) {
      console.error('Error generating forecast:', err);

      // Log detailed error information
      console.log('Error details:', {
        name: err.name,
        code: err.code,
        message: err.message,
        hasResponse: !!err.response,
        hasRequest: !!err.request,
        responseStatus: err.response?.status,
        responseData: err.response?.data
      });

      if (err.name === 'AbortError' || err.code === 'ECONNABORTED') {
        // Timeout error - suggest using background processing
        const errorMsg = 'The forecast request timed out. Please try using background processing for this dataset.';
        setError(errorMsg);
        toast.error('Request timed out. Enable "Use background processing for large datasets" and try again.');

        // Automatically enable background processing for next attempt
        setUseBackground(true);
        toast.info('Background processing has been enabled for your next attempt', {
          autoClose: 5000
        });
      } else if (err.response) {
        // Server returned an error response
        const serverMsg = err.response.data?.detail || 'Server error';
        setError(`Error: ${serverMsg}`);
        toast.error(`Server error: ${serverMsg}`);

        // If server suggests using background processing
        if (serverMsg.includes('large') || serverMsg.includes('timeout')) {
          setUseBackground(true);
          toast.info('Background processing has been enabled for your next attempt', {
            autoClose: 5000
          });
        }
      } else if (err.request) {
        // No response received
        setError('No response received from server. Please try using background processing instead.');
        toast.error('Connection issue. Enable "Use background processing for large datasets" and try again.');

        // Automatically enable background processing for next attempt
        setUseBackground(true);
        toast.info('Background processing has been enabled for your next attempt', {
          autoClose: 5000
        });
      } else {
        // Other errors
        setError(`Error generating forecast: ${err.message}`);
        toast.error(`Error: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Format number for display
  const formatNumber = (num: number) => {
    return Number.isInteger(num) ? num : num.toFixed(4);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <ToastContainer position="top-right" autoClose={5000} />
      <h2 className="text-xl font-semibold text-black mb-4">Time Series Forecasting</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Target Column (Value to Forecast)
          </label>
          <select
            value={targetColumn}
            onChange={(e) => setTargetColumn(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Select a column</option>
            {columns.filter(col => !dateColumns.includes(col)).map(column => (
              <option key={column} value={column}>{column}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date Column
          </label>
          <select
            value={dateColumn}
            onChange={(e) => setDateColumn(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Select a column</option>
            {columns.map(column => (
              <option key={column} value={column}>{column}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Forecast Horizon (Periods)
          </label>
          <input
            type="number"
            value={horizon}
            onChange={(e) => setHorizon(parseInt(e.target.value))}
            min={1}
            max={100}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Frequency (Optional)
          </label>
          <select
            value={frequency}
            onChange={(e) => setFrequency(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">Auto-detect</option>
            <option value="D">Daily</option>
            <option value="W">Weekly</option>
            <option value="M">Monthly</option>
            <option value="Q">Quarterly</option>
            <option value="Y">Yearly</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Forecasting Method
          </label>
          <select
            value={method}
            onChange={(e) => setMethod(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="auto">Auto-select</option>
            <option value="arima">ARIMA</option>
            <option value="exponential_smoothing">Exponential Smoothing</option>
            <option value="prophet">Prophet</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Confidence Interval (0-1)
          </label>
          <input
            type="number"
            value={confidenceInterval}
            onChange={(e) => setConfidenceInterval(parseFloat(e.target.value))}
            min={0.5}
            max={0.99}
            step={0.01}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex items-center">
          <button
            onClick={generateForecast}
            disabled={loading}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              loading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? 'Generating...' : 'Generate Forecast'}
          </button>

          <div className="ml-4 flex items-center">
            <input
              type="checkbox"
              id="useBackground"
              checked={useBackground}
              onChange={(e) => setUseBackground(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="useBackground" className="ml-2 block text-sm text-gray-700">
              Use background processing for large datasets
            </label>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Job Progress */}
      {jobId && (
        <JobProgress
          jobId={jobId}
          onComplete={(result) => {
            setJobId(null);
            setLoading(false);
            if (result && result.forecast) {
              setForecastResults(result.forecast);

              // Cache the result
              try {
                const cacheKey = `forecast_${datasetId}_${targetColumn}_${dateColumn}_${horizon}_${method}`;
                localStorage.setItem(cacheKey, JSON.stringify(result.forecast));
              } catch (e) {
                console.warn('Error caching forecast:', e);
              }

              // Create chart
              setTimeout(() => {
                if (chartRef.current && Plotly && result.forecast) {
                  const forecast = result.forecast;

                  // Create chart data and render it
                  // This is a simplified version - you would need to copy the chart creation code here
                  // or refactor it into a separate function to avoid duplication
                  const data: any[] = [
                    {
                      x: forecast.historical_dates,
                      y: forecast.historical_values,
                      type: 'scatter',
                      mode: 'lines',
                      name: 'Historical',
                      line: { color: 'blue', width: 2 }
                    },
                    {
                      x: forecast.forecast_dates,
                      y: forecast.forecast_values,
                      type: 'scatter',
                      mode: 'lines',
                      name: 'Forecast',
                      line: { color: 'red', width: 2, dash: 'dash' }
                    }
                  ];

                  if (forecast.confidence_intervals?.lower && forecast.confidence_intervals?.upper) {
                    data.push({
                      x: forecast.forecast_dates,
                      y: forecast.confidence_intervals.lower,
                      type: 'scatter',
                      mode: 'lines',
                      name: 'Lower Bound',
                      line: { color: 'transparent' },
                      showlegend: false
                    } as any);

                    data.push({
                      x: forecast.forecast_dates,
                      y: forecast.confidence_intervals.upper,
                      type: 'scatter',
                      mode: 'lines',
                      name: 'Upper Bound',
                      line: { color: 'transparent' },
                      fill: 'tonexty',
                      fillcolor: 'rgba(255, 0, 0, 0.2)',
                      showlegend: false
                    } as any);
                  }

                  const layout: any = {
                    title: `Forecast for ${targetColumn}`,
                    xaxis: { title: dateColumn },
                    yaxis: { title: targetColumn },
                    hovermode: 'closest',
                    legend: { orientation: 'h', y: -0.2 }
                  };

                  Plotly.newPlot(chartRef.current, data, layout);
                }
              }, 100);
            }
          }}
          onError={(errorMsg) => {
            setJobId(null);
            setLoading(false);
            setError(errorMsg);
          }}
        />
      )}

      {/* Forecast Results */}
      {forecastResults && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">Forecast Results ({forecastResults.method})</h3>

          {/* Chart */}
          <div ref={chartRef} className="w-full h-[400px] mb-4" />

          {/* Forecast Metrics */}
          {forecastResults.model_metrics && (
            <div className="bg-gray-50 p-4 rounded-md mb-4">
              <h4 className="text-sm font-medium text-black mb-2">Forecast Accuracy Metrics</h4>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-xs text-gray-500">Mean Absolute Error</p>
                  <p className="text-sm font-medium">
                    {forecastResults.model_metrics.mae !== null && forecastResults.model_metrics.mae !== undefined
                      ? formatNumber(forecastResults.model_metrics.mae)
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Root Mean Squared Error</p>
                  <p className="text-sm font-medium">
                    {forecastResults.model_metrics.rmse !== null && forecastResults.model_metrics.rmse !== undefined
                      ? formatNumber(forecastResults.model_metrics.rmse)
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Mean Absolute Percentage Error</p>
                  <p className="text-sm font-medium">
                    {forecastResults.model_metrics.mape !== null && forecastResults.model_metrics.mape !== undefined
                      ? formatNumber(forecastResults.model_metrics.mape) + '%'
                      : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Forecast Data Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Forecast
                  </th>
                  {forecastResults.confidence_intervals?.lower && (
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lower Bound
                    </th>
                  )}
                  {forecastResults.confidence_intervals?.upper && (
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Upper Bound
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {forecastResults.forecast_dates.map((date: string, index: number) => (
                  <tr key={date}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatNumber(forecastResults.forecast_values[index])}
                    </td>
                    {forecastResults.confidence_intervals?.lower && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatNumber(forecastResults.confidence_intervals.lower[index])}
                      </td>
                    )}
                    {forecastResults.confidence_intervals?.upper && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatNumber(forecastResults.confidence_intervals.upper[index])}
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
