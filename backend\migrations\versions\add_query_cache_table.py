"""Add query cache table for persistent caching

Revision ID: add_query_cache_table
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_query_cache_table'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create query_cache table
    op.create_table(
        'query_cache',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('cache_key', sa.String(), nullable=False),
        sa.Column('dataset_id', sa.Integer(), nullable=False),
        sa.Column('question', sa.Text(), nullable=False),
        sa.Column('question_hash', sa.String(), nullable=False),
        sa.Column('dataset_hash', sa.String(), nullable=False),
        sa.Column('answer', sa.Text(), nullable=False),
        sa.Column('chart_type', sa.String(), nullable=True),
        sa.Column('chart_data', sa.Text(), nullable=True),
        sa.Column('trust_score', sa.Text(), nullable=True),
        sa.Column('reasoning_steps', sa.Text(), nullable=True),
        sa.Column('formula_results', sa.Text(), nullable=True),
        sa.Column('include_cot', sa.Boolean(), nullable=False, default=False),
        sa.Column('hit_count', sa.Integer(), nullable=False, default=1),
        sa.Column('last_accessed', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['dataset_id'], ['datasets.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for better performance
    op.create_index(op.f('ix_query_cache_id'), 'query_cache', ['id'], unique=False)
    op.create_index(op.f('ix_query_cache_cache_key'), 'query_cache', ['cache_key'], unique=True)
    op.create_index(op.f('ix_query_cache_question_hash'), 'query_cache', ['question_hash'], unique=False)
    op.create_index(op.f('ix_query_cache_dataset_hash'), 'query_cache', ['dataset_hash'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_query_cache_dataset_hash'), table_name='query_cache')
    op.drop_index(op.f('ix_query_cache_question_hash'), table_name='query_cache')
    op.drop_index(op.f('ix_query_cache_cache_key'), table_name='query_cache')
    op.drop_index(op.f('ix_query_cache_id'), table_name='query_cache')
    
    # Drop table
    op.drop_table('query_cache')
