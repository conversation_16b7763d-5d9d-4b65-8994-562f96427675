"use client";

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  RefreshCw, 
  Plus, 
  Settings, 
  Trash2, 
  GitBranch, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  ExternalLink,
  Sync,
  Play,
  Pause
} from 'lucide-react';
import { createApiInstance } from '@/lib/api';

interface GitHubConnector {
  id: string;
  name: string;
  description?: string;
  github_username: string;
  status: 'active' | 'error' | 'disabled';
  is_active: boolean;
  auto_sync: boolean;
  total_repositories: number;
  total_files_synced: number;
  last_sync_at?: string;
  created_at: string;
}

interface Repository {
  id: string;
  github_id: number;
  name: string;
  full_name: string;
  description?: string;
  private: boolean;
  language?: string;
  topics: string[];
  size: number;
  stargazers_count: number;
  forks_count: number;
  is_enabled: boolean;
  sync_status: 'pending' | 'syncing' | 'completed' | 'error';
  files_count: number;
  last_sync_at?: string;
  github_updated_at?: string;
}

export default function GitHubConnectorManagement() {
  const { data: session } = useSession();
  const [connectors, setConnectors] = useState<GitHubConnector[]>([]);
  const [selectedConnector, setSelectedConnector] = useState<GitHubConnector | null>(null);
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState<{ [key: string]: boolean }>({});
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session) {
      loadConnectors();
    }
  }, [session]);

  const loadConnectors = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.get('/connectors/github/connectors');
      setConnectors(response.data.connectors);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GitHub connectors');
    } finally {
      setLoading(false);
    }
  };

  const loadRepositories = async (connectorId: string) => {
    if (!session) return;

    try {
      const api = createApiInstance(session);
      const response = await api.get(`/connectors/github/connectors/${connectorId}/repositories`);
      setRepositories(response.data.repositories);
    } catch (err) {
      console.error('Failed to load repositories:', err);
    }
  };

  const handleSyncConnector = async (connectorId: string, repositoryIds?: number[]) => {
    if (!session) return;

    setSyncing(prev => ({ ...prev, [connectorId]: true }));

    try {
      const api = createApiInstance(session);
      
      // If no specific repositories, sync all repositories for the connector
      const connector = connectors.find(c => c.id === connectorId);
      if (!connector) return;

      // Get all repository IDs if none specified
      if (!repositoryIds) {
        await loadRepositories(connectorId);
        repositoryIds = repositories.map(repo => repo.github_id);
      }

      const response = await api.post(`/connectors/github/connectors/${connectorId}/sync`, {
        repository_ids: repositoryIds,
        force_full_sync: false
      });

      if (response.data.success) {
        // Show success message
        alert(`✅ Sync started for ${response.data.repositories_scheduled} repositories`);
        
        // Refresh connector data
        await loadConnectors();
        if (selectedConnector?.id === connectorId) {
          await loadRepositories(connectorId);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Sync failed';
      alert(`❌ Sync failed: ${errorMessage}`);
    } finally {
      setSyncing(prev => ({ ...prev, [connectorId]: false }));
    }
  };

  const handleSyncRepository = async (connectorId: string, repositoryId: number) => {
    await handleSyncConnector(connectorId, [repositoryId]);
  };

  const toggleConnectorStatus = async (connectorId: string, isActive: boolean) => {
    if (!session) return;

    try {
      const api = createApiInstance(session);
      await api.put(`/connectors/github/connectors/${connectorId}`, {
        is_active: !isActive
      });

      // Refresh connectors
      await loadConnectors();
    } catch (err) {
      alert(`Failed to update connector: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'disabled':
        return <Pause className="h-5 w-5 text-gray-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'syncing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <GitBranch className="h-8 w-8 text-gray-700" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">GitHub Connectors</h2>
            <p className="text-sm text-gray-600">Manage GitHub repository integrations and sync data</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadConnectors}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="h-4 w-4" />
            <span>Add Connector</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <XCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Connectors List */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow border">
            <div className="p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">Connectors ({connectors.length})</h3>
            </div>
            <div className="divide-y">
              {connectors.map((connector) => (
                <div
                  key={connector.id}
                  className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                    selectedConnector?.id === connector.id ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                  }`}
                  onClick={() => {
                    setSelectedConnector(connector);
                    loadRepositories(connector.id);
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(connector.status)}
                      <div>
                        <h4 className="font-medium text-gray-900">{connector.name}</h4>
                        <p className="text-sm text-gray-600">@{connector.github_username}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* Sync Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSyncConnector(connector.id);
                        }}
                        disabled={syncing[connector.id]}
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors disabled:opacity-50"
                        title="Sync Now"
                      >
                        {syncing[connector.id] ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <Sync className="h-4 w-4" />
                        )}
                      </button>
                      
                      {/* Toggle Status */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleConnectorStatus(connector.id, connector.is_active);
                        }}
                        className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                        title={connector.is_active ? "Disable" : "Enable"}
                      >
                        {connector.is_active ? (
                          <Pause className="h-4 w-4" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <div className="mt-3 grid grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">{connector.total_repositories}</span> repos
                    </div>
                    <div>
                      <span className="font-medium">{connector.total_files_synced}</span> files
                    </div>
                  </div>
                  
                  <div className="mt-2 text-xs text-gray-500">
                    Last sync: {formatDate(connector.last_sync_at)}
                  </div>
                </div>
              ))}
              
              {connectors.length === 0 && (
                <div className="p-8 text-center text-gray-500">
                  <GitBranch className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No GitHub connectors configured</p>
                  <p className="text-sm mt-1">Add a connector to get started</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Connector Details */}
        <div className="lg:col-span-2">
          {selectedConnector ? (
            <div className="space-y-6">
              {/* Connector Info */}
              <div className="bg-white rounded-lg shadow border p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(selectedConnector.status)}
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">{selectedConnector.name}</h3>
                      <p className="text-gray-600">@{selectedConnector.github_username}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSyncConnector(selectedConnector.id)}
                      disabled={syncing[selectedConnector.id]}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {syncing[selectedConnector.id] ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Sync className="h-4 w-4" />
                      )}
                      <span>Sync All</span>
                    </button>
                    <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg">
                      <Settings className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                {selectedConnector.description && (
                  <p className="text-gray-600 mb-4">{selectedConnector.description}</p>
                )}
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Status</span>
                    <p className="font-medium capitalize">{selectedConnector.status}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Auto Sync</span>
                    <p className="font-medium">{selectedConnector.auto_sync ? 'Enabled' : 'Disabled'}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Repositories</span>
                    <p className="font-medium">{selectedConnector.total_repositories}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Files Synced</span>
                    <p className="font-medium">{selectedConnector.total_files_synced}</p>
                  </div>
                </div>
              </div>

              {/* Repositories */}
              <div className="bg-white rounded-lg shadow border">
                <div className="p-4 border-b">
                  <h4 className="text-lg font-semibold text-gray-900">Repositories ({repositories.length})</h4>
                </div>
                <div className="divide-y max-h-96 overflow-y-auto">
                  {repositories.map((repo) => (
                    <div key={repo.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {getSyncStatusIcon(repo.sync_status)}
                          <div>
                            <div className="flex items-center space-x-2">
                              <h5 className="font-medium text-gray-900">{repo.name}</h5>
                              {repo.private && (
                                <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">Private</span>
                              )}
                              {!repo.is_enabled && (
                                <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">Disabled</span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600">{repo.description || 'No description'}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleSyncRepository(selectedConnector.id, repo.github_id)}
                            disabled={syncing[selectedConnector.id] || repo.sync_status === 'syncing'}
                            className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors disabled:opacity-50"
                            title="Sync Repository"
                          >
                            {repo.sync_status === 'syncing' ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Sync className="h-4 w-4" />
                            )}
                          </button>
                          <a
                            href={`https://github.com/${repo.full_name}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                            title="View on GitHub"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </div>
                      </div>
                      
                      <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">{repo.files_count}</span> files
                        </div>
                        <div>
                          <span className="font-medium">{repo.stargazers_count}</span> stars
                        </div>
                        <div>
                          <span className="font-medium">{repo.language || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-xs">Last sync: {formatDate(repo.last_sync_at)}</span>
                        </div>
                      </div>
                      
                      {repo.topics.length > 0 && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {repo.topics.slice(0, 3).map((topic) => (
                            <span key={topic} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                              {topic}
                            </span>
                          ))}
                          {repo.topics.length > 3 && (
                            <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">
                              +{repo.topics.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {repositories.length === 0 && (
                    <div className="p-8 text-center text-gray-500">
                      <GitBranch className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No repositories found</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow border p-8 text-center">
              <GitBranch className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Connector</h3>
              <p className="text-gray-600">Choose a GitHub connector from the list to view its repositories and manage sync settings.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
