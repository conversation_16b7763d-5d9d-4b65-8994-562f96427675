'use client';

import { useState, useEffect } from 'react';
import api from '@/lib/api';
import { toast } from 'react-toastify';

interface JobProgressProps {
  jobId: string;
  onComplete: (result: any) => void;
  onError: (error: string) => void;
}

export default function JobProgress({ jobId, onComplete, onError }: JobProgressProps) {
  const [progress, setProgress] = useState<number>(0);
  const [status, setStatus] = useState<string>('pending');
  const [statusMessage, setStatusMessage] = useState<string>('Job starting...');
  const [currentStage, setCurrentStage] = useState<string | null>(null);
  const [stages, setStages] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [pollingInterval, setPollingInterval] = useState<number>(1000); // Start with 1 second

  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const checkJobStatus = async () => {
      try {
        const response = await api.get(`/jobs/${jobId}`);
        const job = response.data;

        if (!isMounted) return;

        setProgress(job.progress);
        setStatus(job.status);
        setStatusMessage(job.status_message);
        setCurrentStage(job.current_stage);
        setStages(job.stages);

        if (job.status === 'completed') {
          // Job completed successfully
          setProgress(100);
          onComplete(job.result);
          return; // Stop polling
        } else if (job.status === 'failed') {
          // Job failed - use user-friendly error message if available
          const errorMessage = job.user_error || job.error || 'Job failed';
          setError(errorMessage);

          // Log technical details if available
          if (job.technical_error) {
            console.error('Technical error details:', job.technical_error);
          }

          onError(errorMessage);
          return; // Stop polling
        } else {
          // Job still running, continue polling
          // Adjust polling interval based on progress
          let newInterval = 1000; // Default 1 second
          if (job.progress > 80) {
            newInterval = 500; // Poll faster as we approach completion
          } else if (job.progress > 50) {
            newInterval = 1000;
          } else if (job.progress > 20) {
            newInterval = 2000;
          } else {
            newInterval = 3000; // Poll slower at the beginning
          }
          setPollingInterval(newInterval);

          // Schedule next poll
          timeoutId = setTimeout(checkJobStatus, newInterval);
        }
      } catch (err: any) {
        console.error('Error checking job status:', err);
        if (!isMounted) return;

        setError(err.response?.data?.detail || err.message || 'Error checking job status');
        onError(err.response?.data?.detail || err.message || 'Error checking job status');
      }
    };

    // Start polling
    checkJobStatus();

    // Cleanup
    return () => {
      isMounted = false;
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [jobId, onComplete, onError]);

  // Calculate which stages are complete
  const getStageStatus = (stage: string) => {
    if (!currentStage) return 'pending';
    const currentIndex = stages.indexOf(currentStage);
    const stageIndex = stages.indexOf(stage);

    if (stageIndex < currentIndex) return 'completed';
    if (stageIndex === currentIndex) return 'active';
    return 'pending';
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Forecast Generation Progress</h3>

      {error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      ) : (
        <>
          <div className="mb-4">
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">{statusMessage}</span>
              <span className="text-sm font-medium text-gray-700">{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* Stages progress */}
          {stages.length > 0 && (
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Processing Stages</h4>
              <div className="space-y-2">
                {stages.map((stage, index) => (
                  <div key={index} className="flex items-center">
                    <div className={`w-4 h-4 rounded-full mr-2 flex-shrink-0 ${
                      getStageStatus(stage) === 'completed' ? 'bg-green-500' :
                      getStageStatus(stage) === 'active' ? 'bg-blue-500' : 'bg-gray-300'
                    }`}></div>
                    <span className={`text-sm ${
                      getStageStatus(stage) === 'completed' ? 'text-green-700' :
                      getStageStatus(stage) === 'active' ? 'text-blue-700' : 'text-gray-500'
                    }`}>
                      {stage}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {status === 'running' && progress < 100 && (
            <p className="text-sm text-gray-500 mt-4">
              This may take a few moments. Please don't close this page.
            </p>
          )}
        </>
      )}
    </div>
  );
}
