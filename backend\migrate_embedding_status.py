"""
Simple migration script to add embedding status columns to datasets table
"""
import sqlite3
from datetime import datetime

def migrate_database():
    """Add embedding status columns to datasets table"""
    try:
        # Connect to the database
        conn = sqlite3.connect('aithentiq.db')
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(datasets)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add embedding_status column if it doesn't exist
        if 'embedding_status' not in columns:
            cursor.execute("ALTER TABLE datasets ADD COLUMN embedding_status TEXT DEFAULT 'pending'")
            print("✅ Added embedding_status column")
        
        # Add embedding_created_at column if it doesn't exist
        if 'embedding_created_at' not in columns:
            cursor.execute("ALTER TABLE datasets ADD COLUMN embedding_created_at TIMESTAMP")
            print("✅ Added embedding_created_at column")
        
        # Add embedding_error column if it doesn't exist
        if 'embedding_error' not in columns:
            cursor.execute("ALTER TABLE datasets ADD COLUMN embedding_error TEXT")
            print("✅ Added embedding_error column")
        
        # Update existing records to have 'pending' status
        cursor.execute("UPDATE datasets SET embedding_status = 'pending' WHERE embedding_status IS NULL")
        
        # Commit changes
        conn.commit()
        print("✅ Database migration completed successfully")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_database()
