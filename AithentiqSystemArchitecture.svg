<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 3865.91015625 955.2700805664062" style="max-width: 3865.91015625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a"><style>#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .error-icon{fill:#a44141;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .error-text{fill:#ddd;stroke:#ddd;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edge-thickness-normal{stroke-width:1px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edge-thickness-thick{stroke-width:3.5px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edge-pattern-solid{stroke-dasharray:0;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .marker.cross{stroke:lightgrey;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a p{margin:0;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .cluster-label text{fill:#F9FFFE;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .cluster-label span{color:#F9FFFE;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .cluster-label span p{background-color:transparent;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .label text,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a span{fill:#ccc;color:#ccc;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node rect,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node circle,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node ellipse,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node polygon,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .rough-node .label text,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node .label text,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .image-shape .label,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .icon-shape .label{text-anchor:middle;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .rough-node .label,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node .label,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .image-shape .label,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .icon-shape .label{text-align:center;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .node.clickable{cursor:pointer;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .arrowheadPath{fill:lightgrey;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .cluster text{fill:#F9FFFE;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .cluster span{color:#F9FFFE;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a rect.text{fill:none;stroke-width:0;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .icon-shape,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .icon-shape p,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .icon-shape rect,#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .frontend&gt;*{fill:#e1f5fe!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .frontend span{fill:#e1f5fe!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .api&gt;*{fill:#f3e5f5!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .api span{fill:#f3e5f5!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .processing&gt;*{fill:#e8f5e8!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .processing span{fill:#e8f5e8!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .ai&gt;*{fill:#fff3e0!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .ai span{fill:#fff3e0!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .trust&gt;*{fill:#fce4ec!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .trust span{fill:#fce4ec!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .database&gt;*{fill:#f1f8e9!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .database span{fill:#f1f8e9!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .analytics&gt;*{fill:#e0f2f1!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .analytics span{fill:#e0f2f1!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .external&gt;*{fill:#fafafa!important;}#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a .external span{fill:#fafafa!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph7" class="cluster"><rect height="104" width="688.5234375" y="516.796875" x="1062.712890625" style=""></rect><g transform="translate(1345.611328125, 516.796875)" class="cluster-label"><foreignObject height="24" width="122.7265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="104" width="1004.1640625" y="670.796875" x="8" style=""></rect><g transform="translate(441.4140625, 670.796875)" class="cluster-label"><foreignObject height="24" width="137.3359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Advanced Analytics</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="122.47320556640625" width="861.658203125" y="824.796875" x="2996.251953125" style=""></rect><g transform="translate(3346.4677734375, 824.796875)" class="cluster-label"><foreignObject height="24" width="161.2265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database (PostgreSQL)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="276.47320556640625" width="1076.84375" y="670.796875" x="1032.1640625" style=""></rect><g transform="translate(1506.3515625, 670.796875)" class="cluster-label"><foreignObject height="24" width="128.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Trust Core System</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="122.47320556640625" width="672.140625" y="824.796875" x="2216.162109375" style=""></rect><g transform="translate(2518.490234375, 824.796875)" class="cluster-label"><foreignObject height="24" width="67.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI Models</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="258" width="760.322265625" y="516.796875" x="2169.0078125" style=""></rect><g transform="translate(2468.9189453125, 516.796875)" class="cluster-label"><foreignObject height="24" width="160.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Processing Engine</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="104" width="3248.15625" y="362.796875" x="579.91796875" style=""></rect><g transform="translate(2136.00390625, 362.796875)" class="cluster-label"><foreignObject height="24" width="135.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Layer (FastAPI)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="208" width="1374.513671875" y="104.796875" x="2334.23828125" style=""></rect><g transform="translate(2955.1162109375, 104.796875)" class="cluster-label"><foreignObject height="24" width="132.7578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend (Next.js)</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_User_UI_0" d="M3258.424,54.797L3258.424,58.964C3258.424,63.13,3258.424,71.464,3258.424,79.797C3258.424,88.13,3258.424,96.464,3258.424,104.13C3258.424,111.797,3258.424,118.797,3258.424,122.297L3258.424,125.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_Auth_1" d="M3302.299,183.797L3309.07,187.964C3315.84,192.13,3329.382,200.464,3336.153,208.13C3342.924,215.797,3342.924,222.797,3342.924,226.297L3342.924,229.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_Chat_2" d="M3339.459,170.112L3378.697,176.56C3417.936,183.007,3496.412,195.902,3535.65,205.849C3574.889,215.797,3574.889,222.797,3574.889,226.297L3574.889,229.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_Dashboard_3" d="M3180.28,183.797L3168.22,187.964C3156.161,192.13,3132.042,200.464,3119.983,208.13C3107.924,215.797,3107.924,222.797,3107.924,226.297L3107.924,229.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_Upload_4" d="M3177.389,168.095L3128.733,174.879C3080.077,181.662,2982.766,195.23,2934.111,205.513C2885.455,215.797,2885.455,222.797,2885.455,226.297L2885.455,229.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_Admin_5" d="M3177.389,161.968L3055.074,169.773C2932.759,177.577,2688.13,193.187,2565.815,204.492C2443.5,215.797,2443.5,222.797,2443.5,226.297L2443.5,229.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Auth_AuthAPI_6" d="M3342.924,287.797L3342.924,291.964C3342.924,296.13,3342.924,304.464,3342.924,312.797C3342.924,321.13,3342.924,329.464,3342.924,337.797C3342.924,346.13,3342.924,354.464,3342.924,362.13C3342.924,369.797,3342.924,376.797,3342.924,380.297L3342.924,383.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthAPI_OAuth_7" d="M3258.955,416.82L2913.296,425.15C2567.637,433.479,1876.32,450.138,1530.661,462.634C1185.002,475.13,1185.002,483.464,1185.002,491.797C1185.002,500.13,1185.002,508.464,1185.002,516.13C1185.002,523.797,1185.002,530.797,1185.002,534.297L1185.002,537.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthAPI_UserDB_8" d="M3329.713,441.797L3327.674,445.964C3325.635,450.13,3321.558,458.464,3319.519,466.797C3317.48,475.13,3317.48,483.464,3317.48,491.797C3317.48,500.13,3317.48,508.464,3317.48,521.297C3317.48,534.13,3317.48,551.464,3317.48,568.797C3317.48,586.13,3317.48,603.464,3317.48,616.297C3317.48,629.13,3317.48,637.464,3317.48,645.797C3317.48,654.13,3317.48,662.464,3122.735,675.297C2927.99,688.13,2538.499,705.464,2343.753,722.797C2149.008,740.13,2149.008,757.464,2302.155,770.297C2455.301,783.13,2761.595,791.464,2914.742,799.797C3067.889,808.13,3067.889,816.464,3067.165,825.083C3066.441,833.702,3064.993,842.607,3064.269,847.06L3063.545,851.513"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthAPI_SessionDB_9" d="M3426.893,424.64L3486.834,431.666C3546.775,438.692,3666.657,452.744,3726.598,463.937C3786.539,475.13,3786.539,483.464,3786.539,491.797C3786.539,500.13,3786.539,508.464,3786.539,521.297C3786.539,534.13,3786.539,551.464,3786.539,568.797C3786.539,586.13,3786.539,603.464,3786.539,616.297C3786.539,629.13,3786.539,637.464,3786.539,645.797C3786.539,654.13,3786.539,662.464,3786.539,675.297C3786.539,688.13,3786.539,705.464,3786.539,722.797C3786.539,740.13,3786.539,757.464,3786.539,770.297C3786.539,783.13,3786.539,791.464,3786.539,799.797C3786.539,808.13,3786.539,816.464,3786.539,824.62C3786.539,832.777,3786.539,840.758,3786.539,844.748L3786.539,848.739"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Upload_UploadAPI_10" d="M2885.455,287.797L2885.455,291.964C2885.455,296.13,2885.455,304.464,2885.455,312.797C2885.455,321.13,2885.455,329.464,2885.455,337.797C2885.455,346.13,2885.455,354.464,2885.455,362.13C2885.455,369.797,2885.455,376.797,2885.455,380.297L2885.455,383.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UploadAPI_DocProcessor_11" d="M2798.646,441.797L2785.25,445.964C2771.853,450.13,2745.06,458.464,2731.664,466.797C2718.268,475.13,2718.268,483.464,2718.268,491.797C2718.268,500.13,2718.268,508.464,2718.268,516.13C2718.268,523.797,2718.268,530.797,2718.268,534.297L2718.268,537.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DocProcessor_VectorStore_12" d="M2616.088,592.61L2595.93,597.308C2575.773,602.006,2535.458,611.401,2515.3,620.266C2495.143,629.13,2495.143,637.464,2495.143,645.797C2495.143,654.13,2495.143,662.464,2485.299,670.55C2475.456,678.637,2455.77,686.477,2445.927,690.397L2436.084,694.317"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DocProcessor_DatasetDB_13" d="M2787.051,595.797L2797.666,599.964C2808.281,604.13,2829.511,612.464,2840.125,620.797C2850.74,629.13,2850.74,637.464,2850.74,645.797C2850.74,654.13,2850.74,662.464,2850.74,675.297C2850.74,688.13,2850.74,705.464,2850.74,722.797C2850.74,740.13,2850.74,757.464,2949.135,770.297C3047.531,783.13,3244.321,791.464,3342.716,799.797C3441.111,808.13,3441.111,816.464,3437.831,825.148C3434.55,833.832,3427.988,842.867,3424.708,847.385L3421.427,851.902"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Chat_QueryAPI_14" d="M3574.889,287.797L3574.889,291.964C3574.889,296.13,3574.889,304.464,3574.889,312.797C3574.889,321.13,3574.889,329.464,3574.889,337.797C3574.889,346.13,3574.889,354.464,3574.889,362.13C3574.889,369.797,3574.889,376.797,3574.889,380.297L3574.889,383.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryAPI_QueryEngine_15" d="M3484.326,418.509L3287.992,426.557C3091.658,434.605,2698.989,450.701,2502.655,462.916C2306.32,475.13,2306.32,483.464,2306.32,491.797C2306.32,500.13,2306.32,508.464,2306.32,516.13C2306.32,523.797,2306.32,530.797,2306.32,534.297L2306.32,537.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_VectorStore_16" d="M2331.373,595.797L2335.239,599.964C2339.106,604.13,2346.838,612.464,2350.704,620.797C2354.57,629.13,2354.57,637.464,2354.57,645.797C2354.57,654.13,2354.57,662.464,2355.246,670.142C2355.921,677.821,2357.272,684.845,2357.947,688.357L2358.623,691.869"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_ContextEngine_17" d="M2396.883,582.672L2438.355,589.026C2479.827,595.381,2562.771,608.089,2604.243,618.609C2645.715,629.13,2645.715,637.464,2645.715,645.797C2645.715,654.13,2645.715,662.464,2645.715,670.13C2645.715,677.797,2645.715,684.797,2645.715,688.297L2645.715,691.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ContextEngine_OpenAI_18" d="M2592.508,749.797L2584.297,753.964C2576.086,758.13,2559.664,766.464,2514.849,774.797C2470.033,783.13,2396.824,791.464,2360.22,799.797C2323.615,808.13,2323.615,816.464,2323.615,825.67C2323.615,834.876,2323.615,844.955,2323.615,849.994L2323.615,855.033"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ContextEngine_Gemini_19" d="M2698.922,749.797L2707.133,753.964C2715.344,758.13,2731.766,766.464,2703.372,774.797C2674.979,783.13,2601.77,791.464,2565.165,799.797C2528.561,808.13,2528.561,816.464,2528.561,825.67C2528.561,834.876,2528.561,844.955,2528.561,849.994L2528.561,855.033"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_TrustEngine_20" d="M2320.989,595.797L2323.252,599.964C2325.516,604.13,2330.043,612.464,2332.307,620.797C2334.57,629.13,2334.57,637.464,2334.57,645.797C2334.57,654.13,2334.57,662.464,2204.393,674.316C2074.215,686.168,1813.86,701.538,1683.682,709.224L1553.505,716.909"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustEngine_Convergence_21" d="M1358.035,740.985L1328.373,746.621C1298.711,752.256,1239.387,763.526,1209.725,773.328C1180.063,783.13,1180.063,791.464,1180.063,799.797C1180.063,808.13,1180.063,816.464,1180.063,825.67C1180.063,834.876,1180.063,844.955,1180.063,849.994L1180.063,855.033"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustEngine_Faithfulness_22" d="M1454.264,749.797L1454.34,753.964C1454.416,758.13,1454.567,766.464,1454.643,774.797C1454.719,783.13,1454.719,791.464,1454.719,799.797C1454.719,808.13,1454.719,816.464,1454.719,825.67C1454.719,834.876,1454.719,844.955,1454.719,849.994L1454.719,855.033"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustEngine_Production_23" d="M1549.512,740.985L1579.174,746.621C1608.836,752.256,1668.16,763.526,1697.822,773.328C1727.484,783.13,1727.484,791.464,1727.484,799.797C1727.484,808.13,1727.484,816.464,1727.484,825.67C1727.484,834.876,1727.484,844.955,1727.484,849.994L1727.484,855.033"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustEngine_Citation_24" d="M1549.512,732.235L1621.468,739.329C1693.424,746.422,1837.337,760.61,1909.294,771.87C1981.25,783.13,1981.25,791.464,1981.25,799.797C1981.25,808.13,1981.25,816.464,1981.25,825.67C1981.25,834.876,1981.25,844.955,1981.25,849.994L1981.25,855.033"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TrustEngine_TrustDB_25" d="M1549.512,731.215L1632.115,738.479C1714.717,745.743,1879.923,760.27,1962.526,771.7C2045.129,783.13,2045.129,791.464,2045.129,799.797C2045.129,808.13,2045.129,816.464,2303.51,830.49C2561.89,844.517,3078.652,864.238,3337.032,874.098L3595.413,883.958"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_Predictive_26" d="M2272.814,595.797L2267.643,599.964C2262.472,604.13,2252.131,612.464,2246.96,620.797C2241.789,629.13,2241.789,637.464,2241.789,645.797C2241.789,654.13,2241.789,662.464,1909.397,674.866C1577.005,687.269,912.221,703.742,579.828,711.978L247.436,720.214"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_Anomaly_27" d="M2283.198,595.797L2279.63,599.964C2276.062,604.13,2268.925,612.464,2265.357,620.797C2261.789,629.13,2261.789,637.464,2261.789,645.797C2261.789,654.13,2261.789,662.464,1967.005,674.825C1672.221,687.187,1082.653,703.576,787.869,711.771L493.084,719.966"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_Churn_28" d="M2293.583,595.797L2291.617,599.964C2289.652,604.13,2285.72,612.464,2283.755,620.797C2281.789,629.13,2281.789,637.464,2281.789,645.797C2281.789,654.13,2281.789,662.464,2021.936,674.805C1762.083,687.147,1242.376,703.496,982.523,711.671L722.67,719.846"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_BI_29" d="M2303.968,595.797L2303.604,599.964C2303.241,604.13,2302.515,612.464,2302.152,620.797C2301.789,629.13,2301.789,637.464,2301.789,645.797C2301.789,654.13,2301.789,662.464,2081.684,674.64C1861.58,686.817,1421.371,702.837,1201.266,710.848L981.161,718.858"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryAPI_QueryDB_30" d="M3609.038,441.797L3614.308,445.964C3619.578,450.13,3630.118,458.464,3635.388,466.797C3640.658,475.13,3640.658,483.464,3640.658,491.797C3640.658,500.13,3640.658,508.464,3640.658,521.297C3640.658,534.13,3640.658,551.464,3640.658,568.797C3640.658,586.13,3640.658,603.464,3640.658,616.297C3640.658,629.13,3640.658,637.464,3640.658,645.797C3640.658,654.13,3640.658,662.464,3640.658,675.297C3640.658,688.13,3640.658,705.464,3640.658,722.797C3640.658,740.13,3640.658,757.464,3640.658,770.297C3640.658,783.13,3640.658,791.464,3640.658,799.797C3640.658,808.13,3640.658,816.464,3626.05,827.737C3611.441,839.01,3582.224,853.223,3567.616,860.33L3553.007,867.436"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QueryEngine_DatasetDB_31" d="M2396.883,577.777L2469.192,584.947C2541.502,592.117,2686.121,606.457,2758.431,617.794C2830.74,629.13,2830.74,637.464,2830.74,645.797C2830.74,654.13,2830.74,662.464,2830.74,675.297C2830.74,688.13,2830.74,705.464,2830.74,722.797C2830.74,740.13,2830.74,757.464,2918.53,770.297C3006.32,783.13,3181.9,791.464,3269.69,799.797C3357.48,808.13,3357.48,816.464,3359.763,824.85C3362.047,833.236,3366.613,841.675,3368.896,845.894L3371.179,850.113"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Admin_AdminAPI_32" d="M2443.5,287.797L2443.5,291.964C2443.5,296.13,2443.5,304.464,2443.5,312.797C2443.5,321.13,2443.5,329.464,2443.5,337.797C2443.5,346.13,2443.5,354.464,2443.5,362.13C2443.5,369.797,2443.5,376.797,2443.5,380.297L2443.5,383.797"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AdminAPI_UserDB_33" d="M2353.695,429.646L2316.247,435.838C2278.799,442.029,2203.904,454.413,2166.456,464.772C2129.008,475.13,2129.008,483.464,2129.008,491.797C2129.008,500.13,2129.008,508.464,2129.008,521.297C2129.008,534.13,2129.008,551.464,2129.008,568.797C2129.008,586.13,2129.008,603.464,2129.008,616.297C2129.008,629.13,2129.008,637.464,2129.008,645.797C2129.008,654.13,2129.008,662.464,2129.008,675.297C2129.008,688.13,2129.008,705.464,2129.008,722.797C2129.008,740.13,2129.008,757.464,2282.018,770.297C2435.029,783.13,2741.049,791.464,2894.06,799.797C3047.07,808.13,3047.07,816.464,3047.854,825.088C3048.637,833.713,3050.204,842.629,3050.988,847.086L3051.772,851.544"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AdminAPI_DatasetDB_34" d="M2533.305,420.021L2667.334,427.817C2801.363,435.613,3069.422,451.205,3203.451,463.167C3337.48,475.13,3337.48,483.464,3337.48,491.797C3337.48,500.13,3337.48,508.464,3337.48,521.297C3337.48,534.13,3337.48,551.464,3337.48,568.797C3337.48,586.13,3337.48,603.464,3337.48,616.297C3337.48,629.13,3337.48,637.464,3337.48,645.797C3337.48,654.13,3337.48,662.464,3337.48,675.297C3337.48,688.13,3337.48,705.464,3337.48,722.797C3337.48,740.13,3337.48,757.464,3337.48,770.297C3337.48,783.13,3337.48,791.464,3337.48,799.797C3337.48,808.13,3337.48,816.464,3341.168,825.312C3344.856,834.16,3352.232,843.523,3355.92,848.204L3359.608,852.886"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AdminAPI_QueryDB_35" d="M2533.305,419.375L2688.331,427.279C2843.357,435.183,3153.41,450.99,3308.437,463.06C3463.463,475.13,3463.463,483.464,3463.463,491.797C3463.463,500.13,3463.463,508.464,3463.463,521.297C3463.463,534.13,3463.463,551.464,3463.463,568.797C3463.463,586.13,3463.463,603.464,3463.463,616.297C3463.463,629.13,3463.463,637.464,3463.463,645.797C3463.463,654.13,3463.463,662.464,3463.463,675.297C3463.463,688.13,3463.463,705.464,3463.463,722.797C3463.463,740.13,3463.463,757.464,3463.463,770.297C3463.463,783.13,3463.463,791.464,3463.463,799.797C3463.463,808.13,3463.463,816.464,3467.026,825.409C3470.589,834.353,3477.715,843.91,3481.278,848.688L3484.842,853.467"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AdminAPI_Monitoring_36" d="M2353.695,420.866L2240.423,428.521C2127.15,436.176,1900.604,451.487,1787.331,463.308C1674.059,475.13,1674.059,483.464,1674.059,491.797C1674.059,500.13,1674.059,508.464,1669.97,516.348C1665.882,524.233,1657.705,531.669,1653.617,535.387L1649.529,539.106"></path><path marker-end="url(#mermaid-2f49193a-8bbf-4152-8cb8-4b0e3d6d8f1a_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HealthAPI_Monitoring_37" d="M1074.389,423.445L1152.907,430.67C1231.424,437.895,1388.46,452.346,1466.978,463.738C1545.496,475.13,1545.496,483.464,1545.496,491.797C1545.496,500.13,1545.496,508.464,1550.677,516.404C1555.858,524.345,1566.22,531.893,1571.401,535.668L1576.583,539.442"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(3258.423828125, 156.796875)" id="flowchart-UI-178" class="node default frontend"><rect height="54" width="162.0703125" y="-27" x="-81.03515625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-51.03515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="102.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Interface</p></span></div></foreignObject></g></g><g transform="translate(3342.923828125, 260.796875)" id="flowchart-Auth-179" class="node default frontend"><rect height="54" width="166.203125" y="-27" x="-83.1015625" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-53.1015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="106.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Authentication</p></span></div></foreignObject></g></g><g transform="translate(3574.888671875, 260.796875)" id="flowchart-Chat-180" class="node default frontend"><rect height="54" width="197.7265625" y="-27" x="-98.86328125" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-68.86328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="137.7265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Chat Interface</p></span></div></foreignObject></g></g><g transform="translate(3107.923828125, 260.796875)" id="flowchart-Dashboard-181" class="node default frontend"><rect height="54" width="203.796875" y="-27" x="-101.8984375" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-71.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Analytics Dashboard</p></span></div></foreignObject></g></g><g transform="translate(2885.455078125, 260.796875)" id="flowchart-Upload-182" class="node default frontend"><rect height="54" width="141.140625" y="-27" x="-70.5703125" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-40.5703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="81.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Upload</p></span></div></foreignObject></g></g><g transform="translate(2443.5, 260.796875)" id="flowchart-Admin-183" class="node default frontend"><rect height="54" width="148.5234375" y="-27" x="-74.26171875" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-44.26171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="88.5234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Admin Panel</p></span></div></foreignObject></g></g><g transform="translate(761.369140625, 414.796875)" id="flowchart-Gateway-184" class="node default api"><rect height="54" width="150.1328125" y="-27" x="-75.06640625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-45.06640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="90.1328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Gateway</p></span></div></foreignObject></g></g><g transform="translate(3342.923828125, 414.796875)" id="flowchart-AuthAPI-185" class="node default api"><rect height="54" width="167.9375" y="-27" x="-83.96875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-53.96875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="107.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Endpoints</p></span></div></foreignObject></g></g><g transform="translate(3574.888671875, 414.796875)" id="flowchart-QueryAPI-186" class="node default api"><rect height="54" width="181.125" y="-27" x="-90.5625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-60.5625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="121.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query Processing</p></span></div></foreignObject></g></g><g transform="translate(2885.455078125, 414.796875)" id="flowchart-UploadAPI-187" class="node default api"><rect height="54" width="184.5859375" y="-27" x="-92.29296875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-62.29296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="124.5859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Upload Endpoints</p></span></div></foreignObject></g></g><g transform="translate(2443.5, 414.796875)" id="flowchart-AdminAPI-188" class="node default api"><rect height="54" width="179.609375" y="-27" x="-89.8046875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-59.8046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="119.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Admin Endpoints</p></span></div></foreignObject></g></g><g transform="translate(980.412109375, 414.796875)" id="flowchart-HealthAPI-189" class="node default api"><rect height="54" width="187.953125" y="-27" x="-93.9765625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-63.9765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Health Monitoring</p></span></div></foreignObject></g></g><g transform="translate(2718.267578125, 568.796875)" id="flowchart-DocProcessor-190" class="node default processing"><rect height="54" width="204.359375" y="-27" x="-102.1796875" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-72.1796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Document Processor</p></span></div></foreignObject></g></g><g transform="translate(2364.5703125, 722.796875)" id="flowchart-VectorStore-191" class="node default processing"><rect height="54" width="242.078125" y="-27" x="-121.0390625" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-91.0390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="182.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vector Store/Embeddings</p></span></div></foreignObject></g></g><g transform="translate(2645.71484375, 722.796875)" id="flowchart-ContextEngine-192" class="node default processing"><rect height="54" width="220.2109375" y="-27" x="-110.10546875" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-80.10546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160.2109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Context Augmentation</p></span></div></foreignObject></g></g><g transform="translate(2306.3203125, 568.796875)" id="flowchart-QueryEngine-193" class="node default processing"><rect height="54" width="181.125" y="-27" x="-90.5625" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-60.5625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="121.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query Processing</p></span></div></foreignObject></g></g><g transform="translate(2323.615234375, 886.0334777832031)" id="flowchart-OpenAI-194" class="node default ai"><rect height="54" width="144.90625" y="-27" x="-72.453125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-42.453125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="84.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OpenAI GPT</p></span></div></foreignObject></g></g><g transform="translate(2528.560546875, 886.0334777832031)" id="flowchart-Gemini-195" class="node default ai"><rect height="54" width="164.984375" y="-27" x="-82.4921875" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-52.4921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Google Gemini</p></span></div></foreignObject></g></g><g transform="translate(2757.177734375, 886.0334777832031)" id="flowchart-Embeddings-196" class="node default ai"><rect height="54" width="192.25" y="-27" x="-96.125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-66.125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="132.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Embedding Models</p></span></div></foreignObject></g></g><g transform="translate(1453.7734375, 722.796875)" id="flowchart-TrustEngine-197" class="node default trust"><rect height="54" width="191.4765625" y="-27" x="-95.73828125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-65.73828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="131.4765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Trust Score Engine</p></span></div></foreignObject></g></g><g transform="translate(1180.0625, 886.0334777832031)" id="flowchart-Convergence-198" class="node default trust"><rect height="54" width="225.796875" y="-27" x="-112.8984375" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-82.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="165.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Multi-LLM Convergence</p></span></div></foreignObject></g></g><g transform="translate(1454.71875, 886.0334777832031)" id="flowchart-Faithfulness-199" class="node default trust"><rect height="54" width="223.515625" y="-27" x="-111.7578125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-81.7578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="163.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Faithfulness &amp; Context</p></span></div></foreignObject></g></g><g transform="translate(1727.484375, 886.0334777832031)" id="flowchart-Production-200" class="node default trust"><rect height="54" width="222.015625" y="-27" x="-111.0078125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-81.0078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="162.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Production Confidence</p></span></div></foreignObject></g></g><g transform="translate(1981.25, 886.0334777832031)" id="flowchart-Citation-201" class="node default trust"><rect height="54" width="185.515625" y="-27" x="-92.7578125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-62.7578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="125.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Citation Accuracy</p></span></div></foreignObject></g></g><g transform="translate(3057.888671875, 886.0334777832031)" id="flowchart-UserDB-202" class="node default database"><path transform="translate(-26.63671875, -30.70612209123976)" style="fill:#f1f8e9 !important" class="basic label-container" d="M0,7.470748060826504 a26.63671875,7.470748060826504 0,0,0 53.2734375,0 a26.63671875,7.470748060826504 0,0,0 -53.2734375,0 l0,46.47074806082651 a26.63671875,7.470748060826504 0,0,0 53.2734375,0 l0,-46.47074806082651"></path><g transform="translate(-19.13671875, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="38.2734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Users</p></span></div></foreignObject></g></g><g transform="translate(3392.1484375, 886.0334777832031)" id="flowchart-DatasetDB-203" class="node default database"><path transform="translate(-37.99609375, -33.67819800209896)" style="fill:#f1f8e9 !important" class="basic label-container" d="M0,9.452132001399308 a37.99609375,9.452132001399308 0,0,0 75.9921875,0 a37.99609375,9.452132001399308 0,0,0 -75.9921875,0 l0,48.452132001399306 a37.99609375,9.452132001399308 0,0,0 75.9921875,0 l0,-48.452132001399306"></path><g transform="translate(-30.49609375, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="60.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Datasets</p></span></div></foreignObject></g></g><g transform="translate(3514.77734375, 886.0334777832031)" id="flowchart-QueryDB-204" class="node default database"><path transform="translate(-34.6328125, -32.87066677390815)" style="fill:#f1f8e9 !important" class="basic label-container" d="M0,8.9137778492721 a34.6328125,8.9137778492721 0,0,0 69.265625,0 a34.6328125,8.9137778492721 0,0,0 -69.265625,0 l0,47.9137778492721 a34.6328125,8.9137778492721 0,0,0 69.265625,0 l0,-47.9137778492721"></path><g transform="translate(-27.1328125, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="54.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Queries</p></span></div></foreignObject></g></g><g transform="translate(3649.7890625, 886.0334777832031)" id="flowchart-TrustDB-205" class="node default database"><path transform="translate(-50.37890625, -36.236598954908814)" style="fill:#f1f8e9 !important" class="basic label-container" d="M0,11.157732636605875 a50.37890625,11.157732636605875 0,0,0 100.7578125,0 a50.37890625,11.157732636605875 0,0,0 -100.7578125,0 l0,50.15773263660587 a50.37890625,11.157732636605875 0,0,0 100.7578125,0 l0,-50.15773263660587"></path><g transform="translate(-42.87890625, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="85.7578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Trust Scores</p></span></div></foreignObject></g></g><g transform="translate(3786.5390625, 886.0334777832031)" id="flowchart-SessionDB-206" class="node default database"><path transform="translate(-36.37109375, -33.294891549128835)" style="fill:#f1f8e9 !important" class="basic label-container" d="M0,9.196594366085892 a36.37109375,9.196594366085892 0,0,0 72.7421875,0 a36.37109375,9.196594366085892 0,0,0 -72.7421875,0 l0,48.19659436608589 a36.37109375,9.196594366085892 0,0,0 72.7421875,0 l0,-48.19659436608589"></path><g transform="translate(-28.87109375, -2)" style="" class="label"><rect></rect><foreignObject height="24" width="57.7421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Sessions</p></span></div></foreignObject></g></g><g transform="translate(143.21875, 722.796875)" id="flowchart-Predictive-207" class="node default analytics"><rect height="54" width="200.4375" y="-27" x="-100.21875" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-70.21875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="140.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Predictive Modeling</p></span></div></foreignObject></g></g><g transform="translate(391.26171875, 722.796875)" id="flowchart-Anomaly-208" class="node default analytics"><rect height="54" width="195.6484375" y="-27" x="-97.82421875" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-67.82421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="135.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Anomaly Detection</p></span></div></foreignObject></g></g><g transform="translate(628.87890625, 722.796875)" id="flowchart-Churn-209" class="node default analytics"><rect height="54" width="179.5859375" y="-27" x="-89.79296875" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-59.79296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="119.5859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Churn Prediction</p></span></div></foreignObject></g></g><g transform="translate(872.91796875, 722.796875)" id="flowchart-BI-210" class="node default analytics"><rect height="54" width="208.4921875" y="-27" x="-104.24609375" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-74.24609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="148.4921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Business Intelligence</p></span></div></foreignObject></g></g><g transform="translate(1185.001953125, 568.796875)" id="flowchart-OAuth-211" class="node default external"><rect height="54" width="174.578125" y="-27" x="-87.2890625" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-57.2890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="114.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OAuth Providers</p></span></div></foreignObject></g></g><g transform="translate(1394.908203125, 568.796875)" id="flowchart-Storage-212" class="node default external"><rect height="54" width="145.234375" y="-27" x="-72.6171875" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-42.6171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="85.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Storage</p></span></div></foreignObject></g></g><g transform="translate(1616.880859375, 568.796875)" id="flowchart-Monitoring-213" class="node default external"><rect height="54" width="198.7109375" y="-27" x="-99.35546875" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-69.35546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="138.7109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Monitoring Services</p></span></div></foreignObject></g></g><g transform="translate(3258.423828125, 31.3984375)" id="flowchart-User-214" class="node default"><circle cy="0" cx="0" r="23.3984375" style="" class="basic label-container"></circle><g transform="translate(-15.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="31.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User</p></span></div></foreignObject></g></g></g></g></g></svg>