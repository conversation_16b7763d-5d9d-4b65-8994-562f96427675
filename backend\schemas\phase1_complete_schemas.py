"""
Phase 1 Complete Pydantic Schemas
All entities with proper validation and relationships as required by development plan
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, EmailStr, validator, root_validator
from uuid import UUID

# Enums for validation
class SubscriptionPlan(str, Enum):
    FREE = "free"
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"

class TenantStatus(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    CANCELLED = "cancelled"

class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

class ProcessingStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class ContentType(str, Enum):
    TABULAR = "tabular"
    DOCUMENT = "document"
    MIXED = "mixed"

class QueryType(str, Enum):
    GENERAL = "general"
    ANALYTICAL = "analytical"
    CHART = "chart"
    FORMULA = "formula"
    SUMMARY = "summary"

# Base schemas
class BaseSchema(BaseModel):
    class Config:
        from_attributes = True
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

# Tenant Schemas
class TenantBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=100)
    domain: str = Field(..., min_length=1, max_length=50)
    display_name: str = Field(..., min_length=1, max_length=100)
    subscription_plan: SubscriptionPlan = SubscriptionPlan.FREE
    status: TenantStatus = TenantStatus.ACTIVE
    max_users: Optional[int] = Field(5, ge=1, le=10000)
    max_datasets: Optional[int] = Field(10, ge=1, le=100000)
    max_storage_gb: Optional[float] = Field(1.0, ge=0.1, le=10000.0)
    settings: Optional[Dict[str, Any]] = Field(default_factory=dict)
    features: Optional[Dict[str, Any]] = Field(default_factory=dict)

class TenantCreate(TenantBase):
    admin_email: EmailStr
    admin_name: Optional[str] = None

class TenantUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    display_name: Optional[str] = Field(None, min_length=1, max_length=100)
    subscription_plan: Optional[SubscriptionPlan] = None
    status: Optional[TenantStatus] = None
    max_users: Optional[int] = Field(None, ge=1, le=10000)
    max_datasets: Optional[int] = Field(None, ge=1, le=100000)
    max_storage_gb: Optional[float] = Field(None, ge=0.1, le=10000.0)
    settings: Optional[Dict[str, Any]] = None
    features: Optional[Dict[str, Any]] = None

class TenantResponse(TenantBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

# User Schemas
class UserPermissions(BaseSchema):
    can_upload_datasets: bool = True
    can_create_queries: bool = True
    can_manage_connectors: bool = False
    can_view_analytics: bool = False
    can_manage_users: bool = False

class TenantUserBase(BaseSchema):
    email: EmailStr
    name: str = Field(..., min_length=1, max_length=100)
    avatar_url: Optional[str] = Field(None, max_length=500)
    auth_provider: str = Field("email", max_length=50)
    auth_provider_id: Optional[str] = Field(None, max_length=255)
    role: UserRole = UserRole.USER
    permissions: UserPermissions = Field(default_factory=UserPermissions)
    status: UserStatus = UserStatus.ACTIVE
    email_verified: bool = False
    subscription_status: str = Field("free", max_length=20)
    subscription_expires_at: Optional[datetime] = None

class TenantUserCreate(TenantUserBase):
    password: Optional[str] = Field(None, min_length=8, max_length=100)
    
    @validator('password')
    def validate_password(cls, v):
        if v and len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class TenantUserUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    avatar_url: Optional[str] = Field(None, max_length=500)
    role: Optional[UserRole] = None
    permissions: Optional[UserPermissions] = None
    status: Optional[UserStatus] = None
    email_verified: Optional[bool] = None
    subscription_status: Optional[str] = None
    subscription_expires_at: Optional[datetime] = None

class TenantUserResponse(TenantUserBase):
    id: UUID
    tenant_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login_at: Optional[datetime] = None

# Dataset Schemas
class TenantDatasetBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    filename: Optional[str] = Field(None, max_length=255)
    file_type: Optional[str] = Field(None, max_length=20)
    content_type: ContentType = ContentType.TABULAR
    file_size_bytes: int = Field(0, ge=0)
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    processing_error: Optional[str] = None
    word_count: int = Field(0, ge=0)
    character_count: int = Field(0, ge=0)
    row_count: int = Field(0, ge=0)
    column_count: int = Field(0, ge=0)
    document_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    embeddings_data: Optional[Dict[str, Any]] = Field(default_factory=dict)

class TenantDatasetCreate(TenantDatasetBase):
    pass

class TenantDatasetUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    processing_status: Optional[ProcessingStatus] = None
    processing_error: Optional[str] = None
    word_count: Optional[int] = Field(None, ge=0)
    character_count: Optional[int] = Field(None, ge=0)
    row_count: Optional[int] = Field(None, ge=0)
    column_count: Optional[int] = Field(None, ge=0)
    document_metadata: Optional[Dict[str, Any]] = None
    embeddings_data: Optional[Dict[str, Any]] = None

class TenantDatasetResponse(TenantDatasetBase):
    id: UUID
    tenant_id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

# Document Schemas
class TenantDocumentBase(BaseSchema):
    content: str = Field(..., min_length=1)
    content_hash: Optional[str] = Field(None, max_length=64)
    chunk_index: int = Field(0, ge=0)
    chunk_size: int = Field(0, ge=0)
    overlap_size: int = Field(0, ge=0)
    embedding_model: Optional[str] = Field(None, max_length=100)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    source_reference: Optional[str] = Field(None, max_length=500)

class TenantDocumentCreate(TenantDocumentBase):
    dataset_id: UUID

class TenantDocumentUpdate(BaseSchema):
    content: Optional[str] = Field(None, min_length=1)
    content_hash: Optional[str] = Field(None, max_length=64)
    chunk_index: Optional[int] = Field(None, ge=0)
    chunk_size: Optional[int] = Field(None, ge=0)
    overlap_size: Optional[int] = Field(None, ge=0)
    embedding_model: Optional[str] = Field(None, max_length=100)
    metadata: Optional[Dict[str, Any]] = None
    source_reference: Optional[str] = Field(None, max_length=500)

class TenantDocumentResponse(TenantDocumentBase):
    id: UUID
    tenant_id: UUID
    dataset_id: UUID
    created_at: datetime

# Query Schemas
class TenantQueryBase(BaseSchema):
    question: str = Field(..., min_length=1)
    answer: Optional[str] = None
    query_type: QueryType = QueryType.GENERAL
    query_name: Optional[str] = Field(None, max_length=255)
    chart_type: Optional[str] = Field(None, max_length=50)
    chart_data: Optional[Dict[str, Any]] = None
    trust_score_data: Optional[Dict[str, Any]] = None
    reasoning_steps: Optional[List[Dict[str, Any]]] = None
    formula_results: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = Field(default_factory=list)
    is_bookmarked: bool = False
    is_saved: bool = False
    processing_time_ms: Optional[int] = Field(None, ge=0)
    token_count: Optional[int] = Field(None, ge=0)
    source_count: Optional[int] = Field(None, ge=0)
    trust_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    hallucination_risk: Optional[float] = Field(None, ge=0.0, le=1.0)
    completeness_score: Optional[float] = Field(None, ge=0.0, le=1.0)

class TenantQueryCreate(TenantQueryBase):
    dataset_id: Optional[UUID] = None

class TenantQueryUpdate(BaseSchema):
    answer: Optional[str] = None
    query_name: Optional[str] = Field(None, max_length=255)
    chart_type: Optional[str] = Field(None, max_length=50)
    chart_data: Optional[Dict[str, Any]] = None
    trust_score_data: Optional[Dict[str, Any]] = None
    reasoning_steps: Optional[List[Dict[str, Any]]] = None
    formula_results: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    is_bookmarked: Optional[bool] = None
    is_saved: Optional[bool] = None
    processing_time_ms: Optional[int] = Field(None, ge=0)
    token_count: Optional[int] = Field(None, ge=0)
    source_count: Optional[int] = Field(None, ge=0)
    trust_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    hallucination_risk: Optional[float] = Field(None, ge=0.0, le=1.0)
    completeness_score: Optional[float] = Field(None, ge=0.0, le=1.0)

class TenantQueryResponse(TenantQueryBase):
    id: UUID
    tenant_id: UUID
    user_id: UUID
    dataset_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

# Source Attribution Schemas
class TenantSourceAttributionBase(BaseSchema):
    source_text: str = Field(..., min_length=1)
    attributed_text: str = Field(..., min_length=1)
    confidence_score: float = Field(0.0, ge=0.0, le=1.0)
    start_position: int = Field(0, ge=0)
    end_position: int = Field(0, ge=0)
    attribution_type: str = Field("direct", max_length=50)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

class TenantSourceAttributionCreate(TenantSourceAttributionBase):
    query_id: UUID
    document_id: Optional[UUID] = None

class TenantSourceAttributionUpdate(BaseSchema):
    source_text: Optional[str] = Field(None, min_length=1)
    attributed_text: Optional[str] = Field(None, min_length=1)
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    start_position: Optional[int] = Field(None, ge=0)
    end_position: Optional[int] = Field(None, ge=0)
    attribution_type: Optional[str] = Field(None, max_length=50)
    metadata: Optional[Dict[str, Any]] = None

class TenantSourceAttributionResponse(TenantSourceAttributionBase):
    id: UUID
    tenant_id: UUID
    query_id: UUID
    document_id: Optional[UUID] = None
    created_at: datetime

# Composite Response Schemas
class TenantWithUsers(TenantResponse):
    users: List[TenantUserResponse] = Field(default_factory=list)

class TenantUserWithQueries(TenantUserResponse):
    queries: List[TenantQueryResponse] = Field(default_factory=list)

class TenantDatasetWithDocuments(TenantDatasetResponse):
    documents: List[TenantDocumentResponse] = Field(default_factory=list)

class TenantQueryWithAttributions(TenantQueryResponse):
    source_attributions: List[TenantSourceAttributionResponse] = Field(default_factory=list)

# API Response Schemas
class APIResponse(BaseSchema):
    success: bool = True
    message: str = "Operation completed successfully"
    data: Optional[Any] = None
    errors: Optional[List[str]] = None

class PaginatedResponse(BaseSchema):
    items: List[Any]
    total: int
    page: int = 1
    per_page: int = 50
    pages: int
    has_next: bool
    has_prev: bool

# Error Schemas
class ErrorDetail(BaseSchema):
    field: Optional[str] = None
    message: str
    code: Optional[str] = None

class ValidationError(BaseSchema):
    detail: List[ErrorDetail]

# Authentication Schemas
class TokenData(BaseSchema):
    user_id: Optional[UUID] = None
    tenant_id: Optional[UUID] = None
    role: Optional[UserRole] = None
    permissions: Optional[UserPermissions] = None

class LoginRequest(BaseSchema):
    email: EmailStr
    password: str = Field(..., min_length=8)

class LoginResponse(BaseSchema):
    access_token: str
    token_type: str = "bearer"
    user: TenantUserResponse
    tenant: TenantResponse

# Health Check Schema
class HealthCheck(BaseSchema):
    status: str = "healthy"
    timestamp: datetime
    version: str = "1.0.0"
    database: str = "connected"
    services: Dict[str, str] = Field(default_factory=dict)
