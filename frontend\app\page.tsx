'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

export default function Home() {
  const { data: session } = useSession();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-100 via-gray-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading AIthentiq...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen font-['Inter',_'Poppins',_'Roboto',_sans-serif]">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-100 via-gray-50 to-blue-50 py-8 px-6 overflow-hidden">
        {/* Futuristic animated background */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated gradient orbs */}
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-cyan-400/20 to-blue-500/20 rounded-full blur-3xl animate-float"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl animate-float-delayed"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-indigo-500/10 to-cyan-500/10 rounded-full blur-3xl animate-float-delayed-2"></div>



          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

          {/* Holographic border effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-500/10 to-transparent animate-pulse"></div>
        </div>

        <div className="relative max-w-7xl mx-auto">
          {/* Navigation */}
          <nav className="flex justify-between items-center mb-8 bg-white/90 backdrop-blur-lg rounded-2xl px-8 py-4 shadow-2xl border border-blue-500/20 shadow-glow">
            <div className="flex items-center space-x-6">
              {/* Enhanced Logo Container */}
              <div className="relative group">
                {/* Enhanced holographic glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/30 via-purple-500/30 to-blue-500/30 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-glow-pulse"></div>

                {/* Logo container with futuristic styling */}
                <div className="relative bg-gradient-to-br from-gray-50 to-white p-3 rounded-2xl shadow-2xl border border-blue-500/30 group-hover:shadow-glow group-hover:scale-105 transition-all duration-300">
                  <img
                    src="/AIthentiq_Logo.png"
                    alt="AIthentiq Logo"
                    className="h-12 w-18 object-contain"
                  />

                  {/* Enhanced orbiting elements with glow */}
                  <div className="absolute inset-0 animate-spin-slow opacity-80">
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-400 rounded-full shadow-glow animate-glow-pulse"></div>
                    <div className="absolute top-1/2 -right-3 transform -translate-y-1/2 w-2 h-2 bg-purple-400 rounded-full shadow-glow-purple animate-glow-pulse animation-delay-500"></div>
                    <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-indigo-400 rounded-full shadow-glow animate-glow-pulse animation-delay-1000"></div>
                    <div className="absolute top-1/2 -left-3 transform -translate-y-1/2 w-2 h-2 bg-pink-400 rounded-full shadow-glow-purple animate-glow-pulse animation-delay-2000"></div>
                  </div>
                </div>
              </div>

              {/* Brand Identity */}
              <div className="flex flex-col">
                <h1 className="text-3xl font-black bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent tracking-tight leading-none text-gradient">
                  AIthentiq
                </h1>
                <div className="flex items-center gap-1.5 mt-2">
                  <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-2.5 py-0.5 rounded-md text-xs font-bold shadow-glow border border-blue-400/30">
                    AI
                  </div>
                  <span className="text-xs text-blue-400 font-medium">+</span>
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2.5 py-0.5 rounded-md text-xs font-bold shadow-glow-purple border border-purple-400/30">
                    Authentic
                  </div>
                  <span className="text-xs text-purple-400 font-medium">+</span>
                  <div className="bg-gradient-to-r from-indigo-500 to-blue-500 text-white px-2.5 py-0.5 rounded-md text-xs font-bold shadow-glow border border-indigo-400/30">
                    IQ
                  </div>
                </div>
              </div>
            </div>

            <div className="hidden md:flex items-center space-x-6">
              <a href="#features" className="relative group px-4 py-2 rounded-lg hover:bg-blue-500/10 transition-all duration-300">
                <span className="text-gray-700 hover:text-blue-600 font-semibold text-base transition-colors duration-300">Features</span>
                <div className="absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left shadow-glow"></div>
              </a>
              <a href="#how-it-works" className="relative group px-4 py-2 rounded-lg hover:bg-purple-500/10 transition-all duration-300">
                <span className="text-gray-700 hover:text-purple-600 font-semibold text-base transition-colors duration-300">How it Works</span>
                <div className="absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left shadow-glow-purple"></div>
              </a>
              <a href="#contact" className="relative group px-4 py-2 rounded-lg hover:bg-blue-500/10 transition-all duration-300">
                <span className="text-gray-700 hover:text-blue-600 font-semibold text-base transition-colors duration-300">Contact</span>
                <div className="absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left shadow-glow"></div>
              </a>

              <div className="w-px h-6 bg-blue-500/30"></div>

              {session ? (
                <Link
                  href="/dashboard"
                  className="relative overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-2.5 rounded-xl font-semibold text-sm shadow-glow hover:shadow-2xl transition-all duration-300 group border border-blue-400/30"
                >
                  <span className="relative z-10 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                    Dashboard
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Link>
              ) : (
                <Link
                  href="/auth/signup"
                  className="relative overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-2.5 rounded-xl font-semibold text-sm shadow-glow hover:shadow-2xl transition-all duration-300 group border border-blue-400/30"
                >
                  <span className="relative z-10 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd"/>
                    </svg>
                    Get Started
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Link>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600 transition-colors duration-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {isMobileMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </nav>

          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden mt-4 bg-white/95 backdrop-blur-lg rounded-2xl px-6 py-4 shadow-xl border border-blue-500/20">
              <div className="space-y-4">
                <a
                  href="#features"
                  className="block text-gray-700 hover:text-blue-600 font-semibold transition-colors duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Features
                </a>
                <a
                  href="#how-it-works"
                  className="block text-gray-700 hover:text-purple-600 font-semibold transition-colors duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  How it Works
                </a>
                <a
                  href="#contact"
                  className="block text-gray-700 hover:text-blue-600 font-semibold transition-colors duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Contact
                </a>
                <div className="pt-4 border-t border-gray-200">
                  {session ? (
                    <Link
                      href="/dashboard"
                      className="block w-full text-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-200"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                  ) : (
                    <Link
                      href="/auth/signup"
                      className="block w-full text-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-200"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Get Started
                    </Link>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Hero Content */}
          <div className="text-center max-w-5xl mx-auto">
            <div className="mb-12">
              <h1 className="text-5xl md:text-7xl font-black text-gray-900 leading-tight mb-8 animate-fade-in">
                Transform Your Data into
                <br />
                <div className="mt-4"></div>
                <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent text-gradient animate-glow-pulse">
                  Intelligent Insights
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto font-light animate-slide-up">
                The most advanced AI-powered data analysis platform. Ask questions in natural language,
                get instant visualisations, and discover insights with enterprise-grade trust scoring.
              </p>
            </div>

            {/* Key Value Props */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 max-w-4xl mx-auto">
              {/* Lightning Fast */}
              <div className="group relative bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-2xl border-2 border-blue-500/30 hover:border-blue-400/70 hover:shadow-glow transition-all duration-500 hover:scale-105">
                {/* Enhanced gradient border effect on hover */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-blue-600/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                <div className="relative z-10">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 mx-auto shadow-glow group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
                    <span className="text-white text-lg">⚡</span>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-2 text-lg text-center group-hover:text-blue-600 transition-colors duration-300">Lightning Fast</h3>
                  <p className="text-gray-600 text-sm leading-relaxed text-center group-hover:text-gray-700 transition-colors duration-300">Get answers in seconds with our optimised AI engine</p>
                </div>
              </div>

              {/* Trust Scoring */}
              <div className="group relative bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-2xl border-2 border-purple-500/30 hover:border-purple-400/70 hover:shadow-glow-purple transition-all duration-500 hover:scale-105">
                {/* Enhanced gradient border effect on hover */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-purple-600/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                <div className="relative z-10">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 mx-auto shadow-glow-purple group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
                    <span className="text-white text-lg">🎯</span>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-2 text-lg text-center group-hover:text-purple-600 transition-colors duration-300">Trust Scoring</h3>
                  <p className="text-gray-600 text-sm leading-relaxed text-center group-hover:text-gray-700 transition-colors duration-300">Color-coded reliability scores for every AI response</p>
                </div>
              </div>

              {/* Enterprise Security */}
              <div className="group relative bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-2xl border-2 border-indigo-500/30 hover:border-indigo-400/70 hover:shadow-glow transition-all duration-500 hover:scale-105">
                {/* Enhanced gradient border effect on hover */}
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-blue-500/20 to-indigo-600/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                <div className="relative z-10">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 mx-auto shadow-glow group-hover:shadow-2xl group-hover:scale-110 transition-all duration-300">
                    <span className="text-white text-lg">🔒</span>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-2 text-lg text-center group-hover:text-indigo-600 transition-colors duration-300">Enterprise Security</h3>
                  <p className="text-gray-600 text-sm leading-relaxed text-center group-hover:text-gray-700 transition-colors duration-300">Bank-level encryption and privacy protection</p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              {session ? (
                <Link
                  href="/dashboard"
                  className="group bg-gradient-to-r from-purple-500 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-glow-purple transform hover:scale-[1.02] transition-all duration-300 min-w-[200px] border border-purple-400/30"
                >
                  🚀 Open RAG Chat
                </Link>
              ) : (
                <Link
                  href="/auth/signup"
                  className="group bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-glow transform hover:scale-[1.02] transition-all duration-300 min-w-[200px] border border-blue-400/30"
                >
                  🚀 Start Free Trial
                </Link>
              )}
              <a
                href="#features"
                className="border-2 border-indigo-400 text-white bg-gradient-to-r from-indigo-400 to-purple-500 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gradient-to-r hover:from-indigo-500 hover:to-purple-600 hover:border-indigo-300 hover:shadow-glow transition-all duration-300 min-w-[200px] backdrop-blur-sm shadow-lg"
              >
                📊 View Demo
              </a>
            </div>

            {/* Real Social Proof */}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-12 text-gray-600">
              <div className="flex items-center space-x-3">
                <div className="flex -space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full border-2 border-white shadow-glow"></div>
                  <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full border-2 border-white shadow-glow-purple"></div>
                  <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-full border-2 border-white shadow-glow"></div>
                </div>
                <span className="font-semibold text-gray-900">Growing User Base</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-500 font-bold">✓</span>
                <span className="font-semibold text-gray-900">No Credit Card Required</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-blue-500 font-bold">🚀</span>
                <span className="font-semibold text-gray-900">Free to Start</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-12 px-6 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-20 w-64 h-64 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-float-delayed"></div>
          <div className="bg-grid-pattern opacity-10"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-8">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 text-gradient">
              Advanced AI Features
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Cutting-edge technology designed to make data analysis intuitive, reliable, and actionable for modern businesses
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* RAG Chat Feature */}
            <div className="group bg-gradient-to-br from-white/90 to-blue-50/90 p-8 rounded-2xl hover:shadow-glow transition-all duration-300 border border-blue-500/30 backdrop-blur-sm">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-glow">
                <span className="text-white text-2xl">💬</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">RAG Chat with Memory</h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Engage in continuous conversations with your documents. Our AI remembers context
                and builds on previous discussions for deeper insights and intelligent follow-up questions.
              </p>
            </div>

            {/* Trust Score Feature */}
            <div className="group bg-gradient-to-br from-white/90 to-purple-50/90 p-8 rounded-2xl hover:shadow-glow-purple transition-all duration-300 border border-purple-500/30 backdrop-blur-sm">
              <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-glow-purple">
                <span className="text-white text-2xl">🎯</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">Advanced Trust Scoring</h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Every AI response includes intelligent trust scoring: <span className="text-green-600 font-semibold">Green (80-100%)</span> for high reliability,
                <span className="text-yellow-600 font-semibold"> Yellow (60-79%)</span> for moderate, <span className="text-red-600 font-semibold">Red (&lt;60%)</span> for low reliability.
              </p>
            </div>

            {/* Document Intelligence */}
            <div className="group bg-gradient-to-br from-white/90 to-green-50/90 p-8 rounded-2xl hover:shadow-glow transition-all duration-300 border border-green-500/30 backdrop-blur-sm">
              <div className="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-glow">
                <span className="text-white text-2xl">📄</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors duration-300">Multi-Format Support</h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Process PDF, DOCX, TXT, MD, CSV, Excel, and image files with advanced OCR.
                Extract insights from any document format with enterprise-grade accuracy.
              </p>
            </div>

            {/* Performance */}
            <div className="group bg-gradient-to-br from-white/90 to-yellow-50/90 p-8 rounded-2xl hover:shadow-glow transition-all duration-300 border border-yellow-500/30 backdrop-blur-sm">
              <div className="w-14 h-14 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-glow">
                <span className="text-white text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-yellow-600 transition-colors duration-300">Lightning Performance</h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Get results in seconds with our optimised AI engine. Real-time processing
                with response time tracking and performance analytics built-in.
              </p>
            </div>

            {/* Security */}
            <div className="group bg-gradient-to-br from-white/90 to-red-50/90 p-8 rounded-2xl hover:shadow-glow transition-all duration-300 border border-red-500/30 backdrop-blur-sm">
              <div className="w-14 h-14 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-glow-pink">
                <span className="text-white text-2xl">🔒</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-red-600 transition-colors duration-300">Enterprise Security</h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Bank-level encryption, user isolation, and privacy controls. Your data stays secure
                with enterprise-grade protection and compliance standards.
              </p>
            </div>

            {/* Analytics */}
            <div className="group bg-gradient-to-br from-white/90 to-indigo-50/90 p-8 rounded-2xl hover:shadow-glow transition-all duration-300 border border-indigo-500/30 backdrop-blur-sm">
              <div className="w-14 h-14 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-glow">
                <span className="text-white text-2xl">📊</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-indigo-600 transition-colors duration-300">Smart Analytics</h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Transform raw data into actionable business intelligence. Advanced visualisations,
                trend analysis, and predictive insights for data-driven decisions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-12 px-6 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-40 left-40 w-72 h-72 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl animate-float-delayed"></div>
          <div className="absolute bottom-40 right-40 w-72 h-72 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-float"></div>
          <div className="bg-grid-pattern opacity-10"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 text-gradient">
              How AIthentiq Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Get started in minutes with our intuitive 3-step process designed for both technical and non-technical users
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {/* Step 1 */}
            <div className="text-center group">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-br from-cyan-500 to-blue-600 text-white rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto shadow-glow group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110 border border-cyan-400/30 animate-holographic-glow">
                  1
                </div>
                <div className="absolute -inset-4 bg-gradient-to-br from-cyan-400/20 to-blue-600/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-cyan-600 transition-colors duration-300">Upload Your Data</h3>
              <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                Simply drag and drop your files - CSV, Excel, PDF, DOCX, images, or any document format.
                Our AI automatically understands your data structure and prepares it for intelligent analysis.
              </p>
              <div className="mt-6 flex justify-center space-x-2">
                <span className="px-3 py-1 bg-cyan-500/20 text-cyan-400 rounded-full text-sm font-medium border border-cyan-500/30">CSV</span>
                <span className="px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm font-medium border border-blue-500/30">PDF</span>
                <span className="px-3 py-1 bg-purple-500/20 text-purple-400 rounded-full text-sm font-medium border border-purple-500/30">Excel</span>
                <span className="px-3 py-1 bg-pink-500/20 text-pink-400 rounded-full text-sm font-medium border border-pink-500/30">Images</span>
              </div>
            </div>

            {/* Step 2 */}
            <div className="text-center group">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  2
                </div>
                <div className="absolute -inset-4 bg-gradient-to-br from-purple-400/20 to-purple-600/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Ask Questions</h3>
              <p className="text-gray-600 leading-relaxed">
                Type your questions in natural language. "What are my top selling products?"
                or "Show me customer trends by region" - our advanced AI understands complex queries and context.
              </p>
              <div className="mt-6 bg-gray-100 rounded-lg p-4 text-left">
                <p className="text-sm text-gray-700 italic">"What were the sales trends in Q4 2024?"</p>
                <p className="text-sm text-gray-700 italic mt-2">"Show me customer satisfaction by region"</p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="text-center group">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  3
                </div>
                <div className="absolute -inset-4 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Get Intelligent Insights</h3>
              <p className="text-gray-600 leading-relaxed">
                Receive instant answers with beautiful visualisations, trust scores, and detailed analysis.
                Every response includes reliability indicators and actionable insights for confident decision-making.
              </p>
              <div className="mt-6 flex justify-center space-x-2">
                <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">Charts</span>
                <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">Trust Score</span>
                <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">Sources</span>
              </div>
            </div>
          </div>

          {/* Process Flow Visualization */}
          <div className="mt-16 hidden md:block">
            <div className="flex items-center justify-center space-x-8">
              <div className="flex items-center space-x-4">
                <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                <div className="w-16 h-1 bg-gradient-to-r from-purple-500 to-green-500"></div>
                <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-center mt-4">
              <p className="text-gray-500 text-sm">Upload → Analyse → Insights</p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-12 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Trusted Across Industries
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              AIthentiq adapts to your specific industry needs with intelligent data processing and analysis capabilities
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Sales & Marketing */}
            <div className="group p-8 rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100/50 hover:shadow-xl transition-all duration-300 border border-blue-100">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Sales & Marketing</h3>
              <p className="text-gray-600 leading-relaxed">Analyse customer behavior, track campaign performance, optimise conversion rates, and identify market opportunities with AI-powered insights.</p>
            </div>

            {/* Finance & Accounting */}
            <div className="group p-8 rounded-2xl bg-gradient-to-br from-green-50 to-green-100/50 hover:shadow-xl transition-all duration-300 border border-green-100">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Finance & Accounting</h3>
              <p className="text-gray-600 leading-relaxed">Monitor financial performance, Analyse spending patterns, forecast budgets, and ensure compliance with intelligent financial analytics.</p>
            </div>

            {/* Operations & Logistics */}
            <div className="group p-8 rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100/50 hover:shadow-xl transition-all duration-300 border border-purple-100">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Operations & Logistics</h3>
              <p className="text-gray-600 leading-relaxed">Optimise supply chains, track inventory levels, improve operational efficiency, and reduce costs with data-driven insights.</p>
            </div>

            {/* Human Resources */}
            <div className="group p-8 rounded-2xl bg-gradient-to-br from-yellow-50 to-yellow-100/50 hover:shadow-xl transition-all duration-300 border border-yellow-100">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Human Resources</h3>
              <p className="text-gray-600 leading-relaxed">Analyse employee performance, track recruitment metrics, optimise workforce planning, and improve employee satisfaction.</p>
            </div>

            {/* Healthcare & Research */}
            <div className="group p-8 rounded-2xl bg-gradient-to-br from-red-50 to-red-100/50 hover:shadow-xl transition-all duration-300 border border-red-100">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Healthcare & Research</h3>
              <p className="text-gray-600 leading-relaxed">Analyse patient data, track treatment outcomes, support evidence-based decisions, and accelerate medical research.</p>
            </div>

            {/* Education & Training */}
            <div className="group p-8 rounded-2xl bg-gradient-to-br from-indigo-50 to-indigo-100/50 hover:shadow-xl transition-all duration-300 border border-indigo-100">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Education & Training</h3>
              <p className="text-gray-600 leading-relaxed">Track student progress, analyse learning outcomes, optimise educational programs, and personalise learning experiences.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-12 px-6 bg-gradient-to-br from-gray-50 to-blue-50/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Get in Touch
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Ready to transform your data analysis? Connect with us through any of these channels
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Email */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Email</h3>
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 font-medium">
                <EMAIL>
              </a>
            </div>

            {/* Instagram */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Instagram</h3>
              <a href="https://www.instagram.com/teamaithentiq/" target="_blank" rel="noopener noreferrer" className="text-pink-600 hover:text-pink-700 font-medium">
                @teamaithentiq
              </a>
            </div>

            {/* X (Twitter) */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">X (Twitter)</h3>
              <a href="https://x.com/aithentiq" target="_blank" rel="noopener noreferrer" className="text-gray-800 hover:text-gray-900 font-medium">
                @aithentiq
              </a>
            </div>

            {/* Pinterest */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Pinterest</h3>
              <a href="https://www.pinterest.com/teamaithentiq/?actingBusinessId=867083871915896070" target="_blank" rel="noopener noreferrer" className="text-red-600 hover:text-red-700 font-medium">
                @teamaithentiq
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 px-6 bg-gradient-to-br from-blue-600 to-purple-700">
        <div className="max-w-5xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-8">
            Ready to Transform Your Data?
          </h2>
          <p className="text-xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join the growing community of professionals making smarter decisions with AIthentiq.
            Start your intelligent data journey today - no credit card required.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            {session ? (
              <Link
                href="/dashboard"
                className="bg-white text-blue-600 px-10 py-4 rounded-xl font-bold text-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 min-w-[220px]"
              >
                🚀 Open RAG Chat
              </Link>
            ) : (
              <Link
                href="/auth/signup"
                className="bg-white text-blue-600 px-10 py-4 rounded-xl font-bold text-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 min-w-[220px]"
              >
                🚀 Start Free Trial
              </Link>
            )}
            <a
              href="mailto:<EMAIL>"
              className="border-2 border-white text-white px-10 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300 min-w-[220px]"
            >
              📧 Contact Sales
            </a>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="group">
              <div className="text-4xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">🚀</div>
              <div className="text-blue-200 font-medium">Growing Fast</div>
            </div>
            <div className="group">
              <div className="text-4xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">⚡</div>
              <div className="text-blue-200 font-medium">Lightning Speed</div>
            </div>
            <div className="group">
              <div className="text-4xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">🔒</div>
              <div className="text-blue-200 font-medium">Enterprise Security</div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-6">
                <img
                  src={`/AIthentiq_Logo.png?v=${Date.now()}`}
                  alt="AIthentIQ Logo"
                  className="h-10 w-16 mr-4 rounded-lg object-contain"
                />
                <div>
                  <h3 className="text-2xl font-bold">AIthentIQ</h3>
                  <p className="text-gray-400">AI + Authentic + IQ</p>
                </div>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Empowering businesses to make data-driven decisions through natural language
                interfaces and AI-powered analytics.
              </p>
              <div className="space-y-3">
                <h5 className="text-sm font-semibold text-white mb-2">Connect With Us</h5>
                <div className="flex flex-col space-y-2">
                  <a href="mailto:<EMAIL>" className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                    </svg>
                    <span className="text-sm"><EMAIL></span>
                  </a>
                  <a href="https://www.instagram.com/teamaithentiq/" target="_blank" rel="noopener noreferrer" className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                    <span className="text-sm">@teamaithentiq</span>
                  </a>
                  <a href="https://x.com/aithentiq" target="_blank" rel="noopener noreferrer" className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                    <span className="text-sm">@aithentiq</span>
                  </a>
                  <a href="https://www.pinterest.com/teamaithentiq/?actingBusinessId=867083871915896070" target="_blank" rel="noopener noreferrer" className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                    </svg>
                    <span className="text-sm">@teamaithentiq</span>
                  </a>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/dashboard" className="hover:text-white transition-colors">RAG Chat</a></li>
                <li><a href="#features" className="hover:text-white transition-colors">Features</a></li>
                <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                <li><a href="/admin" className="hover:text-white transition-colors">Analytics</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Status</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2025 AIthentIQ. All rights reserved. | Privacy Policy | Terms of Service</p>
          </div>
        </div>
      </footer>
    </div>
  );
}