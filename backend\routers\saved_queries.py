"""
Saved Queries API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, Head<PERSON>
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from database import get_db
import models

router = APIRouter(prefix="/api/v1/saved-queries", tags=["saved-queries"])



# Request/Response Models
class SavedQueryCreate(BaseModel):
    question: str
    answer: str
    dataset_id: int
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[str] = None
    trust_score: Optional[float] = None
    processing_time: Optional[int] = None

class SavedQueryResponse(BaseModel):
    id: int
    question: str
    answer: str
    dataset_id: int
    dataset_name: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[str] = None
    is_favorite: bool
    trust_score: Optional[float] = None
    processing_time: Optional[int] = None
    created_at: str
    updated_at: str

class SavedQueryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[str] = None
    is_favorite: Optional[bool] = None



# Helper function to get user ID from API key
def get_user_id_from_api_key(x_api_key: str = Header(...)) -> str:
    from database import get_db
    from models import ApiKey
    
    db = next(get_db())
    try:
        api_key_record = db.query(ApiKey).filter(ApiKey.key == x_api_key, ApiKey.is_active == True).first()
        if api_key_record:
            return api_key_record.user_id
        else:
            raise HTTPException(status_code=401, detail="Invalid API key")
    finally:
        db.close()

@router.post("/", response_model=Dict[str, Any])
async def save_query(
    request: SavedQueryCreate,
    user_id: str = Depends(get_user_id_from_api_key),
    db: Session = Depends(get_db)
):
    """Save a query permanently"""
    try:
        # First create the query record
        query = models.Query(
            user_id=user_id,
            dataset_id=request.dataset_id,
            question=request.question,
            answer=request.answer,
            query_name=request.name,
            processing_time=request.processing_time
        )
        db.add(query)
        db.commit()
        db.refresh(query)

        # Then create the saved query record
        saved_query = models.SavedQuery(
            user_id=user_id,
            query_id=query.id,
            name=request.name or request.question[:50] + "...",
            description=request.description,
            tags=request.tags,
            is_favorite=False
        )
        db.add(saved_query)
        db.commit()

        return {
            "success": True,
            "message": "Query saved successfully",
            "saved_query_id": saved_query.id,
            "query_id": query.id
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[SavedQueryResponse])
async def get_saved_queries(
    limit: int = 50,
    user_id: str = Depends(get_user_id_from_api_key),
    db: Session = Depends(get_db)
):
    """Get all saved queries for the current user"""
    try:
        # Join saved_queries with queries and datasets
        saved_queries = db.query(models.SavedQuery).filter(
            models.SavedQuery.user_id == user_id
        ).order_by(models.SavedQuery.created_at.desc()).limit(limit).all()

        response = []
        for sq in saved_queries:
            # Get the associated query
            query = db.query(models.Query).filter(models.Query.id == sq.query_id).first()
            if not query:
                continue
                
            # Get dataset name
            dataset = db.query(models.Dataset).filter(models.Dataset.id == query.dataset_id).first()
            dataset_name = dataset.name if dataset else f"Dataset {query.dataset_id}"

            response.append(SavedQueryResponse(
                id=sq.id,
                question=query.question,
                answer=query.answer,
                dataset_id=query.dataset_id,
                dataset_name=dataset_name,
                name=sq.name,
                description=sq.description,
                tags=sq.tags,
                is_favorite=sq.is_favorite,
                trust_score=None,  # Add if you store this
                processing_time=query.processing_time,
                created_at=sq.created_at.isoformat() if sq.created_at else datetime.now().isoformat(),
                updated_at=sq.updated_at.isoformat() if sq.updated_at else datetime.now().isoformat()
            ))

        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.delete("/{saved_query_id}")
async def delete_saved_query(
    saved_query_id: int,
    user_id: str = Depends(get_user_id_from_api_key),
    db: Session = Depends(get_db)
):
    """Delete a saved query"""
    try:
        saved_query = db.query(models.SavedQuery).filter(
            models.SavedQuery.id == saved_query_id,
            models.SavedQuery.user_id == user_id
        ).first()
        
        if not saved_query:
            raise HTTPException(status_code=404, detail="Saved query not found")
        
        db.delete(saved_query)
        db.commit()
        
        return {"message": "Saved query deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{saved_query_id}")
async def update_saved_query(
    saved_query_id: int,
    request: SavedQueryUpdate,
    user_id: str = Depends(get_user_id_from_api_key),
    db: Session = Depends(get_db)
):
    """Update a saved query"""
    try:
        saved_query = db.query(models.SavedQuery).filter(
            models.SavedQuery.id == saved_query_id,
            models.SavedQuery.user_id == user_id
        ).first()
        
        if not saved_query:
            raise HTTPException(status_code=404, detail="Saved query not found")
        
        if request.name is not None:
            saved_query.name = request.name
        if request.description is not None:
            saved_query.description = request.description
        if request.tags is not None:
            saved_query.tags = request.tags
        if request.is_favorite is not None:
            saved_query.is_favorite = request.is_favorite
            
        saved_query.updated_at = datetime.now()
        db.commit()
        
        return {"message": "Saved query updated successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
