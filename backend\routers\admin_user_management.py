"""
Admin User Management API Router
Handles user creation, editing, deletion, and permission management
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, EmailStr
import logging
from datetime import datetime
import uuid

from database import get_db
from middleware.tenant_isolation import get_current_tenant_id
from middleware.auth import get_current_user_id
from middleware.rbac_complete import require_permission, Permission
from models_multitenant import User, UserRole, UserPermission

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/admin", tags=["Admin User Management"])

# Pydantic models for API

class UserPermissions(BaseModel):
    """User permissions model"""
    can_upload_datasets: bool = True
    can_create_queries: bool = True
    can_manage_connectors: bool = False
    can_view_analytics: bool = False
    can_manage_users: bool = False

class CreateUserRequest(BaseModel):
    """Request to create a new user"""
    name: str = Field(..., description="User's full name")
    email: EmailStr = Field(..., description="User's email address")
    role: str = Field(..., description="User role (admin, user, viewer)")
    permissions: UserPermissions = Field(default_factory=UserPermissions)
    data_sources_access: List[str] = Field(default=[], description="Allowed data source types")
    send_invitation: bool = Field(default=True, description="Send invitation email")

class UpdateUserRequest(BaseModel):
    """Request to update user information"""
    name: Optional[str] = Field(default=None, description="User's full name")
    email: Optional[EmailStr] = Field(default=None, description="User's email address")
    role: Optional[str] = Field(default=None, description="User role")
    permissions: Optional[UserPermissions] = Field(default=None)
    data_sources_access: Optional[List[str]] = Field(default=None)
    status: Optional[str] = Field(default=None, description="User status (active, suspended, inactive)")

class UserStatusUpdate(BaseModel):
    """Request to update user status"""
    status: str = Field(..., description="New user status")

@router.get("/users")
async def list_users(
    role: Optional[str] = None,
    status: Optional[str] = None,
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_USERS)),
    db: Session = Depends(get_db)
):
    """
    List all users in the tenant with optional filtering
    
    Admin-only endpoint to view all users
    """
    try:
        query = db.query(User).filter(User.tenant_id == tenant_id)
        
        # Apply filters
        if role:
            query = query.filter(User.role == role)
        
        if status:
            query = query.filter(User.status == status)
        
        users = query.order_by(User.created_at.desc()).all()
        
        return {
            "users": [
                {
                    "id": user.id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.role,
                    "status": user.status,
                    "created_at": user.created_at.isoformat(),
                    "last_login": user.last_login.isoformat() if user.last_login else None,
                    "email_verified": user.email_verified,
                    "permissions": {
                        "can_upload_datasets": user.can_upload_datasets,
                        "can_create_queries": user.can_create_queries,
                        "can_manage_connectors": user.can_manage_connectors,
                        "can_view_analytics": user.can_view_analytics,
                        "can_manage_users": user.can_manage_users
                    },
                    "data_sources_access": user.data_sources_access or [],
                    "subscription_status": user.subscription_status or "free"
                }
                for user in users
            ],
            "total": len(users),
            "filters": {
                "role": role,
                "status": status
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to list users: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list users: {str(e)}"
        )

@router.get("/users/{user_id}")
async def get_user(
    user_id: str,
    tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_USERS)),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific user
    """
    try:
        user = db.query(User).filter(
            User.id == user_id,
            User.tenant_id == tenant_id
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=404,
                detail="User not found"
            )
        
        return {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "role": user.role,
            "status": user.status,
            "created_at": user.created_at.isoformat(),
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "email_verified": user.email_verified,
            "permissions": {
                "can_upload_datasets": user.can_upload_datasets,
                "can_create_queries": user.can_create_queries,
                "can_manage_connectors": user.can_manage_connectors,
                "can_view_analytics": user.can_view_analytics,
                "can_manage_users": user.can_manage_users
            },
            "data_sources_access": user.data_sources_access or [],
            "subscription_status": user.subscription_status or "free",
            "profile_data": user.profile_data or {}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user: {str(e)}"
        )

@router.post("/users")
async def create_user(
    user_data: CreateUserRequest,
    background_tasks: BackgroundTasks,
    tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_USERS)),
    db: Session = Depends(get_db)
):
    """
    Create a new user
    
    Admin-only endpoint to create new users
    """
    try:
        # Check if user with email already exists
        existing_user = db.query(User).filter(
            User.email == user_data.email,
            User.tenant_id == tenant_id
        ).first()
        
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="User with this email already exists"
            )
        
        # Create new user
        new_user = User(
            id=str(uuid.uuid4()),
            tenant_id=tenant_id,
            email=user_data.email,
            name=user_data.name,
            role=user_data.role,
            status="active",
            email_verified=False,
            can_upload_datasets=user_data.permissions.can_upload_datasets,
            can_create_queries=user_data.permissions.can_create_queries,
            can_manage_connectors=user_data.permissions.can_manage_connectors,
            can_view_analytics=user_data.permissions.can_view_analytics,
            can_manage_users=user_data.permissions.can_manage_users,
            data_sources_access=user_data.data_sources_access,
            created_at=datetime.utcnow()
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # Send invitation email if requested
        if user_data.send_invitation:
            background_tasks.add_task(send_user_invitation, new_user.email, new_user.name, tenant_id)
        
        logger.info(f"Created user {new_user.id} for tenant {tenant_id}")
        
        return {
            "success": True,
            "message": "User created successfully",
            "user_id": new_user.id,
            "invitation_sent": user_data.send_invitation
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create user: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create user: {str(e)}"
        )

@router.put("/users/{user_id}")
async def update_user(
    user_id: str,
    user_data: UpdateUserRequest,
    tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_USERS)),
    db: Session = Depends(get_db)
):
    """
    Update user information
    
    Admin-only endpoint to update user details
    """
    try:
        user = db.query(User).filter(
            User.id == user_id,
            User.tenant_id == tenant_id
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=404,
                detail="User not found"
            )
        
        # Prevent self-demotion from admin
        if user_id == current_user_id and user_data.role and user_data.role != "admin":
            raise HTTPException(
                status_code=400,
                detail="Cannot change your own admin role"
            )
        
        # Update fields
        update_dict = user_data.dict(exclude_unset=True, exclude={"permissions"})
        for field, value in update_dict.items():
            if hasattr(user, field):
                setattr(user, field, value)
        
        # Update permissions if provided
        if user_data.permissions:
            user.can_upload_datasets = user_data.permissions.can_upload_datasets
            user.can_create_queries = user_data.permissions.can_create_queries
            user.can_manage_connectors = user_data.permissions.can_manage_connectors
            user.can_view_analytics = user_data.permissions.can_view_analytics
            user.can_manage_users = user_data.permissions.can_manage_users
        
        user.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(user)
        
        logger.info(f"Updated user {user_id}")
        
        return {
            "success": True,
            "message": "User updated successfully",
            "user_id": user_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update user: {str(e)}"
        )

@router.put("/users/{user_id}/status")
async def update_user_status(
    user_id: str,
    status_data: UserStatusUpdate,
    tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_USERS)),
    db: Session = Depends(get_db)
):
    """
    Update user status (activate/suspend/deactivate)
    """
    try:
        user = db.query(User).filter(
            User.id == user_id,
            User.tenant_id == tenant_id
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=404,
                detail="User not found"
            )
        
        # Prevent self-suspension
        if user_id == current_user_id:
            raise HTTPException(
                status_code=400,
                detail="Cannot change your own status"
            )
        
        user.status = status_data.status
        user.updated_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"Updated user {user_id} status to {status_data.status}")
        
        return {
            "success": True,
            "message": f"User status updated to {status_data.status}",
            "user_id": user_id,
            "new_status": status_data.status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update user status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update user status: {str(e)}"
        )

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: str,
    tenant_id: str = Depends(get_current_tenant_id),
    current_user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_USERS)),
    db: Session = Depends(get_db)
):
    """
    Delete a user
    
    Admin-only endpoint to permanently delete users
    """
    try:
        user = db.query(User).filter(
            User.id == user_id,
            User.tenant_id == tenant_id
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=404,
                detail="User not found"
            )
        
        # Prevent self-deletion
        if user_id == current_user_id:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete your own account"
            )
        
        # Delete user (CASCADE will handle related records)
        db.delete(user)
        db.commit()
        
        logger.info(f"Deleted user {user_id}")
        
        return {
            "success": True,
            "message": "User deleted successfully",
            "user_id": user_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete user {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete user: {str(e)}"
        )

async def send_user_invitation(email: str, name: str, tenant_id: str):
    """
    Send invitation email to new user
    """
    try:
        # TODO: Implement email sending logic
        logger.info(f"Sending invitation email to {email} for tenant {tenant_id}")
        # This would integrate with your email service
        pass
    except Exception as e:
        logger.error(f"Failed to send invitation email: {e}")

@router.get("/users/stats")
async def get_user_stats(
    tenant_id: str = Depends(get_current_tenant_id),
    user_id: str = Depends(get_current_user_id),
    _: None = Depends(require_permission(Permission.MANAGE_USERS)),
    db: Session = Depends(get_db)
):
    """
    Get user statistics for the tenant
    """
    try:
        total_users = db.query(User).filter(User.tenant_id == tenant_id).count()
        active_users = db.query(User).filter(
            User.tenant_id == tenant_id,
            User.status == "active"
        ).count()
        admin_users = db.query(User).filter(
            User.tenant_id == tenant_id,
            User.role == "admin"
        ).count()
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "admin_users": admin_users,
            "suspended_users": total_users - active_users,
            "user_roles": {
                "admin": admin_users,
                "user": db.query(User).filter(
                    User.tenant_id == tenant_id,
                    User.role == "user"
                ).count(),
                "viewer": db.query(User).filter(
                    User.tenant_id == tenant_id,
                    User.role == "viewer"
                ).count()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get user stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user stats: {str(e)}"
        )
