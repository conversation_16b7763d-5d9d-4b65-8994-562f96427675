"""
Professional Monitoring and Analytics API Endpoints
Provides comprehensive monitoring, observability, and quality metrics
"""

from fastapi import APIRouter, Depends, HTTPException, Query as FastAPIQuery
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timed<PERSON>ta
from pydantic import BaseModel

from database import get_db
from models import User
from middleware.api_auth import get_current_user_from_api_key
from services.monitoring_service import MonitoringService
from services.citation_service import CitationService

router = APIRouter(prefix="/api/v1/monitoring", tags=["monitoring"])

# Pydantic models for API requests/responses
class PerformanceMetrics(BaseModel):
    response_time_ms: float
    token_count: int
    query_id: int

class HealthMetricsResponse(BaseModel):
    system_health: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    dataset_performance: List[Dict[str, Any]]
    alerts: List[Dict[str, str]]

class QualityMetricsResponse(BaseModel):
    query_metrics: Dict[str, Any]
    source_quality: Dict[str, Any]
    completeness_metrics: Dict[str, Any]
    search_effectiveness: List[Dict[str, Any]]
    quality_grade: str

@router.get("/health", response_model=HealthMetricsResponse)
async def get_system_health(
    dataset_id: Optional[int] = FastAPIQuery(None),
    hours: int = FastAPIQuery(24, ge=1, le=168),  # 1 hour to 1 week
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive system health metrics
    """
    monitoring_service = MonitoringService()
    
    try:
        metrics = monitoring_service.get_system_health_metrics(
            dataset_id=dataset_id,
            hours=hours,
            db=db
        )
        
        if "error" in metrics:
            raise HTTPException(status_code=500, detail=metrics["error"])
        
        return metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get health metrics: {str(e)}")

@router.get("/quality/{dataset_id}", response_model=QualityMetricsResponse)
async def get_quality_metrics(
    dataset_id: int,
    days: int = FastAPIQuery(7, ge=1, le=90),  # 1 day to 3 months
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive quality metrics for a dataset
    """
    monitoring_service = MonitoringService()

    try:
        metrics = monitoring_service.get_quality_metrics(
            dataset_id=dataset_id,
            days=days,
            db=db
        )

        if "error" in metrics:
            raise HTTPException(status_code=500, detail=metrics["error"])

        return metrics

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get quality metrics: {str(e)}")

@router.get("/user-analytics")
async def get_user_analytics(
    days: int = FastAPIQuery(30, ge=1, le=365),  # 1 day to 1 year
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get user-specific analytics and usage patterns
    """
    monitoring_service = MonitoringService()
    
    try:
        analytics = monitoring_service.get_user_analytics(
            user_id=current_user.id,
            days=days,
            db=db
        )
        
        if "error" in analytics:
            raise HTTPException(status_code=500, detail=analytics["error"])
        
        return analytics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user analytics: {str(e)}")

@router.post("/performance")
async def track_performance(
    metrics: PerformanceMetrics,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Track performance metrics for a query
    """
    monitoring_service = MonitoringService()

    try:
        result = monitoring_service.track_query_performance(
            query_id=metrics.query_id,
            response_time_ms=metrics.response_time_ms,
            token_count=metrics.token_count,
            db=db
        )

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to track performance: {str(e)}")

@router.get("/compliance/{dataset_id}")
async def get_compliance_report(
    dataset_id: int,
    start_date: datetime,
    end_date: datetime,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Generate compliance report for regulatory requirements
    """
    # Check if user has admin access or owns the dataset
    if current_user.role != "admin":
        # Verify dataset ownership
        from models import Dataset
        dataset = db.query(Dataset).filter(
            Dataset.id == dataset_id,
            Dataset.user_id == current_user.id
        ).first()
        
        if not dataset:
            raise HTTPException(status_code=404, detail="Dataset not found or access denied")
    
    citation_service = CitationService()
    
    try:
        report = citation_service.get_compliance_report(
            dataset_id=dataset_id,
            start_date=start_date,
            end_date=end_date,
            db=db
        )
        
        if "error" in report:
            raise HTTPException(status_code=500, detail=report["error"])
        
        return report
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate compliance report: {str(e)}")

@router.get("/audit-trail/{query_id}")
async def get_audit_trail(
    query_id: int,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive audit trail for a specific query
    """
    citation_service = CitationService()
    
    try:
        # Verify query ownership or admin access
        from models import Query
        query = db.query(Query).filter(Query.id == query_id).first()
        
        if not query:
            raise HTTPException(status_code=404, detail="Query not found")
        
        if current_user.role != "admin" and query.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        audit_trail = citation_service.generate_audit_trail(
            query_id=query_id,
            db=db
        )
        
        if "error" in audit_trail:
            raise HTTPException(status_code=500, detail=audit_trail["error"])
        
        return audit_trail
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get audit trail: {str(e)}")

@router.post("/verify-citation/{attribution_id}")
async def verify_citation(
    attribution_id: int,
    verification_notes: str,
    is_accurate: bool,
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Human verification of citation accuracy
    """
    citation_service = CitationService()
    
    try:
        result = citation_service.verify_citation_accuracy(
            attribution_id=attribution_id,
            user_id=current_user.id,
            verification_notes=verification_notes,
            is_accurate=is_accurate,
            db=db
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to verify citation: {str(e)}")

@router.post("/detect-hallucination")
async def detect_hallucination(
    response_text: str,
    source_chunks: List[str],
    current_user: User = Depends(get_current_user_from_api_key)
):
    """
    Detect potential hallucination indicators in AI responses
    """
    citation_service = CitationService()
    
    try:
        indicators = citation_service.detect_hallucination_indicators(
            response_text=response_text,
            source_chunks=source_chunks
        )
        
        return indicators
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to detect hallucination: {str(e)}")

@router.get("/dashboard-metrics")
async def get_dashboard_metrics(
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive metrics for monitoring dashboard
    """
    monitoring_service = MonitoringService()
    
    try:
        # Get system health for last 24 hours
        health_metrics = monitoring_service.get_system_health_metrics(
            dataset_id=None,
            hours=24,
            db=db
        )
        
        # Get user analytics for last 7 days
        user_analytics = monitoring_service.get_user_analytics(
            user_id=current_user.id,
            days=7,
            db=db
        )
        
        return {
            "system_health": health_metrics,
            "user_analytics": user_analytics,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard metrics: {str(e)}")

@router.get("/alerts")
async def get_system_alerts(
    severity: Optional[str] = FastAPIQuery(None),  # critical, warning, info
    hours: int = FastAPIQuery(24, ge=1, le=168),
    current_user: User = Depends(get_current_user_from_api_key),
    db: Session = Depends(get_db)
):
    """
    Get system alerts and notifications
    """
    # Only admins can see system-wide alerts
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    
    monitoring_service = MonitoringService()
    
    try:
        health_metrics = monitoring_service.get_system_health_metrics(
            dataset_id=None,
            hours=hours,
            db=db
        )
        
        alerts = health_metrics.get("alerts", [])
        
        # Filter by severity if specified
        if severity:
            alerts = [alert for alert in alerts if alert.get("level") == severity]
        
        return {
            "alerts": alerts,
            "total_count": len(alerts),
            "period_hours": hours,
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get alerts: {str(e)}")
