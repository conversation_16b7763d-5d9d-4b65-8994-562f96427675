from fastapi import Request, HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import Optional

from database import get_db
import models

async def get_current_user_from_session(request: Request, db: Session = Depends(get_db)) -> Optional[models.User]:
    """
    Get current user from session/cookie (for frontend endpoints)
    """
    # For now, we'll use a simple approach with user_id in query params
    # In production, you'd want to use proper session management or JWT tokens
    
    user_id = request.query_params.get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User authentication required"
        )

    # Get user
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return user

async def verify_dataset_ownership(dataset_id: int, user_id: str, db: Session) -> models.Dataset:
    """
    Verify that a user owns a specific dataset
    """
    dataset = db.query(models.Dataset).filter(
        models.Dataset.id == dataset_id,
        models.Dataset.user_id == user_id
    ).first()
    
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dataset not found or access denied"
        )
    
    return dataset

async def verify_query_ownership(query_id: int, user_id: str, db: Session) -> models.Query:
    """
    Verify that a user owns a specific query
    """
    query = db.query(models.Query).filter(
        models.Query.id == query_id,
        models.Query.user_id == user_id
    ).first()
    
    if not query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Query not found or access denied"
        )
    
    return query

class UserAccessControl:
    """
    User access control utilities
    """
    
    @staticmethod
    def check_user_access(user: models.User, required_role: str = "user") -> bool:
        """
        Check if user has required access level
        """
        role_hierarchy = {
            "user": 1,
            "premium": 2,
            "admin": 3
        }
        
        user_level = role_hierarchy.get(user.role, 0)
        required_level = role_hierarchy.get(required_role, 1)
        
        return user_level >= required_level
    
    @staticmethod
    def check_subscription_access(user: models.User, feature: str) -> bool:
        """
        Check if user's subscription allows access to a feature
        """
        free_features = ["basic_analysis", "simple_charts", "limited_queries"]
        premium_features = ["advanced_analysis", "all_charts", "unlimited_queries", "export_data"]
        
        if user.subscription_status == "free":
            return feature in free_features
        elif user.subscription_status in ["active", "premium"]:
            return feature in free_features + premium_features
        
        return False
