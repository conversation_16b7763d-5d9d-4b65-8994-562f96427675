# Multi-stage build for AIthentIQ
FROM node:18-alpine AS frontend-builder

# Install Python for full-stack build
RUN apk add --no-cache python3 py3-pip

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/requirements.txt ./backend/

# Install root dependencies
RUN npm install

# Install frontend dependencies
WORKDIR /app/frontend
RUN npm install

# Copy frontend source and build
COPY frontend/ .
RUN npm run build

# Final stage
FROM node:18-alpine

# Install Python and system dependencies
RUN apk add --no-cache python3 py3-pip py3-venv postgresql-client

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm install --only=production

# Create Python virtual environment and install backend requirements
COPY backend/requirements.txt ./backend/
RUN python3 -m venv /app/venv && \
    /app/venv/bin/pip install --upgrade pip && \
    /app/venv/bin/pip install -r backend/requirements.txt

# Copy application source
COPY backend/ ./backend/
COPY --from=frontend-builder /app/frontend/.next ./frontend/.next
COPY --from=frontend-builder /app/frontend/public ./frontend/public
COPY --from=frontend-builder /app/frontend/package*.json ./frontend/
COPY frontend/next.config.js ./frontend/

# Install frontend production dependencies
WORKDIR /app/frontend
RUN npm install --only=production

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S aithentiq && \
    adduser -S aithentiq -u 1001

# Set ownership
RUN chown -R aithentiq:aithentiq /app
USER aithentiq

# Expose ports
EXPOSE 3000 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000 || exit 1

# Start both services
CMD ["npm", "run", "start:production"]
