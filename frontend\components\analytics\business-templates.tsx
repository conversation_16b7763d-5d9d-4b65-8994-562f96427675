'use client';

import { useState, useEffect } from 'react';
import api from '@/lib/api';
import dynamic from 'next/dynamic';

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

interface BusinessTemplatesProps {
  datasetId: number;
}

interface Template {
  id: string;
  name: string;
  description: string;
  required_columns: string[];
  use_case: string;
}

export default function BusinessTemplates({ datasetId }: BusinessTemplatesProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [columns, setColumns] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [templateConfig, setTemplateConfig] = useState({
    dateColumn: '',
    valueColumn: '',
    forecastPeriods: 30,
    frequency: ''
  });
  const [templateResults, setTemplateResults] = useState<any>(null);

  // Fetch available templates
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await api.get('/analytics/templates/list');
        setTemplates(response.data.templates);
      } catch (err: any) {
        console.error('Error fetching templates:', err);
        setError('Failed to load templates');
      }
    };

    fetchTemplates();
  }, []);

  // Fetch dataset columns
  useEffect(() => {
    if (!datasetId) return;

    const fetchDatasetColumns = async () => {
      try {
        const userId = 'demo-user-id';
        const response = await api.get(`/datasets/${userId}`);
        const datasets = response.data;
        const currentDataset = datasets.find((d: any) => d.id === datasetId);

        if (currentDataset) {
          setColumns(currentDataset.columns);
          
          // Auto-detect date and value columns
          const dateColumns = currentDataset.columns.filter((col: string) => 
            col.toLowerCase().includes('date') || 
            col.toLowerCase().includes('time') ||
            col.toLowerCase().includes('day') ||
            col.toLowerCase().includes('month') ||
            col.toLowerCase().includes('year')
          );
          
          if (dateColumns.length > 0) {
            setTemplateConfig(prev => ({ ...prev, dateColumn: dateColumns[0] }));
          }

          const numericColumns = currentDataset.columns.filter((col: string) => 
            !dateColumns.includes(col)
          );
          
          if (numericColumns.length > 0) {
            setTemplateConfig(prev => ({ ...prev, valueColumn: numericColumns[0] }));
          }
        }
      } catch (err: any) {
        console.error('Error fetching dataset columns:', err);
        setError('Failed to load dataset columns');
      }
    };

    fetchDatasetColumns();
  }, [datasetId]);

  // Apply business template
  const applyTemplate = async () => {
    if (!selectedTemplate || !templateConfig.dateColumn || !templateConfig.valueColumn) {
      setError('Please select template and configure columns');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/analytics/business-template/${datasetId}`, {
        template_type: selectedTemplate,
        date_column: templateConfig.dateColumn,
        value_column: templateConfig.valueColumn,
        forecast_periods: templateConfig.forecastPeriods,
        frequency: templateConfig.frequency || null
      });

      setTemplateResults(response.data.template_analysis);
    } catch (err: any) {
      console.error('Error applying template:', err);
      setError(err.response?.data?.detail || err.message || 'Error applying template');
    } finally {
      setLoading(false);
    }
  };

  const selectedTemplateInfo = templates.find(t => t.id === selectedTemplate);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-black mb-4">📂 Business Analysis Templates</h2>
      <p className="text-gray-600 mb-6">Choose from predefined templates for common business analytics scenarios</p>

      {/* Template Selection */}
      <div className="mb-6">
        <h3 className="text-md font-medium text-black mb-3">Select Template</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templates.map(template => (
            <div
              key={template.id}
              onClick={() => setSelectedTemplate(template.id)}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedTemplate === template.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
              }`}
            >
              <h4 className="font-medium text-black mb-2">{template.name}</h4>
              <p className="text-sm text-gray-600 mb-2">{template.description}</p>
              <div className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                {template.use_case}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Template Configuration */}
      {selectedTemplate && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-md font-medium text-black mb-3">Configure Template: {selectedTemplateInfo?.name}</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Column *</label>
              <select
                value={templateConfig.dateColumn}
                onChange={(e) => setTemplateConfig(prev => ({ ...prev, dateColumn: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select date column</option>
                {columns.map(column => (
                  <option key={column} value={column}>{column}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Value Column *</label>
              <select
                value={templateConfig.valueColumn}
                onChange={(e) => setTemplateConfig(prev => ({ ...prev, valueColumn: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select value column</option>
                {columns.map(column => (
                  <option key={column} value={column}>{column}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Forecast Periods</label>
              <input
                type="number"
                value={templateConfig.forecastPeriods}
                onChange={(e) => setTemplateConfig(prev => ({ ...prev, forecastPeriods: parseInt(e.target.value) }))}
                min={1}
                max={365}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Frequency (Optional)</label>
              <select
                value={templateConfig.frequency}
                onChange={(e) => setTemplateConfig(prev => ({ ...prev, frequency: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Auto-detect</option>
                <option value="D">Daily</option>
                <option value="W">Weekly</option>
                <option value="M">Monthly</option>
                <option value="Q">Quarterly</option>
                <option value="Y">Yearly</option>
              </select>
            </div>
          </div>

          <button
            onClick={applyTemplate}
            disabled={loading || !templateConfig.dateColumn || !templateConfig.valueColumn}
            className={`px-6 py-2 rounded-md text-sm font-medium ${
              loading || !templateConfig.dateColumn || !templateConfig.valueColumn
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? 'Analyzing...' : `Apply ${selectedTemplateInfo?.name}`}
          </button>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Template Results */}
      {templateResults && (
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-black mb-4">📊 Analysis Results</h3>
          
          {/* Key Metrics */}
          {templateResults.key_metrics && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-black mb-3">Key Metrics</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(templateResults.key_metrics).map(([key, value]: [string, any]) => (
                  <div key={key} className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                    <div className="text-sm font-medium text-blue-900 capitalize">
                      {key.replace(/_/g, ' ')}
                    </div>
                    <div className="text-lg font-bold text-blue-700">
                      {typeof value === 'number' ? 
                        (key.includes('revenue') || key.includes('total') ? 
                          `$${value.toLocaleString()}` : 
                          value.toFixed(2)
                        ) : 
                        value
                      }
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Insights */}
          {templateResults.insights && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-black mb-3">💡 Key Insights</h4>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <ul className="space-y-2">
                  {templateResults.insights.map((insight: string, index: number) => (
                    <li key={index} className="text-sm text-yellow-800 flex items-start">
                      <span className="text-yellow-600 mr-2">•</span>
                      {insight}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Recommendations */}
          {templateResults.recommendations && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-black mb-3">🎯 Recommendations</h4>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <ul className="space-y-2">
                  {templateResults.recommendations.map((rec: string, index: number) => (
                    <li key={index} className="text-sm text-green-800 flex items-start">
                      <span className="text-green-600 mr-2">✓</span>
                      {rec}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Chart Visualization */}
          {templateResults.chart_data && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-black mb-3">📈 Visualization</h4>
              <div className="w-full h-96 border border-gray-200 rounded-lg bg-white p-2">
                <Plot
                  data={templateResults.chart_data.plotly_json.data}
                  layout={{
                    ...templateResults.chart_data.plotly_json.layout,
                    autosize: true,
                    margin: { l: 50, r: 50, t: 50, b: 50 }
                  }}
                  config={{ responsive: true, displayModeBar: true }}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </div>
          )}

          {/* Export Options */}
          <div className="flex gap-2">
            <button
              onClick={() => {
                const dataStr = JSON.stringify(templateResults, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${selectedTemplate}_analysis.json`;
                link.click();
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
            >
              📄 Export JSON
            </button>
            
            {templateResults.chart_data?.image_base64 && (
              <button
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = `data:image/png;base64,${templateResults.chart_data.image_base64}`;
                  link.download = `${selectedTemplate}_chart.png`;
                  link.click();
                }}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
              >
                🖼️ Export Chart
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
