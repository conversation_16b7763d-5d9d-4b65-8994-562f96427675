import requests
import j<PERSON>

def verify_both_fixes():
    """
    Verify both the color retention and admin panel issues are fixed
    """
    print("🔧 VERIFYING FIXES FOR BOTH ISSUES")
    print("="*50)
    
    base_url = "http://localhost:8000"
    
    try:
        # Step 1: Verify backend is working
        print("\n1. 🔍 Verifying backend health...")
        response = requests.get(f"{base_url}/test")
        if response.status_code == 200:
            print("✅ Backend is healthy")
        else:
            print("❌ Backend not responding")
            return False
        
        # Step 2: Create test feedback for color retention
        print("\n2. 🎨 Setting up test data for color retention...")
        
        # Get a real query ID
        response = requests.get(f"{base_url}/queries/demo-user-id")
        if response.status_code == 200:
            queries = response.json()
            if queries:
                query_id = queries[0]['id']
                print(f"✅ Using query ID: {query_id}")
                
                # Submit positive feedback
                feedback_data = {
                    "user_id": "demo-user-id",
                    "query_id": query_id,
                    "rating": "up",
                    "comment": "Testing GREEN color retention!"
                }
                
                response = requests.post(f"{base_url}/feedback", json=feedback_data)
                if response.status_code == 200:
                    print("✅ Positive feedback submitted (should show GREEN)")
                else:
                    print("❌ Failed to submit positive feedback")
                    return False
            else:
                print("❌ No queries found")
                return False
        else:
            print("❌ Failed to get queries")
            return False
        
        # Step 3: Verify admin panel data
        print("\n3. 📊 Verifying admin panel data...")
        
        # Test admin stats
        response = requests.get(f"{base_url}/feedback/admin/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Admin stats working:")
            print(f"   Total: {stats['total_feedback']}")
            print(f"   Positive: {stats['positive_feedback']} ({stats['positive_percentage']}%)")
            print(f"   Negative: {stats['negative_feedback']} ({stats['negative_percentage']}%)")
        else:
            print("❌ Admin stats failed")
            return False
        
        # Test admin feedback list
        response = requests.get(f"{base_url}/feedback/admin/all")
        if response.status_code == 200:
            feedback_list = response.json()
            print(f"✅ Admin feedback list working:")
            print(f"   Total items: {len(feedback_list)}")
            
            if feedback_list:
                latest = feedback_list[0]
                print(f"   Latest feedback:")
                print(f"     User: {latest['user_email']}")
                print(f"     Rating: {latest['rating']}")
                print(f"     Question: {latest['question'][:50]}...")
                print(f"     Comment: {latest['comment'][:50]}...")
        else:
            print("❌ Admin feedback list failed")
            return False
        
        # Step 4: Test feedback retrieval for color retention
        print("\n4. 🔍 Testing feedback retrieval for color retention...")
        response = requests.get(f"{base_url}/feedback/query/{query_id}")
        if response.status_code == 200:
            query_feedback = response.json()
            user_feedback = next((fb for fb in query_feedback if fb['user_id'] == 'demo-user-id'), None)
            
            if user_feedback:
                print(f"✅ User feedback found:")
                print(f"   Rating: {user_feedback['rating']} (frontend should show this color)")
                print(f"   Comment: {user_feedback['comment']}")
            else:
                print("❌ User feedback not found")
                return False
        else:
            print("❌ Feedback retrieval failed")
            return False
        
        print("\n" + "="*50)
        print("🎉 BACKEND VERIFICATION COMPLETE!")
        print("="*50)
        
        print("\n📋 BACKEND STATUS:")
        print("✅ API endpoints: Working")
        print("✅ Database: Working")
        print("✅ Feedback storage: Working")
        print("✅ Admin data: Available")
        print("✅ Color data: Available")
        
        print("\n🎨 COLOR RETENTION TEST:")
        print("="*30)
        print("1. Open: http://localhost:3000/test-colors")
        print("2. Check first rating component (should load existing feedback)")
        print("3. Click thumbs up - should turn GREEN and stay green")
        print("4. Click thumbs down - should turn RED and stay red")
        print("5. Check browser console for debug messages")
        
        print("\n📊 ADMIN PANEL TEST:")
        print("="*25)
        print("1. Open: http://localhost:3000/test-admin")
        print("2. Should see statistics immediately")
        print("3. Should see feedback list with data")
        print("4. Check browser console for API calls")
        
        print("\n🔧 MAIN PAGES:")
        print("="*15)
        print("• Dashboard: http://localhost:3000/dashboard")
        print("• Admin: http://localhost:3000/admin")
        print("• Debug: http://localhost:3000/debug")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Verification failed: {e}")
        return False

if __name__ == "__main__":
    success = verify_both_fixes()
    
    if success:
        print("\n🎯 VERIFICATION COMPLETE!")
        print("Backend is working correctly. Check frontend test pages for UI issues.")
    else:
        print("\n⚠️ Verification failed. Check backend and try again.")
