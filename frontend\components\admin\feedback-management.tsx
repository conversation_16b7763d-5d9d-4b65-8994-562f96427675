'use client';

import { useState, useEffect } from 'react';
import api from '@/lib/api';

interface FeedbackItem {
  id: number;
  user_id: string;
  user_email: string;
  query_id: number;
  question: string;
  answer: string;
  rating: string;
  comment: string | null;
  created_at: string;
}

interface FeedbackStats {
  total_feedback: number;
  positive_feedback: number;
  negative_feedback: number;
  feedback_with_comments: number;
  positive_percentage: number;
  negative_percentage: number;
}

export default function FeedbackManagement() {
  const [feedback, setFeedback] = useState<FeedbackItem[]>([]);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'positive' | 'negative' | 'with_comments'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState<number | null>(null);

  useEffect(() => {
    console.log('FeedbackManagement component mounted');
    loadFeedback();
    loadStats();
  }, []);

  const loadFeedback = async () => {
    try {
      setLoading(true);
      setError('');
      console.log('Loading feedback from:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/feedback/admin/all`);

      const response = await api.get('/feedback/admin/all');
      console.log('Feedback response status:', response.status);
      console.log('Feedback response data:', response.data);

      if (response.data && Array.isArray(response.data)) {
        setFeedback(response.data);
        console.log(`Successfully loaded ${response.data.length} feedback items`);
      } else {
        console.error('Invalid response format:', response.data);
        setError('Invalid response format from server');
      }
    } catch (err: any) {
      console.error('Error loading feedback:', err);
      console.error('Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        config: err.config
      });
      setError(`Error loading feedback: ${err.response?.data?.detail || err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      console.log('Loading stats from:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/feedback/admin/stats`);
      const response = await api.get('/feedback/admin/stats');
      console.log('Stats response status:', response.status);
      console.log('Stats response data:', response.data);
      setStats(response.data);
    } catch (err: any) {
      console.error('Error loading stats:', err);
      console.error('Stats error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
    }
  };

  const exportFeedback = async () => {
    try {
      const response = await api.get('/feedback/admin/export', {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `feedback_export_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError('Error exporting feedback');
    }
  };

  const deleteFeedback = async (feedbackId: number) => {
    try {
      await api.delete(`/feedback/${feedbackId}`);
      setFeedback(prev => prev.filter(f => f.id !== feedbackId));
      setDeleteConfirm(null);
      loadStats(); // Refresh stats
    } catch (err: any) {
      setError('Error deleting feedback');
    }
  };

  const filteredFeedback = feedback.filter(item => {
    // Apply filter
    if (filter === 'positive' && item.rating !== 'up') return false;
    if (filter === 'negative' && item.rating !== 'down') return false;
    if (filter === 'with_comments' && !item.comment) return false;

    // Apply search
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        item.question.toLowerCase().includes(searchLower) ||
        item.answer.toLowerCase().includes(searchLower) ||
        item.user_email.toLowerCase().includes(searchLower) ||
        (item.comment && item.comment.toLowerCase().includes(searchLower))
      );
    }

    return true;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading feedback data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Feedback Management</h2>
        <button
          onClick={exportFeedback}
          className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export CSV
        </button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="text-2xl font-bold text-gray-900">{stats.total_feedback}</div>
            <div className="text-sm text-gray-600">Total Feedback</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="text-2xl font-bold text-green-600">{stats.positive_feedback}</div>
            <div className="text-sm text-gray-600">Positive ({stats.positive_percentage}%)</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="text-2xl font-bold text-red-600">{stats.negative_feedback}</div>
            <div className="text-sm text-gray-600">Negative ({stats.negative_percentage}%)</div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="text-2xl font-bold text-blue-600">{stats.feedback_with_comments}</div>
            <div className="text-sm text-gray-600">With Comments</div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow border">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex space-x-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-3 py-1 rounded text-sm ${filter === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              All
            </button>
            <button
              onClick={() => setFilter('positive')}
              className={`px-3 py-1 rounded text-sm ${filter === 'positive' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              Positive
            </button>
            <button
              onClick={() => setFilter('negative')}
              className={`px-3 py-1 rounded text-sm ${filter === 'negative' ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              Negative
            </button>
            <button
              onClick={() => setFilter('with_comments')}
              className={`px-3 py-1 rounded text-sm ${filter === 'with_comments' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700'}`}
            >
              With Comments
            </button>
          </div>

          <input
            type="text"
            placeholder="Search feedback..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Feedback List */}
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Feedback ({filteredFeedback.length})
          </h3>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredFeedback.map((item) => (
            <div key={item.id} className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      item.rating === 'up'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.rating === 'up' ? '👍 Positive' : '👎 Negative'}
                    </span>
                    <span className="text-sm text-gray-500">
                      {item.user_email} • {new Date(item.created_at).toLocaleDateString()}
                    </span>
                  </div>

                  <div className="mb-2">
                    <div className="text-sm font-medium text-gray-900">Question:</div>
                    <div className="text-sm text-gray-700">{item.question}</div>
                  </div>

                  <div className="mb-2">
                    <div className="text-sm font-medium text-gray-900">Answer:</div>
                    <div className="text-sm text-gray-700">{item.answer}</div>
                  </div>

                  {item.comment && (
                    <div className="mb-2">
                      <div className="text-sm font-medium text-gray-900">Comment:</div>
                      <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded">{item.comment}</div>
                    </div>
                  )}
                </div>

                <button
                  onClick={() => setDeleteConfirm(item.id)}
                  className="ml-4 text-red-600 hover:text-red-800"
                  title="Delete feedback"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          ))}

          {filteredFeedback.length === 0 && (
            <div className="p-6 text-center text-gray-500">
              No feedback found matching your criteria.
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Feedback</h3>
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete this feedback? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteFeedback(deleteConfirm)}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
