-- Advanced Analytics Database Migration Script
-- Creates tables for storing analytics results and business template outputs

-- 1. Create analytics_results table for storing statistical analysis results
CREATE TABLE IF NOT EXISTS analytics_results (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    dataset_id INTEGER NOT NULL,
    analysis_type VARCHAR NOT NULL, -- 'distribution', 'correlation', 'boxplot', 'pivot_table'
    analysis_config JSON NOT NULL, -- Configuration parameters used
    results_data JSON NOT NULL, -- Analysis results
    chart_data JSON, -- Chart visualization data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (dataset_id) REFERENCES datasets (id) ON DELETE CASCADE
);

-- 2. Create business_template_results table for storing business template analysis
CREATE TABLE IF NOT EXISTS business_template_results (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    dataset_id INTEGER NOT NULL,
    template_type VARCHAR NOT NULL, -- 'revenue_trend', 'sales_forecast', 'demand_planning'
    template_config JSON NOT NULL, -- Template configuration
    key_metrics JSON, -- Key business metrics
    insights JSON, -- Generated insights
    recommendations JSON, -- Business recommendations
    chart_data JSON, -- Visualization data
    forecast_data JSON, -- Forecasting results if applicable
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (dataset_id) REFERENCES datasets (id) ON DELETE CASCADE
);

-- 3. Create time_series_analysis_results table for storing time series analysis
CREATE TABLE IF NOT EXISTS time_series_analysis_results (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    dataset_id INTEGER NOT NULL,
    analysis_type VARCHAR NOT NULL, -- 'forecast', 'seasonality', 'trend', 'anomaly'
    date_column VARCHAR NOT NULL,
    value_column VARCHAR NOT NULL,
    frequency VARCHAR, -- 'D', 'W', 'M', 'Q', 'Y'
    method VARCHAR, -- 'arima', 'prophet', 'exponential_smoothing'
    parameters JSON, -- Method-specific parameters
    results_data JSON NOT NULL, -- Analysis results
    accuracy_metrics JSON, -- Model accuracy metrics
    chart_data JSON, -- Visualization data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (dataset_id) REFERENCES datasets (id) ON DELETE CASCADE
);

-- 4. Create analytics_exports table for tracking export activities
CREATE TABLE IF NOT EXISTS analytics_exports (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    analysis_id INTEGER, -- Reference to analytics_results or business_template_results
    analysis_type VARCHAR NOT NULL, -- 'statistical', 'business_template', 'time_series'
    export_format VARCHAR NOT NULL, -- 'json', 'csv', 'png', 'pdf'
    file_path VARCHAR, -- Path to exported file
    file_size INTEGER, -- File size in bytes
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 5. Create analytics_usage_logs table for tracking feature usage
CREATE TABLE IF NOT EXISTS analytics_usage_logs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    feature_type VARCHAR NOT NULL, -- 'statistical_analysis', 'time_series', 'business_template'
    feature_name VARCHAR NOT NULL, -- Specific feature used
    dataset_id INTEGER,
    execution_time_ms INTEGER, -- Processing time
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    parameters JSON, -- Parameters used
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (dataset_id) REFERENCES datasets (id) ON DELETE CASCADE
);

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_analytics_results_user_id ON analytics_results(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_results_dataset_id ON analytics_results(dataset_id);
CREATE INDEX IF NOT EXISTS idx_analytics_results_analysis_type ON analytics_results(analysis_type);
CREATE INDEX IF NOT EXISTS idx_analytics_results_created_at ON analytics_results(created_at);

CREATE INDEX IF NOT EXISTS idx_business_template_results_user_id ON business_template_results(user_id);
CREATE INDEX IF NOT EXISTS idx_business_template_results_dataset_id ON business_template_results(dataset_id);
CREATE INDEX IF NOT EXISTS idx_business_template_results_template_type ON business_template_results(template_type);
CREATE INDEX IF NOT EXISTS idx_business_template_results_created_at ON business_template_results(created_at);

CREATE INDEX IF NOT EXISTS idx_time_series_analysis_results_user_id ON time_series_analysis_results(user_id);
CREATE INDEX IF NOT EXISTS idx_time_series_analysis_results_dataset_id ON time_series_analysis_results(dataset_id);
CREATE INDEX IF NOT EXISTS idx_time_series_analysis_results_analysis_type ON time_series_analysis_results(analysis_type);
CREATE INDEX IF NOT EXISTS idx_time_series_analysis_results_created_at ON time_series_analysis_results(created_at);

CREATE INDEX IF NOT EXISTS idx_analytics_exports_user_id ON analytics_exports(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_exports_analysis_type ON analytics_exports(analysis_type);
CREATE INDEX IF NOT EXISTS idx_analytics_exports_created_at ON analytics_exports(created_at);

CREATE INDEX IF NOT EXISTS idx_analytics_usage_logs_user_id ON analytics_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_usage_logs_feature_type ON analytics_usage_logs(feature_type);
CREATE INDEX IF NOT EXISTS idx_analytics_usage_logs_created_at ON analytics_usage_logs(created_at);

-- 7. Create views for analytics dashboard
CREATE OR REPLACE VIEW analytics_usage_summary AS
SELECT 
    aul.user_id,
    u.name as user_name,
    u.email as user_email,
    aul.feature_type,
    COUNT(*) as usage_count,
    AVG(aul.execution_time_ms) as avg_execution_time,
    SUM(CASE WHEN aul.success THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN NOT aul.success THEN 1 ELSE 0 END) as error_count,
    MAX(aul.created_at) as last_used
FROM analytics_usage_logs aul
JOIN users u ON aul.user_id = u.id
WHERE aul.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY aul.user_id, u.name, u.email, aul.feature_type;

CREATE OR REPLACE VIEW popular_analytics_features AS
SELECT 
    feature_type,
    feature_name,
    COUNT(*) as usage_count,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(execution_time_ms) as avg_execution_time,
    SUM(CASE WHEN success THEN 1 ELSE 0 END)::FLOAT / COUNT(*) * 100 as success_rate
FROM analytics_usage_logs
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY feature_type, feature_name
ORDER BY usage_count DESC;

CREATE OR REPLACE VIEW analytics_export_summary AS
SELECT 
    ae.user_id,
    u.name as user_name,
    ae.analysis_type,
    ae.export_format,
    COUNT(*) as export_count,
    SUM(ae.download_count) as total_downloads,
    SUM(ae.file_size) as total_file_size,
    MAX(ae.created_at) as last_export
FROM analytics_exports ae
JOIN users u ON ae.user_id = u.id
WHERE ae.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY ae.user_id, u.name, ae.analysis_type, ae.export_format;

-- 8. Add sample data for testing (optional)
/*
INSERT INTO analytics_usage_logs (user_id, feature_type, feature_name, execution_time_ms, success)
VALUES 
    ('demo-user-id', 'statistical_analysis', 'distribution_analysis', 1500, true),
    ('demo-user-id', 'statistical_analysis', 'correlation_heatmap', 2200, true),
    ('demo-user-id', 'business_template', 'revenue_trend', 3500, true),
    ('demo-user-id', 'time_series', 'forecast', 4200, true);
*/

-- Commit all changes
COMMIT;

-- Display success message
SELECT 'Advanced Analytics Database Migration Completed Successfully!' as status;
