"""
🚀 ULTIMATE DATA ANALYSIS ENGINE
The world's most advanced, bulletproof data analysis system
Combines Excel functions, statistical analysis, visualization, and AI
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime
import json
from dataclasses import dataclass
from enum import Enum
import logging

# Statistical and ML imports
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score

# Visualization imports
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Excel function service
from .excel_function_service import ExcelFunctionService
from .statistical_analysis_service import StatisticalAnalysisService
from .chart_service import generate_chart


class QueryComplexity(Enum):
    """Query complexity levels"""
    SIMPLE = "simple"           # Basic aggregations: sum, count, average
    CONDITIONAL = "conditional" # Filtered operations: sum where X = Y
    ANALYTICAL = "analytical"   # Statistical analysis: correlation, regression
    ADVANCED = "advanced"       # Complex multi-step: pivot tables, forecasting
    EXCEL_FUNCTION = "excel"    # Excel-like functions: VLOOKUP, SUMIFS
    VISUALIZATION = "visual"    # Chart and graph generation


@dataclass
class AnalysisResult:
    """Comprehensive analysis result"""
    query_type: QueryComplexity
    primary_answer: str
    numerical_result: Union[float, int, Dict, List, None]
    confidence_score: float
    processing_time_ms: float
    
    # Detailed results
    filtered_data: Optional[pd.DataFrame] = None
    statistical_summary: Optional[Dict] = None
    chart_data: Optional[Dict] = None
    excel_formula_result: Optional[Any] = None
    
    # Metadata
    columns_used: List[str] = None
    rows_processed: int = 0
    filters_applied: List[str] = None
    
    # Insights and recommendations
    insights: List[str] = None
    recommendations: List[str] = None
    related_queries: List[str] = None


class UltimateDataEngine:
    """
    🚀 The Ultimate Data Analysis Engine
    
    Capabilities:
    - Complex conditional analysis (COVID-19 example)
    - Excel-like functions (VLOOKUP, SUMIFS, COUNTIFS)
    - Advanced statistics (correlation, regression, distribution)
    - Data visualization (charts, graphs, dashboards)
    - Business intelligence (KPIs, forecasting, trends)
    - Macro-like automation (multi-step workflows)
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize sub-engines
        self.excel_engine = None  # Will be initialized per dataset
        self.stats_engine = None  # Will be initialized per dataset
        
        # Pattern libraries for different query types
        self._initialize_pattern_libraries()
        
        # Excel function mappings
        self._initialize_excel_functions()
        
        # Chart type mappings
        self._initialize_chart_mappings()
    
    def _initialize_pattern_libraries(self):
        """Initialize comprehensive pattern recognition libraries"""
        
        # 🎯 CONDITIONAL ANALYSIS PATTERNS
        self.conditional_patterns = [
            # Natural language: "total cost of COVID-19", "sum revenue for premium customers"
            r'(total|sum|average|mean|count|max|min)\s+(\w+)\s+(?:of|for)\s+([A-Za-z0-9\-\s]+?)(?:\s+(?:treatment|cases?|patients?|customers?|products?|items?))?(?:\s*\?|$)',
            
            # Explicit conditions: "sum sales where region = North"
            r'(total|sum|average|mean|count|max|min)\s+(\w+)\s+where\s+(\w+)\s*([><=!]+)\s*[\'"]?([^\'"\s?]+)[\'"]?',
            
            # Excel-like conditions: "SUMIF(region, 'North', sales)"
            r'(SUMIF|COUNTIF|AVERAGEIF)\s*\(\s*(\w+)\s*,\s*[\'"]?([^\'",]+)[\'"]?\s*,\s*(\w+)\s*\)',
            
            # Complex conditions: "total sales where region = 'North' AND quarter = 'Q1'"
            r'(total|sum|average|mean|count)\s+(\w+)\s+where\s+(.+?)(?:\s*\?|$)'
        ]
        
        # 📊 STATISTICAL ANALYSIS PATTERNS
        self.statistical_patterns = [
            r'correlation\s+between\s+(\w+)\s+and\s+(\w+)',
            r'regression\s+(?:of\s+)?(\w+)\s+(?:on|against)\s+(\w+)',
            r'distribution\s+(?:of\s+)?(\w+)',
            r'outliers?\s+in\s+(\w+)',
            r'statistics\s+(?:of|for)\s+(\w+)',
            r'trend\s+(?:in|of)\s+(\w+)',
            r'forecast\s+(\w+)',
            r'predict\s+(\w+)'
        ]
        
        # 📈 VISUALIZATION PATTERNS
        self.visualization_patterns = [
            r'(?:create|generate|show|plot)\s+(bar|line|pie|scatter|histogram|box)\s+chart',
            r'(?:chart|graph|plot)\s+(?:of\s+)?(\w+)\s+(?:by|vs|against)\s+(\w+)',
            r'visualize\s+(\w+)',
            r'dashboard\s+(?:for|of)\s+(\w+)'
        ]
        
        # 🔧 EXCEL FUNCTION PATTERNS
        self.excel_patterns = [
            r'VLOOKUP\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*(\d+)\s*,?\s*(TRUE|FALSE|0|1)?\s*\)',
            r'INDEX\s*\(\s*([^,]+)\s*,\s*(\d+)\s*,?\s*(\d+)?\s*\)',
            r'MATCH\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,?\s*(0|1|-1)?\s*\)',
            r'SUMIFS?\s*\(\s*([^,]+)(?:\s*,\s*([^,]+)\s*,\s*([^,)]+))*\s*\)',
            r'COUNTIFS?\s*\(\s*([^,]+)(?:\s*,\s*([^,]+)\s*,\s*([^,)]+))*\s*\)'
        ]
    
    def _initialize_excel_functions(self):
        """Initialize Excel function mappings"""
        self.excel_function_map = {
            'VLOOKUP': self._excel_vlookup,
            'INDEX': self._excel_index,
            'MATCH': self._excel_match,
            'SUMIF': self._excel_sumif,
            'SUMIFS': self._excel_sumifs,
            'COUNTIF': self._excel_countif,
            'COUNTIFS': self._excel_countifs,
            'AVERAGEIF': self._excel_averageif,
            'AVERAGEIFS': self._excel_averageifs
        }
    
    def _initialize_chart_mappings(self):
        """Initialize chart type mappings"""
        self.chart_mappings = {
            'bar': 'bar',
            'column': 'bar',
            'line': 'line',
            'pie': 'pie',
            'scatter': 'scatter',
            'histogram': 'histogram',
            'box': 'box',
            'heatmap': 'heatmap'
        }
    
    async def analyze_query(self, df: pd.DataFrame, query: str) -> AnalysisResult:
        """
        🚀 MAIN ANALYSIS METHOD
        
        This is the bulletproof entry point that handles ANY query type
        """
        start_time = datetime.now()
        
        # Initialize engines with current dataset
        self.excel_engine = ExcelFunctionService(df)
        self.stats_engine = StatisticalAnalysisService(df)
        
        # Clean and normalize query
        query_clean = query.strip().lower()
        
        # 🧠 STEP 1: Classify query complexity and type
        query_type = self._classify_query_type(query_clean, df)
        
        # 🎯 STEP 2: Route to appropriate processing engine
        if query_type == QueryComplexity.CONDITIONAL:
            result = await self._process_conditional_query(df, query_clean, query)
        elif query_type == QueryComplexity.EXCEL_FUNCTION:
            result = await self._process_excel_function(df, query_clean, query)
        elif query_type == QueryComplexity.ANALYTICAL:
            result = await self._process_analytical_query(df, query_clean, query)
        elif query_type == QueryComplexity.VISUALIZATION:
            result = await self._process_visualization_query(df, query_clean, query)
        elif query_type == QueryComplexity.ADVANCED:
            result = await self._process_advanced_query(df, query_clean, query)
        else:
            result = await self._process_simple_query(df, query_clean, query)
        
        # 📊 STEP 3: Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        result.processing_time_ms = processing_time
        
        # 🎯 STEP 4: Generate insights and recommendations
        result.insights = self._generate_insights(result, df)
        result.recommendations = self._generate_recommendations(result, df)
        result.related_queries = self._generate_related_queries(result, query)
        
        return result
    
    def _classify_query_type(self, query: str, df: pd.DataFrame) -> QueryComplexity:
        """🧠 Intelligent query classification"""
        
        # Check for Excel functions first (highest priority)
        for pattern in self.excel_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return QueryComplexity.EXCEL_FUNCTION
        
        # Check for visualization requests
        for pattern in self.visualization_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return QueryComplexity.VISUALIZATION
        
        # Check for statistical analysis
        for pattern in self.statistical_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return QueryComplexity.ANALYTICAL
        
        # Check for conditional analysis
        for pattern in self.conditional_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                return QueryComplexity.CONDITIONAL
        
        # Check for advanced operations
        if any(word in query for word in ['pivot', 'forecast', 'predict', 'trend', 'regression']):
            return QueryComplexity.ADVANCED
        
        # Default to simple
        return QueryComplexity.SIMPLE

    async def _process_conditional_query(self, df: pd.DataFrame, query_clean: str, original_query: str) -> AnalysisResult:
        """🎯 BULLETPROOF CONDITIONAL ANALYSIS - Handles COVID-19 type queries"""

        # Parse the conditional query
        parsed = self._parse_conditional_query(query_clean, df)

        if not parsed:
            return AnalysisResult(
                query_type=QueryComplexity.CONDITIONAL,
                primary_answer="Could not parse conditional query",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

        operation = parsed['operation']
        target_column = parsed['target_column']
        condition_column = parsed['condition_column']
        condition_value = parsed['condition_value']
        condition_operator = parsed.get('condition_operator', '==')

        # Apply the filter
        try:
            if condition_operator == '==':
                # Case-insensitive partial matching for text
                if df[condition_column].dtype == 'object':
                    filtered_df = df[df[condition_column].astype(str).str.contains(condition_value, case=False, na=False)]
                else:
                    filtered_df = df[df[condition_column] == condition_value]
            elif condition_operator == '>':
                filtered_df = df[df[condition_column] > float(condition_value)]
            elif condition_operator == '<':
                filtered_df = df[df[condition_column] < float(condition_value)]
            elif condition_operator == '>=':
                filtered_df = df[df[condition_column] >= float(condition_value)]
            elif condition_operator == '<=':
                filtered_df = df[df[condition_column] <= float(condition_value)]
            elif condition_operator == '!=':
                if df[condition_column].dtype == 'object':
                    filtered_df = df[~df[condition_column].astype(str).str.contains(condition_value, case=False, na=False)]
                else:
                    filtered_df = df[df[condition_column] != condition_value]
            else:
                filtered_df = df

            # Perform the operation on filtered data
            if operation in ['total', 'sum']:
                result_value = filtered_df[target_column].sum()
            elif operation in ['average', 'mean']:
                result_value = filtered_df[target_column].mean()
            elif operation == 'count':
                result_value = len(filtered_df)
            elif operation == 'max':
                result_value = filtered_df[target_column].max()
            elif operation == 'min':
                result_value = filtered_df[target_column].min()
            else:
                result_value = filtered_df[target_column].sum()  # Default to sum

            # Format the answer
            if operation == 'count':
                primary_answer = f"{int(result_value):,} records match the condition"
            else:
                primary_answer = f"{operation.title()} {target_column} where {condition_column} contains '{condition_value}': {result_value:,.2f}" if isinstance(result_value, float) else f"{result_value:,}"

            return AnalysisResult(
                query_type=QueryComplexity.CONDITIONAL,
                primary_answer=primary_answer,
                numerical_result=float(result_value) if pd.notna(result_value) else 0.0,
                confidence_score=0.95,
                processing_time_ms=0.0,
                filtered_data=filtered_df,
                columns_used=[target_column, condition_column],
                rows_processed=len(filtered_df),
                filters_applied=[f"{condition_column} {condition_operator} {condition_value}"]
            )

        except Exception as e:
            return AnalysisResult(
                query_type=QueryComplexity.CONDITIONAL,
                primary_answer=f"Error processing conditional query: {str(e)}",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

    def _parse_conditional_query(self, query: str, df: pd.DataFrame) -> Optional[Dict]:
        """🧠 Parse conditional queries with smart column detection"""

        for pattern in self.conditional_patterns:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                groups = match.groups()

                if len(groups) >= 3:
                    operation = groups[0].lower()
                    target_col_hint = groups[1].lower()
                    condition_hint = groups[2].lower()

                    # Smart column detection
                    target_column = self._find_best_column(df, target_col_hint, prefer_numeric=True)
                    condition_column = self._find_best_column(df, condition_hint, prefer_text=True)

                    # Extract condition value
                    if len(groups) >= 5:
                        condition_operator = groups[3]
                        condition_value = groups[4]
                    else:
                        condition_operator = '=='
                        condition_value = condition_hint

                    if target_column and condition_column:
                        return {
                            'operation': operation,
                            'target_column': target_column,
                            'condition_column': condition_column,
                            'condition_value': condition_value,
                            'condition_operator': condition_operator
                        }

        return None

    def _find_best_column(self, df: pd.DataFrame, hint: str, prefer_numeric: bool = False, prefer_text: bool = False) -> Optional[str]:
        """🔍 Smart column detection with fuzzy matching"""

        # Exact match first
        for col in df.columns:
            if hint.lower() in col.lower() or col.lower() in hint.lower():
                return col

        # Priority-based matching
        if prefer_numeric:
            # Look for numeric columns with keywords
            priority_keywords = ['cost', 'price', 'amount', 'value', 'total', 'revenue', 'sales', 'salary', 'income']
            for keyword in priority_keywords:
                if keyword in hint.lower():
                    for col in df.select_dtypes(include=['number']).columns:
                        if keyword in col.lower():
                            return col

            # Fallback to first numeric column
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                return numeric_cols[0]

        if prefer_text:
            # Look for text columns that might contain the condition value
            for col in df.select_dtypes(include=['object']).columns:
                if df[col].astype(str).str.contains(hint, case=False, na=False).any():
                    return col

        # Fuzzy matching as last resort
        for col in df.columns:
            if any(word in col.lower() for word in hint.lower().split()):
                return col

        return None

    async def _process_excel_function(self, df: pd.DataFrame, query_clean: str, original_query: str) -> AnalysisResult:
        """🔧 EXCEL FUNCTION PROCESSING - VLOOKUP, SUMIFS, etc."""

        try:
            # Execute the Excel function using the Excel engine
            result_value = self.excel_engine.execute_formula(original_query)

            # Determine the function type
            function_name = original_query.split('(')[0].upper().strip('=')

            return AnalysisResult(
                query_type=QueryComplexity.EXCEL_FUNCTION,
                primary_answer=f"{function_name} result: {result_value}",
                numerical_result=result_value if isinstance(result_value, (int, float)) else None,
                confidence_score=0.90,
                processing_time_ms=0.0,
                excel_formula_result=result_value,
                columns_used=self._extract_columns_from_formula(original_query, df)
            )

        except Exception as e:
            return AnalysisResult(
                query_type=QueryComplexity.EXCEL_FUNCTION,
                primary_answer=f"Excel function error: {str(e)}",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

    async def _process_analytical_query(self, df: pd.DataFrame, query_clean: str, original_query: str) -> AnalysisResult:
        """📊 STATISTICAL ANALYSIS PROCESSING"""

        try:
            # Determine the type of statistical analysis
            if 'correlation' in query_clean:
                return await self._process_correlation(df, query_clean)
            elif 'regression' in query_clean:
                return await self._process_regression(df, query_clean)
            elif 'distribution' in query_clean:
                return await self._process_distribution(df, query_clean)
            elif 'outlier' in query_clean:
                return await self._process_outliers(df, query_clean)
            elif 'statistics' in query_clean or 'stats' in query_clean:
                return await self._process_statistics(df, query_clean)
            else:
                # Default to summary statistics
                return await self._process_statistics(df, query_clean)

        except Exception as e:
            return AnalysisResult(
                query_type=QueryComplexity.ANALYTICAL,
                primary_answer=f"Statistical analysis error: {str(e)}",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

    async def _process_correlation(self, df: pd.DataFrame, query: str) -> AnalysisResult:
        """Calculate correlation between two variables"""

        # Extract column names from query
        pattern = r'correlation\s+between\s+(\w+)\s+and\s+(\w+)'
        match = re.search(pattern, query)

        if match:
            col1_hint = match.group(1)
            col2_hint = match.group(2)

            col1 = self._find_best_column(df, col1_hint, prefer_numeric=True)
            col2 = self._find_best_column(df, col2_hint, prefer_numeric=True)

            if col1 and col2 and col1 in df.columns and col2 in df.columns:
                correlation = df[col1].corr(df[col2])

                # Interpret correlation strength
                if abs(correlation) >= 0.8:
                    strength = "very strong"
                elif abs(correlation) >= 0.6:
                    strength = "strong"
                elif abs(correlation) >= 0.4:
                    strength = "moderate"
                elif abs(correlation) >= 0.2:
                    strength = "weak"
                else:
                    strength = "very weak"

                direction = "positive" if correlation > 0 else "negative"

                return AnalysisResult(
                    query_type=QueryComplexity.ANALYTICAL,
                    primary_answer=f"Correlation between {col1} and {col2}: {correlation:.3f} ({strength} {direction} correlation)",
                    numerical_result=float(correlation),
                    confidence_score=0.95,
                    processing_time_ms=0.0,
                    statistical_summary={
                        'correlation_coefficient': correlation,
                        'strength': strength,
                        'direction': direction,
                        'column1': col1,
                        'column2': col2
                    },
                    columns_used=[col1, col2]
                )

        return AnalysisResult(
            query_type=QueryComplexity.ANALYTICAL,
            primary_answer="Could not determine columns for correlation analysis",
            numerical_result=None,
            confidence_score=0.0,
            processing_time_ms=0.0
        )

    async def _process_statistics(self, df: pd.DataFrame, query: str) -> AnalysisResult:
        """Generate comprehensive statistics"""

        numeric_cols = df.select_dtypes(include=['number']).columns

        if len(numeric_cols) == 0:
            return AnalysisResult(
                query_type=QueryComplexity.ANALYTICAL,
                primary_answer="No numeric columns found for statistical analysis",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

        # Generate summary statistics for all numeric columns
        stats_summary = {}
        for col in numeric_cols:
            data = df[col].dropna()
            if len(data) > 0:
                stats_summary[col] = {
                    'count': len(data),
                    'mean': float(data.mean()),
                    'median': float(data.median()),
                    'std': float(data.std()),
                    'min': float(data.min()),
                    'max': float(data.max()),
                    'q25': float(data.quantile(0.25)),
                    'q75': float(data.quantile(0.75))
                }

        # Create summary text
        summary_text = f"Statistical summary for {len(numeric_cols)} numeric columns: "
        summary_text += ", ".join([f"{col} (mean: {stats_summary[col]['mean']:.2f})" for col in list(stats_summary.keys())[:3]])

        return AnalysisResult(
            query_type=QueryComplexity.ANALYTICAL,
            primary_answer=summary_text,
            numerical_result=None,
            confidence_score=0.90,
            processing_time_ms=0.0,
            statistical_summary=stats_summary,
            columns_used=list(numeric_cols)
        )

    async def _process_visualization_query(self, df: pd.DataFrame, query_clean: str, original_query: str) -> AnalysisResult:
        """📈 VISUALIZATION PROCESSING"""

        # Extract chart type and columns
        chart_type = 'bar'  # Default
        for chart_name in self.chart_mappings.keys():
            if chart_name in query_clean:
                chart_type = self.chart_mappings[chart_name]
                break

        # Try to extract columns from query
        x_col = None
        y_col = None

        # Look for "X by Y" or "X vs Y" patterns
        by_pattern = r'(\w+)\s+(?:by|vs|against)\s+(\w+)'
        match = re.search(by_pattern, query_clean)
        if match:
            y_col = self._find_best_column(df, match.group(1), prefer_numeric=True)
            x_col = self._find_best_column(df, match.group(2))

        # Fallback: use first text and first numeric column
        if not x_col:
            text_cols = df.select_dtypes(include=['object']).columns
            x_col = text_cols[0] if len(text_cols) > 0 else df.columns[0]

        if not y_col:
            numeric_cols = df.select_dtypes(include=['number']).columns
            y_col = numeric_cols[0] if len(numeric_cols) > 0 else df.columns[1] if len(df.columns) > 1 else df.columns[0]

        try:
            # Generate chart using existing chart service
            chart_data = generate_chart(df, chart_type, {'x': x_col, 'y': y_col})

            return AnalysisResult(
                query_type=QueryComplexity.VISUALIZATION,
                primary_answer=f"Generated {chart_type} chart of {y_col} by {x_col}",
                numerical_result=None,
                confidence_score=0.85,
                processing_time_ms=0.0,
                chart_data=chart_data,
                columns_used=[x_col, y_col]
            )

        except Exception as e:
            return AnalysisResult(
                query_type=QueryComplexity.VISUALIZATION,
                primary_answer=f"Chart generation error: {str(e)}",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

    async def _process_advanced_query(self, df: pd.DataFrame, query_clean: str, original_query: str) -> AnalysisResult:
        """🚀 ADVANCED PROCESSING - Pivot tables, forecasting, etc."""

        if 'pivot' in query_clean:
            return await self._process_pivot_table(df, query_clean)
        elif 'forecast' in query_clean or 'predict' in query_clean:
            return await self._process_forecast(df, query_clean)
        else:
            # Default to complex aggregation
            return await self._process_simple_query(df, query_clean, original_query)

    async def _process_simple_query(self, df: pd.DataFrame, query_clean: str, original_query: str) -> AnalysisResult:
        """📊 SIMPLE QUERY PROCESSING - Basic aggregations"""

        # Extract operation and column
        operation = 'count'
        target_column = None

        for op in ['sum', 'total', 'average', 'mean', 'count', 'max', 'min']:
            if op in query_clean:
                operation = op
                break

        # Find target column
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            target_column = numeric_cols[0]

        try:
            if operation in ['sum', 'total']:
                result_value = df[target_column].sum() if target_column else len(df)
            elif operation in ['average', 'mean']:
                result_value = df[target_column].mean() if target_column else 0
            elif operation == 'count':
                result_value = len(df)
            elif operation == 'max':
                result_value = df[target_column].max() if target_column else 0
            elif operation == 'min':
                result_value = df[target_column].min() if target_column else 0
            else:
                result_value = len(df)

            return AnalysisResult(
                query_type=QueryComplexity.SIMPLE,
                primary_answer=f"{operation.title()}: {result_value:,.2f}" if isinstance(result_value, float) else f"{result_value:,}",
                numerical_result=float(result_value) if pd.notna(result_value) else 0.0,
                confidence_score=0.80,
                processing_time_ms=0.0,
                columns_used=[target_column] if target_column else [],
                rows_processed=len(df)
            )

        except Exception as e:
            return AnalysisResult(
                query_type=QueryComplexity.SIMPLE,
                primary_answer=f"Simple query error: {str(e)}",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

    def _extract_columns_from_formula(self, formula: str, df: pd.DataFrame) -> List[str]:
        """Extract column names referenced in Excel formula"""
        columns = []
        for col in df.columns:
            if col in formula:
                columns.append(col)
        return columns

    def _generate_insights(self, result: AnalysisResult, df: pd.DataFrame) -> List[str]:
        """Generate intelligent insights based on analysis results"""
        insights = []

        if result.query_type == QueryComplexity.CONDITIONAL and result.filtered_data is not None:
            total_rows = len(df)
            filtered_rows = len(result.filtered_data)
            percentage = (filtered_rows / total_rows) * 100 if total_rows > 0 else 0

            insights.append(f"📊 Filter matched {filtered_rows:,} out of {total_rows:,} rows ({percentage:.1f}%)")

            if percentage < 10:
                insights.append("⚠️ Low match rate - consider broadening filter criteria")
            elif percentage > 90:
                insights.append("ℹ️ High match rate - filter may be too broad")

        if result.numerical_result is not None:
            if isinstance(result.numerical_result, (int, float)):
                if result.numerical_result == 0:
                    insights.append("🔍 Result is zero - verify data and filter conditions")
                elif result.numerical_result < 0:
                    insights.append("📉 Negative result detected")

        return insights

    def _generate_recommendations(self, result: AnalysisResult, df: pd.DataFrame) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []

        if result.query_type == QueryComplexity.CONDITIONAL:
            recommendations.append("💡 Try visualizing this data with a chart")
            recommendations.append("🔍 Consider analyzing trends over time")

        if result.query_type == QueryComplexity.ANALYTICAL:
            recommendations.append("📈 Explore correlations with other variables")
            recommendations.append("🎯 Consider predictive modeling")

        return recommendations

    def _generate_related_queries(self, result: AnalysisResult, original_query: str) -> List[str]:
        """Generate related query suggestions"""
        related = []

        if result.columns_used:
            for col in result.columns_used[:2]:  # Limit to 2 columns
                related.append(f"Show trend of {col} over time")
                related.append(f"Find outliers in {col}")

        if result.query_type == QueryComplexity.CONDITIONAL:
            related.append("Create a chart of this filtered data")
            related.append("Compare with other categories")

        return related[:5]  # Limit to 5 suggestions

    # 🔧 EXCEL FUNCTION IMPLEMENTATIONS

    def _excel_vlookup(self, lookup_value, table_array, col_index_num, range_lookup=False):
        """VLOOKUP implementation"""
        try:
            # This would integrate with the existing Excel function service
            return self.excel_engine.excel_vlookup(lookup_value, table_array, col_index_num, range_lookup)
        except Exception as e:
            return f"VLOOKUP Error: {str(e)}"

    def _excel_sumif(self, range_col, criteria, sum_range):
        """SUMIF implementation"""
        try:
            return self.excel_engine.excel_sumif(range_col, criteria, sum_range)
        except Exception as e:
            return f"SUMIF Error: {str(e)}"

    def _excel_sumifs(self, sum_range, *criteria_pairs):
        """SUMIFS implementation"""
        try:
            return self.excel_engine.excel_sumifs(sum_range, *criteria_pairs)
        except Exception as e:
            return f"SUMIFS Error: {str(e)}"

    def _excel_countif(self, range_col, criteria):
        """COUNTIF implementation"""
        try:
            return self.excel_engine.excel_countif(range_col, criteria)
        except Exception as e:
            return f"COUNTIF Error: {str(e)}"

    def _excel_countifs(self, *criteria_pairs):
        """COUNTIFS implementation"""
        try:
            return self.excel_engine.excel_countifs(*criteria_pairs)
        except Exception as e:
            return f"COUNTIFS Error: {str(e)}"

    def _excel_averageif(self, range_col, criteria, average_range):
        """AVERAGEIF implementation"""
        try:
            return self.excel_engine.excel_averageif(range_col, criteria, average_range)
        except Exception as e:
            return f"AVERAGEIF Error: {str(e)}"

    def _excel_averageifs(self, average_range, *criteria_pairs):
        """AVERAGEIFS implementation"""
        try:
            return self.excel_engine.excel_averageifs(average_range, *criteria_pairs)
        except Exception as e:
            return f"AVERAGEIFS Error: {str(e)}"

    def _excel_index(self, array, row_num, col_num=None):
        """INDEX implementation"""
        try:
            return self.excel_engine.excel_index(array, row_num, col_num)
        except Exception as e:
            return f"INDEX Error: {str(e)}"

    def _excel_match(self, lookup_value, lookup_array, match_type=0):
        """MATCH implementation"""
        try:
            return self.excel_engine.excel_match(lookup_value, lookup_array, match_type)
        except Exception as e:
            return f"MATCH Error: {str(e)}"

    # 📊 ADVANCED PROCESSING METHODS

    async def _process_pivot_table(self, df: pd.DataFrame, query: str) -> AnalysisResult:
        """Create pivot table analysis"""
        try:
            # Use existing statistical analysis service
            pivot_result = self.stats_engine.create_pivot_table(
                index_col=df.columns[0],
                values_col=df.select_dtypes(include=['number']).columns[0] if len(df.select_dtypes(include=['number']).columns) > 0 else df.columns[1],
                aggfunc='sum'
            )

            return AnalysisResult(
                query_type=QueryComplexity.ADVANCED,
                primary_answer="Pivot table created successfully",
                numerical_result=None,
                confidence_score=0.85,
                processing_time_ms=0.0,
                statistical_summary=pivot_result
            )

        except Exception as e:
            return AnalysisResult(
                query_type=QueryComplexity.ADVANCED,
                primary_answer=f"Pivot table error: {str(e)}",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )

    async def _process_forecast(self, df: pd.DataFrame, query: str) -> AnalysisResult:
        """Simple forecasting using linear regression"""
        try:
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) == 0:
                raise ValueError("No numeric columns for forecasting")

            target_col = numeric_cols[0]
            data = df[target_col].dropna()

            if len(data) < 3:
                raise ValueError("Insufficient data for forecasting")

            # Simple linear trend
            X = np.arange(len(data)).reshape(-1, 1)
            y = data.values

            model = LinearRegression()
            model.fit(X, y)

            # Forecast next 3 periods
            future_X = np.arange(len(data), len(data) + 3).reshape(-1, 1)
            forecast = model.predict(future_X)

            return AnalysisResult(
                query_type=QueryComplexity.ADVANCED,
                primary_answer=f"Forecast for {target_col}: Next 3 values: {forecast[0]:.2f}, {forecast[1]:.2f}, {forecast[2]:.2f}",
                numerical_result=float(forecast[0]),
                confidence_score=0.75,
                processing_time_ms=0.0,
                statistical_summary={
                    'forecast_values': forecast.tolist(),
                    'r_squared': r2_score(y, model.predict(X)),
                    'trend_slope': float(model.coef_[0])
                },
                columns_used=[target_col]
            )

        except Exception as e:
            return AnalysisResult(
                query_type=QueryComplexity.ADVANCED,
                primary_answer=f"Forecasting error: {str(e)}",
                numerical_result=None,
                confidence_score=0.0,
                processing_time_ms=0.0
            )


# 🚀 GLOBAL INSTANCE
ultimate_engine = UltimateDataEngine()
