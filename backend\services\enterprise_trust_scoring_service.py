"""
Enterprise Trust Scoring Service for AIthentiq
Advanced trust scoring with multiple algorithms and confidence metrics
"""

import os
import json
import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import redis
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class TrustScoringMethod(Enum):
    MULTI_LLM_CONVERGENCE = "multi_llm_convergence"
    FAITHFULNESS_CONTEXT = "faithfulness_context"
    PRODUCTION_STANDARD = "production_standard"
    ENSEMBLE_AVERAGE = "ensemble_average"
    SEMANTIC_CONSISTENCY = "semantic_consistency"
    SOURCE_RELIABILITY = "source_reliability"

class ConfidenceLevel(Enum):
    VERY_LOW = "very_low"    # 0-20%
    LOW = "low"              # 20-40%
    MODERATE = "moderate"    # 40-60%
    HIGH = "high"            # 60-80%
    VERY_HIGH = "very_high"  # 80-100%

@dataclass
class TrustMetrics:
    model_confidence: float      # 40% weight
    source_quality: float        # 30% weight
    citation_accuracy: float     # 20% weight
    question_match: float        # 10% weight
    semantic_consistency: float
    factual_accuracy: float
    response_completeness: float

@dataclass
class TrustScoringResult:
    overall_score: float
    confidence_level: ConfidenceLevel
    method_used: str
    individual_scores: Dict[str, float]
    metrics: TrustMetrics
    reasoning: str
    processing_time_ms: float
    sources_analyzed: int
    flags: List[str]

@dataclass
class SourceReliability:
    source_id: str
    reliability_score: float
    credibility_factors: Dict[str, float]
    historical_accuracy: float
    domain_expertise: float
    recency_factor: float

class EnterpriseTrustScoringService:
    """Enterprise trust scoring with advanced algorithms"""
    
    def __init__(self):
        self.redis_client = self._init_redis()
        self.thread_pool = ThreadPoolExecutor(max_workers=6)
        
        # Trust scoring weights (configurable per tenant)
        self.default_weights = {
            "model_confidence": 0.40,
            "source_quality": 0.30,
            "citation_accuracy": 0.20,
            "question_match": 0.10
        }
        
        # Source reliability cache
        self.source_reliability_cache = {}
        
        # Performance optimization
        self.scoring_cache = {}
        self.cache_ttl = 1800  # 30 minutes
        
        # Initialize ML models for advanced scoring
        self._init_ml_models()
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """Initialize Redis for caching"""
        try:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/5")
            client = redis.from_url(redis_url, decode_responses=True)
            client.ping()
            logger.info("Trust scoring Redis cache initialized")
            return client
        except Exception as e:
            logger.warning(f"Trust scoring Redis cache unavailable: {e}")
            return None
    
    def _init_ml_models(self):
        """Initialize ML models for advanced scoring"""
        try:
            # Initialize sentence transformers for semantic analysis
            from sentence_transformers import SentenceTransformer
            self.semantic_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Semantic analysis model loaded")
        except ImportError:
            logger.warning("Sentence transformers not available")
            self.semantic_model = None
        
        # Initialize other ML models as needed
        self.factual_checker = None  # Placeholder for factual checking model
        self.bias_detector = None    # Placeholder for bias detection model
    
    async def calculate_trust_score(
        self,
        query: str,
        response: str,
        sources: List[Dict[str, Any]],
        method: TrustScoringMethod = TrustScoringMethod.ENSEMBLE_AVERAGE,
        tenant_id: str = None,
        llm_responses: List[str] = None
    ) -> TrustScoringResult:
        """Calculate comprehensive trust score"""
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(query, response, method)
            cached_result = await self._get_cached_score(cache_key)
            if cached_result:
                return cached_result
            
            # Calculate individual metrics
            metrics = await self._calculate_trust_metrics(query, response, sources, llm_responses)
            
            # Apply scoring method
            if method == TrustScoringMethod.MULTI_LLM_CONVERGENCE:
                score_result = await self._multi_llm_convergence_scoring(query, response, llm_responses, metrics)
            elif method == TrustScoringMethod.FAITHFULNESS_CONTEXT:
                score_result = await self._faithfulness_context_scoring(query, response, sources, metrics)
            elif method == TrustScoringMethod.PRODUCTION_STANDARD:
                score_result = await self._production_standard_scoring(metrics)
            elif method == TrustScoringMethod.ENSEMBLE_AVERAGE:
                score_result = await self._ensemble_average_scoring(query, response, sources, llm_responses, metrics)
            elif method == TrustScoringMethod.SEMANTIC_CONSISTENCY:
                score_result = await self._semantic_consistency_scoring(query, response, sources, metrics)
            elif method == TrustScoringMethod.SOURCE_RELIABILITY:
                score_result = await self._source_reliability_scoring(sources, metrics)
            else:
                score_result = await self._production_standard_scoring(metrics)
            
            # Determine confidence level
            confidence_level = self._determine_confidence_level(score_result["overall_score"])
            
            # Generate reasoning
            reasoning = self._generate_reasoning(score_result, metrics, method)
            
            # Detect potential issues
            flags = self._detect_flags(query, response, sources, metrics)
            
            # Create result
            result = TrustScoringResult(
                overall_score=score_result["overall_score"],
                confidence_level=confidence_level,
                method_used=method.value,
                individual_scores=score_result["individual_scores"],
                metrics=metrics,
                reasoning=reasoning,
                processing_time_ms=(time.time() - start_time) * 1000,
                sources_analyzed=len(sources),
                flags=flags
            )
            
            # Cache result
            await self._cache_score(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Trust scoring failed: {e}")
            return TrustScoringResult(
                overall_score=0.5,  # Neutral score on error
                confidence_level=ConfidenceLevel.LOW,
                method_used=method.value,
                individual_scores={},
                metrics=TrustMetrics(0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5),
                reasoning=f"Error in trust scoring: {str(e)}",
                processing_time_ms=(time.time() - start_time) * 1000,
                sources_analyzed=len(sources) if sources else 0,
                flags=["error"]
            )
    
    async def _calculate_trust_metrics(
        self,
        query: str,
        response: str,
        sources: List[Dict[str, Any]],
        llm_responses: List[str] = None
    ) -> TrustMetrics:
        """Calculate individual trust metrics"""
        
        # Model confidence (based on response characteristics)
        model_confidence = await self._calculate_model_confidence(response, llm_responses)
        
        # Source quality (based on source characteristics)
        source_quality = await self._calculate_source_quality(sources)
        
        # Citation accuracy (how well response matches sources)
        citation_accuracy = await self._calculate_citation_accuracy(response, sources)
        
        # Question match (how well response answers the question)
        question_match = await self._calculate_question_match(query, response)
        
        # Semantic consistency (consistency across sources)
        semantic_consistency = await self._calculate_semantic_consistency(sources)
        
        # Factual accuracy (fact-checking against known facts)
        factual_accuracy = await self._calculate_factual_accuracy(response)
        
        # Response completeness (how complete the answer is)
        response_completeness = await self._calculate_response_completeness(query, response)
        
        return TrustMetrics(
            model_confidence=model_confidence,
            source_quality=source_quality,
            citation_accuracy=citation_accuracy,
            question_match=question_match,
            semantic_consistency=semantic_consistency,
            factual_accuracy=factual_accuracy,
            response_completeness=response_completeness
        )
    
    async def _calculate_model_confidence(self, response: str, llm_responses: List[str] = None) -> float:
        """Calculate model confidence score"""
        confidence_factors = []
        
        # Response length factor (very short or very long responses may be less confident)
        length_factor = min(len(response) / 500, 1.0) if len(response) < 500 else max(1.0 - (len(response) - 500) / 2000, 0.5)
        confidence_factors.append(length_factor)
        
        # Uncertainty indicators
        uncertainty_phrases = [
            "i'm not sure", "i don't know", "might be", "could be", "possibly",
            "perhaps", "maybe", "uncertain", "unclear", "not confident"
        ]
        uncertainty_count = sum(1 for phrase in uncertainty_phrases if phrase in response.lower())
        uncertainty_factor = max(0.0, 1.0 - (uncertainty_count * 0.2))
        confidence_factors.append(uncertainty_factor)
        
        # Hedge words factor
        hedge_words = ["probably", "likely", "seems", "appears", "suggests", "indicates"]
        hedge_count = sum(1 for word in hedge_words if word in response.lower())
        hedge_factor = max(0.3, 1.0 - (hedge_count * 0.15))
        confidence_factors.append(hedge_factor)
        
        # Multi-LLM consistency (if available)
        if llm_responses and len(llm_responses) > 1:
            consistency_factor = await self._calculate_llm_consistency(llm_responses)
            confidence_factors.append(consistency_factor)
        
        return np.mean(confidence_factors)
    
    async def _calculate_source_quality(self, sources: List[Dict[str, Any]]) -> float:
        """Calculate source quality score"""
        if not sources:
            return 0.0
        
        quality_scores = []
        
        for source in sources:
            source_score = 0.5  # Base score
            
            # Source metadata quality
            metadata = source.get("metadata", {})
            
            # Check for important metadata fields
            if metadata.get("source_document"):
                source_score += 0.1
            if metadata.get("chunk_index") is not None:
                source_score += 0.1
            if metadata.get("word_count", 0) > 50:
                source_score += 0.1
            
            # Content quality indicators
            content = source.get("content", "")
            if len(content) > 100:  # Substantial content
                source_score += 0.1
            if content.count('.') > 2:  # Multiple sentences
                source_score += 0.1
            
            # Similarity score (how relevant to query)
            similarity_score = source.get("score", 0.0)
            source_score += similarity_score * 0.3
            
            quality_scores.append(min(source_score, 1.0))
        
        return np.mean(quality_scores)
    
    async def _calculate_citation_accuracy(self, response: str, sources: List[Dict[str, Any]]) -> float:
        """Calculate how accurately the response cites sources"""
        if not sources:
            return 0.0
        
        # Simple implementation - check for content overlap
        response_lower = response.lower()
        citation_scores = []
        
        for source in sources:
            content = source.get("content", "").lower()
            if not content:
                continue
            
            # Calculate word overlap
            response_words = set(response_lower.split())
            source_words = set(content.split())
            
            if len(source_words) == 0:
                continue
            
            overlap = len(response_words.intersection(source_words))
            overlap_ratio = overlap / len(source_words)
            
            citation_scores.append(min(overlap_ratio * 2, 1.0))  # Scale up overlap
        
        return np.mean(citation_scores) if citation_scores else 0.0
    
    async def _calculate_question_match(self, query: str, response: str) -> float:
        """Calculate how well response matches the question"""
        # Simple keyword matching approach
        query_words = set(query.lower().split())
        response_words = set(response.lower().split())
        
        # Remove common stop words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        query_words -= stop_words
        response_words -= stop_words
        
        if len(query_words) == 0:
            return 0.5
        
        # Calculate overlap
        overlap = len(query_words.intersection(response_words))
        match_score = overlap / len(query_words)
        
        # Boost score if response is substantial
        if len(response) > 100:
            match_score = min(match_score * 1.2, 1.0)
        
        return match_score
    
    async def _calculate_semantic_consistency(self, sources: List[Dict[str, Any]]) -> float:
        """Calculate semantic consistency across sources"""
        if not sources or len(sources) < 2:
            return 1.0  # Single source is consistent with itself
        
        if not self.semantic_model:
            return 0.7  # Default score when model unavailable
        
        try:
            # Extract content from sources
            contents = [source.get("content", "") for source in sources if source.get("content")]
            
            if len(contents) < 2:
                return 1.0
            
            # Generate embeddings
            embeddings = self.semantic_model.encode(contents)
            
            # Calculate pairwise similarities
            similarities = []
            for i in range(len(embeddings)):
                for j in range(i + 1, len(embeddings)):
                    similarity = np.dot(embeddings[i], embeddings[j]) / (
                        np.linalg.norm(embeddings[i]) * np.linalg.norm(embeddings[j])
                    )
                    similarities.append(similarity)
            
            return np.mean(similarities) if similarities else 0.7
            
        except Exception as e:
            logger.error(f"Semantic consistency calculation failed: {e}")
            return 0.7
    
    async def _calculate_factual_accuracy(self, response: str) -> float:
        """Calculate factual accuracy score"""
        # Placeholder for factual checking
        # In production, this would use fact-checking APIs or models
        
        # Simple heuristics for now
        accuracy_score = 0.7  # Base score
        
        # Check for specific factual indicators
        if any(word in response.lower() for word in ["according to", "based on", "research shows"]):
            accuracy_score += 0.1
        
        # Penalize for absolute statements without evidence
        absolute_words = ["always", "never", "all", "none", "every", "completely"]
        absolute_count = sum(1 for word in absolute_words if word in response.lower())
        accuracy_score -= absolute_count * 0.05
        
        return max(0.0, min(accuracy_score, 1.0))
    
    async def _calculate_response_completeness(self, query: str, response: str) -> float:
        """Calculate how complete the response is"""
        # Simple heuristics for completeness
        completeness_score = 0.5  # Base score
        
        # Length factor
        if len(response) > 200:
            completeness_score += 0.2
        if len(response) > 500:
            completeness_score += 0.1
        
        # Structure indicators
        if response.count('.') > 2:  # Multiple sentences
            completeness_score += 0.1
        if any(word in response.lower() for word in ["because", "therefore", "however", "additionally"]):
            completeness_score += 0.1  # Explanatory language
        
        return min(completeness_score, 1.0)
    
    async def _multi_llm_convergence_scoring(
        self,
        query: str,
        response: str,
        llm_responses: List[str],
        metrics: TrustMetrics
    ) -> Dict[str, Any]:
        """Multi-LLM convergence scoring method"""
        
        if not llm_responses or len(llm_responses) < 2:
            # Fallback to standard scoring
            return await self._production_standard_scoring(metrics)
        
        # Calculate convergence score
        convergence_score = await self._calculate_llm_consistency(llm_responses)
        
        # Weight convergence heavily in this method
        overall_score = (
            convergence_score * 0.5 +
            metrics.model_confidence * 0.2 +
            metrics.source_quality * 0.15 +
            metrics.citation_accuracy * 0.15
        )
        
        return {
            "overall_score": overall_score,
            "individual_scores": {
                "llm_convergence": convergence_score,
                "model_confidence": metrics.model_confidence,
                "source_quality": metrics.source_quality,
                "citation_accuracy": metrics.citation_accuracy
            }
        }
    
    async def _faithfulness_context_scoring(
        self,
        query: str,
        response: str,
        sources: List[Dict[str, Any]],
        metrics: TrustMetrics
    ) -> Dict[str, Any]:
        """Faithfulness and context scoring method"""
        
        # Focus on citation accuracy and source quality
        overall_score = (
            metrics.citation_accuracy * 0.4 +
            metrics.source_quality * 0.3 +
            metrics.semantic_consistency * 0.2 +
            metrics.question_match * 0.1
        )
        
        return {
            "overall_score": overall_score,
            "individual_scores": {
                "citation_accuracy": metrics.citation_accuracy,
                "source_quality": metrics.source_quality,
                "semantic_consistency": metrics.semantic_consistency,
                "question_match": metrics.question_match
            }
        }
    
    async def _production_standard_scoring(self, metrics: TrustMetrics) -> Dict[str, Any]:
        """Production standard scoring method (original weights)"""
        
        overall_score = (
            metrics.model_confidence * self.default_weights["model_confidence"] +
            metrics.source_quality * self.default_weights["source_quality"] +
            metrics.citation_accuracy * self.default_weights["citation_accuracy"] +
            metrics.question_match * self.default_weights["question_match"]
        )
        
        return {
            "overall_score": overall_score,
            "individual_scores": {
                "model_confidence": metrics.model_confidence,
                "source_quality": metrics.source_quality,
                "citation_accuracy": metrics.citation_accuracy,
                "question_match": metrics.question_match
            }
        }
    
    async def _ensemble_average_scoring(
        self,
        query: str,
        response: str,
        sources: List[Dict[str, Any]],
        llm_responses: List[str],
        metrics: TrustMetrics
    ) -> Dict[str, Any]:
        """Ensemble average of all scoring methods"""
        
        # Get scores from all methods
        production_score = await self._production_standard_scoring(metrics)
        faithfulness_score = await self._faithfulness_context_scoring(query, response, sources, metrics)
        
        if llm_responses and len(llm_responses) > 1:
            convergence_score = await self._multi_llm_convergence_scoring(query, response, llm_responses, metrics)
            overall_score = (
                production_score["overall_score"] +
                faithfulness_score["overall_score"] +
                convergence_score["overall_score"]
            ) / 3
        else:
            overall_score = (
                production_score["overall_score"] +
                faithfulness_score["overall_score"]
            ) / 2
        
        return {
            "overall_score": overall_score,
            "individual_scores": {
                "production_standard": production_score["overall_score"],
                "faithfulness_context": faithfulness_score["overall_score"],
                "ensemble_average": overall_score
            }
        }
    
    def _determine_confidence_level(self, score: float) -> ConfidenceLevel:
        """Determine confidence level from score"""
        if score >= 0.8:
            return ConfidenceLevel.VERY_HIGH
        elif score >= 0.6:
            return ConfidenceLevel.HIGH
        elif score >= 0.4:
            return ConfidenceLevel.MODERATE
        elif score >= 0.2:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def _generate_reasoning(
        self,
        score_result: Dict[str, Any],
        metrics: TrustMetrics,
        method: TrustScoringMethod
    ) -> str:
        """Generate human-readable reasoning for the trust score"""
        
        score = score_result["overall_score"]
        reasoning_parts = []
        
        # Overall assessment
        if score >= 0.8:
            reasoning_parts.append("This response has very high trustworthiness.")
        elif score >= 0.6:
            reasoning_parts.append("This response has high trustworthiness.")
        elif score >= 0.4:
            reasoning_parts.append("This response has moderate trustworthiness.")
        else:
            reasoning_parts.append("This response has low trustworthiness.")
        
        # Key factors
        if metrics.source_quality >= 0.7:
            reasoning_parts.append("Sources are of good quality.")
        elif metrics.source_quality < 0.4:
            reasoning_parts.append("Source quality is concerning.")
        
        if metrics.citation_accuracy >= 0.7:
            reasoning_parts.append("Response accurately reflects the sources.")
        elif metrics.citation_accuracy < 0.4:
            reasoning_parts.append("Response may not accurately reflect the sources.")
        
        if metrics.model_confidence >= 0.7:
            reasoning_parts.append("Model shows high confidence in the response.")
        elif metrics.model_confidence < 0.4:
            reasoning_parts.append("Model shows uncertainty in the response.")
        
        return " ".join(reasoning_parts)
    
    def _detect_flags(
        self,
        query: str,
        response: str,
        sources: List[Dict[str, Any]],
        metrics: TrustMetrics
    ) -> List[str]:
        """Detect potential issues or flags"""
        flags = []
        
        # Low confidence flags
        if metrics.model_confidence < 0.3:
            flags.append("low_model_confidence")
        
        if metrics.source_quality < 0.3:
            flags.append("poor_source_quality")
        
        if metrics.citation_accuracy < 0.3:
            flags.append("poor_citation_accuracy")
        
        # Content flags
        if len(response) < 50:
            flags.append("very_short_response")
        
        if len(sources) == 0:
            flags.append("no_sources")
        elif len(sources) == 1:
            flags.append("single_source")
        
        # Uncertainty flags
        uncertainty_phrases = ["i don't know", "not sure", "uncertain"]
        if any(phrase in response.lower() for phrase in uncertainty_phrases):
            flags.append("explicit_uncertainty")
        
        return flags
    
    async def _calculate_llm_consistency(self, llm_responses: List[str]) -> float:
        """Calculate consistency across multiple LLM responses"""
        if len(llm_responses) < 2:
            return 1.0
        
        if not self.semantic_model:
            # Simple text similarity fallback
            similarities = []
            for i in range(len(llm_responses)):
                for j in range(i + 1, len(llm_responses)):
                    # Simple word overlap
                    words1 = set(llm_responses[i].lower().split())
                    words2 = set(llm_responses[j].lower().split())
                    if len(words1) == 0 or len(words2) == 0:
                        continue
                    overlap = len(words1.intersection(words2))
                    similarity = overlap / max(len(words1), len(words2))
                    similarities.append(similarity)
            
            return np.mean(similarities) if similarities else 0.5
        
        try:
            # Use semantic similarity
            embeddings = self.semantic_model.encode(llm_responses)
            similarities = []
            
            for i in range(len(embeddings)):
                for j in range(i + 1, len(embeddings)):
                    similarity = np.dot(embeddings[i], embeddings[j]) / (
                        np.linalg.norm(embeddings[i]) * np.linalg.norm(embeddings[j])
                    )
                    similarities.append(similarity)
            
            return np.mean(similarities) if similarities else 0.5
            
        except Exception as e:
            logger.error(f"LLM consistency calculation failed: {e}")
            return 0.5
    
    def _generate_cache_key(self, query: str, response: str, method: TrustScoringMethod) -> str:
        """Generate cache key for trust scoring"""
        import hashlib
        key_data = f"{query}{response}{method.value}"
        return f"trust_score:{hashlib.md5(key_data.encode()).hexdigest()}"
    
    async def _get_cached_score(self, cache_key: str) -> Optional[TrustScoringResult]:
        """Get cached trust score"""
        if not self.redis_client:
            return None
        
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                score_data = json.loads(cached_data)
                return TrustScoringResult(**score_data)
        except Exception as e:
            logger.error(f"Cache retrieval failed: {e}")
        
        return None
    
    async def _cache_score(self, cache_key: str, result: TrustScoringResult):
        """Cache trust score result"""
        if not self.redis_client:
            return
        
        try:
            score_data = asdict(result)
            self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(score_data, default=str)
            )
        except Exception as e:
            logger.error(f"Cache storage failed: {e}")

# Global enterprise trust scoring service instance
enterprise_trust_scoring_service = EnterpriseTrustScoringService()
