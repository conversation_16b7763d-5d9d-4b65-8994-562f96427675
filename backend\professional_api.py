"""
Standalone Professional Source Attribution API
Independent service for professional monitoring and citation features
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, Depends, Query as FastAPIQuery
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel

from database import get_db
from models import User, Query, DocumentChunk, Dataset
from services.citation_service import CitationService
from services.monitoring_service import MonitoringService
from services.chunk_service import ChunkService

# Create FastAPI app
app = FastAPI(
    title="AIthentiq Professional API",
    description="Professional Source Attribution and Monitoring API",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple auth function
async def get_professional_user(db: Session = Depends(get_db)) -> User:
    """Get a default user for professional endpoints"""
    user = db.query(User).first()
    if not user:
        user = User(
            id="professional_user",
            email="<EMAIL>",
            name="Professional User",
            role="admin"
        )
        db.add(user)
        db.commit()
        db.refresh(user)
    return user

# Pydantic models
class HallucinationRequest(BaseModel):
    response_text: str
    source_chunks: List[str]

class HallucinationResponse(BaseModel):
    risk_score: float
    confidence_level: str
    uncertainty_phrases: int
    unsupported_claims: int
    details: List[str]

class SystemHealthResponse(BaseModel):
    status: str
    total_queries: int
    error_rate: float
    source_attribution_rate: float
    average_confidence: float

# Professional API Endpoints

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AIthentiq Professional Source Attribution API",
        "version": "1.0.0",
        "status": "operational"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/citation/detect-hallucination", response_model=HallucinationResponse)
async def detect_hallucination(
    request: HallucinationRequest,
    current_user: User = Depends(get_professional_user)
):
    """
    Detect potential hallucination indicators in AI responses
    """
    try:
        citation_service = CitationService()
        
        indicators = citation_service.detect_hallucination_indicators(
            response_text=request.response_text,
            source_chunks=request.source_chunks
        )
        
        return HallucinationResponse(**indicators)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Hallucination detection failed: {str(e)}")

@app.get("/monitoring/system-health", response_model=SystemHealthResponse)
async def get_system_health(
    hours: int = FastAPIQuery(24, ge=1, le=168),
    current_user: User = Depends(get_professional_user),
    db: Session = Depends(get_db)
):
    """
    Get system health metrics
    """
    try:
        monitoring_service = MonitoringService()
        
        metrics = monitoring_service.get_system_health_metrics(
            dataset_id=None,
            hours=hours,
            db=db
        )
        
        if "error" in metrics:
            raise HTTPException(status_code=500, detail=metrics["error"])
        
        return SystemHealthResponse(**metrics["system_health"])
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get health metrics: {str(e)}")

@app.get("/monitoring/quality/{dataset_id}")
async def get_quality_metrics(
    dataset_id: int,
    days: int = FastAPIQuery(7, ge=1, le=90),
    current_user: User = Depends(get_professional_user),
    db: Session = Depends(get_db)
):
    """
    Get quality metrics for a dataset
    """
    try:
        monitoring_service = MonitoringService()
        
        metrics = monitoring_service.get_quality_metrics(
            dataset_id=dataset_id,
            days=days,
            db=db
        )
        
        if "error" in metrics:
            raise HTTPException(status_code=500, detail=metrics["error"])
        
        return metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get quality metrics: {str(e)}")

@app.get("/chunks/{dataset_id}/search")
async def search_chunks(
    dataset_id: int,
    query: str = FastAPIQuery(..., min_length=1),
    limit: int = FastAPIQuery(10, ge=1, le=50),
    search_method: str = FastAPIQuery("hybrid"),
    current_user: User = Depends(get_professional_user),
    db: Session = Depends(get_db)
):
    """
    Search chunks within a dataset
    """
    try:
        chunk_service = ChunkService()
        
        results = chunk_service.search_chunks(
            dataset_id=dataset_id,
            query=query,
            limit=limit,
            search_method=search_method,
            db=db
        )
        
        return {
            "chunks": results,
            "total_results": len(results),
            "search_method": search_method,
            "dataset_id": dataset_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.get("/chunks/{dataset_id}/stats")
async def get_chunk_stats(
    dataset_id: int,
    current_user: User = Depends(get_professional_user),
    db: Session = Depends(get_db)
):
    """
    Get chunk statistics for a dataset
    """
    try:
        chunk_service = ChunkService()
        
        stats = chunk_service.get_chunk_statistics(dataset_id, db)
        
        return {
            "dataset_id": dataset_id,
            **stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")

@app.get("/citation/audit-trail/{query_id}")
async def get_audit_trail(
    query_id: int,
    current_user: User = Depends(get_professional_user),
    db: Session = Depends(get_db)
):
    """
    Get audit trail for a query
    """
    try:
        citation_service = CitationService()
        
        audit_trail = citation_service.generate_audit_trail(
            query_id=query_id,
            db=db
        )
        
        if "error" in audit_trail:
            raise HTTPException(status_code=500, detail=audit_trail["error"])
        
        return audit_trail
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get audit trail: {str(e)}")

@app.get("/status")
async def get_service_status(
    current_user: User = Depends(get_professional_user),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive service status
    """
    try:
        # Test all services
        services_status = {}
        
        # Test Citation Service
        try:
            citation_service = CitationService()
            test_result = citation_service.detect_hallucination_indicators("test", ["test"])
            services_status["citation_service"] = "operational"
        except Exception as e:
            services_status["citation_service"] = f"error: {str(e)}"
        
        # Test Monitoring Service
        try:
            monitoring_service = MonitoringService()
            services_status["monitoring_service"] = "operational"
        except Exception as e:
            services_status["monitoring_service"] = f"error: {str(e)}"
        
        # Test Chunk Service
        try:
            chunk_service = ChunkService()
            services_status["chunk_service"] = "operational"
        except Exception as e:
            services_status["chunk_service"] = f"error: {str(e)}"
        
        # Database status
        try:
            user_count = db.query(User).count()
            query_count = db.query(Query).count()
            chunk_count = db.query(DocumentChunk).count()
            services_status["database"] = {
                "status": "operational",
                "users": user_count,
                "queries": query_count,
                "chunks": chunk_count
            }
        except Exception as e:
            services_status["database"] = f"error: {str(e)}"
        
        operational_services = sum(1 for status in services_status.values() 
                                 if isinstance(status, str) and status == "operational" or 
                                    isinstance(status, dict) and status.get("status") == "operational")
        
        return {
            "overall_status": "operational" if operational_services >= 3 else "degraded",
            "services": services_status,
            "operational_services": operational_services,
            "total_services": len(services_status),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "overall_status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting AIthentiq Professional API...")
    print("📊 Professional Source Attribution and Monitoring Service")
    print("🌐 API Documentation: http://localhost:8001/docs")
    
    uvicorn.run(
        "professional_api:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
