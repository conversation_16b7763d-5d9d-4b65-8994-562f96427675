"""
Migration script to remove Enhanced Hybrid Search features from the database.
This script removes embedding-related columns from the datasets table.

Run this script to clean up the database after removing hybrid search functionality.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_database_url():
    """Get database URL from environment variables"""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is required")
    return database_url

def remove_embedding_columns():
    """Remove embedding-related columns from datasets table"""
    database_url = get_database_url()
    engine = create_engine(database_url)
    
    # List of columns to remove
    columns_to_remove = [
        "embedding_status",
        "embedding_created_at", 
        "embedding_error",
        "embedding_progress",
        "embedding_current_step"
    ]
    
    print("🔄 Starting database migration to remove hybrid search features...")
    
    with engine.connect() as connection:
        # Start a transaction
        trans = connection.begin()
        
        try:
            # Check if columns exist before trying to drop them
            for column in columns_to_remove:
                # Check if column exists
                result = connection.execute(text(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'datasets' AND column_name = '{column}'
                """))
                
                if result.fetchone():
                    print(f"🗑️  Dropping column: {column}")
                    connection.execute(text(f"ALTER TABLE datasets DROP COLUMN {column}"))
                else:
                    print(f"ℹ️  Column {column} does not exist, skipping...")
            
            # Commit the transaction
            trans.commit()
            print("✅ Database migration completed successfully!")
            print("🚀 Enhanced Hybrid Search features have been completely removed from the database.")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"❌ Error during migration: {e}")
            raise

def main():
    """Main function to run the migration"""
    try:
        print("🔍 Removing Enhanced Hybrid Search features from database...")
        remove_embedding_columns()
        print("\n✅ Migration completed successfully!")
        print("📝 The following features have been removed:")
        print("   - Embedding status tracking")
        print("   - Embedding progress indicators") 
        print("   - Embedding error logging")
        print("   - Embedding creation timestamps")
        print("\n🎯 Your database is now clean and optimized!")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
