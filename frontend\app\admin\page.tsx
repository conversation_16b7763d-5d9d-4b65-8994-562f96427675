'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../hooks/useAuth';
import Navbar from '../../components/ui/navbar';
import Footer from '../../components/ui/footer';
import FeedbackManagement from '../../components/admin/feedback-management';
import UserManagement from '../../components/admin/user-management';
import MonitoringDashboard from '../../components/monitoring/monitoring-dashboard';
import ComplianceDashboard from '../../components/admin/compliance-dashboard';
import ResearchDocuments from '../../components/admin/research-documents';
import TestScripts from '../../components/admin/test-scripts';
import dynamic from 'next/dynamic';

// Dynamically import the advanced Trust Score Comparison page component
const TrustScoreComparison = dynamic(() => import('../trust-comparison/page'), {
  ssr: false,
  loading: () => <div className="p-6">Loading Trust Score Comparison...</div>
});

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState<'feedback' | 'users' | 'analytics' | 'monitoring' | 'compliance' | 'research' | 'tests' | 'trust-comparison'>('feedback');
  const { user, loading, isAdmin, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated()) {
        router.push('/auth/signin');
        return;
      }

      if (!isAdmin()) {
        router.push('/dashboard');
        return;
      }
    }
  }, [loading, isAuthenticated, isAdmin, router]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking permissions...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Show access denied if not admin
  if (!isAdmin()) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="text-red-600 text-6xl mb-4">🚫</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
            <button
              onClick={() => router.push('/dashboard')}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600 mt-2">Manage feedback, users, and system analytics</p>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('feedback')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'feedback'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Feedback Management
              </button>
              <button
                onClick={() => setActiveTab('users')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'users'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                User Management
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Analytics
              </button>

              <button
                onClick={() => setActiveTab('monitoring')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'monitoring'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                System Monitoring
              </button>

              <button
                onClick={() => setActiveTab('compliance')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'compliance'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Compliance & Audit
              </button>

              <button
                onClick={() => setActiveTab('research')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'research'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Research Documents
              </button>

              <button
                onClick={() => setActiveTab('tests')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'tests'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Test Scripts
              </button>

              <button
                onClick={() => setActiveTab('trust-comparison')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'trust-comparison'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Trust Score Comparison
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'feedback' && <FeedbackManagement />}

          {activeTab === 'users' && <UserManagement />}

          {activeTab === 'analytics' && (
            <div className="bg-white rounded-lg shadow border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">System Analytics</h2>
              <p className="text-gray-600">Analytics dashboard coming soon...</p>
            </div>
          )}

          {activeTab === 'monitoring' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Professional System Monitoring</h2>
                <MonitoringDashboard />
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Compliance & Audit Management</h2>
                <ComplianceDashboard />
              </div>
            </div>
          )}

          {activeTab === 'research' && <ResearchDocuments />}

          {activeTab === 'tests' && <TestScripts />}

          {activeTab === 'trust-comparison' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Trust Score Algorithm Comparison</h2>
                <p className="text-gray-600 mb-6">Compare different trust score algorithms for system optimization and analysis.</p>
                <TrustScoreComparison />
              </div>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
