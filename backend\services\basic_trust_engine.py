"""
Basic Trust Score Engine for AIthentiq Phase 4
Implements 2-factor trust scoring: Model Confidence (40%) + Citation Accuracy (20%)
"""

import os
import json
import logging
import re
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)

class TrustLevel(Enum):
    HIGH = "high"      # 80-100% (Green)
    MODERATE = "moderate"  # 60-79% (Yellow)
    LOW = "low"        # 0-59% (Red)

@dataclass
class BasicTrustScore:
    overall_score: float  # 0.0 to 1.0
    trust_level: TrustLevel
    color_code: str  # "green", "yellow", "red"
    model_confidence: float  # 40% weight
    citation_accuracy: float  # 20% weight
    rationale: str
    factors: List[str]
    processing_time_ms: float
    timestamp: datetime

class BasicTrustEngine:
    """Basic Trust Score Engine implementing Phase 4 requirements"""
    
    def __init__(self):
        # Phase 4 weights as specified in development plan
        self.model_confidence_weight = 0.40  # 40%
        self.citation_accuracy_weight = 0.20  # 20%
        
        # Remaining 40% for future phases (Phase 8)
        self.future_weight = 0.40
        
        # Trust level thresholds
        self.high_threshold = 0.80    # 80%+ = Green
        self.moderate_threshold = 0.60  # 60-79% = Yellow
        # Below 60% = Red
        
        # Confidence indicators for model confidence scoring
        self.uncertainty_patterns = [
            r'\b(might|may|could|possibly|perhaps|likely|probably)\b',
            r'\b(uncertain|unsure|unclear|ambiguous|not sure)\b',
            r'\b(approximately|around|about|roughly|estimate)\b',
            r'\b(seems|appears|suggests|indicates)\b',
            r'\b(I think|I believe|in my opinion)\b'
        ]
        
        self.confidence_patterns = [
            r'\b(definitely|certainly|absolutely|clearly|obviously)\b',
            r'\b(confirmed|verified|established|proven)\b',
            r'\b(according to|based on|research shows|studies indicate)\b',
            r'\b(specifically|precisely|exactly)\b'
        ]
    
    def compute_trust_score(
        self,
        query: str,
        answer: str,
        sources: List[Dict[str, Any]] = None,
        metadata: Dict[str, Any] = None
    ) -> BasicTrustScore:
        """
        Compute basic trust score using Phase 4 2-factor system
        
        Args:
            query: User question
            answer: AI-generated answer
            sources: List of source documents with content
            metadata: Additional metadata
            
        Returns:
            BasicTrustScore with overall score and breakdown
        """
        start_time = time.time()
        
        try:
            # 1. Model Confidence Scoring (40% weight)
            model_confidence = self._calculate_model_confidence(answer, metadata)
            
            # 2. Citation Accuracy Validation (20% weight)
            citation_accuracy = self._validate_citation_accuracy(answer, sources or [])
            
            # 3. Calculate overall score (60% total, 40% reserved for future phases)
            weighted_score = (
                model_confidence * self.model_confidence_weight +
                citation_accuracy * self.citation_accuracy_weight
            )
            
            # Normalize to current implementation (60% of total)
            # Add baseline for missing components
            baseline_score = 0.3  # 30% baseline for missing Phase 8 components
            overall_score = weighted_score + baseline_score
            overall_score = min(1.0, overall_score)  # Cap at 100%
            
            # 4. Determine trust level and color
            trust_level, color_code = self._determine_trust_level(overall_score)
            
            # 5. Generate rationale and factors
            rationale, factors = self._generate_rationale(
                overall_score, model_confidence, citation_accuracy
            )
            
            # 6. Calculate processing time
            processing_time = (time.time() - start_time) * 1000
            
            return BasicTrustScore(
                overall_score=overall_score,
                trust_level=trust_level,
                color_code=color_code,
                model_confidence=model_confidence,
                citation_accuracy=citation_accuracy,
                rationale=rationale,
                factors=factors,
                processing_time_ms=processing_time,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"Trust score computation failed: {e}")
            # Return low trust score on error
            return BasicTrustScore(
                overall_score=0.3,
                trust_level=TrustLevel.LOW,
                color_code="red",
                model_confidence=0.3,
                citation_accuracy=0.3,
                rationale=f"Trust score computation failed: {str(e)}",
                factors=["computation_error"],
                processing_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(timezone.utc)
            )
    
    def _calculate_model_confidence(self, answer: str, metadata: Dict[str, Any] = None) -> float:
        """
        Calculate model confidence score (40% weight)
        Extract confidence scores from LLM responses and normalize across models
        """
        try:
            # Start with base confidence
            base_confidence = 0.7
            
            # 1. Check for uncertainty indicators (reduce confidence)
            uncertainty_count = 0
            for pattern in self.uncertainty_patterns:
                matches = re.findall(pattern, answer, re.IGNORECASE)
                uncertainty_count += len(matches)
            
            uncertainty_penalty = min(0.3, uncertainty_count * 0.05)
            
            # 2. Check for confidence indicators (increase confidence)
            confidence_count = 0
            for pattern in self.confidence_patterns:
                matches = re.findall(pattern, answer, re.IGNORECASE)
                confidence_count += len(matches)
            
            confidence_bonus = min(0.2, confidence_count * 0.04)
            
            # 3. Answer length and specificity
            length_factor = min(0.1, len(answer) / 1000)  # Longer answers may be more confident
            
            # 4. Check for specific details (numbers, dates, names)
            specificity_bonus = 0.1 if self._has_specific_details(answer) else 0
            
            # 5. Handle cases where confidence is not available from LLM
            llm_confidence = metadata.get("llm_confidence") if metadata else None
            if llm_confidence is not None:
                # Use actual LLM confidence if available
                base_confidence = float(llm_confidence)
            
            # Calculate final confidence
            confidence = (
                base_confidence 
                - uncertainty_penalty 
                + confidence_bonus 
                + length_factor 
                + specificity_bonus
            )
            
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            logger.error(f"Model confidence calculation failed: {e}")
            return 0.5  # Default moderate confidence
    
    def _validate_citation_accuracy(self, answer: str, sources: List[Dict[str, Any]]) -> float:
        """
        Validate citation accuracy (20% weight)
        Verify citations reference correct source material
        """
        try:
            if not sources:
                # No sources available
                if self._answer_claims_sources(answer):
                    return 0.2  # Claims sources but none provided
                else:
                    return 0.7  # No sources claimed, acceptable
            
            # Extract claims from answer that should be verifiable
            verifiable_claims = self._extract_verifiable_claims(answer)
            
            if not verifiable_claims:
                return 0.8  # No specific claims to verify
            
            # Check semantic similarity between claims and sources
            supported_claims = 0
            for claim in verifiable_claims:
                if self._claim_supported_by_sources(claim, sources):
                    supported_claims += 1
            
            # Calculate accuracy ratio
            accuracy_ratio = supported_claims / len(verifiable_claims)
            
            # Bonus for multiple diverse sources
            source_diversity_bonus = min(0.1, len(sources) * 0.02)
            
            # Check for inaccurate or missing citations
            citation_penalty = self._check_citation_issues(answer, sources)
            
            final_accuracy = accuracy_ratio + source_diversity_bonus - citation_penalty
            
            return max(0.0, min(1.0, final_accuracy))
            
        except Exception as e:
            logger.error(f"Citation accuracy validation failed: {e}")
            return 0.5  # Default moderate accuracy
    
    def _determine_trust_level(self, score: float) -> Tuple[TrustLevel, str]:
        """Determine trust level and color code based on score"""
        if score >= self.high_threshold:
            return TrustLevel.HIGH, "green"
        elif score >= self.moderate_threshold:
            return TrustLevel.MODERATE, "yellow"
        else:
            return TrustLevel.LOW, "red"
    
    def _generate_rationale(
        self, 
        overall_score: float, 
        model_confidence: float, 
        citation_accuracy: float
    ) -> Tuple[str, List[str]]:
        """Generate trust score rationale and contributing factors"""
        
        factors = []
        
        # Model confidence factors
        if model_confidence >= 0.8:
            factors.append("High model confidence")
        elif model_confidence >= 0.6:
            factors.append("Moderate model confidence")
        else:
            factors.append("Low model confidence")
        
        # Citation accuracy factors
        if citation_accuracy >= 0.8:
            factors.append("Strong citation accuracy")
        elif citation_accuracy >= 0.6:
            factors.append("Adequate citation accuracy")
        else:
            factors.append("Weak citation accuracy")
        
        # Overall assessment
        if overall_score >= 0.8:
            rationale = "High trust score based on strong model confidence and accurate citations."
        elif overall_score >= 0.6:
            rationale = "Moderate trust score with acceptable confidence and citation quality."
        else:
            rationale = "Low trust score due to uncertainty indicators or citation issues."
        
        # Add specific details
        rationale += f" Model confidence: {model_confidence:.1%}, Citation accuracy: {citation_accuracy:.1%}."
        
        return rationale, factors
    
    def _has_specific_details(self, answer: str) -> bool:
        """Check if answer contains specific details (numbers, dates, names)"""
        # Check for numbers
        if re.search(r'\b\d+(?:\.\d+)?\b', answer):
            return True
        
        # Check for dates
        if re.search(r'\b\d{4}\b|\b\d{1,2}/\d{1,2}/\d{2,4}\b', answer):
            return True
        
        # Check for proper nouns (capitalized words)
        proper_nouns = re.findall(r'\b[A-Z][a-z]+\b', answer)
        if len(proper_nouns) >= 3:
            return True
        
        return False
    
    def _answer_claims_sources(self, answer: str) -> bool:
        """Check if answer claims to have sources"""
        source_indicators = [
            r'according to',
            r'based on',
            r'source',
            r'reference',
            r'study shows',
            r'research indicates',
            r'data suggests'
        ]
        
        for indicator in source_indicators:
            if re.search(indicator, answer, re.IGNORECASE):
                return True
        
        return False
    
    def _extract_verifiable_claims(self, answer: str) -> List[str]:
        """Extract verifiable claims from answer"""
        # Split answer into sentences
        sentences = re.split(r'[.!?]+', answer)
        
        verifiable_claims = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:  # Skip very short sentences
                continue
            
            # Check if sentence contains verifiable information
            if (self._contains_factual_content(sentence) and 
                not self._is_opinion_or_speculation(sentence)):
                verifiable_claims.append(sentence)
        
        return verifiable_claims[:5]  # Limit to 5 claims for performance
    
    def _contains_factual_content(self, sentence: str) -> bool:
        """Check if sentence contains factual content"""
        factual_indicators = [
            r'\b\d+',  # Numbers
            r'\b(is|are|was|were|has|have|will|can|does)\b',  # Factual verbs
            r'\b(percent|percentage|rate|ratio|amount)\b',  # Quantitative terms
            r'\b(company|organization|study|research|report)\b'  # Institutional terms
        ]
        
        for indicator in factual_indicators:
            if re.search(indicator, sentence, re.IGNORECASE):
                return True
        
        return False
    
    def _is_opinion_or_speculation(self, sentence: str) -> bool:
        """Check if sentence is opinion or speculation"""
        opinion_indicators = [
            r'\b(I think|I believe|in my opinion|personally)\b',
            r'\b(might|may|could|possibly|perhaps)\b',
            r'\b(seems|appears|suggests)\b'
        ]
        
        for indicator in opinion_indicators:
            if re.search(indicator, sentence, re.IGNORECASE):
                return True
        
        return False
    
    def _claim_supported_by_sources(self, claim: str, sources: List[Dict[str, Any]]) -> bool:
        """Check if claim is supported by sources using semantic similarity"""
        try:
            claim_lower = claim.lower()
            
            for source in sources:
                source_content = source.get("content", "").lower()
                
                # Simple semantic similarity using sequence matching
                similarity = SequenceMatcher(None, claim_lower, source_content).ratio()
                
                if similarity > 0.3:  # 30% similarity threshold
                    return True
                
                # Check for key phrase overlap
                claim_words = set(claim_lower.split())
                source_words = set(source_content.split())
                
                overlap = len(claim_words.intersection(source_words))
                if overlap >= 3:  # At least 3 words in common
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Claim support check failed: {e}")
            return False
    
    def _check_citation_issues(self, answer: str, sources: List[Dict[str, Any]]) -> float:
        """Check for citation issues and return penalty"""
        penalty = 0.0
        
        # Check for missing citations when sources are available
        if sources and not re.search(r'(according to|based on|source)', answer, re.IGNORECASE):
            penalty += 0.1
        
        # Check for vague citations
        if re.search(r'(some sources|various studies|research shows)', answer, re.IGNORECASE):
            penalty += 0.05
        
        return penalty

# Global basic trust engine instance
basic_trust_engine = BasicTrustEngine()
