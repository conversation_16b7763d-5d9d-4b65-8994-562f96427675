'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FileText,
  Search,
  Brain,
  Target,
  Zap,
  BookOpen,
  Eye,
  Star,
  AlertCircle,
  CheckCircle,
  Info,
  Activity,
  Database,
  Layers,
  Hash,
  Clock,
  Award
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { createApiInstance } from '../../lib/api';

interface DocumentInsights {
  basic_stats: {
    total_chunks: number;
    total_words: number;
    total_characters: number;
    avg_words_per_chunk: number;
    avg_readability_score: number;
    avg_information_density: number;
    min_words_per_chunk: number;
    max_words_per_chunk: number;
  };
  chunk_types: Array<{
    type: string;
    count: number;
    avg_words: number;
  }>;
  sections: Array<{
    section: string;
    chunk_count: number;
    word_count: number;
  }>;
  documents: Array<{
    document: string;
    chunk_count: number;
    word_count: number;
    line_range: string;
  }>;
  quality_metrics: {
    high_readability_chunks: number;
    high_density_chunks: number;
    chunks_with_embeddings: number;
    readability_percentage: number;
    density_percentage: number;
    embedding_percentage: number;
  };
  document_insights: {
    complexity_score: string;
    readability_grade: string;
    information_richness: string;
    structure_quality: string;
    search_optimization: string;
  };
}

interface DocumentInsightsDashboardProps {
  datasetId: number;
  datasetName: string;
}

export default function DocumentInsightsDashboard({ datasetId, datasetName }: DocumentInsightsDashboardProps) {
  const { data: session } = useSession();
  const [insights, setInsights] = useState<DocumentInsights | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (datasetId && session) {
      loadInsights();
    }
  }, [datasetId, session]);

  const loadInsights = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.get(`/source-attribution/datasets/${datasetId}/stats`);
      setInsights(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load insights');
    } finally {
      setLoading(false);
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Very Low': return 'text-green-600 bg-green-100';
      case 'Low': return 'text-green-600 bg-green-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'High': return 'text-orange-600 bg-orange-100';
      case 'Very High': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'Excellent': return 'text-green-600 bg-green-100';
      case 'Good': return 'text-blue-600 bg-blue-100';
      case 'Fair': return 'text-yellow-600 bg-yellow-100';
      case 'Poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Analysing document intelligence...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6">
        <div className="flex items-center space-x-3 text-red-600">
          <AlertCircle className="h-6 w-6" />
          <div>
            <h3 className="font-semibold">Analysis Failed</h3>
            <p className="text-sm text-red-500">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!insights) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12 text-gray-500">
          <Database className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p>No insights available for this dataset</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Brain className="h-5 w-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Document Intelligence</h2>
            <p className="text-gray-600">{datasetName}</p>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <FileText className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">Total Chunks</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{insights.basic_stats.total_chunks.toLocaleString()}</p>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <Hash className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Total Words</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{insights.basic_stats.total_words.toLocaleString()}</p>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <BookOpen className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Avg Readability</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{insights.basic_stats.avg_readability_score.toFixed(1)}</p>
          </div>

          <div className="bg-white/80 rounded-lg p-4 border border-gray-200/60">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium text-gray-700">Info Density</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{(insights.basic_stats.avg_information_density * 100).toFixed(1)}%</p>
          </div>
        </div>
      </div>

      {/* Document Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Complexity Analysis */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Activity className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Complexity Analysis</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Overall Complexity</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getComplexityColor(insights.document_insights.complexity_score)}`}>
                {insights.document_insights.complexity_score}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Readability Grade</span>
              <span className="text-sm font-medium text-gray-900">{insights.document_insights.readability_grade}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Information Richness</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getQualityColor(insights.document_insights.information_richness)}`}>
                {insights.document_insights.information_richness}
              </span>
            </div>
          </div>
        </div>

        {/* Structure Quality */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Layers className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Structure Quality</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Overall Structure</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getQualityColor(insights.document_insights.structure_quality)}`}>
                {insights.document_insights.structure_quality}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Sections</span>
              <span className="text-sm font-medium text-gray-900">{insights.sections.length}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Chunk Types</span>
              <span className="text-sm font-medium text-gray-900">{insights.chunk_types.length}</span>
            </div>
          </div>
        </div>

        {/* Search Optimization */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Search className="h-5 w-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">Search Optimization</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Search Readiness</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getQualityColor(insights.document_insights.search_optimization)}`}>
                {insights.document_insights.search_optimization}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Embedding Coverage</span>
              <span className="text-sm font-medium text-gray-900">{insights.quality_metrics.embedding_percentage.toFixed(1)}%</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Avg Chunk Size</span>
              <span className="text-sm font-medium text-gray-900">{insights.basic_stats.avg_words_per_chunk.toFixed(0)} words</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quality Metrics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Award className="h-5 w-5 text-yellow-600" />
          <h3 className="text-lg font-semibold text-gray-900">Quality Metrics</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-3 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{insights.quality_metrics.readability_percentage.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">High Readability Chunks</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center">
              <Zap className="h-8 w-8 text-blue-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{insights.quality_metrics.density_percentage.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">High Density Chunks</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center">
              <Brain className="h-8 w-8 text-purple-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{insights.quality_metrics.embedding_percentage.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">Chunks with Embeddings</p>
          </div>
        </div>
      </div>
    </div>
  );
}
