from typing import Dict, Any
import logging
import time
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.job_queue import job_queue, Job
from services.forecasting_service import ForecastingService
from services.dataset_service import DatasetService
import models
from database import get_db
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

def forecast_handler(job: Job):
    """
    Handler for forecast jobs

    Args:
        job: The job to process
    """
    logger.info(f"Starting forecast job {job.id}")

    try:
        # Extract parameters
        dataset_id = job.params.get("dataset_id")
        target_column = job.params.get("target_column")
        date_column = job.params.get("date_column")
        horizon = job.params.get("horizon", 10)
        frequency = job.params.get("frequency")
        method = job.params.get("method", "auto")
        confidence_interval = job.params.get("confidence_interval", 0.95)

        # Log parameters
        logger.info(f"Forecast parameters: dataset_id={dataset_id}, target={target_column}, date={date_column}, method={method}")

        # Update job status
        job.set_stage("Loading dataset", 10)

        # Get database session
        db = next(get_db())

        # Get the dataset from database
        dataset = db.query(models.Dataset).filter(models.Dataset.id == dataset_id).first()

        if not dataset:
            raise ValueError(f"Dataset with ID {dataset_id} not found")

        # Load the dataset data
        job.set_stage("Parsing dataset", 20)

        try:
            # Create dataset service
            dataset_service = DatasetService()

            # Try to load from parsed_data if available
            if hasattr(dataset, 'parsed_data') and dataset.parsed_data:
                df = dataset_service.load_dataset(json_data=dataset.parsed_data)
            # Fall back to file_path if parsed_data is not available
            elif hasattr(dataset, 'file_path') and dataset.file_path:
                df = dataset_service.load_dataset(file_path=dataset.file_path)
            else:
                raise ValueError("Dataset has no data")

            if df is None:
                raise ValueError("Failed to parse dataset")
        except Exception as e:
            raise ValueError(f"Failed to load dataset: {str(e)}")

        # Check if dataset is large
        job.set_stage("Analyzing dataset", 30)
        is_large_dataset = len(df) > 1000

        # For large datasets, use simpler methods
        if is_large_dataset and method == "auto":
            method = "simple"
            job.update_progress(35, f"Large dataset detected ({len(df)} rows). Using simple forecasting method.")

        # Initialize forecasting service
        job.set_stage("Initializing forecasting service", 40)
        forecasting_service = ForecastingService(df)

        # Prepare time series data
        job.set_stage("Preparing time series data", 50)

        # Generate forecast
        job.set_stage("Generating forecast", 60)

        # Simulate progress updates during forecasting
        for progress in range(60, 90, 5):
            time.sleep(0.5)  # Simulate work
            job.update_progress(progress, f"Forecasting in progress... ({progress}%)")

        # Generate the actual forecast
        forecast_results = forecasting_service.forecast(
            target_column=target_column,
            date_column=date_column,
            horizon=horizon,
            frequency=frequency,
            method=method,
            confidence_interval=confidence_interval
        )

        # Complete the job with the forecast results
        job.set_stage("Finalizing results", 95)

        # Add metadata to the results
        result = {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "forecast": forecast_results
        }

        job.complete(result)

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error in forecast job {job.id}: {error_msg}")
        import traceback
        trace = traceback.format_exc()
        logger.error(trace)

        # Create a more user-friendly error message
        user_msg = error_msg
        if "Failed to load dataset" in error_msg:
            user_msg = "Could not load the dataset. Please check if the dataset exists and is valid."
        elif "no attribute" in error_msg.lower() or "not found" in error_msg.lower():
            user_msg = "There was a problem with the column names. Please check that the target and date columns exist in the dataset."
        elif "convert" in error_msg.lower() or "parse" in error_msg.lower():
            user_msg = "Could not convert data to the required format. Please check that your date column contains valid dates and your target column contains numeric values."

        # Add the detailed error for debugging
        detailed_error = f"{user_msg}\n\nTechnical details: {error_msg}"

        # Log the failure with the user-friendly message
        logger.info(f"Marking job {job.id} as failed with message: {user_msg}")
        job.fail(detailed_error)


# Register the forecast handler with the job queue
job_queue.register_handler("forecast", forecast_handler)
