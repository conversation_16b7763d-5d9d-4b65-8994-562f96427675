"use client";

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { 
  GitBranch, 
  ExternalLink, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  AlertCircle,
  Settings,
  Key,
  Sync
} from 'lucide-react';
import { createApiInstance } from '@/lib/api';

interface GitHubConnectorSetupProps {
  onComplete?: (connectorId: string) => void;
  onCancel?: () => void;
}

interface ConnectorConfig {
  name: string;
  description: string;
  auto_sync: boolean;
  sync_frequency: number;
  include_private_repos: boolean;
  max_file_size: number;
  max_files_per_repo: number;
}

export default function GitHubConnectorSetup({ onComplete, onCancel }: GitHubConnectorSetupProps) {
  const { data: session } = useSession();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [oauthUrl, setOauthUrl] = useState<string | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [githubUser, setGithubUser] = useState<any>(null);
  
  const [config, setConfig] = useState<ConnectorConfig>({
    name: '',
    description: '',
    auto_sync: true,
    sync_frequency: 300, // 5 minutes
    include_private_repos: false,
    max_file_size: 1048576, // 1MB
    max_files_per_repo: 1000
  });

  const steps = [
    { id: 1, title: 'GitHub Authorization', description: 'Connect your GitHub account' },
    { id: 2, title: 'Connector Configuration', description: 'Configure sync settings' },
    { id: 3, title: 'Review & Create', description: 'Review and create connector' }
  ];

  const handleStartOAuth = async () => {
    if (!session) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.get('/connectors/github/oauth/authorize', {
        params: {
          scopes: ['repo', 'read:user', 'user:email'].join(' ')
        }
      });

      setOauthUrl(response.data.authorization_url);
      
      // Open OAuth URL in new window
      const popup = window.open(
        response.data.authorization_url,
        'github-oauth',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );

      // Listen for OAuth completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          // Check if we received the token (this would be handled by the OAuth callback)
          // For now, we'll simulate success
          handleOAuthSuccess();
        }
      }, 1000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start OAuth flow');
    } finally {
      setLoading(false);
    }
  };

  const handleOAuthSuccess = () => {
    // This would be called after successful OAuth
    // For demo purposes, we'll simulate the response
    setAccessToken('demo_access_token');
    setGithubUser({
      id: 12345,
      login: 'demo-user',
      name: 'Demo User',
      email: '<EMAIL>',
      avatar_url: 'https://github.com/identicons/demo.png'
    });
    
    // Auto-fill connector name
    setConfig(prev => ({
      ...prev,
      name: `GitHub - demo-user`,
      description: 'GitHub connector for demo-user repositories'
    }));
    
    setCurrentStep(2);
  };

  const handleCreateConnector = async () => {
    if (!session || !accessToken) return;

    setLoading(true);
    setError(null);

    try {
      const api = createApiInstance(session);
      const response = await api.post('/connectors/github/connectors', {
        ...config,
        access_token: accessToken
      });

      if (onComplete) {
        onComplete(response.data.id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create connector');
    } finally {
      setLoading(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <GitBranch className="h-16 w-16 mx-auto mb-4 text-blue-500" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Connect to GitHub</h3>
        <p className="text-gray-600 mb-6">
          Authorize AIthentiq to access your GitHub repositories for document ingestion and synchronization.
        </p>
      </div>

      {!accessToken ? (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-blue-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-blue-800">Required Permissions</h4>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>Read repository contents and metadata</li>
                    <li>Access user profile information</li>
                    <li>Read private repositories (optional)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <button
            onClick={handleStartOAuth}
            disabled={loading}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
          >
            <GitBranch className="h-5 w-5" />
            <span>Authorize with GitHub</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-green-800">Successfully Connected</h4>
                <p className="text-sm text-green-700 mt-1">
                  Connected as <strong>@{githubUser?.login}</strong>
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
            <img
              src={githubUser?.avatar_url}
              alt={githubUser?.name}
              className="h-10 w-10 rounded-full"
            />
            <div>
              <p className="font-medium text-gray-900">{githubUser?.name}</p>
              <p className="text-sm text-gray-600">@{githubUser?.login}</p>
            </div>
          </div>

          <button
            onClick={() => setCurrentStep(2)}
            className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <span>Continue to Configuration</span>
            <ArrowRight className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-blue-500" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Configure Connector</h3>
        <p className="text-gray-600 mb-6">
          Set up sync preferences and repository access settings.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Connector Name *
          </label>
          <input
            type="text"
            value={config.name}
            onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., GitHub - My Repositories"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={config.description}
            onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Optional description for this connector"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sync Frequency (seconds)
            </label>
            <select
              value={config.sync_frequency}
              onChange={(e) => setConfig(prev => ({ ...prev, sync_frequency: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={300}>5 minutes</option>
              <option value={600}>10 minutes</option>
              <option value={1800}>30 minutes</option>
              <option value={3600}>1 hour</option>
              <option value={21600}>6 hours</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Files per Repository
            </label>
            <select
              value={config.max_files_per_repo}
              onChange={(e) => setConfig(prev => ({ ...prev, max_files_per_repo: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={500}>500 files</option>
              <option value={1000}>1,000 files</option>
              <option value={2000}>2,000 files</option>
              <option value={5000}>5,000 files</option>
            </select>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center">
            <input
              id="auto-sync"
              type="checkbox"
              checked={config.auto_sync}
              onChange={(e) => setConfig(prev => ({ ...prev, auto_sync: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="auto-sync" className="ml-2 block text-sm text-gray-900">
              Enable automatic synchronization
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="private-repos"
              type="checkbox"
              checked={config.include_private_repos}
              onChange={(e) => setConfig(prev => ({ ...prev, include_private_repos: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="private-repos" className="ml-2 block text-sm text-gray-900">
              Include private repositories
            </label>
          </div>
        </div>
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setCurrentStep(1)}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </button>
        <button
          onClick={() => setCurrentStep(3)}
          disabled={!config.name.trim()}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <span>Review</span>
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="h-16 w-16 mx-auto mb-4 text-green-500" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Review & Create</h3>
        <p className="text-gray-600 mb-6">
          Review your connector configuration and create the GitHub connector.
        </p>
      </div>

      <div className="bg-gray-50 rounded-lg p-6 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Connector Name:</span>
            <p className="font-medium">{config.name}</p>
          </div>
          <div>
            <span className="text-gray-500">GitHub User:</span>
            <p className="font-medium">@{githubUser?.login}</p>
          </div>
          <div>
            <span className="text-gray-500">Auto Sync:</span>
            <p className="font-medium">{config.auto_sync ? 'Enabled' : 'Disabled'}</p>
          </div>
          <div>
            <span className="text-gray-500">Sync Frequency:</span>
            <p className="font-medium">{config.sync_frequency / 60} minutes</p>
          </div>
          <div>
            <span className="text-gray-500">Private Repos:</span>
            <p className="font-medium">{config.include_private_repos ? 'Included' : 'Excluded'}</p>
          </div>
          <div>
            <span className="text-gray-500">Max Files:</span>
            <p className="font-medium">{config.max_files_per_repo.toLocaleString()}</p>
          </div>
        </div>
        
        {config.description && (
          <div>
            <span className="text-gray-500 text-sm">Description:</span>
            <p className="text-sm mt-1">{config.description}</p>
          </div>
        )}
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setCurrentStep(2)}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </button>
        <button
          onClick={handleCreateConnector}
          disabled={loading}
          className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
        >
          <Sync className="h-4 w-4" />
          <span>{loading ? 'Creating...' : 'Create Connector'}</span>
        </button>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                currentStep >= step.id
                  ? 'bg-blue-600 border-blue-600 text-white'
                  : 'border-gray-300 text-gray-500'
              }`}>
                {currentStep > step.id ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <span className="text-sm font-medium">{step.id}</span>
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-2 ${
                  currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="mt-4">
          <h2 className="text-lg font-semibold text-gray-900">{steps[currentStep - 1].title}</h2>
          <p className="text-sm text-gray-600">{steps[currentStep - 1].description}</p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Step Content */}
      <div className="bg-white rounded-lg shadow border p-6">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
      </div>

      {/* Cancel Button */}
      {onCancel && (
        <div className="mt-4 text-center">
          <button
            onClick={onCancel}
            className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel Setup
          </button>
        </div>
      )}
    </div>
  );
}
