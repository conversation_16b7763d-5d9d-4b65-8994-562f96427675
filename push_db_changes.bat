@echo off
echo.
echo 🔧 AIthentiq Database Schema Fix Script
echo ========================================
echo.

REM Configuration
set BACKEND_URL=https://aithentiq-backend.onrender.com
set API_KEY=your-api-key-here

echo 📡 Backend URL: %BACKEND_URL%
echo 🔑 Using API Key: %API_KEY:~0,10%...
echo.

echo 🔧 Starting Database Schema Fixes...
echo.

REM 1. Emergency Schema Fix
echo 🚀 Calling: Emergency Schema Fix
echo 📍 Endpoint: /emergency-schema-fix
curl -X POST -H "Content-Type: application/json" -H "X-API-Key: %API_KEY%" "%BACKEND_URL%/emergency-schema-fix"
echo.
echo ----------------------------------------
echo.

REM 2. Fix PostgreSQL Schema
echo 🚀 Calling: PostgreSQL Schema Fix
echo 📍 Endpoint: /fix-postgresql-schema
curl -X GET -H "Content-Type: application/json" -H "X-API-Key: %API_KEY%" "%BACKEND_URL%/fix-postgresql-schema"
echo.
echo ----------------------------------------
echo.

REM 3. Fix Database Schema (Main Fix)
echo 🚀 Calling: Main Database Schema Fix
echo 📍 Endpoint: /fix-database-schema
curl -X POST -H "Content-Type: application/json" -H "X-API-Key: %API_KEY%" "%BACKEND_URL%/fix-database-schema"
echo.
echo ----------------------------------------
echo.

REM 4. Analytics Database Migration
echo 🚀 Calling: Analytics Database Migration
echo 📍 Endpoint: /migrate-analytics-database
curl -X POST -H "Content-Type: application/json" -H "X-API-Key: %API_KEY%" "%BACKEND_URL%/migrate-analytics-database"
echo.
echo ----------------------------------------
echo.

echo 🎉 Database Schema Fix Script Completed!
echo.
echo 📝 Next Steps:
echo 1. Check your backend logs for any errors
echo 2. Test the frontend functionality
echo 3. Verify all features are working correctly
echo.
echo 🔗 Backend Health Check: %BACKEND_URL%/health
echo 🔗 API Documentation: %BACKEND_URL%/docs
echo.
pause
