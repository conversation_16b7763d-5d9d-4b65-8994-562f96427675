version: '3.8'

services:
  # Lift & Shift - Full Application in One Container
  aithentiq:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/aithentiq
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - STRIPE_API_KEY=${STRIPE_API_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - NODE_ENV=production
    depends_on:
      - db
    restart: unless-stopped
    profiles:
      - fullstack

  # Separate Services (Default)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      - NEXT_PUBLIC_PRO_PRICE_ID=${NEXT_PUBLIC_PRO_PRICE_ID}
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/aithentiq
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - STRIPE_API_KEY=${STRIPE_API_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
    depends_on:
      - db
    volumes:
      - ./backend:/app

  db:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=aithentiq
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
