# Frontend Deployment Instructions

## Current Status
- **Backend**: Converting existing Render service to Python/FastAPI backend
- **Frontend**: Needs separate deployment

## Option 1: Deploy Frontend to Vercel (Recommended)

1. **Install Vercel CLI**:
   ```bash
   npm i -g vercel
   ```

2. **Deploy from frontend directory**:
   ```bash
   cd frontend
   vercel --prod
   ```

3. **Set Environment Variables in Vercel**:
   - `NEXT_PUBLIC_API_URL`: `https://aithentiq.onrender.com`

## Option 2: Deploy Frontend to Netlify

1. **Build the frontend**:
   ```bash
   cd frontend
   npm run build
   ```

2. **Deploy to Netlify**:
   - Drag and drop the `frontend/.next` folder to netlify.com/drop

## Option 3: Create Second Render Service

1. Go to Render Dashboard
2. Click "New +" → "Web Service"
3. Connect same GitHub repo
4. Configure:
   - **Name**: `aithentiq-frontend`
   - **Environment**: `Node`
   - **Build Command**: `cd frontend && npm install && npm run build`
   - **Start Command**: `cd frontend && npm start`
   - **Environment Variables**:
     - `NODE_ENV`: `production`
     - `NEXT_PUBLIC_API_URL`: `https://aithentiq.onrender.com`

## Testing Backend

Once backend deploys, test these URLs:
- https://aithentiq.onrender.com/health
- https://aithentiq.onrender.com/test
- https://aithentiq.onrender.com/datasets/demo-user-id

## Current Configuration

The existing Render service is now configured as:
- **Environment**: Python
- **Backend**: FastAPI with uvicorn
- **Database**: Connected to PostgreSQL
- **CORS**: Enabled for all origins
