"""
Query Cache Service for persistent database-based caching
Provides faster responses and consistent results for identical questions
"""

import json
import hashlib
import pandas as pd
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func

import models
from database import SessionLocal


class QueryCacheService:
    """Service for managing persistent query cache in database"""
    
    def __init__(self):
        self.db = None
    
    def _get_db_session(self) -> Session:
        """Get database session"""
        if self.db is None:
            self.db = SessionLocal()
        return self.db
    
    def _close_db_session(self):
        """Close database session"""
        if self.db:
            self.db.close()
            self.db = None
    
    def create_cache_key(self, question: str, df: pd.DataFrame, include_cot: bool = False) -> str:
        """
        Create a deterministic cache key based on question and dataset content
        """
        # Normalize question
        normalized_question = question.lower().strip()
        
        # Sort columns to ensure consistent ordering
        sorted_columns = sorted(df.columns.tolist())
        
        # Create a deterministic representation of the dataframe
        df_sorted = df.reindex(sorted_columns, axis=1)
        if len(df_sorted) > 0:
            df_sorted = df_sorted.sort_values(by=sorted_columns[0])
        
        # Create hash of the dataframe content
        df_hash = hashlib.md5(df_sorted.to_string().encode()).hexdigest()
        
        # Create hash of the question
        question_hash = hashlib.md5(normalized_question.encode()).hexdigest()
        
        # Combine hashes with method indicator
        cache_key = f"{question_hash}_{df_hash}_cot_{include_cot}"
        
        return cache_key
    
    def create_question_hash(self, question: str) -> str:
        """Create hash for question only"""
        normalized_question = question.lower().strip()
        return hashlib.md5(normalized_question.encode()).hexdigest()
    
    def create_dataset_hash(self, df: pd.DataFrame) -> str:
        """Create hash for dataset only"""
        sorted_columns = sorted(df.columns.tolist())
        df_sorted = df.reindex(sorted_columns, axis=1)
        if len(df_sorted) > 0:
            df_sorted = df_sorted.sort_values(by=sorted_columns[0])
        return hashlib.md5(df_sorted.to_string().encode()).hexdigest()
    
    def get_cached_response(self, question: str, dataset_id: int, df: pd.DataFrame, include_cot: bool = False) -> Optional[Dict[str, Any]]:
        """
        Get cached response from database if available
        """
        try:
            db = self._get_db_session()
            
            # Create cache key
            cache_key = self.create_cache_key(question, df, include_cot)
            
            # Query database for cached response
            cached_query = db.query(models.QueryCache).filter(
                models.QueryCache.cache_key == cache_key,
                models.QueryCache.dataset_id == dataset_id
            ).first()
            
            if cached_query:
                # Update hit count and last accessed time
                cached_query.hit_count += 1
                cached_query.last_accessed = datetime.utcnow()
                db.commit()
                
                # Prepare response
                response = {
                    "answer": cached_query.answer,
                    "chart_type": cached_query.chart_type,
                    "chart_data": json.loads(cached_query.chart_data) if cached_query.chart_data else None,
                    "trust_score": json.loads(cached_query.trust_score) if cached_query.trust_score else None,
                    "reasoning_steps": json.loads(cached_query.reasoning_steps) if cached_query.reasoning_steps else None,
                    "formula_results": json.loads(cached_query.formula_results) if cached_query.formula_results else None,
                    "_cached": True,
                    "_cache_hit_count": cached_query.hit_count,
                    "_cache_created": cached_query.created_at.isoformat()
                }
                
                print(f"✅ Database cache HIT for question: {question[:50]}... (hit count: {cached_query.hit_count})")
                return response
            
            print(f"❌ Database cache MISS for question: {question[:50]}...")
            return None
            
        except Exception as e:
            print(f"❌ Error retrieving from cache: {str(e)}")
            return None
        finally:
            self._close_db_session()
    
    def cache_response(self, question: str, dataset_id: int, df: pd.DataFrame, response: Dict[str, Any], include_cot: bool = False) -> bool:
        """
        Cache response in database
        """
        try:
            db = self._get_db_session()
            
            # Create hashes
            cache_key = self.create_cache_key(question, df, include_cot)
            question_hash = self.create_question_hash(question)
            dataset_hash = self.create_dataset_hash(df)
            
            # Check if already exists (shouldn't happen, but just in case)
            existing = db.query(models.QueryCache).filter(
                models.QueryCache.cache_key == cache_key
            ).first()
            
            if existing:
                print(f"⚠️ Cache entry already exists for key: {cache_key}")
                return True
            
            # Create new cache entry
            cache_entry = models.QueryCache(
                cache_key=cache_key,
                dataset_id=dataset_id,
                question=question,
                question_hash=question_hash,
                dataset_hash=dataset_hash,
                answer=response.get("answer", ""),
                chart_type=response.get("chart_type"),
                chart_data=json.dumps(response.get("chart_data")) if response.get("chart_data") else None,
                trust_score=json.dumps(response.get("trust_score")) if response.get("trust_score") else None,
                reasoning_steps=json.dumps(response.get("reasoning_steps")) if response.get("reasoning_steps") else None,
                formula_results=json.dumps(response.get("formula_results")) if response.get("formula_results") else None,
                include_cot=include_cot,
                hit_count=1,
                last_accessed=datetime.utcnow(),
                created_at=datetime.utcnow()
            )
            
            db.add(cache_entry)
            db.commit()
            
            print(f"✅ Response cached in database for question: {question[:50]}...")
            return True
            
        except Exception as e:
            print(f"❌ Error caching response: {str(e)}")
            if db:
                db.rollback()
            return False
        finally:
            self._close_db_session()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            db = self._get_db_session()
            
            total_entries = db.query(func.count(models.QueryCache.id)).scalar()
            total_hits = db.query(func.sum(models.QueryCache.hit_count)).scalar() or 0
            
            # Get most popular queries
            popular_queries = db.query(
                models.QueryCache.question,
                models.QueryCache.hit_count
            ).order_by(models.QueryCache.hit_count.desc()).limit(5).all()
            
            # Get recent cache entries
            recent_entries = db.query(
                models.QueryCache.question,
                models.QueryCache.created_at
            ).order_by(models.QueryCache.created_at.desc()).limit(5).all()
            
            return {
                "total_entries": total_entries,
                "total_hits": total_hits,
                "cache_efficiency": round((total_hits - total_entries) / max(total_hits, 1) * 100, 2),
                "popular_queries": [{"question": q.question[:100], "hits": q.hit_count} for q in popular_queries],
                "recent_entries": [{"question": q.question[:100], "created": q.created_at.isoformat()} for q in recent_entries]
            }
            
        except Exception as e:
            print(f"❌ Error getting cache stats: {str(e)}")
            return {"error": str(e)}
        finally:
            self._close_db_session()
    
    def clear_cache(self, dataset_id: Optional[int] = None) -> bool:
        """Clear cache entries (optionally for specific dataset)"""
        try:
            db = self._get_db_session()
            
            if dataset_id:
                deleted = db.query(models.QueryCache).filter(
                    models.QueryCache.dataset_id == dataset_id
                ).delete()
                print(f"✅ Cleared {deleted} cache entries for dataset {dataset_id}")
            else:
                deleted = db.query(models.QueryCache).delete()
                print(f"✅ Cleared all {deleted} cache entries")
            
            db.commit()
            return True
            
        except Exception as e:
            print(f"❌ Error clearing cache: {str(e)}")
            if db:
                db.rollback()
            return False
        finally:
            self._close_db_session()


# Global instance
query_cache_service = QueryCacheService()
