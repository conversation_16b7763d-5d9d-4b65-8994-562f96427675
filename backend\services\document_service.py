"""
Document Processing Service for AIthentiq
Handles PDF, DOCX, TXT, MD file processing with metadata extraction and embeddings
Enhanced with RAGFlow-inspired template-based chunking and advanced document understanding
"""

import os
import json
import hashlib
import re
from typing import Dict, Any, Optional, List, Tuple, Union
from io import BytesIO
import pandas as pd
from datetime import datetime
from enum import Enum

# Document processing imports
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("PyMuPDF not available - PDF processing disabled")

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("python-docx not available - DOCX processing disabled")

try:
    # from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = False  # Temporarily disabled due to torch issues
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    print("sentence-transformers not available - embeddings disabled")

# Import chunk service for professional chunk management
try:
    from services.chunk_service import ChunkService
    CHUNK_SERVICE_AVAILABLE = True
except ImportError:
    CHUNK_SERVICE_AVAILABLE = False
    print("ChunkService not available - using legacy chunking")

# OCR imports
try:
    import pytesseract
    import cv2
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("OCR libraries not available - image processing disabled")


class ChunkingTemplate(Enum):
    """Template types for different document structures"""
    GENERAL = "general"
    ACADEMIC = "academic"
    TECHNICAL = "technical"
    LEGAL = "legal"
    FINANCIAL = "financial"
    MANUAL = "manual"
    RESEARCH = "research"
    BOOK = "book"
    ARTICLE = "article"
    PRESENTATION = "presentation"


class ChunkingStrategy:
    """Advanced chunking strategies inspired by RAGFlow"""

    def __init__(self):
        self.templates = {
            ChunkingTemplate.GENERAL: {
                'chunk_size': 512,
                'overlap': 50,
                'respect_sentences': True,
                'respect_paragraphs': True,
                'preserve_structure': False,
                'min_chunk_size': 100,
                'max_chunk_size': 1000
            },
            ChunkingTemplate.ACADEMIC: {
                'chunk_size': 800,
                'overlap': 100,
                'respect_sentences': True,
                'respect_paragraphs': True,
                'preserve_structure': True,
                'min_chunk_size': 200,
                'max_chunk_size': 1500,
                'section_aware': True,
                'citation_aware': True
            },
            ChunkingTemplate.TECHNICAL: {
                'chunk_size': 600,
                'overlap': 80,
                'respect_sentences': True,
                'respect_paragraphs': True,
                'preserve_structure': True,
                'min_chunk_size': 150,
                'max_chunk_size': 1200,
                'code_aware': True,
                'list_aware': True
            },
            ChunkingTemplate.LEGAL: {
                'chunk_size': 1000,
                'overlap': 150,
                'respect_sentences': True,
                'respect_paragraphs': True,
                'preserve_structure': True,
                'min_chunk_size': 300,
                'max_chunk_size': 2000,
                'clause_aware': True,
                'numbering_aware': True
            },
            ChunkingTemplate.FINANCIAL: {
                'chunk_size': 400,
                'overlap': 60,
                'respect_sentences': True,
                'respect_paragraphs': True,
                'preserve_structure': True,
                'min_chunk_size': 100,
                'max_chunk_size': 800,
                'table_aware': True,
                'number_aware': True
            },
            ChunkingTemplate.RESEARCH: {
                'chunk_size': 700,
                'overlap': 100,
                'respect_sentences': True,
                'respect_paragraphs': True,
                'preserve_structure': True,
                'min_chunk_size': 200,
                'max_chunk_size': 1400,
                'methodology_aware': True,
                'reference_aware': True
            }
        }

    def detect_document_type(self, text: str, metadata: Dict[str, Any]) -> ChunkingTemplate:
        """Automatically detect the best chunking template for a document"""
        text_lower = text.lower()

        # Academic indicators
        academic_keywords = ['abstract', 'methodology', 'conclusion', 'references', 'bibliography',
                           'hypothesis', 'research', 'study', 'analysis', 'findings']
        academic_score = sum(1 for keyword in academic_keywords if keyword in text_lower)

        # Technical indicators
        technical_keywords = ['function', 'algorithm', 'implementation', 'system', 'architecture',
                            'configuration', 'installation', 'setup', 'api', 'documentation']
        technical_score = sum(1 for keyword in technical_keywords if keyword in text_lower)

        # Legal indicators
        legal_keywords = ['whereas', 'therefore', 'pursuant', 'clause', 'section', 'article',
                         'agreement', 'contract', 'terms', 'conditions', 'liability']
        legal_score = sum(1 for keyword in legal_keywords if keyword in text_lower)

        # Financial indicators
        financial_keywords = ['revenue', 'profit', 'loss', 'balance', 'financial', 'accounting',
                             'investment', 'assets', 'liabilities', 'cash flow', 'budget']
        financial_score = sum(1 for keyword in financial_keywords if keyword in text_lower)

        # Check for code blocks (technical)
        code_patterns = len(re.findall(r'```|`[^`]+`|def |class |function|import |#include', text))
        if code_patterns > 5:
            technical_score += 3

        # Check for citations (academic)
        citation_patterns = len(re.findall(r'\[\d+\]|\(\d{4}\)|et al\.|doi:', text))
        if citation_patterns > 3:
            academic_score += 2

        # Check for numbered sections (legal/technical)
        numbered_sections = len(re.findall(r'^\d+\.\d+|Section \d+|Article \d+', text, re.MULTILINE))
        if numbered_sections > 3:
            legal_score += 2
            technical_score += 1

        # Determine best template
        scores = {
            ChunkingTemplate.ACADEMIC: academic_score,
            ChunkingTemplate.TECHNICAL: technical_score,
            ChunkingTemplate.LEGAL: legal_score,
            ChunkingTemplate.FINANCIAL: financial_score
        }

        max_score = max(scores.values())
        if max_score >= 3:
            return max(scores, key=scores.get)

        return ChunkingTemplate.GENERAL

    def chunk_text_with_template(self, text: str, template: ChunkingTemplate,
                               metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Chunk text using the specified template with advanced strategies"""
        config = self.templates[template]

        # Pre-process text based on template
        if template == ChunkingTemplate.ACADEMIC:
            return self._chunk_academic_text(text, config, metadata)
        elif template == ChunkingTemplate.TECHNICAL:
            return self._chunk_technical_text(text, config, metadata)
        elif template == ChunkingTemplate.LEGAL:
            return self._chunk_legal_text(text, config, metadata)
        elif template == ChunkingTemplate.FINANCIAL:
            return self._chunk_financial_text(text, config, metadata)
        else:
            return self._chunk_general_text(text, config, metadata)

    def _chunk_general_text(self, text: str, config: Dict[str, Any],
                          metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """General purpose text chunking with sentence and paragraph awareness"""
        chunks = []
        lines = text.split('\n')

        if config.get('respect_paragraphs', True):
            paragraphs = text.split('\n\n')
            current_chunk = ""
            chunk_id = 0
            current_line_start = 1
            current_char_start = 0

            for para in paragraphs:
                para = para.strip()
                if not para:
                    continue

                # Calculate line and character positions
                para_line_count = para.count('\n') + 1
                para_char_count = len(para)

                # If adding this paragraph would exceed chunk size, finalize current chunk
                if (len(current_chunk) + len(para) > config['chunk_size'] and
                    len(current_chunk) >= config['min_chunk_size']):

                    if current_chunk:
                        # Calculate line end for current chunk
                        current_line_end = current_line_start + current_chunk.count('\n')
                        current_char_end = current_char_start + len(current_chunk)

                        source_metadata = {
                            'document_name': metadata.get('filename') if metadata else None,
                            'line_start': current_line_start,
                            'line_end': current_line_end,
                            'char_start': current_char_start,
                            'char_end': current_char_end,
                            'page_number': metadata.get('page_number') if metadata else None
                        }

                        chunks.append(self._create_chunk(current_chunk, chunk_id, 'paragraph', source_metadata))
                        chunk_id += 1

                    # Start new chunk
                    current_line_start = current_line_end + 1 if current_chunk else 1
                    current_char_start = current_char_end if current_chunk else 0
                    current_chunk = para
                else:
                    current_chunk += ("\n\n" if current_chunk else "") + para

            # Add final chunk
            if current_chunk and len(current_chunk) >= config['min_chunk_size']:
                current_line_end = current_line_start + current_chunk.count('\n')
                current_char_end = current_char_start + len(current_chunk)

                source_metadata = {
                    'document_name': metadata.get('filename') if metadata else None,
                    'line_start': current_line_start,
                    'line_end': current_line_end,
                    'char_start': current_char_start,
                    'char_end': current_char_end,
                    'page_number': metadata.get('page_number') if metadata else None
                }

                chunks.append(self._create_chunk(current_chunk, chunk_id, 'paragraph', source_metadata))

        else:
            # Simple word-based chunking with overlap
            words = text.split()
            chunk_size = config['chunk_size']
            overlap = config['overlap']
            chunk_id = 0
            char_position = 0

            for i in range(0, len(words), chunk_size - overlap):
                chunk_words = words[i:i + chunk_size]
                chunk_text = ' '.join(chunk_words)

                if len(chunk_text) >= config['min_chunk_size']:
                    # Calculate approximate line positions for word-based chunks
                    chunk_start_char = char_position
                    chunk_end_char = char_position + len(chunk_text)

                    # Find line numbers based on character positions
                    text_before_chunk = text[:chunk_start_char]
                    line_start = text_before_chunk.count('\n') + 1
                    line_end = line_start + chunk_text.count('\n')

                    source_metadata = {
                        'document_name': metadata.get('filename') if metadata else None,
                        'line_start': line_start,
                        'line_end': line_end,
                        'char_start': chunk_start_char,
                        'char_end': chunk_end_char,
                        'page_number': metadata.get('page_number') if metadata else None
                    }

                    chunks.append(self._create_chunk(chunk_text, chunk_id, 'word', source_metadata))
                    chunk_id += 1

                char_position += len(' '.join(words[i:i + chunk_size - overlap])) + 1

        return chunks

    def _chunk_academic_text(self, text: str, config: Dict[str, Any],
                           metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Academic text chunking with section and citation awareness"""
        chunks = []

        # Detect sections (Abstract, Introduction, Methods, etc.)
        section_patterns = [
            r'^(Abstract|Introduction|Background|Literature Review|Methodology|Methods|Results|Discussion|Conclusion|References|Bibliography).*$',
            r'^\d+\.\s+(Introduction|Background|Methods|Results|Discussion|Conclusion)',
            r'^[IVX]+\.\s+[A-Z]'
        ]

        sections = []
        current_section = {'title': 'Introduction', 'start': 0, 'content': ''}

        lines = text.split('\n')
        for i, line in enumerate(lines):
            line_stripped = line.strip()

            # Check if this line is a section header
            is_section = False
            for pattern in section_patterns:
                if re.match(pattern, line_stripped, re.IGNORECASE):
                    # Save previous section
                    if current_section['content']:
                        sections.append(current_section)

                    # Start new section
                    current_section = {
                        'title': line_stripped,
                        'start': i,
                        'content': ''
                    }
                    is_section = True
                    break

            if not is_section:
                current_section['content'] += line + '\n'

        # Add final section
        if current_section['content']:
            sections.append(current_section)

        # Chunk each section separately
        chunk_id = 0
        for section in sections:
            section_chunks = self._chunk_section_content(
                section['content'],
                config,
                section['title'],
                chunk_id
            )
            chunks.extend(section_chunks)
            chunk_id += len(section_chunks)

        return chunks

    def _chunk_technical_text(self, text: str, config: Dict[str, Any],
                            metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Technical text chunking with code block and list awareness"""
        chunks = []

        # Split by code blocks first
        code_pattern = r'```[\s\S]*?```|`[^`]+`'
        parts = re.split(f'({code_pattern})', text)

        chunk_id = 0
        current_text_chunk = ""

        for part in parts:
            if re.match(code_pattern, part):
                # This is a code block - treat as separate chunk if large enough
                if len(part) >= config['min_chunk_size']:
                    # Finalize current text chunk if exists
                    if current_text_chunk and len(current_text_chunk) >= config['min_chunk_size']:
                        source_metadata = {
                            'document_name': metadata.get('filename') if metadata else None,
                            'chunk_type': 'text'
                        }
                        chunks.append(self._create_chunk(current_text_chunk, chunk_id, 'text', source_metadata))
                        chunk_id += 1
                        current_text_chunk = ""

                    # Add code block as separate chunk
                    source_metadata = {
                        'document_name': metadata.get('filename') if metadata else None,
                        'chunk_type': 'code'
                    }
                    chunks.append(self._create_chunk(part, chunk_id, 'code', source_metadata))
                    chunk_id += 1
                else:
                    current_text_chunk += part
            else:
                # Regular text - add to current chunk
                current_text_chunk += part

                # Check if chunk is getting too large
                if len(current_text_chunk) > config['chunk_size']:
                    # Split the text chunk using simple word-based chunking to avoid recursion
                    words = current_text_chunk.split()
                    chunk_size_words = config['chunk_size'] // 5  # Approximate words per chunk
                    overlap_words = config['overlap'] // 5

                    for i in range(0, len(words), chunk_size_words - overlap_words):
                        chunk_words = words[i:i + chunk_size_words]
                        chunk_text = ' '.join(chunk_words)

                        if len(chunk_text) >= config['min_chunk_size']:
                            chunks.append(self._create_chunk(chunk_text, chunk_id, 'text'))
                            chunk_id += 1

                    current_text_chunk = ""

        # Add final text chunk
        if current_text_chunk and len(current_text_chunk) >= config['min_chunk_size']:
            chunks.append(self._create_chunk(current_text_chunk, chunk_id, 'text'))

        return chunks

    def _chunk_legal_text(self, text: str, config: Dict[str, Any],
                        metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Legal text chunking with clause and section awareness"""
        chunks = []

        # Legal documents often have numbered sections/clauses
        clause_patterns = [
            r'^\d+\.\d+\.?\s+',  # 1.1, 1.2.1, etc.
            r'^Section \d+',
            r'^Article \d+',
            r'^\([a-z]\)\s+',    # (a), (b), etc.
            r'^\([ivx]+\)\s+'    # (i), (ii), etc.
        ]

        lines = text.split('\n')
        current_clause = {'content': '', 'level': 0}
        clauses = []

        for line in lines:
            line_stripped = line.strip()

            # Check if this is a new clause/section
            is_clause = False
            clause_level = 0

            for i, pattern in enumerate(clause_patterns):
                if re.match(pattern, line_stripped):
                    is_clause = True
                    clause_level = i + 1
                    break

            if is_clause and current_clause['content']:
                # Save previous clause
                clauses.append(current_clause)
                current_clause = {'content': line + '\n', 'level': clause_level}
            else:
                current_clause['content'] += line + '\n'

        # Add final clause
        if current_clause['content']:
            clauses.append(current_clause)

        # Chunk clauses, potentially combining smaller ones
        chunk_id = 0
        current_chunk = ""

        for clause in clauses:
            if (len(current_chunk) + len(clause['content']) > config['chunk_size'] and
                len(current_chunk) >= config['min_chunk_size']):

                source_metadata = {
                    'document_name': metadata.get('filename') if metadata else None,
                    'chunk_type': 'legal_clause'
                }
                chunks.append(self._create_chunk(current_chunk, chunk_id, 'legal_clause', source_metadata))
                chunk_id += 1
                current_chunk = clause['content']
            else:
                current_chunk += clause['content']

        # Add final chunk
        if current_chunk and len(current_chunk) >= config['min_chunk_size']:
            source_metadata = {
                'document_name': metadata.get('filename') if metadata else None,
                'chunk_type': 'legal_clause'
            }
            chunks.append(self._create_chunk(current_chunk, chunk_id, 'legal_clause', source_metadata))

        return chunks

    def _chunk_financial_text(self, text: str, config: Dict[str, Any],
                            metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Financial text chunking with table and number awareness"""
        chunks = []

        # Financial documents often have tables and numerical data
        # Split by table markers first
        table_pattern = r'(--- TABLE.*?---[\s\S]*?)(?=--- TABLE|$)'
        parts = re.split(table_pattern, text)

        chunk_id = 0

        for part in parts:
            part = part.strip()
            if not part:
                continue

            if '--- TABLE' in part:
                # This is a table - treat as separate chunk
                source_metadata = {
                    'document_name': metadata.get('filename') if metadata else None,
                    'chunk_type': 'table'
                }
                chunks.append(self._create_chunk(part, chunk_id, 'table', source_metadata))
                chunk_id += 1
            else:
                # Regular text - chunk using simple method to avoid recursion
                paragraphs = part.split('\n\n')
                current_chunk = ""

                for para in paragraphs:
                    para = para.strip()
                    if not para:
                        continue

                    if (len(current_chunk) + len(para) > config['chunk_size'] and
                        len(current_chunk) >= config['min_chunk_size']):

                        if current_chunk:
                            source_metadata = {
                                'document_name': metadata.get('filename') if metadata else None,
                                'chunk_type': 'financial_text'
                            }
                            chunk_obj = self._create_chunk(current_chunk, chunk_id, 'financial_text', source_metadata)
                            chunks.append(chunk_obj)
                            chunk_id += 1

                        current_chunk = para
                    else:
                        current_chunk += ("\n\n" if current_chunk else "") + para

                # Add final chunk
                if current_chunk and len(current_chunk) >= config['min_chunk_size']:
                    source_metadata = {
                        'document_name': metadata.get('filename') if metadata else None,
                        'chunk_type': 'financial_text'
                    }
                    chunk_obj = self._create_chunk(current_chunk, chunk_id, 'financial_text', source_metadata)
                    chunks.append(chunk_obj)
                    chunk_id += 1

        return chunks

    def _chunk_section_content(self, content: str, config: Dict[str, Any],
                             section_title: str, start_chunk_id: int) -> List[Dict[str, Any]]:
        """Chunk content within a section"""
        # Use simple paragraph-based chunking to avoid recursion
        chunks = []
        paragraphs = content.split('\n\n')
        current_chunk = ""
        chunk_id = start_chunk_id

        for para in paragraphs:
            para = para.strip()
            if not para:
                continue

            # If adding this paragraph would exceed chunk size, finalize current chunk
            if (len(current_chunk) + len(para) > config['chunk_size'] and
                len(current_chunk) >= config['min_chunk_size']):

                if current_chunk:
                    source_metadata = {
                        'document_name': metadata.get('filename') if metadata else None,
                        'section': section_title
                    }
                    chunks.append(self._create_chunk(current_chunk, chunk_id, 'section_content', source_metadata))
                    chunk_id += 1

                current_chunk = para
            else:
                current_chunk += ("\n\n" if current_chunk else "") + para

        # Add final chunk
        if current_chunk and len(current_chunk) >= config['min_chunk_size']:
            source_metadata = {
                'document_name': metadata.get('filename') if metadata else None,
                'section': section_title
            }
            chunks.append(self._create_chunk(current_chunk, chunk_id, 'section_content', source_metadata))

        return chunks

    def _create_chunk(self, text: str, chunk_id: int, chunk_type: str,
                     source_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a standardized chunk object with source attribution"""
        chunk = {
            'chunk_id': chunk_id,
            'text': text.strip(),
            'chunk_type': chunk_type,
            'word_count': len(text.split()),
            'char_count': len(text),
            'created_at': datetime.utcnow().isoformat()
        }

        # Add source attribution metadata if provided
        if source_metadata:
            chunk.update({
                'source_document': source_metadata.get('document_name'),
                'line_start': source_metadata.get('line_start'),
                'line_end': source_metadata.get('line_end'),
                'char_start': source_metadata.get('char_start'),
                'char_end': source_metadata.get('char_end'),
                'page_number': source_metadata.get('page_number'),
                'section': source_metadata.get('section')
            })

        return chunk


class DocumentProcessor:
    """
    Advanced document processing with metadata extraction and embeddings
    Enhanced with RAGFlow-inspired template-based chunking
    """

    def __init__(self):
        self.supported_formats = {
            'pdf': self._process_pdf,
            'docx': self._process_docx,
            'txt': self._process_txt,
            'md': self._process_markdown,
            'jpg': self._process_image,
            'jpeg': self._process_image,
            'png': self._process_image
        }

        # Initialize advanced chunking strategy
        self.chunking_strategy = ChunkingStrategy()

        # Initialize professional chunk service if available
        if CHUNK_SERVICE_AVAILABLE:
            self.chunk_service = ChunkService()
        else:
            self.chunk_service = None

        # Initialize embedding model if available
        self.embedding_model = None
        if EMBEDDINGS_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                print("Embedding model loaded successfully")
            except Exception as e:
                print(f"Failed to load embedding model: {e}")

        # Initialize OCR if available
        self.ocr_available = OCR_AVAILABLE
        self.supported_languages = ['en']  # Default to English
        if OCR_AVAILABLE:
            try:
                # Test if Tesseract is available
                pytesseract.get_tesseract_version()
                print("Tesseract OCR loaded successfully")

                # Try to get available languages
                try:
                    langs = pytesseract.get_languages(config='')
                    # Common languages to support
                    priority_langs = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko', 'ar', 'hi']
                    self.supported_languages = [lang for lang in priority_langs if lang in langs]
                    print(f"OCR languages available: {self.supported_languages}")
                except:
                    print("Could not detect available OCR languages, using English only")

            except Exception as e:
                print(f"Tesseract not available: {e}")
                self.ocr_available = False
    
    def process_document(self, file_content: bytes, filename: str, file_type: str) -> Dict[str, Any]:
        """
        Process a document and extract text, metadata, and embeddings
        
        Args:
            file_content: Raw file bytes
            filename: Original filename
            file_type: File extension (pdf, docx, txt, md)
            
        Returns:
            Dictionary with processed content and metadata
        """
        try:
            # Get processor for file type
            processor = self.supported_formats.get(file_type.lower())
            if not processor:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            # Process the document
            result = processor(file_content, filename)
            
            # Add common metadata
            result.update({
                'filename': filename,
                'file_type': file_type,
                'processed_at': datetime.utcnow().isoformat(),
                'file_size': len(file_content),
                'content_hash': hashlib.md5(file_content).hexdigest()
            })
            
            # Enhanced chunking with template-based strategy
            if result.get('text'):
                try:
                    # Detect document type and apply appropriate chunking template
                    detected_template = self.chunking_strategy.detect_document_type(
                        result['text'],
                        result.get('metadata', {})
                    )

                    # Create intelligent chunks with filename metadata
                    chunk_metadata = result.get('metadata', {})
                    chunk_metadata['filename'] = filename

                    intelligent_chunks = self.chunking_strategy.chunk_text_with_template(
                        result['text'],
                        detected_template,
                        chunk_metadata
                    )

                    result['chunks'] = intelligent_chunks
                    result['chunking_template'] = detected_template.value
                    result['total_chunks'] = len(intelligent_chunks)

                    print(f"Created {len(intelligent_chunks)} chunks using {detected_template.value} template")

                except Exception as e:
                    print(f"Advanced chunking failed, falling back to simple chunking: {e}")
                    # Fallback to simple chunking
                    simple_chunks = self._chunk_text_simple(result['text'])
                    result['chunks'] = [
                        {'chunk_id': i, 'text': chunk, 'chunk_type': 'simple',
                         'word_count': len(chunk.split()), 'char_count': len(chunk)}
                        for i, chunk in enumerate(simple_chunks)
                    ]
                    result['chunking_template'] = 'simple_fallback'
                    result['total_chunks'] = len(simple_chunks)

            # Generate embeddings if available
            if self.embedding_model and result.get('chunks'):
                try:
                    embeddings = []

                    for chunk_data in result['chunks']:
                        chunk_text = chunk_data['text']
                        embedding = self.embedding_model.encode(chunk_text).tolist()

                        chunk_data['embedding'] = embedding
                        embeddings.append({
                            'chunk_id': chunk_data['chunk_id'],
                            'text': chunk_text,
                            'embedding': embedding,
                            'chunk_type': chunk_data.get('chunk_type', 'unknown')
                        })

                    result['embeddings'] = embeddings
                    result['embedding_model'] = 'all-MiniLM-L6-v2'

                except Exception as e:
                    print(f"Failed to generate embeddings: {e}")
                    result['embeddings'] = []
            
            return result
            
        except Exception as e:
            return {
                'error': str(e),
                'filename': filename,
                'file_type': file_type,
                'processed_at': datetime.utcnow().isoformat()
            }
    
    def _process_pdf(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process PDF files using PyMuPDF with advanced table and form extraction"""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("PyMuPDF not available for PDF processing")

        try:
            # Open PDF from bytes
            pdf_document = fitz.open(stream=file_content, filetype="pdf")

            text_content = ""
            all_tables = []
            all_forms = []
            metadata = {
                'page_count': pdf_document.page_count,
                'pages': [],
                'document_intelligence': {
                    'total_tables': 0,
                    'total_forms': 0,
                    'has_structured_content': False
                }
            }

            # Extract content from each page
            for page_num in range(pdf_document.page_count):
                page = pdf_document[page_num]
                page_text = page.get_text()

                # Extract tables from page
                page_tables = self._extract_tables_from_pdf_page(page)
                all_tables.extend(page_tables)

                # Extract form fields
                page_forms = self._extract_forms_from_pdf_page(page)
                all_forms.extend(page_forms)

                # Add table content to text
                if page_tables:
                    table_text = "\n\n--- TABLES ON PAGE {} ---\n".format(page_num + 1)
                    for i, table in enumerate(page_tables):
                        table_text += f"\nTable {i+1}:\n"
                        table_text += self._format_table_as_text(table)
                    page_text += table_text

                # Add form content to text
                if page_forms:
                    form_text = "\n\n--- FORMS ON PAGE {} ---\n".format(page_num + 1)
                    for form in page_forms:
                        form_text += f"Form Field: {form['name']} = {form['value']}\n"
                    page_text += form_text

                text_content += f"\n--- Page {page_num + 1} ---\n{page_text}"

                # Extract page metadata
                page_metadata = {
                    'page_number': page_num + 1,
                    'text_length': len(page_text),
                    'has_images': len(page.get_images()) > 0,
                    'image_count': len(page.get_images()),
                    'table_count': len(page_tables),
                    'form_field_count': len(page_forms),
                    'has_structured_content': len(page_tables) > 0 or len(page_forms) > 0
                }
                metadata['pages'].append(page_metadata)

            # Update document intelligence metadata
            metadata['document_intelligence'].update({
                'total_tables': len(all_tables),
                'total_forms': len(all_forms),
                'has_structured_content': len(all_tables) > 0 or len(all_forms) > 0,
                'tables': all_tables[:5],  # Store first 5 tables
                'forms': all_forms[:10]    # Store first 10 form fields
            })

            # Extract document metadata
            doc_metadata = pdf_document.metadata
            metadata.update({
                'title': doc_metadata.get('title', ''),
                'author': doc_metadata.get('author', ''),
                'subject': doc_metadata.get('subject', ''),
                'creator': doc_metadata.get('creator', ''),
                'creation_date': doc_metadata.get('creationDate', ''),
                'modification_date': doc_metadata.get('modDate', '')
            })

            pdf_document.close()

            return {
                'text': text_content.strip(),
                'metadata': metadata,
                'word_count': len(text_content.split()),
                'character_count': len(text_content),
                'processing_status': 'success'
            }

        except Exception as e:
            raise Exception(f"PDF processing failed: {str(e)}")

    def _extract_tables_from_pdf_page(self, page) -> List[Dict[str, Any]]:
        """Extract tables from a PDF page"""
        tables = []
        try:
            # Try to find tables using PyMuPDF's table detection
            tabs = page.find_tables()
            for tab in tabs:
                try:
                    table_data = tab.extract()
                    if table_data and len(table_data) > 1:  # At least header + 1 row
                        tables.append({
                            'data': table_data,
                            'bbox': tab.bbox,
                            'rows': len(table_data),
                            'cols': len(table_data[0]) if table_data else 0
                        })
                except:
                    continue
        except Exception as e:
            print(f"Table extraction failed: {e}")

        return tables

    def _extract_forms_from_pdf_page(self, page) -> List[Dict[str, Any]]:
        """Extract form fields from a PDF page"""
        forms = []
        try:
            # Get form fields (widgets) from the page
            widgets = page.widgets()
            for widget in widgets:
                try:
                    forms.append({
                        'name': widget.field_name or 'unnamed',
                        'type': widget.field_type_string,
                        'value': widget.field_value or '',
                        'bbox': widget.rect
                    })
                except:
                    continue
        except Exception as e:
            print(f"Form extraction failed: {e}")

        return forms

    def _format_table_as_text(self, table: Dict[str, Any]) -> str:
        """Format extracted table as readable text"""
        try:
            data = table['data']
            if not data:
                return "Empty table\n"

            # Calculate column widths
            col_widths = []
            for col_idx in range(len(data[0])):
                max_width = max(len(str(row[col_idx] or '')) for row in data)
                col_widths.append(min(max_width, 30))  # Cap at 30 chars

            # Format table
            formatted = ""
            for row_idx, row in enumerate(data):
                row_text = " | ".join(str(cell or '').ljust(col_widths[col_idx])
                                    for col_idx, cell in enumerate(row))
                formatted += row_text + "\n"

                # Add separator after header
                if row_idx == 0:
                    separator = " | ".join("-" * col_widths[col_idx]
                                         for col_idx in range(len(row)))
                    formatted += separator + "\n"

            return formatted

        except Exception as e:
            return f"Table formatting error: {e}\n"
    
    def _process_docx(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process DOCX files using python-docx"""
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx not available for DOCX processing")
        
        try:
            # Open DOCX from bytes
            document = Document(BytesIO(file_content))
            
            text_content = ""
            metadata = {
                'paragraph_count': 0,
                'table_count': 0,
                'image_count': 0,
                'paragraphs': []
            }
            
            # Extract text from paragraphs
            for para in document.paragraphs:
                if para.text.strip():
                    text_content += para.text + "\n"
                    metadata['paragraphs'].append({
                        'text': para.text,
                        'style': para.style.name if para.style else 'Normal'
                    })
                    metadata['paragraph_count'] += 1
            
            # Extract text from tables
            for table in document.tables:
                metadata['table_count'] += 1
                table_text = "\n--- Table ---\n"
                for row in table.rows:
                    row_text = " | ".join([cell.text for cell in row.cells])
                    table_text += row_text + "\n"
                text_content += table_text
            
            # Count images (approximate)
            for rel in document.part.rels.values():
                if "image" in rel.target_ref:
                    metadata['image_count'] += 1
            
            # Extract core properties
            core_props = document.core_properties
            metadata.update({
                'title': core_props.title or '',
                'author': core_props.author or '',
                'subject': core_props.subject or '',
                'created': core_props.created.isoformat() if core_props.created else '',
                'modified': core_props.modified.isoformat() if core_props.modified else ''
            })
            
            return {
                'text': text_content.strip(),
                'metadata': metadata,
                'word_count': len(text_content.split()),
                'character_count': len(text_content),
                'processing_status': 'success'
            }
            
        except Exception as e:
            raise Exception(f"DOCX processing failed: {str(e)}")
    
    def _process_txt(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process TXT files"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            text_content = None
            
            for encoding in encodings:
                try:
                    text_content = file_content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if text_content is None:
                raise Exception("Could not decode text file with any supported encoding")
            
            # Basic text analysis
            lines = text_content.split('\n')
            metadata = {
                'line_count': len(lines),
                'encoding': encoding,
                'empty_lines': sum(1 for line in lines if not line.strip()),
                'max_line_length': max(len(line) for line in lines) if lines else 0
            }
            
            return {
                'text': text_content.strip(),
                'metadata': metadata,
                'word_count': len(text_content.split()),
                'character_count': len(text_content),
                'processing_status': 'success'
            }
            
        except Exception as e:
            raise Exception(f"TXT processing failed: {str(e)}")
    
    def _process_markdown(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process Markdown files"""
        try:
            # Decode markdown content
            text_content = file_content.decode('utf-8')
            
            # Basic markdown analysis
            lines = text_content.split('\n')
            metadata = {
                'line_count': len(lines),
                'header_count': sum(1 for line in lines if line.strip().startswith('#')),
                'code_block_count': text_content.count('```'),
                'link_count': text_content.count('['),
                'image_count': text_content.count('![')
            }
            
            # Extract headers
            headers = []
            for line in lines:
                stripped = line.strip()
                if stripped.startswith('#'):
                    level = len(stripped) - len(stripped.lstrip('#'))
                    text = stripped.lstrip('#').strip()
                    headers.append({'level': level, 'text': text})
            
            metadata['headers'] = headers
            
            return {
                'text': text_content.strip(),
                'metadata': metadata,
                'word_count': len(text_content.split()),
                'character_count': len(text_content),
                'processing_status': 'success'
            }
            
        except Exception as e:
            raise Exception(f"Markdown processing failed: {str(e)}")

    def _process_image(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process image files using advanced OCR with preprocessing"""
        try:
            # Store original image data as base64 for serving later
            import base64
            original_image_base64 = base64.b64encode(file_content).decode('utf-8')

            # Convert bytes to PIL Image
            image = Image.open(BytesIO(file_content))
            original_size = image.size

            # Convert to RGB if necessary (for PNG with transparency)
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')

            # Try OCR if available
            extracted_text = ""
            avg_confidence = 0
            ocr_engine = "None"
            detected_languages = []
            preprocessing_applied = []

            if self.ocr_available:
                try:
                    # Apply image preprocessing for better OCR
                    processed_image = self._preprocess_image(image)
                    preprocessing_applied = ['resize', 'contrast_enhancement', 'noise_reduction']

                    # Try simple OCR first
                    extracted_text = pytesseract.image_to_string(processed_image)
                    ocr_engine = "Tesseract (basic)"

                    # If we got text, try to get confidence scores
                    if extracted_text.strip():
                        try:
                            ocr_data = pytesseract.image_to_data(processed_image, output_type=pytesseract.Output.DICT)
                            confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
                            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                            # Try to detect languages in the text
                            detected_languages = self._detect_languages_in_text(extracted_text)

                        except Exception as data_error:
                            print(f"OCR data extraction failed: {data_error}")
                            avg_confidence = 75  # Assume reasonable confidence if we got text
                    else:
                        avg_confidence = 0

                except Exception as ocr_error:
                    print(f"OCR failed: {ocr_error}")
                    # Check if it's a Tesseract installation issue
                    if "tesseract is not installed" in str(ocr_error).lower() or "no such file" in str(ocr_error).lower():
                        extracted_text = "The OCR engine was not available to extract text from the image."
                        ocr_engine = "Tesseract (not installed)"
                    else:
                        extracted_text = f"OCR processing failed: {str(ocr_error)}"
                        ocr_engine = "Tesseract (error)"
            else:
                extracted_text = "The OCR engine was not available to extract text from the image."
                ocr_engine = "Not available"

            # Enhanced image metadata
            metadata = {
                'image_size': original_size,
                'processed_size': processed_image.size if 'processed_image' in locals() else original_size,
                'image_mode': image.mode,
                'image_format': image.format,
                'average_confidence': avg_confidence,
                'ocr_engine': ocr_engine,
                'ocr_available': self.ocr_available,
                'supported_languages': self.supported_languages,
                'detected_languages': detected_languages,
                'preprocessing_applied': preprocessing_applied,
                'file_size_bytes': len(file_content)
            }

            # If no text found
            if not extracted_text.strip():
                extracted_text = "[No text detected in image]"

            return {
                'text': extracted_text.strip(),
                'metadata': metadata,
                'word_count': len(extracted_text.split()),
                'character_count': len(extracted_text),
                'processing_status': 'success',
                'original_image_data': original_image_base64
            }

        except Exception as e:
            raise Exception(f"Image processing failed: {str(e)}")

    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Apply preprocessing to improve OCR accuracy"""
        try:
            import numpy as np

            # Convert PIL to numpy array
            img_array = np.array(image)

            # Resize if image is too small (OCR works better on larger images)
            if image.size[0] < 300 or image.size[1] < 300:
                scale_factor = max(300 / image.size[0], 300 / image.size[1])
                new_size = (int(image.size[0] * scale_factor), int(image.size[1] * scale_factor))
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            # Convert to grayscale for better OCR
            if len(img_array.shape) == 3:
                image = image.convert('L')

            # Enhance contrast
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)

            # Sharpen the image
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)

            return image

        except Exception as e:
            print(f"Image preprocessing failed: {e}")
            return image  # Return original if preprocessing fails

    def _try_alternative_ocr_modes(self, image: Image.Image, lang_config: str) -> Dict[str, Any]:
        """Try different PSM modes for better OCR results"""
        psm_modes = [6, 8, 13, 7, 3]  # Different page segmentation modes
        best_result = {'text': '', 'confidence': 0}

        for psm in psm_modes:
            try:
                config = f'--oem 3 --psm {psm} -l {lang_config}'
                text = pytesseract.image_to_string(image, config=config)

                # Get confidence for this mode
                try:
                    data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)
                    confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                    avg_conf = sum(confidences) / len(confidences) if confidences else 0

                    if avg_conf > best_result['confidence'] and text.strip():
                        best_result = {'text': text, 'confidence': avg_conf}

                except:
                    continue

            except:
                continue

        return best_result

    def _detect_languages_in_text(self, text: str) -> List[str]:
        """Simple language detection based on character patterns"""
        detected = []

        if not text.strip():
            return detected

        # Simple heuristics for language detection
        if any(ord(char) > 127 for char in text):
            # Non-ASCII characters detected
            if any(0x4e00 <= ord(char) <= 0x9fff for char in text):
                detected.append('Chinese')
            if any(0x3040 <= ord(char) <= 0x309f or 0x30a0 <= ord(char) <= 0x30ff for char in text):
                detected.append('Japanese')
            if any(0xac00 <= ord(char) <= 0xd7af for char in text):
                detected.append('Korean')
            if any(0x0600 <= ord(char) <= 0x06ff for char in text):
                detected.append('Arabic')
            if any(0x0900 <= ord(char) <= 0x097f for char in text):
                detected.append('Hindi')
            if any(0x0400 <= ord(char) <= 0x04ff for char in text):
                detected.append('Cyrillic')
        else:
            detected.append('Latin-based')

        return detected if detected else ['Unknown']

    def _chunk_text_simple(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """
        Simple fallback text chunking method
        Split text into overlapping chunks for better embeddings
        """
        words = text.split()
        chunks = []

        for i in range(0, len(words), chunk_size - overlap):
            chunk = ' '.join(words[i:i + chunk_size])
            if chunk.strip():
                chunks.append(chunk)

        return chunks

    def get_chunking_templates(self) -> List[str]:
        """Return available chunking templates"""
        return [template.value for template in ChunkingTemplate]

    def process_with_custom_template(self, file_content: bytes, filename: str,
                                   file_type: str, template: str) -> Dict[str, Any]:
        """Process document with a specific chunking template"""
        try:
            # First process normally
            result = self.process_document(file_content, filename, file_type)

            if result.get('error'):
                return result

            # Re-chunk with specified template
            if template in [t.value for t in ChunkingTemplate]:
                template_enum = ChunkingTemplate(template)

                intelligent_chunks = self.chunking_strategy.chunk_text_with_template(
                    result['text'],
                    template_enum,
                    result.get('metadata', {})
                )

                result['chunks'] = intelligent_chunks
                result['chunking_template'] = template
                result['total_chunks'] = len(intelligent_chunks)

                # Regenerate embeddings if available
                if self.embedding_model:
                    embeddings = []
                    for chunk_data in result['chunks']:
                        chunk_text = chunk_data['text']
                        embedding = self.embedding_model.encode(chunk_text).tolist()
                        chunk_data['embedding'] = embedding
                        embeddings.append({
                            'chunk_id': chunk_data['chunk_id'],
                            'text': chunk_text,
                            'embedding': embedding,
                            'chunk_type': chunk_data.get('chunk_type', 'unknown')
                        })
                    result['embeddings'] = embeddings

            return result

        except Exception as e:
            return {
                'error': str(e),
                'filename': filename,
                'file_type': file_type,
                'processed_at': datetime.utcnow().isoformat()
            }
    
    def get_supported_formats(self) -> List[str]:
        """Return list of supported file formats"""
        return list(self.supported_formats.keys())
    
    def is_format_supported(self, file_type: str) -> bool:
        """Check if a file format is supported"""
        return file_type.lower() in self.supported_formats

    def create_professional_chunks(self, dataset_id: int, chunks_data: List[Dict[str, Any]], db_session) -> Dict[str, Any]:
        """
        Create professional chunks from processed document data
        This method should be called after document processing and dataset creation
        """
        if not self.chunk_service:
            return {"error": "Chunk service not available"}

        try:
            # Convert document chunks to professional chunk format
            professional_chunks = []

            for i, chunk in enumerate(chunks_data):
                professional_chunk = {
                    'text': chunk.get('text', ''),
                    'source_document': chunk.get('source_metadata', {}).get('document_name', 'unknown'),
                    'line_start': chunk.get('source_metadata', {}).get('line_start'),
                    'line_end': chunk.get('source_metadata', {}).get('line_end'),
                    'char_start': chunk.get('source_metadata', {}).get('char_start'),
                    'char_end': chunk.get('source_metadata', {}).get('char_end'),
                    'page_number': chunk.get('source_metadata', {}).get('page_number'),
                    'section': chunk.get('source_metadata', {}).get('section'),
                    'chunk_type': chunk.get('chunk_type', 'paragraph')
                }
                professional_chunks.append(professional_chunk)

            # Create chunks using the professional service
            result = self.chunk_service.create_chunks_from_document(
                dataset_id=dataset_id,
                chunks_data=professional_chunks,
                db=db_session
            )

            return result

        except Exception as e:
            return {"error": f"Failed to create professional chunks: {str(e)}"}

    def get_document_insights(self, dataset_id: int, db_session) -> Dict[str, Any]:
        """
        Get comprehensive insights about a document dataset using professional chunk analysis
        """
        if not self.chunk_service:
            return {"error": "Chunk service not available"}

        try:
            # Get chunk statistics
            stats = self.chunk_service.get_chunk_statistics(dataset_id, db_session)

            # Add document-level insights
            insights = {
                **stats,
                'document_insights': {
                    'complexity_score': self._calculate_document_complexity(stats),
                    'readability_grade': self._get_readability_grade(stats['basic_stats']['avg_readability_score']),
                    'information_richness': self._assess_information_richness(stats),
                    'structure_quality': self._assess_structure_quality(stats),
                    'search_optimization': self._assess_search_optimization(stats)
                }
            }

            return insights

        except Exception as e:
            return {"error": f"Failed to get document insights: {str(e)}"}

    def _calculate_document_complexity(self, stats: Dict[str, Any]) -> str:
        """Calculate overall document complexity based on various metrics"""
        basic_stats = stats['basic_stats']

        # Factors that increase complexity
        complexity_score = 0

        # Average words per chunk
        if basic_stats['avg_words_per_chunk'] > 100:
            complexity_score += 2
        elif basic_stats['avg_words_per_chunk'] > 50:
            complexity_score += 1

        # Readability score (lower = more complex)
        if basic_stats['avg_readability_score'] < 30:
            complexity_score += 3
        elif basic_stats['avg_readability_score'] < 50:
            complexity_score += 2
        elif basic_stats['avg_readability_score'] < 70:
            complexity_score += 1

        # Information density
        if basic_stats['avg_information_density'] > 0.9:
            complexity_score += 2
        elif basic_stats['avg_information_density'] > 0.8:
            complexity_score += 1

        # Number of sections
        section_count = len(stats.get('sections', []))
        if section_count > 20:
            complexity_score += 2
        elif section_count > 10:
            complexity_score += 1

        # Classify complexity
        if complexity_score >= 7:
            return "Very High"
        elif complexity_score >= 5:
            return "High"
        elif complexity_score >= 3:
            return "Medium"
        elif complexity_score >= 1:
            return "Low"
        else:
            return "Very Low"

    def _get_readability_grade(self, readability_score: float) -> str:
        """Convert readability score to grade level"""
        if readability_score >= 90:
            return "5th Grade (Very Easy)"
        elif readability_score >= 80:
            return "6th Grade (Easy)"
        elif readability_score >= 70:
            return "7th Grade (Fairly Easy)"
        elif readability_score >= 60:
            return "8th-9th Grade (Standard)"
        elif readability_score >= 50:
            return "10th-12th Grade (Fairly Difficult)"
        elif readability_score >= 30:
            return "College Level (Difficult)"
        else:
            return "Graduate Level (Very Difficult)"

    def _assess_information_richness(self, stats: Dict[str, Any]) -> str:
        """Assess how information-rich the document is"""
        basic_stats = stats['basic_stats']
        quality_metrics = stats['quality_metrics']

        richness_score = 0

        # High information density
        if basic_stats['avg_information_density'] > 0.85:
            richness_score += 2
        elif basic_stats['avg_information_density'] > 0.75:
            richness_score += 1

        # Good chunk size distribution
        if 50 <= basic_stats['avg_words_per_chunk'] <= 150:
            richness_score += 1

        # High density chunks percentage
        if quality_metrics['density_percentage'] > 70:
            richness_score += 2
        elif quality_metrics['density_percentage'] > 50:
            richness_score += 1

        # Multiple chunk types
        if len(stats.get('chunk_types', [])) > 3:
            richness_score += 1

        if richness_score >= 5:
            return "Very Rich"
        elif richness_score >= 3:
            return "Rich"
        elif richness_score >= 2:
            return "Moderate"
        else:
            return "Limited"

    def _assess_structure_quality(self, stats: Dict[str, Any]) -> str:
        """Assess the structural quality of the document"""
        sections = stats.get('sections', [])
        chunk_types = stats.get('chunk_types', [])

        structure_score = 0

        # Has clear sections
        if len(sections) > 5:
            structure_score += 2
        elif len(sections) > 2:
            structure_score += 1

        # Diverse chunk types
        if len(chunk_types) > 3:
            structure_score += 2
        elif len(chunk_types) > 1:
            structure_score += 1

        # Balanced section sizes
        if sections:
            section_sizes = [s['chunk_count'] for s in sections]
            if max(section_sizes) / min(section_sizes) < 3:  # Not too imbalanced
                structure_score += 1

        if structure_score >= 4:
            return "Excellent"
        elif structure_score >= 3:
            return "Good"
        elif structure_score >= 2:
            return "Fair"
        else:
            return "Poor"

    def _assess_search_optimization(self, stats: Dict[str, Any]) -> str:
        """Assess how well-optimized the document is for search"""
        basic_stats = stats['basic_stats']
        quality_metrics = stats['quality_metrics']

        search_score = 0

        # Good chunk size for search
        if 30 <= basic_stats['avg_words_per_chunk'] <= 200:
            search_score += 2
        elif 20 <= basic_stats['avg_words_per_chunk'] <= 300:
            search_score += 1

        # High embedding coverage
        if quality_metrics['embedding_percentage'] > 90:
            search_score += 2
        elif quality_metrics['embedding_percentage'] > 70:
            search_score += 1

        # Good information density
        if 0.7 <= basic_stats['avg_information_density'] <= 0.9:
            search_score += 1

        # Reasonable readability
        if 40 <= basic_stats['avg_readability_score'] <= 80:
            search_score += 1

        if search_score >= 5:
            return "Excellent"
        elif search_score >= 3:
            return "Good"
        elif search_score >= 2:
            return "Fair"
        else:
            return "Poor"


# Global instance
document_processor = DocumentProcessor()
