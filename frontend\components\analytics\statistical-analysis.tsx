'use client';

import { useState, useEffect, useRef } from 'react';
import api from '@/lib/api';
import dynamic from 'next/dynamic';

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

interface StatisticalAnalysisProps {
  datasetId: number;
}

interface SummaryStatistics {
  count: number;
  mean: number;
  std: number;
  min: number;
  '25%': number;
  '50%': number;
  '75%': number;
  max: number;
  skewness: number;
  kurtosis: number;
}

export default function StatisticalAnalysis({ datasetId }: StatisticalAnalysisProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [columns, setColumns] = useState<string[]>([]);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [summaryStats, setSummaryStats] = useState<Record<string, SummaryStatistics> | null>(null);
  const [correlationData, setCorrelationData] = useState<any>(null);
  const [correlationMethod, setCorrelationMethod] = useState<string>('pearson');
  const [distributionData, setDistributionData] = useState<any>(null);
  const [selectedDistributionColumn, setSelectedDistributionColumn] = useState<string>('');
  const [boxPlotData, setBoxPlotData] = useState<any>(null);
  const [heatmapData, setHeatmapData] = useState<any>(null);
  const [pivotTableData, setPivotTableData] = useState<any>(null);
  const [pivotConfig, setPivotConfig] = useState({
    indexColumn: '',
    columnsColumn: '',
    valuesColumn: '',
    aggFunction: 'mean'
  });

  // Fetch dataset columns
  useEffect(() => {
    if (!datasetId) return;

    const fetchDatasetColumns = async () => {
      try {
        // Use demo ID that matches backend expectation
        const userId = 'demo-user-id';
        const response = await api.get(`/datasets/${userId}`);
        const datasets = response.data;
        const currentDataset = datasets.find((d: any) => d.id === datasetId);

        if (currentDataset) {
          setColumns(currentDataset.columns);
        }
      } catch (err: any) {
        console.error('Error fetching dataset columns:', err);
        setError(err.response?.data?.detail || err.message || 'Error fetching dataset columns');
      }
    };

    fetchDatasetColumns();
  }, [datasetId]);

  // Get summary statistics
  const getSummaryStatistics = async () => {
    if (!datasetId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.get(`/analytics/summary/${datasetId}`, {
        params: {
          columns: selectedColumns.length > 0 ? selectedColumns : undefined
        }
      });

      setSummaryStats(response.data.summary_statistics);
    } catch (err: any) {
      console.error('Error fetching summary statistics:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching summary statistics');
    } finally {
      setLoading(false);
    }
  };

  // Get correlation analysis
  const getCorrelationAnalysis = async () => {
    if (!datasetId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/analytics/correlation/${datasetId}`, {
        columns: selectedColumns.length > 0 ? selectedColumns : undefined,
        method: correlationMethod
      });

      setCorrelationData(response.data.correlation_analysis);
    } catch (err: any) {
      console.error('Error fetching correlation analysis:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching correlation analysis');
    } finally {
      setLoading(false);
    }
  };

  // Get distribution analysis
  const getDistributionAnalysis = async () => {
    if (!datasetId || !selectedDistributionColumn) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.get(`/analytics/distribution/${datasetId}`, {
        params: { column: selectedDistributionColumn }
      });

      setDistributionData(response.data.distribution_analysis);
    } catch (err: any) {
      console.error('Error fetching distribution analysis:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching distribution analysis');
    } finally {
      setLoading(false);
    }
  };

  // Get box plot analysis
  const getBoxPlotAnalysis = async () => {
    if (!datasetId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.get(`/analytics/boxplot/${datasetId}`, {
        params: {
          columns: selectedColumns.length > 0 ? selectedColumns : undefined
        }
      });

      setBoxPlotData(response.data.boxplot_analysis);
    } catch (err: any) {
      console.error('Error fetching box plot analysis:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching box plot analysis');
    } finally {
      setLoading(false);
    }
  };

  // Get correlation heatmap
  const getCorrelationHeatmap = async () => {
    if (!datasetId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.get(`/analytics/correlation-heatmap/${datasetId}`, {
        params: {
          columns: selectedColumns.length > 0 ? selectedColumns : undefined,
          method: correlationMethod
        }
      });

      setHeatmapData(response.data.correlation_heatmap);
    } catch (err: any) {
      console.error('Error fetching correlation heatmap:', err);
      setError(err.response?.data?.detail || err.message || 'Error fetching correlation heatmap');
    } finally {
      setLoading(false);
    }
  };

  // Create pivot table
  const createPivotTable = async () => {
    if (!datasetId || !pivotConfig.indexColumn) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/analytics/pivot-table/${datasetId}`, {
        index_column: pivotConfig.indexColumn,
        columns_column: pivotConfig.columnsColumn || null,
        values_column: pivotConfig.valuesColumn || null,
        aggregation_function: pivotConfig.aggFunction
      });

      setPivotTableData(response.data.pivot_table);
    } catch (err: any) {
      console.error('Error creating pivot table:', err);
      setError(err.response?.data?.detail || err.message || 'Error creating pivot table');
    } finally {
      setLoading(false);
    }
  };

  // Handle column selection
  const handleColumnChange = (column: string) => {
    setSelectedColumns(prev => {
      if (prev.includes(column)) {
        return prev.filter(c => c !== column);
      } else {
        return [...prev, column];
      }
    });
  };

  // Format number for display
  const formatNumber = (num: number) => {
    return Number.isInteger(num) ? num : num.toFixed(4);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-black mb-4">Statistical Analysis</h2>

      <div className="mb-6">
        <h3 className="text-md font-medium text-black mb-2">Select Columns</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 mb-4">
          {columns.map(column => (
            <div key={column} className="flex items-center">
              <input
                type="checkbox"
                id={`column-${column}`}
                checked={selectedColumns.includes(column)}
                onChange={() => handleColumnChange(column)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor={`column-${column}`} className="ml-2 block text-sm text-black truncate">
                {column}
              </label>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <button
            onClick={getSummaryStatistics}
            disabled={loading}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              loading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            📊 Summary Statistics
          </button>

          <button
            onClick={getBoxPlotAnalysis}
            disabled={loading}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              loading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            📦 Box Plot Analysis
          </button>

          <button
            onClick={getCorrelationHeatmap}
            disabled={loading}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              loading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-purple-600 text-white hover:bg-purple-700'
            }`}
          >
            🔥 Correlation Heatmap
          </button>
        </div>

        {/* Distribution Analysis Section */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-md font-medium text-black mb-3">📈 Distribution Analysis</h3>
          <div className="flex items-center gap-4">
            <select
              value={selectedDistributionColumn}
              onChange={(e) => setSelectedDistributionColumn(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select column for distribution analysis</option>
              {columns.filter(col => !selectedColumns.includes(col) || selectedColumns.length === 0).map(column => (
                <option key={column} value={column}>{column}</option>
              ))}
            </select>
            <button
              onClick={getDistributionAnalysis}
              disabled={loading || !selectedDistributionColumn}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                loading || !selectedDistributionColumn
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-indigo-600 text-white hover:bg-indigo-700'
              }`}
            >
              Analyze Distribution
            </button>
          </div>
        </div>

        {/* Pivot Table Section */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-md font-medium text-black mb-3">🧮 Pivot Table</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Index Column</label>
              <select
                value={pivotConfig.indexColumn}
                onChange={(e) => setPivotConfig(prev => ({ ...prev, indexColumn: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select index column</option>
                {columns.map(column => (
                  <option key={column} value={column}>{column}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Columns (Optional)</label>
              <select
                value={pivotConfig.columnsColumn}
                onChange={(e) => setPivotConfig(prev => ({ ...prev, columnsColumn: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select columns column</option>
                {columns.map(column => (
                  <option key={column} value={column}>{column}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Values (Optional)</label>
              <select
                value={pivotConfig.valuesColumn}
                onChange={(e) => setPivotConfig(prev => ({ ...prev, valuesColumn: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select values column</option>
                {columns.map(column => (
                  <option key={column} value={column}>{column}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Aggregation</label>
              <select
                value={pivotConfig.aggFunction}
                onChange={(e) => setPivotConfig(prev => ({ ...prev, aggFunction: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="mean">Mean</option>
                <option value="sum">Sum</option>
                <option value="count">Count</option>
                <option value="min">Min</option>
                <option value="max">Max</option>
                <option value="std">Standard Deviation</option>
              </select>
            </div>
          </div>
          <button
            onClick={createPivotTable}
            disabled={loading || !pivotConfig.indexColumn}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              loading || !pivotConfig.indexColumn
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-orange-600 text-white hover:bg-orange-700'
            }`}
          >
            Create Pivot Table
          </button>
        </div>

        {/* Correlation Method Selection */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-md font-medium text-black mb-3">🔗 Correlation Method</h3>
          <div className="flex items-center gap-4">
            <select
              value={correlationMethod}
              onChange={(e) => setCorrelationMethod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="pearson">Pearson (Linear relationships)</option>
              <option value="spearman">Spearman (Monotonic relationships)</option>
              <option value="kendall">Kendall (Rank-based)</option>
            </select>
            <button
              onClick={getCorrelationAnalysis}
              disabled={loading}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                loading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              Get Correlation Matrix
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Summary Statistics Results */}
      {summaryStats && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">Summary Statistics</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statistic
                  </th>
                  {Object.keys(summaryStats).map(column => (
                    <th key={column} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max', 'skewness', 'kurtosis'].map(stat => (
                  <tr key={stat}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {stat}
                    </td>
                    {Object.keys(summaryStats).map(column => (
                      <td key={`${column}-${stat}`} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatNumber(summaryStats[column][stat as keyof SummaryStatistics])}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Correlation Analysis Results */}
      {correlationData && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">Correlation Analysis ({correlationData.method})</h3>

          {/* Correlation Matrix */}
          <div className="overflow-x-auto mb-4">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Column
                  </th>
                  {Object.keys(correlationData.correlation_matrix).map(column => (
                    <th key={column} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.keys(correlationData.correlation_matrix).map(row => (
                  <tr key={row}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {row}
                    </td>
                    {Object.keys(correlationData.correlation_matrix).map(col => {
                      const value = correlationData.correlation_matrix[row][col];
                      // Color based on correlation strength
                      let bgColor = 'bg-white';
                      if (row !== col) {
                        if (value > 0.7) bgColor = 'bg-green-100';
                        else if (value > 0.4) bgColor = 'bg-green-50';
                        else if (value < -0.7) bgColor = 'bg-red-100';
                        else if (value < -0.4) bgColor = 'bg-red-50';
                      }

                      return (
                        <td key={`${row}-${col}`} className={`px-6 py-4 whitespace-nowrap text-sm text-gray-500 ${bgColor}`}>
                          {formatNumber(value)}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Strongest Correlations */}
          {correlationData.strongest_correlations && correlationData.strongest_correlations.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-black mb-2">Strongest Correlations</h4>
              <ul className="space-y-1">
                {correlationData.strongest_correlations.map((corr: any, index: number) => (
                  <li key={index} className="text-sm">
                    <span className={corr.correlation > 0 ? 'text-green-600' : 'text-red-600'}>
                      {corr.column1} ↔ {corr.column2}: {formatNumber(corr.correlation)}
                    </span>
                    {corr.significant && (
                      <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                        Significant
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Distribution Analysis Results */}
      {distributionData && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">📈 Distribution Analysis - {selectedDistributionColumn}</h3>

          {/* Distribution Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-blue-900">Mean</div>
              <div className="text-lg font-bold text-blue-700">{formatNumber(distributionData.distribution_statistics.mean)}</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-green-900">Median</div>
              <div className="text-lg font-bold text-green-700">{formatNumber(distributionData.distribution_statistics.median)}</div>
            </div>
            <div className="bg-purple-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-purple-900">Std Dev</div>
              <div className="text-lg font-bold text-purple-700">{formatNumber(distributionData.distribution_statistics.std)}</div>
            </div>
            <div className="bg-orange-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-orange-900">Skewness</div>
              <div className="text-lg font-bold text-orange-700">{formatNumber(distributionData.distribution_statistics.skewness)}</div>
            </div>
          </div>

          {/* Normality Tests */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-black mb-2">Normality Tests</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm font-medium">Shapiro-Wilk Test</div>
                <div className={`text-sm ${distributionData.normality_tests.shapiro_wilk.is_normal ? 'text-green-600' : 'text-red-600'}`}>
                  {distributionData.normality_tests.shapiro_wilk.is_normal ? '✅ Normal' : '❌ Not Normal'}
                  (p = {formatNumber(distributionData.normality_tests.shapiro_wilk.p_value)})
                </div>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm font-medium">Kolmogorov-Smirnov Test</div>
                <div className={`text-sm ${distributionData.normality_tests.kolmogorov_smirnov.is_normal ? 'text-green-600' : 'text-red-600'}`}>
                  {distributionData.normality_tests.kolmogorov_smirnov.is_normal ? '✅ Normal' : '❌ Not Normal'}
                  (p = {formatNumber(distributionData.normality_tests.kolmogorov_smirnov.p_value)})
                </div>
              </div>
            </div>
          </div>

          {/* Histogram Chart */}
          {distributionData.histogram_chart && (
            <div className="mb-4">
              <div className="w-full h-96 border border-gray-200 rounded-lg bg-white p-2">
                <Plot
                  data={distributionData.histogram_chart.plotly_json.data}
                  layout={{
                    ...distributionData.histogram_chart.plotly_json.layout,
                    autosize: true,
                    margin: { l: 50, r: 50, t: 50, b: 50 }
                  }}
                  config={{ responsive: true, displayModeBar: true }}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Box Plot Analysis Results */}
      {boxPlotData && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">📦 Box Plot Analysis - Outlier Detection</h3>

          {/* Outlier Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            {Object.entries(boxPlotData.outlier_summary).map(([column, summary]: [string, any]) => (
              <div key={column} className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-black mb-2">{column}</h4>
                <div className="space-y-1 text-sm">
                  <div>Total Values: {summary.total_values}</div>
                  <div className={`${summary.outlier_percentage > 5 ? 'text-red-600' : 'text-green-600'}`}>
                    Outliers: {summary.outlier_count} ({summary.outlier_percentage.toFixed(1)}%)
                  </div>
                  <div>IQR: {formatNumber(summary.iqr)}</div>
                  <div className="text-xs text-gray-500">
                    Range: [{formatNumber(summary.lower_bound)}, {formatNumber(summary.upper_bound)}]
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Box Plot Chart */}
          {boxPlotData.box_plot_chart && (
            <div className="w-full h-96 border border-gray-200 rounded-lg bg-white p-2">
              <Plot
                data={boxPlotData.box_plot_chart.plotly_json.data}
                layout={{
                  ...boxPlotData.box_plot_chart.plotly_json.layout,
                  autosize: true,
                  margin: { l: 50, r: 50, t: 50, b: 50 }
                }}
                config={{ responsive: true, displayModeBar: true }}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          )}
        </div>
      )}

      {/* Correlation Heatmap Results */}
      {heatmapData && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">🔥 Correlation Heatmap ({heatmapData.method})</h3>

          {/* Strongest Correlations Summary */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-black mb-2">Top Correlations</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {heatmapData.strongest_correlations.slice(0, 6).map((corr: any, index: number) => (
                <div key={index} className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm font-medium">{corr.column1} ↔ {corr.column2}</div>
                  <div className={`text-lg font-bold ${corr.correlation > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatNumber(corr.correlation)}
                  </div>
                  <div className="text-xs text-gray-500">{corr.strength} correlation</div>
                </div>
              ))}
            </div>
          </div>

          {/* Heatmap Chart */}
          {heatmapData.heatmap_chart && (
            <div className="w-full h-96 border border-gray-200 rounded-lg bg-white p-2">
              <Plot
                data={heatmapData.heatmap_chart.plotly_json.data}
                layout={{
                  ...heatmapData.heatmap_chart.plotly_json.layout,
                  autosize: true,
                  margin: { l: 50, r: 50, t: 50, b: 50 }
                }}
                config={{ responsive: true, displayModeBar: true }}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          )}
        </div>
      )}

      {/* Pivot Table Results */}
      {pivotTableData && (
        <div className="mb-6">
          <h3 className="text-md font-medium text-black mb-2">🧮 Pivot Table Results</h3>

          {/* Pivot Table Data */}
          <div className="overflow-x-auto mb-4">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {pivotTableData.index_column}
                  </th>
                  {Object.keys(Object.values(pivotTableData.pivot_table)[0] || {}).map((col: string) => (
                    <th key={col} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.entries(pivotTableData.pivot_table).map(([index, values]: [string, any]) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {index}
                    </td>
                    {Object.values(values).map((value: any, idx: number) => (
                      <td key={idx} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {typeof value === 'number' ? formatNumber(value) : value}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pivot Chart */}
          {pivotTableData.pivot_chart && (
            <div className="w-full h-96 border border-gray-200 rounded-lg bg-white p-2">
              <Plot
                data={pivotTableData.pivot_chart.plotly_json.data}
                layout={{
                  ...pivotTableData.pivot_chart.plotly_json.layout,
                  autosize: true,
                  margin: { l: 50, r: 50, t: 50, b: 50 }
                }}
                config={{ responsive: true, displayModeBar: true }}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}
