from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session
from typing import List
import csv
import io
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database import get_db
import models
import schemas

router = APIRouter(
    prefix="/feedback",
    tags=["feedback"]
)

@router.post("", response_model=schemas.FeedbackResponse)
async def submit_feedback(feedback: schemas.FeedbackCreate, db: Session = Depends(get_db)):
    """
    Submit user feedback for a query response
    """
    # Validate rating
    if feedback.rating not in ['up', 'down', 'neutral']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Rating must be 'up', 'down', or 'neutral'"
        )
    
    # Check if user exists
    user = db.query(models.User).filter(models.User.id == feedback.user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Check if query exists
    query = db.query(models.Query).filter(models.Query.id == feedback.query_id).first()
    if not query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Query not found"
        )
    
    # Check if feedback already exists for this user and query
    existing_feedback = db.query(models.Feedback).filter(
        models.Feedback.user_id == feedback.user_id,
        models.Feedback.query_id == feedback.query_id
    ).first()
    
    if existing_feedback:
        # If rating is 'neutral', delete the feedback (remove it)
        if feedback.rating == 'neutral':
            db.delete(existing_feedback)
            db.commit()
            return {"message": "Feedback removed"}
        else:
            # Update existing feedback
            existing_feedback.rating = feedback.rating
            existing_feedback.comment = feedback.comment
            db.commit()
            db.refresh(existing_feedback)
            return existing_feedback
    else:
        # Don't create new feedback if rating is 'neutral'
        if feedback.rating == 'neutral':
            return {"message": "No feedback to create"}

        # Create new feedback
        db_feedback = models.Feedback(
            user_id=feedback.user_id,
            query_id=feedback.query_id,
            rating=feedback.rating,
            comment=feedback.comment
        )

        db.add(db_feedback)
        db.commit()
        db.refresh(db_feedback)

        return db_feedback

@router.get("/query/{query_id}", response_model=List[schemas.FeedbackResponse])
async def get_query_feedback(query_id: int, db: Session = Depends(get_db)):
    """
    Get all feedback for a specific query
    """
    feedback = db.query(models.Feedback).filter(models.Feedback.query_id == query_id).all()
    return feedback

@router.get("/user/{user_id}", response_model=List[schemas.FeedbackResponse])
async def get_user_feedback(user_id: str, db: Session = Depends(get_db)):
    """
    Get all feedback submitted by a specific user
    """
    feedback = db.query(models.Feedback).filter(models.Feedback.user_id == user_id).all()
    return feedback

@router.get("/admin/all", response_model=List[schemas.FeedbackSummary])
async def get_all_feedback_admin(db: Session = Depends(get_db)):
    """
    Admin endpoint: Get all feedback with detailed information
    """
    # Join feedback with user and query data
    feedback_data = db.query(
        models.Feedback.id,
        models.Feedback.user_id,
        models.User.email.label('user_email'),
        models.Feedback.query_id,
        models.Query.question,
        models.Query.answer,
        models.Feedback.rating,
        models.Feedback.comment,
        models.Feedback.created_at
    ).join(
        models.User, models.Feedback.user_id == models.User.id
    ).join(
        models.Query, models.Feedback.query_id == models.Query.id
    ).order_by(models.Feedback.created_at.desc()).all()
    
    return [
        {
            "id": row.id,
            "user_id": row.user_id,
            "user_email": row.user_email,
            "query_id": row.query_id,
            "question": row.question[:100] + "..." if len(row.question) > 100 else row.question,
            "answer": row.answer[:200] + "..." if len(row.answer) > 200 else row.answer,
            "rating": row.rating,
            "comment": row.comment,
            "created_at": row.created_at
        }
        for row in feedback_data
    ]

@router.get("/admin/export")
async def export_feedback_csv(db: Session = Depends(get_db)):
    """
    Admin endpoint: Export all feedback as CSV
    """
    # Get all feedback with detailed information
    feedback_data = db.query(
        models.Feedback.id,
        models.Feedback.user_id,
        models.User.email.label('user_email'),
        models.Feedback.query_id,
        models.Query.question,
        models.Query.answer,
        models.Feedback.rating,
        models.Feedback.comment,
        models.Feedback.created_at
    ).join(
        models.User, models.Feedback.user_id == models.User.id
    ).join(
        models.Query, models.Feedback.query_id == models.Query.id
    ).order_by(models.Feedback.created_at.desc()).all()
    
    # Create CSV content
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        'Feedback ID',
        'User ID', 
        'User Email',
        'Query ID',
        'Question',
        'Answer',
        'Rating',
        'Comment',
        'Created At'
    ])
    
    # Write data rows
    for row in feedback_data:
        writer.writerow([
            row.id,
            row.user_id,
            row.user_email,
            row.query_id,
            row.question,
            row.answer,
            row.rating,
            row.comment or '',
            row.created_at.isoformat()
        ])
    
    # Get CSV content
    csv_content = output.getvalue()
    output.close()
    
    # Return CSV file
    filename = f"feedback_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    return Response(
        content=csv_content,
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

@router.get("/admin/stats")
async def get_feedback_stats(db: Session = Depends(get_db)):
    """
    Admin endpoint: Get feedback statistics
    """
    total_feedback = db.query(models.Feedback).count()
    positive_feedback = db.query(models.Feedback).filter(models.Feedback.rating == 'up').count()
    negative_feedback = db.query(models.Feedback).filter(models.Feedback.rating == 'down').count()
    
    # Get feedback with comments
    feedback_with_comments = db.query(models.Feedback).filter(
        models.Feedback.comment.isnot(None),
        models.Feedback.comment != ''
    ).count()
    
    return {
        "total_feedback": total_feedback,
        "positive_feedback": positive_feedback,
        "negative_feedback": negative_feedback,
        "feedback_with_comments": feedback_with_comments,
        "positive_percentage": round((positive_feedback / total_feedback * 100) if total_feedback > 0 else 0, 2),
        "negative_percentage": round((negative_feedback / total_feedback * 100) if total_feedback > 0 else 0, 2)
    }

@router.delete("/{feedback_id}")
async def delete_feedback(feedback_id: int, db: Session = Depends(get_db)):
    """
    Admin endpoint: Delete specific feedback
    """
    feedback = db.query(models.Feedback).filter(models.Feedback.id == feedback_id).first()
    
    if not feedback:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Feedback not found"
        )
    
    db.delete(feedback)
    db.commit()
    
    return {"message": "Feedback deleted successfully"}
