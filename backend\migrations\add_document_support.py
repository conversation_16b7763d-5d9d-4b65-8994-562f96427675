"""
Database migration to add document support fields to datasets table
"""

import sqlite3
import os

def migrate_database():
    """Add document support columns to existing database"""
    
    # Get database path
    db_path = os.path.join(os.path.dirname(__file__), '..', 'aithentiq.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(datasets)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add new columns if they don't exist
        new_columns = [
            ('file_type', 'TEXT DEFAULT "csv"'),
            ('content_type', 'TEXT DEFAULT "tabular"'),
            ('document_metadata', 'TEXT'),
            ('embeddings_data', 'TEXT'),
            ('processing_status', 'TEXT DEFAULT "completed"'),
            ('word_count', 'INTEGER DEFAULT 0'),
            ('character_count', 'INTEGER DEFAULT 0')
        ]
        
        for column_name, column_def in new_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f'ALTER TABLE datasets ADD COLUMN {column_name} {column_def}')
                    print(f"Added column: {column_name}")
                except sqlite3.Error as e:
                    print(f"Error adding column {column_name}: {e}")
        
        conn.commit()
        print("Database migration completed successfully!")
        
    except sqlite3.Error as e:
        print(f"Database migration failed: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_database()
