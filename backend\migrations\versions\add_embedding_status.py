"""Add embedding status tracking to datasets

Revision ID: add_embedding_status
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_embedding_status'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Add embedding status columns to datasets table
    op.add_column('datasets', sa.Column('embedding_status', sa.String(), nullable=True))
    op.add_column('datasets', sa.Column('embedding_created_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('datasets', sa.Column('embedding_error', sa.Text(), nullable=True))
    
    # Set default value for existing records
    op.execute("UPDATE datasets SET embedding_status = 'pending' WHERE embedding_status IS NULL")
    
    # Make embedding_status non-nullable with default
    op.alter_column('datasets', 'embedding_status', nullable=False, server_default='pending')


def downgrade():
    # Remove embedding status columns
    op.drop_column('datasets', 'embedding_error')
    op.drop_column('datasets', 'embedding_created_at')
    op.drop_column('datasets', 'embedding_status')
