# AIthentiq Database Setup Guide

This guide will help you set up the database for AIthentiq's predictive analytics system.

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

**For Linux/Mac:**
```bash
chmod +x setup_database.sh
./setup_database.sh
```

**For Windows:**
```cmd
setup_database.bat
```

### Option 2: Manual Setup

1. **Connect to your PostgreSQL database:**
   ```bash
   psql -h localhost -p 5432 -U postgres -d aithentiq
   ```

2. **Run the migration script:**
   ```sql
   \i database_migration.sql
   ```

## 📋 Prerequisites

- PostgreSQL 12+ installed and running
- Database named `aithentiq` (or your preferred name)
- Database user with CREATE TABLE permissions

## 🗄️ Database Schema

The migration creates the following tables:

### Core Tables

#### `predictive_models`
Stores trained machine learning models
- `id` - Unique model identifier
- `user_id` - Owner of the model
- `dataset_id` - Source dataset
- `name` - Model name
- `model_type` - Type (classification, regression, etc.)
- `target_column` - Prediction target
- `feature_columns` - Input features
- `hyperparameters` - Model configuration
- `metrics` - Performance metrics
- `model_data` - Serialized model
- `status` - Training status

#### `predictive_predictions`
Stores prediction results
- `id` - Unique prediction identifier
- `model_id` - Model used for prediction
- `user_id` - User who made prediction
- `input_data` - Input values
- `prediction_result` - Prediction output
- `confidence_score` - Prediction confidence

#### `time_series_forecasts`
Stores time series forecasting results
- `id` - Unique forecast identifier
- `user_id` - User who created forecast
- `dataset_id` - Source dataset
- `target_column` - Forecasted variable
- `date_column` - Time column
- `forecast_horizon` - Number of periods
- `method` - Forecasting method
- `forecast_data` - Forecast results
- `accuracy_metrics` - Model accuracy

#### `anomaly_detections`
Stores anomaly detection results
- `id` - Unique detection identifier
- `user_id` - User who ran detection
- `dataset_id` - Source dataset
- `method` - Detection method
- `parameters` - Detection parameters
- `anomalies` - Detected anomalies
- `summary_stats` - Detection summary

#### `background_jobs`
Handles asynchronous processing
- `id` - Unique job identifier
- `user_id` - Job owner
- `job_type` - Type of job
- `status` - Job status
- `parameters` - Job configuration
- `result` - Job output
- `progress` - Completion percentage

## 🔧 Configuration

After running the migration, update your backend `.env` file:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/aithentiq
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aithentiq
DB_USER=your_username
DB_PASSWORD=your_password
```

## 🧪 Testing the Setup

1. **Start the backend server:**
   ```bash
   cd backend
   uvicorn main:app --reload --port 8000
   ```

2. **Start the frontend server:**
   ```bash
   cd frontend
   npm run dev
   ```

3. **Visit the predictive analytics page:**
   ```
   http://localhost:3000/dashboard/predictive
   ```

## 📊 Features Enabled

After setup, you'll have access to:

- ✅ **Time Series Forecasting** - ARIMA, Prophet, Exponential Smoothing
- ✅ **Model Training** - Classification and Regression models
- ✅ **Anomaly Detection** - Statistical and ML-based detection
- ✅ **Risk Assessment** - Business risk analysis
- ✅ **Churn Prediction** - Customer retention analysis
- ✅ **Advanced AI Insights** - Comprehensive analytics dashboard

## 🔍 Troubleshooting

### Common Issues

**Connection Failed:**
- Verify PostgreSQL is running
- Check connection credentials
- Ensure database exists

**Permission Denied:**
- Grant CREATE privileges to your user
- Check database ownership

**Migration Failed:**
- Check PostgreSQL version (12+ required)
- Verify UUID extension is available
- Review error logs for specific issues

### Verification Queries

Check if tables were created:
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%predictive%';
```

Check table structure:
```sql
\d predictive_models
```

## 🔒 Security Notes

- Use strong database passwords
- Limit database user permissions
- Enable SSL for production deployments
- Regularly backup your data

## 📈 Performance Tips

- The migration includes optimized indexes
- Consider partitioning for large datasets
- Monitor query performance
- Use connection pooling in production

## 🆘 Support

If you encounter issues:
1. Check the error logs
2. Verify all prerequisites are met
3. Review the troubleshooting section
4. Check the GitHub issues page

---

**🎉 Congratulations!** Your AIthentiq database is now ready for advanced predictive analytics!
