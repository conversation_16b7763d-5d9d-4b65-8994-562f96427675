'use client';

import Link from 'next/link';
import Navbar from '../../components/ui/navbar';
import Footer from '../../components/ui/footer';

const plans = [
  {
    name: 'Free',
    price: '$0',
    description: 'Perfect for trying out AIthentiq',
    features: [
      'Upload up to 3 datasets',
      'Maximum 1,000 rows per dataset',
      'Basic visualisations',
      '10 queries per day',
      'Community support',
    ],
    cta: 'Get Started',
    href: '/sign-up',
    highlighted: false,
  },
  {
    name: 'Pro',
    price: '$19',
    period: '/month',
    description: 'For individuals and small teams',
    features: [
      'Upload up to 20 datasets',
      'Maximum 100,000 rows per dataset',
      'Advanced visualisations',
      'Unlimited queries',
      'Data export options',
      'Priority support',
    ],
    cta: 'Upgrade to Pro',
    href: '/sign-up?plan=pro',
    highlighted: true,
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    description: 'For organizations with advanced needs',
    features: [
      'Unlimited datasets',
      'Unlimited rows',
      'Custom integrations',
      'Advanced security features',
      'Dedicated support',
      'Training and onboarding',
      'SLA guarantees',
    ],
    cta: 'Contact Sales',
    href: '/contact',
    highlighted: false,
  },
];

export default function Pricing() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Simple, transparent pricing
            </h1>
            <p className="mt-4 text-xl text-gray-600">
              Choose the plan that's right for you
            </p>
          </div>

          <div className="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:grid-cols-3">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`rounded-lg shadow-md divide-y divide-gray-200 ${
                  plan.highlighted ? 'border-2 border-blue-500' : 'border border-gray-200'
                }`}
              >
                <div className="p-6">
                  <h2 className="text-lg font-medium text-gray-900">{plan.name}</h2>
                  <p className="mt-4">
                    <span className="text-4xl font-extrabold text-gray-900">{plan.price}</span>
                    {plan.period && <span className="text-base font-medium text-gray-500">{plan.period}</span>}
                  </p>
                  <p className="mt-1 text-sm text-gray-500">{plan.description}</p>

                  <Link
                    href={plan.href}
                    className={`mt-8 block w-full py-2 px-4 rounded-md text-sm font-semibold text-center ${
                      plan.highlighted
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-blue-50 text-blue-700 hover:bg-blue-100'
                    }`}
                  >
                    {plan.cta}
                  </Link>
                </div>
                <div className="px-6 pt-6 pb-8">
                  <h3 className="text-xs font-medium text-gray-900 tracking-wide uppercase">
                    What's included
                  </h3>
                  <ul className="mt-6 space-y-4">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex space-x-3">
                        <svg
                          className="flex-shrink-0 h-5 w-5 text-green-500"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm text-gray-500">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <h2 className="text-2xl font-bold text-gray-900">Frequently Asked Questions</h2>
            <div className="mt-8 max-w-3xl mx-auto">
              <div className="space-y-8">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Can I cancel my subscription?</h3>
                  <p className="mt-2 text-gray-500">
                    Yes, you can cancel your subscription at any time. Your plan will remain active until the end of your billing period.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Is there a free trial?</h3>
                  <p className="mt-2 text-gray-500">
                    Yes, you can use the Free plan indefinitely with limited features. No credit card required.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">What payment methods do you accept?</h3>
                  <p className="mt-2 text-gray-500">
                    We accept all major credit cards, including Visa, Mastercard, and American Express.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Can I upgrade or downgrade my plan?</h3>
                  <p className="mt-2 text-gray-500">
                    Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll be charged the prorated amount for the remainder of your billing period.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
