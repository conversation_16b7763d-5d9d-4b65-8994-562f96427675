@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for the logo */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce-delayed {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animation-delay-500 {
  animation-delay: 0.5s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

/* Central animation effects */
@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-10px) translateX(5px);
  }
  50% {
    transform: translateY(-5px) translateX(-5px);
  }
  75% {
    transform: translateY(-15px) translateX(3px);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-15px) translateX(-5px);
  }
  50% {
    transform: translateY(-8px) translateX(8px);
  }
  75% {
    transform: translateY(-12px) translateX(-3px);
  }
}

@keyframes float-delayed-2 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-8px) translateX(7px);
  }
  50% {
    transform: translateY(-18px) translateX(-4px);
  }
  75% {
    transform: translateY(-6px) translateX(6px);
  }
}

.animate-spin-reverse {
  animation: spin-reverse 25s linear infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-float-delayed-2 {
  animation: float-delayed-2 7s ease-in-out infinite;
  animation-delay: 4s;
}

@layer base {
  html {
    font-family: 'Inter', 'Poppins', 'Roboto', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: 'rlig' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #2563eb, #7c3aed);
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 2s infinite;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Focus styles for accessibility */
@layer base {
  *:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  button:focus,
  a:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

/* Custom background pattern for auth pages */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Futuristic grid pattern for hero section */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(6, 182, 212, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(6, 182, 212, 0.05) 1px, transparent 1px),
    linear-gradient(rgba(139, 92, 246, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(139, 92, 246, 0.03) 1px, transparent 1px);
  background-size: 40px 40px, 40px 40px, 80px 80px, 80px 80px;
  background-position: 0 0, 0 0, 20px 20px, 20px 20px;
}

/* Enhanced futuristic scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #06b6d4, #8b5cf6);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #0891b2, #7c3aed);
  box-shadow: 0 0 15px rgba(6, 182, 212, 0.5);
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Disable dark mode for better readability */
/*
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/

body {
  background: #ffffff;
  color: #171717;
  font-family: 'Geist', sans-serif;
}

/* Override Tailwind text colors for better readability */
.text-gray-500 {
  color: #4b5563 !important;
}

.text-gray-600 {
  color: #374151 !important;
}

.text-gray-700 {
  color: #1f2937 !important;
}

.text-gray-400 {
  color: #6b7280 !important;
}

/* Custom animations for enhanced logo effects */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

.animation-delay-500 {
  animation-delay: 0.5s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Backdrop blur support */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Custom gradient text effects */
.text-gradient {
  background: linear-gradient(135deg, #3b82f6, #6366f1, #8b5cf6, #ec4899);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 4s ease-in-out infinite;
}

/* Advanced futuristic animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes holographic-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3), 0 0 40px rgba(6, 182, 212, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.4), 0 0 60px rgba(139, 92, 246, 0.2);
  }
}

@keyframes data-stream {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes circuit-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* Enhanced shadow effects */
.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-pink {
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-orange {
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-emerald {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-violet {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-cyan {
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-indigo {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-glow-rose {
  box-shadow: 0 0 20px rgba(244, 63, 94, 0.4), 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Holographic border effect */
.border-holographic {
  border: 1px solid transparent;
  background: linear-gradient(45deg, rgba(6, 182, 212, 0.3), rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.3)) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Utility classes for futuristic effects */
.animate-holographic-glow {
  animation: holographic-glow 3s ease-in-out infinite;
}

.animate-data-stream {
  animation: data-stream 2s linear infinite;
}

.animate-circuit-pulse {
  animation: circuit-pulse 2s ease-in-out infinite;
}

/* Additional futuristic effects */
@keyframes neon-flicker {
  0%, 100% {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }
  50% {
    text-shadow: 0 0 2px currentColor, 0 0 5px currentColor, 0 0 8px currentColor;
  }
}

@keyframes matrix-rain {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

@keyframes energy-pulse {
  0%, 100% {
    box-shadow: 0 0 5px currentColor, inset 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, inset 0 0 10px currentColor;
  }
}

.animate-neon-flicker {
  animation: neon-flicker 3s ease-in-out infinite;
}

.animate-matrix-rain {
  animation: matrix-rain 3s linear infinite;
}

.animate-energy-pulse {
  animation: energy-pulse 2s ease-in-out infinite;
}

/* Futuristic text effects */
.text-neon {
  color: #3b82f6;
  text-shadow: 0 0 5px #3b82f6, 0 0 10px #3b82f6, 0 0 15px #3b82f6;
}

.text-neon-purple {
  color: #8b5cf6;
  text-shadow: 0 0 5px #8b5cf6, 0 0 10px #8b5cf6, 0 0 15px #8b5cf6;
}

.text-neon-pink {
  color: #ec4899;
  text-shadow: 0 0 5px #ec4899, 0 0 10px #ec4899, 0 0 15px #ec4899;
}

/* Holographic card effect */
.card-holographic {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 25%,
    rgba(236, 72, 153, 0.1) 50%,
    rgba(99, 102, 241, 0.1) 75%,
    rgba(59, 130, 246, 0.1) 100%);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* Enhanced backdrop blur */
.backdrop-blur-futuristic {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
}

