-- Database Migration Script for Predictive Analytics Features
-- Run this script to add predictive analytics tables and functionality

-- 1. Create user_behavior_logs table for tracking user behavior patterns
CREATE TABLE IF NOT EXISTS user_behavior_logs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    action_type VARCHAR,
    action_details JSON,
    session_id VARCHAR,
    ip_address VARCHAR,
    user_agent VARCHAR,
    feature_used VARCHAR,
    feature_duration INTEGER,
    response_time_ms FLOAT,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 2. Create risk_predictions table for storing user risk level predictions
CREATE TABLE IF NOT EXISTS risk_predictions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    risk_score FLOAT,
    risk_level VARCHAR,
    risk_factors JSON,
    model_version VARCHAR,
    confidence_score FLOAT,
    prediction_horizon_days INTEGER DEFAULT 30,
    features_used JSON,
    feature_importance JSON,
    actual_outcome BOOLEAN,
    feedback_provided BO<PERSON>EAN DEFAULT FALSE,
    predicted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 3. Create churn_predictions table for storing user churn probability predictions
CREATE TABLE IF NOT EXISTS churn_predictions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    churn_probability FLOAT,
    churn_risk_level VARCHAR,
    days_to_churn INTEGER,
    churn_factors JSON,
    retention_recommendations JSON,
    model_version VARCHAR,
    confidence_score FLOAT,
    prediction_horizon_days INTEGER DEFAULT 90,
    last_login_days_ago INTEGER,
    queries_last_30_days INTEGER,
    features_used_count INTEGER,
    avg_session_duration FLOAT,
    actual_churned BOOLEAN,
    churn_date TIMESTAMP,
    feedback_provided BOOLEAN DEFAULT FALSE,
    predicted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 4. Create anomaly_detections table for storing detected user behavior anomalies
CREATE TABLE IF NOT EXISTS anomaly_detections (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    anomaly_type VARCHAR,
    anomaly_score FLOAT,
    severity_level VARCHAR,
    description TEXT,
    affected_features JSON,
    anomaly_patterns JSON,
    baseline_behavior JSON,
    anomalous_behavior JSON,
    detection_method VARCHAR,
    model_version VARCHAR,
    confidence_score FLOAT,
    status VARCHAR DEFAULT 'detected',
    investigated_by VARCHAR,
    resolution_notes TEXT,
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 5. Create feature_adoption_predictions table for storing feature adoption likelihood predictions
CREATE TABLE IF NOT EXISTS feature_adoption_predictions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    feature_name VARCHAR,
    adoption_probability FLOAT,
    adoption_likelihood VARCHAR,
    adoption_factors JSON,
    barriers JSON,
    recommendations JSON,
    current_features_used JSON,
    user_segment VARCHAR,
    experience_level VARCHAR,
    model_version VARCHAR,
    confidence_score FLOAT,
    prediction_horizon_days INTEGER DEFAULT 60,
    actual_adopted BOOLEAN,
    adoption_date TIMESTAMP,
    feedback_provided BOOLEAN DEFAULT FALSE,
    predicted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 6. Create usage_trend_predictions table for storing system and module usage trend predictions
CREATE TABLE IF NOT EXISTS usage_trend_predictions (
    id SERIAL PRIMARY KEY,
    scope_type VARCHAR,
    scope_identifier VARCHAR,
    current_usage FLOAT,
    predicted_usage FLOAT,
    trend_direction VARCHAR,
    trend_strength FLOAT,
    prediction_horizon_days INTEGER DEFAULT 30,
    confidence_interval_lower FLOAT,
    confidence_interval_upper FLOAT,
    trend_factors JSON,
    seasonal_patterns JSON,
    external_factors JSON,
    model_type VARCHAR,
    model_version VARCHAR,
    accuracy_metrics JSON,
    actual_usage FLOAT,
    prediction_error FLOAT,
    predicted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    prediction_date TIMESTAMP
);

-- 7. Create prediction_feedback table for storing admin feedback on predictions
CREATE TABLE IF NOT EXISTS prediction_feedback (
    id SERIAL PRIMARY KEY,
    admin_user_id VARCHAR NOT NULL,
    prediction_type VARCHAR,
    prediction_id INTEGER,
    target_user_id VARCHAR,
    feedback_type VARCHAR,
    accuracy_rating INTEGER,
    feedback_notes TEXT,
    correct_outcome JSON,
    suggested_improvements TEXT,
    feedback_weight FLOAT DEFAULT 1.0,
    used_for_retraining BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 8. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_user_id ON user_behavior_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_created_at ON user_behavior_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_action_type ON user_behavior_logs(action_type);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_feature_used ON user_behavior_logs(feature_used);

CREATE INDEX IF NOT EXISTS idx_risk_predictions_user_id ON risk_predictions(user_id);
CREATE INDEX IF NOT EXISTS idx_risk_predictions_predicted_at ON risk_predictions(predicted_at);
CREATE INDEX IF NOT EXISTS idx_risk_predictions_risk_level ON risk_predictions(risk_level);
CREATE INDEX IF NOT EXISTS idx_risk_predictions_valid_until ON risk_predictions(valid_until);

CREATE INDEX IF NOT EXISTS idx_churn_predictions_user_id ON churn_predictions(user_id);
CREATE INDEX IF NOT EXISTS idx_churn_predictions_predicted_at ON churn_predictions(predicted_at);
CREATE INDEX IF NOT EXISTS idx_churn_predictions_churn_risk_level ON churn_predictions(churn_risk_level);
CREATE INDEX IF NOT EXISTS idx_churn_predictions_valid_until ON churn_predictions(valid_until);

CREATE INDEX IF NOT EXISTS idx_anomaly_detections_user_id ON anomaly_detections(user_id);
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_detected_at ON anomaly_detections(detected_at);
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_severity_level ON anomaly_detections(severity_level);
CREATE INDEX IF NOT EXISTS idx_anomaly_detections_status ON anomaly_detections(status);

CREATE INDEX IF NOT EXISTS idx_feature_adoption_predictions_user_id ON feature_adoption_predictions(user_id);
CREATE INDEX IF NOT EXISTS idx_feature_adoption_predictions_feature_name ON feature_adoption_predictions(feature_name);
CREATE INDEX IF NOT EXISTS idx_feature_adoption_predictions_predicted_at ON feature_adoption_predictions(predicted_at);

CREATE INDEX IF NOT EXISTS idx_usage_trend_predictions_scope_type ON usage_trend_predictions(scope_type);
CREATE INDEX IF NOT EXISTS idx_usage_trend_predictions_predicted_at ON usage_trend_predictions(predicted_at);
CREATE INDEX IF NOT EXISTS idx_usage_trend_predictions_prediction_date ON usage_trend_predictions(prediction_date);

CREATE INDEX IF NOT EXISTS idx_prediction_feedback_prediction_type ON prediction_feedback(prediction_type);
CREATE INDEX IF NOT EXISTS idx_prediction_feedback_target_user_id ON prediction_feedback(target_user_id);
CREATE INDEX IF NOT EXISTS idx_prediction_feedback_created_at ON prediction_feedback(created_at);

-- 9. Create views for easier data access
CREATE OR REPLACE VIEW user_risk_summary AS
SELECT 
    rp.user_id,
    rp.risk_score,
    rp.risk_level,
    rp.confidence_score,
    rp.predicted_at,
    u.name as user_name,
    u.email as user_email,
    u.subscription_status
FROM risk_predictions rp
JOIN users u ON rp.user_id = u.id
WHERE rp.predicted_at = (
    SELECT MAX(predicted_at) 
    FROM risk_predictions rp2 
    WHERE rp2.user_id = rp.user_id
);

CREATE OR REPLACE VIEW user_churn_summary AS
SELECT 
    cp.user_id,
    cp.churn_probability,
    cp.churn_risk_level,
    cp.days_to_churn,
    cp.confidence_score,
    cp.predicted_at,
    u.name as user_name,
    u.email as user_email,
    u.subscription_status
FROM churn_predictions cp
JOIN users u ON cp.user_id = u.id
WHERE cp.predicted_at = (
    SELECT MAX(predicted_at) 
    FROM churn_predictions cp2 
    WHERE cp2.user_id = cp.user_id
);

CREATE OR REPLACE VIEW user_anomaly_summary AS
SELECT 
    ad.user_id,
    COUNT(*) as total_anomalies,
    MAX(ad.severity_level) as max_severity,
    MAX(ad.detected_at) as last_anomaly_date,
    u.name as user_name,
    u.email as user_email
FROM anomaly_detections ad
JOIN users u ON ad.user_id = u.id
WHERE ad.detected_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY ad.user_id, u.name, u.email;

-- 10. Add sample data for testing (optional)
-- INSERT INTO user_behavior_logs (user_id, action_type, feature_used, feature_duration, success)
-- VALUES 
--     ('demo-user-12345', 'login', 'authentication', 5, true),
--     ('demo-user-12345', 'query', 'rag_chat', 120, true),
--     ('demo-user-12345', 'upload', 'dataset_management', 30, true);

-- Commit all changes
COMMIT;
