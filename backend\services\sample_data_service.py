"""
Sample Data Service
Generates realistic sample data for predictive analytics testing and demonstration
"""

import random
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any
from sqlalchemy.orm import Session
import json

import models
from models_predictive_analytics import UserBehaviorLog


class SampleDataService:
    """Service for generating sample user behavior data"""
    
    def __init__(self):
        self.action_types = [
            'login', 'logout', 'query', 'upload', 'download', 
            'view_dataset', 'create_chart', 'save_query', 'share_result'
        ]
        
        self.features = [
            'rag_chat', 'analytics', 'predictive', 'dataset_management', 
            'authentication', 'visualization', 'export'
        ]
    
    def generate_user_behavior_data(self, user_id: str, db: Session, days_back: int = 90) -> int:
        """Generate realistic user behavior data for the past N days"""
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Clear existing behavior logs for this user
            db.query(UserBehaviorLog).filter(UserBehaviorLog.user_id == user_id).delete()
            
            total_logs = 0
            current_date = start_date
            
            # Generate different user patterns
            user_pattern = self._determine_user_pattern(user_id)
            
            while current_date <= end_date:
                # Determine if user is active on this day
                if self._is_active_day(current_date, user_pattern):
                    daily_logs = self._generate_daily_activity(
                        user_id, current_date, user_pattern, db
                    )
                    total_logs += daily_logs
                
                current_date += timedelta(days=1)
            
            db.commit()
            return total_logs
            
        except Exception as e:
            print(f"Error generating sample data: {e}")
            db.rollback()
            return 0
    
    def _determine_user_pattern(self, user_id: str) -> Dict[str, Any]:
        """Determine user behavior pattern based on user ID"""
        # Use user ID hash to consistently assign patterns
        hash_val = hash(user_id) % 100
        
        if hash_val < 20:
            # High-risk user pattern
            return {
                'type': 'high_risk',
                'activity_level': 'declining',
                'login_frequency': 0.3,  # 30% of days
                'session_duration_range': (30, 120),  # 30s to 2min
                'error_rate': 0.3,
                'feature_diversity': 2
            }
        elif hash_val < 40:
            # Moderate-risk user pattern
            return {
                'type': 'moderate_risk',
                'activity_level': 'irregular',
                'login_frequency': 0.6,  # 60% of days
                'session_duration_range': (60, 300),  # 1min to 5min
                'error_rate': 0.15,
                'feature_diversity': 3
            }
        else:
            # Low-risk user pattern
            return {
                'type': 'low_risk',
                'activity_level': 'consistent',
                'login_frequency': 0.8,  # 80% of days
                'session_duration_range': (180, 600),  # 3min to 10min
                'error_rate': 0.05,
                'feature_diversity': 5
            }
    
    def _is_active_day(self, date: datetime, pattern: Dict[str, Any]) -> bool:
        """Determine if user is active on a given day"""
        # Weekend activity is lower
        if date.weekday() >= 5:  # Saturday, Sunday
            return random.random() < (pattern['login_frequency'] * 0.5)
        
        # Declining activity for high-risk users
        if pattern['activity_level'] == 'declining':
            days_ago = (datetime.now(timezone.utc) - date).days
            decline_factor = max(0.1, 1 - (days_ago / 90) * 0.7)
            return random.random() < (pattern['login_frequency'] * decline_factor)
        
        # Irregular activity for moderate-risk users
        if pattern['activity_level'] == 'irregular':
            # Random spikes and dips
            spike_factor = random.choice([0.3, 0.8, 1.2, 1.5])
            return random.random() < (pattern['login_frequency'] * spike_factor)
        
        # Consistent activity for low-risk users
        return random.random() < pattern['login_frequency']
    
    def _generate_daily_activity(self, user_id: str, date: datetime, pattern: Dict[str, Any], db: Session) -> int:
        """Generate activity for a single day"""
        logs_created = 0
        
        # Generate login
        login_time = date.replace(
            hour=random.randint(8, 10),
            minute=random.randint(0, 59),
            second=random.randint(0, 59)
        )
        
        session_id = f"session_{user_id}_{date.strftime('%Y%m%d')}"
        
        # Login log
        login_log = UserBehaviorLog(
            user_id=user_id,
            action_type='login',
            session_id=session_id,
            ip_address=self._generate_ip(),
            user_agent=self._generate_user_agent(),
            feature_used='authentication',
            feature_duration=random.randint(3, 10),
            response_time_ms=random.uniform(200, 800),
            success=True,
            created_at=login_time
        )
        db.add(login_log)
        logs_created += 1
        
        # Generate session activities
        current_time = login_time + timedelta(minutes=random.randint(1, 5))
        session_duration = random.randint(*pattern['session_duration_range'])
        session_end = login_time + timedelta(seconds=session_duration)
        
        # Select features to use based on diversity
        features_to_use = random.sample(
            self.features, 
            min(pattern['feature_diversity'], len(self.features))
        )
        
        while current_time < session_end:
            # Generate activity
            action_type = random.choice(self.action_types[2:])  # Exclude login/logout
            feature_used = random.choice(features_to_use)
            
            # Determine if this action has an error
            has_error = random.random() < pattern['error_rate']
            
            activity_log = UserBehaviorLog(
                user_id=user_id,
                action_type=action_type,
                session_id=session_id,
                feature_used=feature_used,
                feature_duration=random.randint(10, 180),
                response_time_ms=random.uniform(300, 2000) if has_error else random.uniform(100, 1000),
                success=not has_error,
                error_message="Timeout error" if has_error else None,
                created_at=current_time
            )
            db.add(activity_log)
            logs_created += 1
            
            # Move to next activity
            current_time += timedelta(minutes=random.randint(1, 10))
        
        # Generate logout
        logout_log = UserBehaviorLog(
            user_id=user_id,
            action_type='logout',
            session_id=session_id,
            feature_used='authentication',
            feature_duration=2,
            response_time_ms=random.uniform(100, 300),
            success=True,
            created_at=session_end
        )
        db.add(logout_log)
        logs_created += 1
        
        return logs_created
    
    def _generate_ip(self) -> str:
        """Generate a realistic IP address"""
        return f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"
    
    def _generate_user_agent(self) -> str:
        """Generate a realistic user agent string"""
        browsers = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
        ]
        return random.choice(browsers)
    
    def generate_sample_queries(self, user_id: str, db: Session, count: int = 50) -> int:
        """Generate sample queries for more realistic predictions"""
        try:
            # Create queries without requiring a dataset (use dataset_id = 1 as default)
            dataset_id = 1  # Use a default dataset ID
            
            queries_created = 0
            end_date = datetime.now(timezone.utc)

            print(f"Starting to create {count} sample queries for user {user_id}")

            for i in range(count):
                # Generate query at random time in past 30 days
                query_time = end_date - timedelta(
                    days=random.randint(0, 30),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                )
                
                # Generate realistic questions
                questions = [
                    "What is the total revenue for this month?",
                    "Show me the user growth trend",
                    "What's the average conversion rate?",
                    "Which day had the highest revenue?",
                    "How many users signed up last week?",
                    "What's the correlation between users and revenue?",
                    "Show me the revenue by day",
                    "What's the trend in conversion rates?"
                ]
                
                question = random.choice(questions)
                
                # Generate trust score
                trust_score = {
                    "overall_score": random.uniform(0.3, 0.95),
                    "factors": ["data_quality", "model_confidence"],
                    "explanation": "Generated trust score for sample data"
                }
                
                query = models.Query(
                    user_id=user_id,
                    dataset_id=dataset_id,
                    question=question,
                    answer=f"Sample answer for: {question}",
                    trust_score=json.dumps(trust_score),
                    response_time_ms=random.uniform(500, 3000),
                    created_at=query_time
                )
                db.add(query)
                queries_created += 1

                if i % 10 == 0:  # Log every 10 queries
                    print(f"Created {i+1} queries so far...")

            db.commit()
            print(f"Successfully created {queries_created} queries")
            return queries_created
            
        except Exception as e:
            print(f"Error generating sample queries: {e}")
            import traceback
            traceback.print_exc()
            db.rollback()
            return 0
