import type { Metada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "../components/providers/auth-provider";

export const metadata: Metadata = {
  title: "AIthentiq - Natural Language Interface for Data Insights",
  description: "Interact with your tabular datasets through natural language queries",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta
          name="format-detection"
          content="telephone=no, date=no, email=no, address=no"
        />
      </head>
      <body className="bg-gray-50" suppressHydrationWarning>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
