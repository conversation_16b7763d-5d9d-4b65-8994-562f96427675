#!/usr/bin/env python3
"""
Initialize database with all required tables and demo data
"""

import sqlite3
import os
import sys

def init_database():
    """Initialize the database with all required tables"""
    
    # Connect to SQLite database
    db_path = "aithentiq.db"
    print(f"Initializing database: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR PRIMARY KEY,
                email VARCHAR UNIQUE NOT NULL,
                name VA<PERSON>HA<PERSON>,
                password_hash VARCHAR,
                role VA<PERSON>HAR DEFAULT 'user',
                subscription_status VARCHAR DEFAULT 'free',
                referral_code VARCHAR,
                failed_login_attempts INTEGER DEFAULT 0,
                locked_until DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ Created users table")
        
        # Create datasets table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS datasets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR NOT NULL,
                user_id VARCHAR NOT NULL,
                parsed_data TEXT NOT NULL,
                columns TEXT NOT NULL,
                row_count INTEGER NOT NULL,
                file_type VARCHAR DEFAULT 'csv',
                content_type VARCHAR DEFAULT 'tabular',
                document_metadata TEXT,
                embeddings_data TEXT,
                processing_status VARCHAR DEFAULT 'completed',
                word_count INTEGER DEFAULT 0,
                character_count INTEGER DEFAULT 0,
                has_image BOOLEAN DEFAULT FALSE,
                chunking_template VARCHAR,
                total_chunks INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        print("✅ Created datasets table")
        
        # Create queries table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS queries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id VARCHAR NOT NULL,
                dataset_id INTEGER NOT NULL,
                question TEXT NOT NULL,
                answer TEXT NOT NULL,
                chart_type VARCHAR,
                chart_data TEXT,
                query_name VARCHAR,
                is_bookmarked BOOLEAN DEFAULT FALSE,
                tags TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (dataset_id) REFERENCES datasets (id)
            )
        """)
        print("✅ Created queries table")
        
        # Create conversations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS conversations (
                conversation_id VARCHAR PRIMARY KEY,
                user_id VARCHAR NOT NULL,
                dataset_id INTEGER NOT NULL,
                title VARCHAR NOT NULL,
                last_message TEXT,
                message_count INTEGER DEFAULT 0,
                is_starred BOOLEAN DEFAULT FALSE,
                is_important BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (dataset_id) REFERENCES datasets (id)
            )
        """)
        print("✅ Created conversations table")
        
        # Create conversation_messages table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS conversation_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                conversation_id VARCHAR NOT NULL,
                role VARCHAR NOT NULL,
                content TEXT NOT NULL,
                processing_time INTEGER,
                trust_score REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (conversation_id) REFERENCES conversations (conversation_id)
            )
        """)
        print("✅ Created conversation_messages table")
        
        # Create api_keys table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS api_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id VARCHAR NOT NULL,
                key VARCHAR UNIQUE NOT NULL,
                name VARCHAR,
                is_active BOOLEAN DEFAULT TRUE,
                last_used DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        print("✅ Created api_keys table")
        
        # Create query_cache table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS query_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id VARCHAR,
                dataset_id INTEGER NOT NULL,
                question_hash VARCHAR NOT NULL,
                question TEXT NOT NULL,
                answer TEXT NOT NULL,
                chart_type VARCHAR,
                chart_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (dataset_id) REFERENCES datasets (id)
            )
        """)
        print("✅ Created query_cache table")
        
        # Insert demo API key
        cursor.execute("""
            INSERT OR IGNORE INTO api_keys (user_id, key, name, is_active)
            VALUES ('demo-user-12345', 'demo-api-key', 'Demo API Key', TRUE)
        """)
        print("✅ Inserted demo API key")
        
        # Insert demo user
        cursor.execute("""
            INSERT OR IGNORE INTO users (id, email, name, role)
            VALUES ('demo-user-12345', '<EMAIL>', 'Demo User', 'user')
        """)
        print("✅ Inserted demo user")
        
        # Commit all changes
        conn.commit()
        print("✅ Database initialization completed successfully!")
        
        # Test the database
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ Created {len(tables)} tables: {[t[0] for t in tables]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = init_database()
    sys.exit(0 if success else 1)
