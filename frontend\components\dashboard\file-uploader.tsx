'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useSession } from 'next-auth/react';
import { createApiInstance } from '@/lib/api';

interface FileUploaderProps {
  onUploadSuccess: (datasetId: number) => void;
}

interface ProcessingStatus {
  stage: string;
  message: string;
  progress: number;
}

export default function FileUploader({ onUploadSuccess }: FileUploaderProps) {
  const { data: session } = useSession();
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [availableTemplates, setAvailableTemplates] = useState<string[]>([]);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus | null>(null);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Load available templates when session is available
  React.useEffect(() => {
    loadTemplates();
  }, [session]);

  const loadTemplates = async () => {
    try {
      if (!session) {
        // Fallback to basic templates if no session
        setAvailableTemplates(['general', 'academic', 'technical', 'legal', 'financial', 'research']);
        return;
      }

      const sessionApi = createApiInstance(session);
      const response = await sessionApi.get('/api/v1/document-intelligence/chunking-templates');
      setAvailableTemplates(response.data);
    } catch (err) {
      console.error('Failed to load templates:', err);
      // Fallback to basic templates if API fails
      setAvailableTemplates(['general', 'academic', 'technical', 'legal', 'financial', 'research']);
    }
  };

  const simulateProcessingStages = (file: File) => {
    const stages = [
      { stage: 'upload', message: 'Uploading file...', progress: 20 },
      { stage: 'detection', message: 'Detecting document type...', progress: 40 },
      { stage: 'chunking', message: 'Creating intelligent chunks...', progress: 60 },
      { stage: 'embeddings', message: 'Generating embeddings...', progress: 80 },
      { stage: 'complete', message: 'Processing complete!', progress: 100 }
    ];

    let currentStage = 0;
    const interval = setInterval(() => {
      if (currentStage < stages.length) {
        setProcessingStatus(stages[currentStage]);
        currentStage++;
      } else {
        clearInterval(interval);
      }
    }, 800);

    return interval;
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Check if file type is supported
    const supportedExtensions = ['.csv', '.xlsx', '.xls', '.pdf', '.docx', '.txt', '.md', '.jpg', '.jpeg', '.png'];
    const fileExtension = file.name.toLowerCase();
    const isSupported = supportedExtensions.some(ext => fileExtension.endsWith(ext));

    if (!isSupported) {
      setError('Supported formats: CSV, Excel (XLSX, XLS), PDF, DOCX, TXT, MD, JPG, PNG');
      return;
    }

    setFileName(file.name);
    setError(null);
    setUploading(true);
    setProcessingStatus({ stage: 'upload', message: 'Starting upload...', progress: 10 });

    // Start processing simulation
    const processingInterval = simulateProcessingStages(file);

    try {
      console.log('Uploading file:', file.name);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', file.name);

      // Add template selection if specified
      if (selectedTemplate) {
        formData.append('chunking_template', selectedTemplate);
      }

      console.log('FormData created with:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      });

      // Use authenticated API instance for API v1 endpoint
      const sessionApi = createApiInstance(session);
      const response = await sessionApi.post(
        `/api/v1/datasets`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      console.log('Upload response:', response);

      if (response && response.data && response.data.id) {
        console.log('Upload successful, dataset ID:', response.data.id);

        // Show completion status
        setProcessingStatus({
          stage: 'complete',
          message: `Successfully processed with ${response.data.chunking_template || 'auto-detected'} template!`,
          progress: 100
        });

        // Clear processing status after a delay
        setTimeout(() => {
          setProcessingStatus(null);
          clearInterval(processingInterval);
        }, 2000);

        onUploadSuccess(response.data.id);
      } else {
        console.error('Invalid upload response format:', response);
        setError('Invalid response format from server');
      }
    } catch (err: any) {
      console.error('Error uploading file:', err);
      setError(err.response?.data?.detail || err.message || 'Error uploading file');
      clearInterval(processingInterval);
      setProcessingStatus(null);
    } finally {
      setUploading(false);
    }
  }, [onUploadSuccess]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'text/markdown': ['.md'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
    },
    maxFiles: 1,
  });

  return (
    <div className="w-full space-y-4">
      {/* Title and Advanced Options Toggle */}
      <div className="flex items-center justify-between flex-nowrap">
        <h3 className="text-sm font-semibold text-black whitespace-nowrap">Upload Dataset</h3>
        <button
          onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
          className="text-xs text-blue-600 hover:text-blue-800 flex items-center whitespace-nowrap"
        >
          <span>Advanced Options</span>
          <svg
            className={`ml-1 h-4 w-4 transition-transform ${showAdvancedOptions ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {/* Advanced Options Panel */}
      {showAdvancedOptions && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document Processing Template
            </label>
            <select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-none"
            >
              <option value="">Auto-detect (Recommended)</option>
              {availableTemplates.map((template) => (
                <option key={template} value={template}>
                  {template.charAt(0).toUpperCase() + template.slice(1)} Template
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">
              Choose a specific template or let the system auto-detect the best approach
            </p>
          </div>
        </div>
      )}

      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
        }`}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          <svg
            className="mx-auto h-12 w-12 text-gray-600"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
            aria-hidden="true"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <div className="text-sm text-black">
            <span className="font-medium text-blue-600">Click to upload</span> or drag and drop
          </div>
          <p className="text-xs text-black">
            CSV, Excel (XLSX, XLS), PDF, DOCX, TXT, MD, JPG, PNG files up to 10MB
          </p>
          {selectedTemplate && (
            <p className="text-xs text-blue-600 mt-1">
              Using {selectedTemplate.charAt(0).toUpperCase() + selectedTemplate.slice(1)} template
            </p>
          )}
        </div>
      </div>

      {/* Processing Status */}
      {processingStatus && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-900">{processingStatus.message}</span>
            <span className="text-sm text-blue-700">{processingStatus.progress}%</span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${processingStatus.progress}%` }}
            ></div>
          </div>
          {processingStatus.stage === 'complete' && (
            <div className="mt-2 flex items-center text-green-700">
              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-sm">Ready for analysis!</span>
            </div>
          )}
        </div>
      )}

      {/* Legacy uploading indicator */}
      {uploading && !processingStatus && (
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-sm text-black">Uploading {fileName}...</p>
        </div>
      )}

      {error && (
        <div className="text-center text-red-500 text-sm bg-red-50 border border-red-200 rounded-lg p-3">
          {error}
        </div>
      )}
    </div>
  );
}
