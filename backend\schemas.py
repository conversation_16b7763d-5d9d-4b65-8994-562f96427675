from pydantic import BaseModel
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: str
    role: str = "user"
    subscription_status: str = "free"

class UserCreate(UserBase):
    name: str
    password: str
    id: Optional[str] = None  # Make ID optional, generate if not provided
    referral_code: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class ForgotPassword(BaseModel):
    email: str

class ResetPassword(BaseModel):
    email: str
    reset_code: str
    new_password: str

class UserResponse(UserBase):
    id: str
    referral_code: str
    created_at: datetime

    class Config:
        from_attributes = True

# Dataset schemas
class DatasetBase(BaseModel):
    name: str

class DatasetCreate(DatasetBase):
    user_id: str

class DatasetResponse(DatasetBase):
    id: int
    columns: List[str]
    row_count: int
    created_at: datetime
    file_type: Optional[str] = 'csv'
    content_type: Optional[str] = 'tabular'
    processing_status: Optional[str] = 'completed'
    word_count: Optional[int] = 0
    character_count: Optional[int] = 0

    class Config:
        from_attributes = True

# Query schemas
class QueryRequest(BaseModel):
    user_id: str
    dataset_id: int
    question: str
    include_cot: bool = False
    include_trust_score: bool = True
    trust_score_method: str = "multi_llm_convergence"

class TrustScore(BaseModel):
    overall_score: float
    factors: List[str]
    explanation: str

class SourceAttribution(BaseModel):
    document: str
    line_start: Optional[int] = None
    line_end: Optional[int] = None
    char_start: Optional[int] = None
    char_end: Optional[int] = None
    text: str
    page_number: Optional[int] = None
    section: Optional[str] = None
    chunk_type: Optional[str] = None
    score: Optional[float] = None
    rank: Optional[int] = None

class QueryResponse(BaseModel):
    id: int
    answer: str
    chart_type: Optional[str] = None
    chart_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    processing_time: Optional[int] = None
    trust_score: Optional[TrustScore] = None
    reasoning_steps: Optional[List[str]] = None
    query_name: Optional[str] = None
    is_bookmarked: Optional[bool] = False
    tags: Optional[List[str]] = None
    sources: Optional[List[SourceAttribution]] = None

    class Config:
        from_attributes = True

# Saved Query schemas
class SavedQueryCreate(BaseModel):
    query_id: int
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_favorite: bool = False

class SavedQueryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_favorite: Optional[bool] = None

class SavedQueryResponse(BaseModel):
    id: int
    user_id: str
    query_id: int
    name: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    is_favorite: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Query History schemas
class QueryHistoryResponse(BaseModel):
    id: int
    user_id: str
    dataset_id: int
    dataset_name: Optional[str] = None
    question: str
    answer: str
    chart_type: Optional[str] = None
    chart_data: Optional[Dict[str, Any]] = None
    trust_score: Optional[TrustScore] = None
    reasoning_steps: Optional[List[str]] = None
    query_name: Optional[str] = None
    is_bookmarked: bool
    tags: Optional[List[str]] = None
    created_at: datetime
    is_saved: bool = False

    class Config:
        from_attributes = True

# Referral schemas
class ReferralCreate(BaseModel):
    referrer_id: str
    referee_id: str

class ReferralResponse(BaseModel):
    id: int
    referrer_id: str
    referee_id: str
    redeemed: bool
    created_at: datetime

    class Config:
        from_attributes = True

# Feedback schemas
class FeedbackCreate(BaseModel):
    user_id: str
    query_id: int
    rating: str  # 'up', 'down', or 'neutral'
    comment: Optional[str] = None

class FeedbackResponse(BaseModel):
    id: int
    user_id: str
    query_id: int
    rating: str
    comment: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

class FeedbackSummary(BaseModel):
    id: int
    user_id: str
    user_email: str
    query_id: int
    question: str
    answer: str
    rating: str
    comment: Optional[str] = None
    created_at: datetime

# Stripe schemas
class CheckoutRequest(BaseModel):
    user_id: str
    price_id: str
    success_url: str
    cancel_url: str

class CheckoutResponse(BaseModel):
    checkout_url: str

class WebhookRequest(BaseModel):
    payload: str
    signature: str

# API Key schemas
class ApiKeyCreate(BaseModel):
    user_id: str
    name: str

class ApiKeyResponse(BaseModel):
    id: int
    name: str
    key: str
    is_active: bool
    created_at: datetime
    last_used: Optional[datetime] = None

    class Config:
        from_attributes = True

class ApiKeyUpdate(BaseModel):
    name: Optional[str] = None
    is_active: Optional[bool] = None

# Analytics schemas
class CorrelationRequest(BaseModel):
    columns: Optional[List[str]] = None
    method: str = "pearson"  # "pearson", "spearman", or "kendall"

class HypothesisTestRequest(BaseModel):
    test_type: str  # "ttest_1samp", "ttest_ind", "chi2", "anova"
    column1: str
    column2: Optional[str] = None
    alpha: float = 0.05
    alternative: str = "two-sided"  # "two-sided", "less", "greater"

class OutlierDetectionRequest(BaseModel):
    column: str
    method: str = "zscore"  # "zscore", "iqr", "percentile"
    threshold: float = 3.0

class TimeSeriesAnalysisRequest(BaseModel):
    value_column: str
    date_column: str
    frequency: Optional[str] = None  # "D", "W", "M", "Q", "Y"

class TimeSeriesForecastRequest(BaseModel):
    value_column: str
    date_column: str
    forecast_periods: int = 5
    frequency: Optional[str] = None  # "D", "W", "M", "Q", "Y"
    method: str = "auto"  # "auto", "arima", "exponential_smoothing", "prophet"

class TimeSeriesAnomalyRequest(BaseModel):
    value_column: str
    date_column: str
    method: str = "zscore"  # "zscore", "iqr", "prophet"
    threshold: float = 3.0
    frequency: Optional[str] = None  # "D", "W", "M", "Q", "Y"

# Predictive model schemas
class ForecastRequest(BaseModel):
    target_column: str
    date_column: str
    horizon: int = 10
    frequency: Optional[str] = None
    method: str = "auto"
    confidence_interval: float = 0.95
    use_background: Optional[bool] = None  # If true, use background processing

class PredictiveModelTrainRequest(BaseModel):
    target_column: str
    feature_columns: List[str]
    model_type: str = "auto"  # "auto", "classification", "regression"
    model_params: Optional[Dict[str, Any]] = None
    test_size: float = 0.2
    model_name: Optional[str] = None
    description: Optional[str] = None

class PredictiveModelPredictRequest(BaseModel):
    model_id: str
    input_data: Union[Dict[str, Any], List[Dict[str, Any]]]

# Advanced Analytics schemas
class PivotTableRequest(BaseModel):
    index_column: str
    columns_column: Optional[str] = None
    values_column: Optional[str] = None
    aggregation_function: str = "mean"  # mean, sum, count, min, max, std

class BusinessTemplateRequest(BaseModel):
    template_type: str  # revenue_trend, sales_forecast, demand_planning, traffic_analysis, energy_usage
    date_column: str
    value_column: str
    forecast_periods: int = 30
    frequency: Optional[str] = None

class PredictiveModelResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    model_type: str
    target_column: str
    feature_columns: List[str]
    metrics: Dict[str, Any]
    feature_importance: Optional[Dict[str, float]] = None
    created_at: datetime
    is_active: bool

class PredictionResponse(BaseModel):
    model_id: str
    model_name: str
    model_type: str
    predictions: List[Any]
    probabilities: Optional[List[Dict[str, float]]] = None
