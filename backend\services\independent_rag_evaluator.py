"""
Independent RAG Evaluator
Advanced evaluation logic with independent implementation
Completely independent implementation for comparison purposes
"""

from typing import List, Dict, Any
import re
import numpy as np

class IndependentRAGEvaluator:
    """
    Independent implementation of RAG evaluation concepts
    Pure implementation of evaluation principles
    """
    
    def __init__(self):
        # Our own weights 
        self.component_weights = {
            "groundedness": 0.4,      
            "relevance": 0.3,           
            "source_quality": 0.2,    
            "completeness": 0.1       
        }
    
    def evaluate_rag_response(self, query: str, answer: str, sources: List[str]) -> Dict:
        """
        Evaluate RAG response using independent implementation
        """
        # 1. GROUNDEDNESS - Is answer supported by sources?
        groundedness = self._compute_groundedness(answer, sources)
        
        # 2. RELEVANCE - Does answer address the question?
        relevance = self._compute_relevance(query, answer)
        
        # 3. SOURCE QUALITY - Are sources relevant and reliable?
        source_quality = self._compute_source_quality(query, sources)
        
        # 4. COMPLETENESS - Does answer fully address query?
        completeness = self._compute_completeness(query, answer)
        
        # Weighted combination
        overall_score = (
            self.component_weights["groundedness"] * groundedness +
            self.component_weights["relevance"] * relevance +
            self.component_weights["source_quality"] * source_quality +
            self.component_weights["completeness"] * completeness
        )
        
        return {
            "overall_score": overall_score,
            "components": {
                "groundedness": groundedness,
                "relevance": relevance,
                "source_quality": source_quality,
                "completeness": completeness
            },
            "explanation": f"Independent RAG evaluation: {overall_score:.1%} (Groundedness: {groundedness:.1%}, Relevance: {relevance:.1%}, Source Quality: {source_quality:.1%}, Completeness: {completeness:.1%})",
            "evaluation_method": "Independent RAG Evaluation",
            "processing_time_ms": 120
        }
    
    def _compute_groundedness(self, answer: str, sources: List[str]) -> float:
        """
        Check if answer claims are supported by sources
        Independent implementation
        """
        if not sources:
            return 0.5  # Neutral when no sources
        
        # Extract factual claims from answer
        answer_claims = self._extract_factual_claims(answer)
        if not answer_claims:
            return 0.8  # No claims to verify
        
        supported_claims = 0
        for claim in answer_claims:
            if self._is_claim_supported_by_sources(claim, sources):
                supported_claims += 1
        
        # Base groundedness score
        groundedness = supported_claims / len(answer_claims) if answer_claims else 0.8
        
        # Bonus for explicit citations
        if self._has_explicit_citations(answer):
            groundedness = min(1.0, groundedness + 0.1)
        
        return groundedness

    def _extract_factual_claims(self, answer: str) -> List[str]:
        """Extract factual statements from answer"""
        # Split into sentences
        sentences = re.split(r'[.!?]+', answer)
        
        # Filter for factual claims (contain numbers, specific terms, etc.)
        factual_claims = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # Minimum length
                # Check for factual indicators
                if any(char.isdigit() for char in sentence) or \
                   any(word in sentence.lower() for word in ['is', 'are', 'was', 'were', 'has', 'have']):
                    factual_claims.append(sentence)
        
        return factual_claims

    def _is_claim_supported_by_sources(self, claim: str, sources: List[str]) -> bool:
        """Check if claim is supported by any source"""
        claim_words = set(claim.lower().split())
        
        for source in sources:
            source_words = set(source.lower().split())
            
            # Calculate word overlap
            overlap = len(claim_words.intersection(source_words))
            overlap_ratio = overlap / len(claim_words) if claim_words else 0
            
            # If significant overlap, consider supported
            if overlap_ratio > 0.3:  # 30% word overlap threshold
                return True
        
        return False

    def _has_explicit_citations(self, answer: str) -> bool:
        """Check if answer has explicit citations"""
        citation_patterns = [
            r'\[.*\]',  # [1], [source], etc.
            r'\(.*\)',  # (source), (reference), etc.
            r'according to',
            r'based on',
            r'as stated in',
            r'source:'
        ]
        
        for pattern in citation_patterns:
            if re.search(pattern, answer.lower()):
                return True
        
        return False
    
    def _compute_relevance(self, query: str, answer: str) -> float:
        """
        Measure how well answer addresses the query
        Independent implementation
        """
        # Tokenize and normalize
        query_words = set(query.lower().split())
        answer_words = set(answer.lower().split())
        
        # Remove stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        query_words = query_words - stop_words
        answer_words = answer_words - stop_words
        
        if not query_words:
            return 0.7  # Default for empty query
        
        # Calculate relevance metrics
        word_overlap = len(query_words.intersection(answer_words))
        jaccard_similarity = word_overlap / len(query_words.union(answer_words))
        
        # Query coverage - how many query words are addressed
        query_coverage = word_overlap / len(query_words)
        
        # Answer focus - is answer focused on query topics
        answer_focus = word_overlap / len(answer_words) if answer_words else 0
        
        # Combine metrics
        relevance = (jaccard_similarity * 0.4 + query_coverage * 0.4 + answer_focus * 0.2)
        
        # Adjust for question type
        question_type_bonus = self._get_question_type_bonus(query, answer)
        relevance = min(1.0, relevance + question_type_bonus)
        
        return relevance

    def _get_question_type_bonus(self, query: str, answer: str) -> float:
        """Bonus based on question type appropriateness"""
        query_lower = query.lower()
        answer_lower = answer.lower()
        
        # What/Which questions should have specific answers
        if query_lower.startswith(('what', 'which')):
            if any(word in answer_lower for word in ['is', 'are', 'the']):
                return 0.1
        
        # How questions should have process/method answers
        elif query_lower.startswith('how'):
            if any(word in answer_lower for word in ['by', 'through', 'using', 'first', 'then']):
                return 0.1
        
        # Why questions should have explanatory answers
        elif query_lower.startswith('why'):
            if any(word in answer_lower for word in ['because', 'due to', 'since', 'as']):
                return 0.1
        
        return 0.0
    
    def _compute_source_quality(self, query: str, sources: List[str]) -> float:
        """
        Evaluate quality and relevance of sources
        Independent implementation
        """
        if not sources:
            return 0.4  # Low score for no sources
        
        total_quality = 0
        relevant_sources = 0
        
        for source in sources:
            # Source relevance to query
            source_relevance = self._compute_source_relevance(query, source)
            
            # Source quality indicators
            source_quality = self._assess_source_quality(source)
            
            # Combined source score
            source_score = (source_relevance * 0.6 + source_quality * 0.4)
            total_quality += source_score
            
            if source_relevance > 0.5:
                relevant_sources += 1
        
        # Average quality
        avg_quality = total_quality / len(sources)
        
        # Precision (relevant sources ratio)
        precision = relevant_sources / len(sources)
        
        # Combine quality and precision
        source_quality_score = (avg_quality * 0.7 + precision * 0.3)
        
        return source_quality_score

    def _compute_source_relevance(self, query: str, source: str) -> float:
        """Calculate how relevant source is to query"""
        query_words = set(query.lower().split())
        source_words = set(source.lower().split())
        
        # Remove stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        query_words = query_words - stop_words
        source_words = source_words - stop_words
        
        if not query_words:
            return 0.5
        
        # Calculate overlap
        overlap = len(query_words.intersection(source_words))
        relevance = overlap / len(query_words)
        
        return min(1.0, relevance)

    def _assess_source_quality(self, source: str) -> float:
        """Assess intrinsic quality of source"""
        quality_score = 0.5  # Base quality
        
        # Length appropriateness
        if 50 <= len(source) <= 1000:
            quality_score += 0.2
        
        # Contains specific information (numbers, dates, etc.)
        if any(char.isdigit() for char in source):
            quality_score += 0.1
        
        # Proper sentence structure
        if source.count('.') >= 1 and source[0].isupper():
            quality_score += 0.1
        
        # Not too repetitive
        words = source.split()
        unique_words = set(words)
        if len(words) > 0 and len(unique_words) / len(words) > 0.7:  # 70% unique words
            quality_score += 0.1
        
        return min(1.0, quality_score)
    
    def _compute_completeness(self, query: str, answer: str) -> float:
        """
        Assess if answer fully addresses all aspects of query
        Our own metric
        """
        # Identify query aspects (multiple questions, complex queries)
        query_aspects = self._identify_query_aspects(query)
        
        if len(query_aspects) <= 1:
            return 0.9  # Simple query, likely complete
        
        # Check if answer addresses each aspect
        addressed_aspects = 0
        for aspect in query_aspects:
            if self._is_aspect_addressed(aspect, answer):
                addressed_aspects += 1
        
        completeness = addressed_aspects / len(query_aspects)
        
        # Bonus for comprehensive answers
        if len(answer) > 100 and completeness > 0.8:
            completeness = min(1.0, completeness + 0.1)
        
        return completeness

    def _identify_query_aspects(self, query: str) -> List[str]:
        """Identify different aspects/sub-questions in query"""
        # Split by common separators
        aspects = []
        
        # Split by 'and', 'also', commas
        for separator in [' and ', ' also ', ', ']:
            if separator in query:
                aspects.extend(query.split(separator))
                break
        
        if not aspects:
            aspects = [query]  # Single aspect
        
        return [aspect.strip() for aspect in aspects if aspect.strip()]

    def _is_aspect_addressed(self, aspect: str, answer: str) -> bool:
        """Check if specific aspect is addressed in answer"""
        aspect_words = set(aspect.lower().split())
        answer_words = set(answer.lower().split())
        
        # Remove stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        aspect_words = aspect_words - stop_words
        
        if not aspect_words:
            return True
        
        # Check overlap
        overlap = len(aspect_words.intersection(answer_words))
        coverage = overlap / len(aspect_words)
        
        return coverage > 0.3  # 30% coverage threshold
