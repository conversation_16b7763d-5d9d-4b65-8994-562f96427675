"""
Trust Score Comparison Service
Compares 5 different trust score algorithms for evaluation
"""

import time
import asyncio
from typing import Dict, List, Optional, Any
import numpy as np
from dataclasses import dataclass
import json

@dataclass
class TrustScoreResult:
    """Result from a trust score computation"""
    method_name: str
    score: float
    processing_time_ms: float
    components: Dict[str, float]
    explanation: str
    error: Optional[str] = None

class TrustComparisonService:
    """
    Service to compare multiple trust score algorithms
    """
    
    def __init__(self):
        self.methods = {
            "legacy_deterministic": "Legacy Deterministic (Current Fallback)",
            "enhanced_v25": "Enhanced V2.5 (Advanced System)",
            "current_production": "Current Production (In Use)",
            "independent_rag": "Independent RAG Evaluator",
            "aithentiq_convergence": "AIthentiq Convergence (Innovative)"
        }
    
    async def compare_trust_scores(
        self, 
        query: str, 
        answer: str, 
        sources: List[Dict], 
        selected_methods: List[str],
        user_id: str = "demo_user"
    ) -> Dict[str, Any]:
        """
        Compare selected trust score methods
        """
        results = []
        
        for method in selected_methods:
            if method in self.methods:
                try:
                    result = await self._compute_trust_score(
                        method, query, answer, sources, user_id
                    )
                    results.append(result)
                except Exception as e:
                    results.append(TrustScoreResult(
                        method_name=self.methods[method],
                        score=0.0,
                        processing_time_ms=0.0,
                        components={},
                        explanation=f"Error: {str(e)}",
                        error=str(e)
                    ))
        
        # Generate comparison analysis
        analysis = self._analyze_results(results)
        
        return {
            "results": [self._result_to_dict(r) for r in results],
            "analysis": analysis,
            "query": query,
            "answer": answer,
            "timestamp": time.time()
        }
    
    async def _compute_trust_score(
        self, 
        method: str, 
        query: str, 
        answer: str, 
        sources: List[Dict],
        user_id: str
    ) -> TrustScoreResult:
        """Compute trust score using specified method"""
        
        start_time = time.time()
        
        if method == "legacy_deterministic":
            result = await self._legacy_deterministic(query, answer, sources)
        elif method == "enhanced_v25":
            result = await self._enhanced_v25(query, answer, sources, user_id)
        elif method == "current_production":
            result = await self._current_production(query, answer, sources, user_id)
        elif method == "independent_rag":
            result = await self._independent_rag(query, answer, sources)
        elif method == "aithentiq_convergence":
            result = await self._aithentiq_convergence(query, answer, sources)
        else:
            raise ValueError(f"Unknown method: {method}")
        
        processing_time = (time.time() - start_time) * 1000  # Convert to ms
        
        return TrustScoreResult(
            method_name=self.methods[method],
            score=result.get("overall_score", 0.0),
            processing_time_ms=processing_time,
            components=result.get("components", {}),
            explanation=result.get("explanation", ""),
            error=result.get("error")
        )
    
    async def _legacy_deterministic(self, query: str, answer: str, sources: List[Dict]) -> Dict:
        """Legacy deterministic trust score (current fallback)"""
        try:
            # Import here to avoid circular imports
            from services.llm_service import LLMService

            llm_service = LLMService()

            # Create a dummy dataframe for the legacy function
            import pandas as pd
            df = pd.DataFrame({"dummy": [1]})

            score = llm_service.calculate_deterministic_trust_score(query, answer, df)

            return {
                "overall_score": score,
                "components": {
                    "deterministic_score": score
                },
                "explanation": f"Legacy deterministic score: {score:.1%}",
                "method": "Legacy Deterministic"
            }
        except ImportError as e:
            # Fallback implementation if LLMService not available
            return self._simple_legacy_fallback(query, answer, sources)

    def _simple_legacy_fallback(self, query: str, answer: str, sources: List[Dict]) -> Dict:
        """Simple fallback when LLMService is not available"""
        # Implement the same logic as the original deterministic function
        score = 0.85  # Start at 85% (optimistic baseline)

        # Check if answer contains specific numbers
        digit_count = sum(1 for char in answer if char.isdigit())
        if digit_count > 0:
            score += 0.08

        # Check answer length
        answer_length = len(answer.strip())
        if answer_length >= 15:
            score += 0.05

        # Check for data-specific terms
        data_terms = ['average', 'mean', 'median', 'sum', 'total', 'count', 'maximum', 'minimum', 'analysis', 'data']
        data_term_count = sum(1 for term in data_terms if term in answer.lower())
        if data_term_count > 0:
            score += min(0.05, data_term_count * 0.02)

        # Check for question relevance
        question_words = set(query.lower().split())
        answer_words = set(answer.lower().split())
        relevance_score = len(question_words.intersection(answer_words)) / max(len(question_words), 1)
        if relevance_score > 0.2:
            score += 0.03

        # Evidence-based language bonus
        if any(word in answer.lower() for word in ['based on', 'according to', 'analysis shows', 'data indicates']):
            score += 0.03

        # Cap the score at 1.0
        score = min(score, 1.0)

        return {
            "overall_score": score,
            "components": {
                "deterministic_score": score,
                "digit_bonus": 0.08 if digit_count > 0 else 0,
                "length_bonus": 0.05 if answer_length >= 15 else 0,
                "data_terms_bonus": min(0.05, data_term_count * 0.02),
                "relevance_bonus": 0.03 if relevance_score > 0.2 else 0,
                "evidence_bonus": 0.03 if any(word in answer.lower() for word in ['based on', 'according to']) else 0
            },
            "explanation": f"Legacy deterministic score (fallback): {score:.1%}",
            "method": "Legacy Deterministic (Fallback)"
        }
    
    async def _enhanced_v25(self, query: str, answer: str, sources: List[Dict], user_id: str) -> Dict:
        """Enhanced V2.5 trust score system"""
        try:
            from services.trust_integration_service import trust_integration_service

            if trust_integration_service:
                result = trust_integration_service.compute_enhanced_trust_score(
                    query=query,
                    answer=answer,
                    user_id=user_id,
                    sources=sources,
                    topic="comparison_demo"
                )
                return result
            else:
                # Fallback to simulated V2.5 logic
                return self._simulate_enhanced_v25(query, answer, sources)
        except Exception as e:
            # Fallback to simulated V2.5 logic
            return self._simulate_enhanced_v25(query, answer, sources)

    def _simulate_enhanced_v25(self, query: str, answer: str, sources: List[Dict]) -> Dict:
        """Simulate Enhanced V2.5 logic when service not available"""
        # Simulate the V2.5 multi-component approach

        # 1. Model Confidence (40%)
        model_confidence = self._calculate_model_confidence(answer)

        # 2. Source Quality (30%)
        source_quality = self._calculate_source_quality(sources)

        # 3. Citation Accuracy (20%)
        citation_accuracy = self._calculate_citation_accuracy(answer, sources)

        # 4. Question Match (10%)
        question_match = self._calculate_question_match(query, answer)

        # Weighted combination (V2.5 weights)
        overall_score = (
            model_confidence * 0.4 +
            source_quality * 0.3 +
            citation_accuracy * 0.2 +
            question_match * 0.1
        )

        return {
            "overall_score": overall_score,
            "components": {
                "model_confidence": model_confidence,
                "source_quality": source_quality,
                "citation_accuracy": citation_accuracy,
                "question_match": question_match
            },
            "explanation": f"Enhanced V2.5 (simulated): {overall_score:.1%} - Multi-component analysis",
            "method": "Enhanced V2.5 (Simulated)",
            "weights": {
                "model_confidence": 0.4,
                "source_quality": 0.3,
                "citation_accuracy": 0.2,
                "question_match": 0.1
            }
        }

    def _calculate_model_confidence(self, answer: str) -> float:
        """Calculate model confidence based on answer characteristics"""
        confidence = 0.7  # Base confidence

        # Confident language indicators
        confident_phrases = ['clearly', 'definitely', 'certainly', 'obviously', 'precisely']
        uncertain_phrases = ['might', 'could', 'possibly', 'perhaps', 'maybe']

        confident_count = sum(1 for phrase in confident_phrases if phrase in answer.lower())
        uncertain_count = sum(1 for phrase in uncertain_phrases if phrase in answer.lower())

        confidence += confident_count * 0.05
        confidence -= uncertain_count * 0.1

        # Length and structure bonus
        if 50 <= len(answer) <= 300:
            confidence += 0.1

        # Specific data bonus
        if any(char.isdigit() for char in answer):
            confidence += 0.1

        return max(0.0, min(1.0, confidence))

    def _calculate_source_quality(self, sources: List[Dict]) -> float:
        """Calculate source quality score"""
        if not sources:
            return 0.4  # Low score for no sources

        quality_score = 0.6  # Base quality

        # Number of sources bonus
        if len(sources) >= 2:
            quality_score += 0.2
        if len(sources) >= 3:
            quality_score += 0.1

        # Source length and content quality
        total_length = sum(len(str(source.get('text', str(source)))) for source in sources)
        avg_length = total_length / len(sources)

        if avg_length > 50:
            quality_score += 0.1

        return min(1.0, quality_score)

    def _calculate_citation_accuracy(self, answer: str, sources: List[Dict]) -> float:
        """Calculate citation accuracy"""
        if not sources:
            return 0.6  # Neutral for no sources

        # Check for explicit citations
        citation_indicators = ['according to', 'based on', 'as stated', 'source:', '[', ']', '(', ')']
        has_citations = any(indicator in answer.lower() for indicator in citation_indicators)

        if has_citations:
            return 0.9

        # Check for implicit source alignment
        answer_words = set(answer.lower().split())
        source_alignment = 0

        for source in sources:
            source_text = source.get('text', str(source))
            source_words = set(source_text.lower().split())
            overlap = len(answer_words.intersection(source_words))
            alignment = overlap / len(answer_words) if answer_words else 0
            source_alignment = max(source_alignment, alignment)

        return min(1.0, 0.5 + source_alignment)

    def _calculate_question_match(self, query: str, answer: str) -> float:
        """Calculate question-answer match score"""
        query_words = set(query.lower().split())
        answer_words = set(answer.lower().split())

        # Remove stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        query_words = query_words - stop_words
        answer_words = answer_words - stop_words

        if not query_words:
            return 0.7

        overlap = len(query_words.intersection(answer_words))
        match_score = overlap / len(query_words)

        # Question type bonus
        if query.lower().startswith(('what', 'how', 'why', 'when', 'where')):
            if any(word in answer.lower() for word in ['is', 'are', 'by', 'because', 'in', 'at']):
                match_score += 0.1

        return min(1.0, match_score + 0.3)  # Add base score
    
    async def _current_production(self, query: str, answer: str, sources: List[Dict], user_id: str) -> Dict:
        """Current production trust score (whatever is in use)"""
        # This would call the actual production system
        # For now, simulate the current production behavior
        
        # Try enhanced first, fallback to legacy
        try:
            enhanced_result = await self._enhanced_v25(query, answer, sources, user_id)
            if enhanced_result.get("overall_score", 0) > 0:
                enhanced_result["method"] = "Current Production (Enhanced)"
                return enhanced_result
        except:
            pass
        
        # Fallback to legacy
        legacy_result = await self._legacy_deterministic(query, answer, sources)
        legacy_result["method"] = "Current Production (Legacy Fallback)"
        return legacy_result

    async def _independent_rag(self, query: str, answer: str, sources: List[Dict]) -> Dict:
        from services.independent_rag_evaluator import IndependentRAGEvaluator
        """Independent RAG evaluator (Without dependencies)"""

        evaluator = IndependentRAGEvaluator()

        # Convert sources to text list
        source_texts = []
        for source in sources:
            if isinstance(source, dict):
                source_texts.append(source.get('text', str(source)))
            else:
                source_texts.append(str(source))

        result = evaluator.evaluate_rag_response(query, answer, source_texts)
        result["method"] = "Independent RAG Evaluator"
        return result

    async def _aithentiq_convergence(self, query: str, answer: str, sources: List[Dict]) -> Dict:
        """AIthentiq Convergence Algorithm (Your innovative approach)"""
        from services.aithentiq_convergence import AIthentiqTrustScore

        convergence_scorer = AIthentiqTrustScore()

        result = convergence_scorer.compute_aithentiq_trust(query, answer, sources)
        result["method"] = "AIthentiq Convergence"
        return result

    def _analyze_results(self, results: List[TrustScoreResult]) -> Dict[str, Any]:
        """Analyze and compare results across methods"""
        if not results:
            return {"error": "No results to analyze"}

        scores = [r.score for r in results if r.error is None]
        times = [r.processing_time_ms for r in results if r.error is None]

        analysis = {
            "summary": {
                "methods_tested": len(results),
                "successful_methods": len([r for r in results if r.error is None]),
                "failed_methods": len([r for r in results if r.error is not None])
            },
            "score_analysis": {},
            "performance_analysis": {},
            "recommendations": []
        }

        if scores:
            analysis["score_analysis"] = {
                "highest_score": max(scores),
                "lowest_score": min(scores),
                "average_score": np.mean(scores),
                "score_variance": np.var(scores),
                "score_range": max(scores) - min(scores)
            }

            # Find best and worst performing methods
            best_method = max(results, key=lambda r: r.score if r.error is None else 0)
            worst_method = min(results, key=lambda r: r.score if r.error is None else 1)

            analysis["score_analysis"]["best_method"] = {
                "name": best_method.method_name,
                "score": best_method.score
            }
            analysis["score_analysis"]["worst_method"] = {
                "name": worst_method.method_name,
                "score": worst_method.score
            }

        if times:
            analysis["performance_analysis"] = {
                "fastest_time_ms": min(times),
                "slowest_time_ms": max(times),
                "average_time_ms": np.mean(times),
                "total_time_ms": sum(times)
            }

            # Find fastest and slowest methods
            fastest_method = min(results, key=lambda r: r.processing_time_ms if r.error is None else float('inf'))
            slowest_method = max(results, key=lambda r: r.processing_time_ms if r.error is None else 0)

            analysis["performance_analysis"]["fastest_method"] = {
                "name": fastest_method.method_name,
                "time_ms": fastest_method.processing_time_ms
            }
            analysis["performance_analysis"]["slowest_method"] = {
                "name": slowest_method.method_name,
                "time_ms": slowest_method.processing_time_ms
            }

        # Generate recommendations
        analysis["recommendations"] = self._generate_recommendations(results, analysis)

        return analysis

    def _generate_recommendations(self, results: List[TrustScoreResult], analysis: Dict) -> List[str]:
        """Generate recommendations based on comparison results"""
        recommendations = []

        if not results:
            return ["No results available for recommendations"]

        # Score-based recommendations
        if "score_analysis" in analysis:
            score_range = analysis["score_analysis"].get("score_range", 0)
            if score_range > 0.3:  # High variance in scores
                recommendations.append("High variance in trust scores detected - consider investigating why methods disagree")

            avg_score = analysis["score_analysis"].get("average_score", 0)
            if avg_score < 0.5:
                recommendations.append("Low average trust scores - consider improving answer quality or source relevance")
            elif avg_score > 0.8:
                recommendations.append("High trust scores across methods - answer appears reliable")

        # Performance-based recommendations
        if "performance_analysis" in analysis:
            avg_time = analysis["performance_analysis"].get("average_time_ms", 0)
            if avg_time > 1000:  # More than 1 second
                recommendations.append("Some methods are slow - consider optimizing for production use")

            fastest_time = analysis["performance_analysis"].get("fastest_time_ms", 0)
            slowest_time = analysis["performance_analysis"].get("slowest_time_ms", 0)
            if slowest_time > fastest_time * 10:  # 10x difference
                recommendations.append("Large performance differences between methods - consider speed vs accuracy trade-offs")

        # Error-based recommendations
        failed_methods = [r for r in results if r.error is not None]
        if failed_methods:
            recommendations.append(f"{len(failed_methods)} method(s) failed - check system configuration")

        if not recommendations:
            recommendations.append("All methods performed well - consider using fastest method for production")

        return recommendations

    def _result_to_dict(self, result: TrustScoreResult) -> Dict:
        """Convert TrustScoreResult to dictionary"""
        return {
            "method_name": result.method_name,
            "score": result.score,
            "processing_time_ms": result.processing_time_ms,
            "components": result.components,
            "explanation": result.explanation,
            "error": result.error,
            "score_percentage": f"{result.score:.1%}",
            "processing_time_formatted": f"{result.processing_time_ms:.1f}ms"
        }
